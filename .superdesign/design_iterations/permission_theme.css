:root {
  --background: oklch(0.9900 0 0);
  --foreground: oklch(0.1500 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1500 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1500 0 0);
  --primary: oklch(0.6200 0.1800 260.0000);
  --primary-foreground: oklch(0.9900 0 0);
  --secondary: oklch(0.9600 0 0);
  --secondary-foreground: oklch(0.2000 0 0);
  --muted: oklch(0.9600 0 0);
  --muted-foreground: oklch(0.5000 0 0);
  --accent: oklch(0.9500 0 0);
  --accent-foreground: oklch(0.2000 0 0);
  --destructive: oklch(0.6000 0.2000 15.0000);
  --destructive-foreground: oklch(0.9900 0 0);
  --border: oklch(0.9000 0 0);
  --input: oklch(0.9000 0 0);
  --ring: oklch(0.6200 0.1800 260.0000);
  --chart-1: oklch(0.6200 0.1800 260.0000);
  --chart-2: oklch(0.7000 0.1500 200.0000);
  --chart-3: oklch(0.6500 0.2000 300.0000);
  --chart-4: oklch(0.6000 0.1800 120.0000);
  --chart-5: oklch(0.5500 0.2000 60.0000);
  --sidebar: oklch(0.9800 0 0);
  --sidebar-foreground: oklch(0.1500 0 0);
  --sidebar-primary: oklch(0.6200 0.1800 260.0000);
  --sidebar-primary-foreground: oklch(0.9900 0 0);
  --sidebar-accent: oklch(0.9500 0 0);
  --sidebar-accent-foreground: oklch(0.2000 0 0);
  --sidebar-border: oklch(0.9000 0 0);
  --sidebar-ring: oklch(0.6200 0.1800 260.0000);
  --font-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --font-serif: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.10);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.10), 0 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.10), 0 4px 6px -4px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.10), 0 8px 10px -6px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Permission Denied Specific Styles */
.permission-denied {
  background: linear-gradient(135deg, var(--background) 0%, var(--muted) 100%);
  min-height: 100vh;
}

.permission-icon {
  color: var(--destructive);
  filter: drop-shadow(0 4px 8px hsl(0 0% 0% / 0.1));
}

.permission-card {
  background: var(--card);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.permission-title {
  color: var(--foreground);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.permission-message {
  color: var(--muted-foreground);
  line-height: 1.6;
}

.permission-list {
  background: var(--accent);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
}

.permission-item {
  color: var(--foreground);
  font-weight: 500;
}

.permission-button {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.permission-button:hover {
  background: oklch(0.5800 0.1800 260.0000);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.permission-button-secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
}

.permission-button-secondary:hover {
  background: var(--accent);
  border-color: var(--ring);
}
