<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permission Denied - App City CMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="permission_theme.css">
    <style>
        .animate-fade-in {
            animation: fadeIn 0.4s ease-out forwards;
        }
        
        .animate-slide-up {
            animation: slideUp 0.3s ease-out forwards;
        }
        
        .animate-bounce-in {
            animation: bounceIn 0.6s ease-out forwards;
        }
        
        .animate-pulse-slow {
            animation: pulseSlow 2s ease-in-out infinite;
        }
        
        .animate-stagger-1 {
            animation: fadeIn 0.5s ease-out 0.2s forwards;
            opacity: 0;
        }
        
        .animate-stagger-2 {
            animation: fadeIn 0.6s ease-out 0.4s forwards;
            opacity: 0;
        }
        
        .animate-stagger-3 {
            animation: slideInLeft 0.4s ease-out 0.6s forwards;
            opacity: 0;
        }
        
        .animate-stagger-4 {
            animation: slideInLeft 0.4s ease-out 0.7s forwards;
            opacity: 0;
        }
        
        .animate-stagger-5 {
            animation: slideInLeft 0.4s ease-out 0.8s forwards;
            opacity: 0;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        @keyframes bounceIn {
            0% { 
                opacity: 0; 
                transform: scale(0.8); 
            }
            50% { 
                opacity: 1; 
                transform: scale(1.1); 
            }
            100% { 
                opacity: 1; 
                transform: scale(1); 
            }
        }
        
        @keyframes pulseSlow {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes slideInLeft {
            from { 
                opacity: 0; 
                transform: translateX(-20px); 
            }
            to { 
                opacity: 1; 
                transform: translateX(0); 
            }
        }
        
        .hover-lift {
            transition: all 0.2s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
        }
        
        .button-hover {
            transition: all 0.2s ease;
        }
        
        .button-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        }
        
        .button-press:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body class="permission-denied">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="building" class="w-5 h-5 text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-gray-900">App City CMS</h1>
            </div>
            <nav class="flex items-center space-x-4 text-sm text-gray-600">
                <span>Trang chủ</span>
                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                <span>Quản trị</span>
                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                <span class="text-gray-900 font-medium">Lỗi</span>
            </nav>
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <i data-lucide="user" class="w-4 h-4 text-gray-600"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex items-center justify-center px-6 py-12">
        <div class="max-w-2xl w-full">
            <!-- Permission Card -->
            <div class="permission-card rounded-2xl p-8 text-center animate-fade-in animate-slide-up">
                <!-- Icon -->
                <div class="mb-6 animate-bounce-in">
                    <div class="w-20 h-20 mx-auto bg-red-50 rounded-full flex items-center justify-center permission-icon animate-pulse-slow">
                        <i data-lucide="shield-x" class="w-10 h-10"></i>
                    </div>
                </div>

                <!-- Title -->
                <h2 class="permission-title text-3xl font-bold mb-4 animate-stagger-1">
                    Truy cập bị từ chối
                </h2>

                <!-- Message -->
                <p class="permission-message text-lg mb-8 animate-stagger-2">
                    Bạn không có quyền truy cập trang này. Vui lòng liên hệ quản trị viên để được cấp quyền phù hợp.
                </p>

                <!-- Required Permissions -->
                <div class="permission-list p-6 mb-8 text-left animate-stagger-3">
                    <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                        <i data-lucide="key" class="w-5 h-5 mr-2 text-blue-600"></i>
                        Quyền cần thiết: 
                    </h3>
                    <ul class="space-y-3">
                        <li class="permission-item flex items-center animate-stagger-4">
                            <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-green-600"></i>
                            <span>Quản lý người dùng</span>
                        </li>
                        <li class="permission-item flex items-center animate-stagger-5">
                            <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-green-600"></i>
                            <span>Xem báo cáo</span>
                        </li>
                        <li class="permission-item flex items-center animate-stagger-5">
                            <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-green-600"></i>
                            <span>Quản lý cài đặt hệ thống</span>
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center animate-stagger-2">
                    <button class="permission-button permission-button-secondary button-hover button-press px-6 py-3 rounded-lg font-semibold">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                        Quay lại trang trước
                    </button>
                    <button class="permission-button button-hover button-press px-6 py-3 rounded-lg font-semibold">
                        <i data-lucide="mail" class="w-4 h-4 mr-2 inline"></i>
                        Liên hệ quản trị viên
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 border-t border-gray-200 px-6 py-4">
        <div class="max-w-7xl mx-auto text-center text-sm text-gray-600">
            <p>Cần hỗ trợ? Liên hệ: <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium"><EMAIL></a></p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Add interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Button ripple effect
            const buttons = document.querySelectorAll('.button-hover');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.4s ease-out;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => ripple.remove(), 400);
                });
            });
            
            // Add ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
