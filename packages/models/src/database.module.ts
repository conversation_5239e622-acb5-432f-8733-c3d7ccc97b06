import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { DataSource } from 'typeorm'
import { addTransactionalDataSource, deleteDataSourceByName } from 'typeorm-transactional'

import {
  AccountRepository,
  AssignPermissionRepository,
  AssignWorkTargetGroupRepository,
  CaseAdvancedRepository,
  CaseAnswerRepository,
  CaseSimpleRepository,
  CategoryRepository,
  EFormRepository,
  MediaRepository,
  OrganizationUnitRepository,
  ProcessRepository,
  RoleRepository,
  SessionRepository,
  StaffCardHistoryRepository,
  StaffContractRepository,
  StaffOrganizationHistoryRepository,
  StaffOrgEventRepository,
  StaffOrgHistoryActionRepository,
  StaffOrgRoleRepository,
  StaffPreviousPositionRepository,
  StaffRepository,
  StaffSpecializationRepository,
  StaffTrainingHistoryRepository,
  TrainingClassRepository,
  UploadSessionRepository,
  UserRepository,
  VerificationTokenRepository,
  WorkflowHistoryRepository,
  WorkflowRepository,
  WorkflowsSubtasksRepository,
  WorkTargetRepository,
} from './data-access'
import { AssignRoleRepository } from './data-access/assign-role.repository'
import { DatabaseService } from './database.service'
import {
  Account,
  AssignPermission,
  AssignRole,
  AssignWorkTargetGroup,
  Authenticator,
  CaseAdvanced,
  CaseAnswer,
  CaseSimple,
  Category,
  EForm,
  Media,
  OrganizationUnit,
  Process,
  Role,
  Session,
  Staff,
  StaffCardHistory,
  StaffContract,
  StaffOrganizationHistory,
  StaffOrgEvent,
  StaffOrgHistoryAction,
  StaffOrgRole,
  StaffPreviousPosition,
  StaffSpecialization,
  StaffTrainingHistory,
  TrainingClass,
  UploadPart,
  UploadSession,
  User,
  VerificationToken,
  Workflow,
  WorkflowHistory,
  WorkflowSubTask,
  WorkTargetGroup,
} from './entity'
import { TypeOrmConfigService } from './typeorm-config.service'

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      async dataSourceFactory(options) {
        if (!options) {
          throw new Error('Invalid options passed')
        }

        deleteDataSourceByName('default')
        const dataSource = new DataSource(options)

        return addTransactionalDataSource(dataSource)
      },
    }),
    TypeOrmModule.forFeature([
      Account,
      AssignPermission,
      AssignRole,
      Authenticator,
      CaseAdvanced,
      WorkTargetGroup,
      CaseAnswer,
      CaseSimple,
      Category,
      EForm,
      OrganizationUnit,
      Role,
      Session,
      Staff,
      StaffCardHistory,
      StaffContract,
      StaffOrganizationHistory,
      StaffOrgEvent,
      StaffOrgHistoryAction,
      StaffOrgRole,
      StaffPreviousPosition,
      StaffSpecialization,
      TrainingClass,
      StaffTrainingHistory,
      User,
      VerificationToken,
      Process,
      Workflow,
      WorkflowHistory,
      WorkflowSubTask,
      AssignWorkTargetGroup,
      UploadSession,
      UploadPart,
      Media,
    ]),
  ],
  providers: [
    AccountRepository,
    AssignPermissionRepository,
    AssignRoleRepository,
    CaseAdvancedRepository,
    CaseAnswerRepository,
    CaseSimpleRepository,
    CategoryRepository,
    EFormRepository,
    TrainingClassRepository,
    DatabaseService,
    OrganizationUnitRepository,
    ProcessRepository,
    RoleRepository,
    SessionRepository,
    StaffCardHistoryRepository,
    StaffContractRepository,
    StaffOrganizationHistoryRepository,
    StaffOrgEventRepository,
    StaffOrgHistoryActionRepository,
    StaffOrgRoleRepository,
    StaffPreviousPositionRepository,
    StaffRepository,
    StaffSpecializationRepository,
    StaffTrainingHistoryRepository,
    TypeOrmConfigService,
    UserRepository,
    VerificationTokenRepository,
    AccountRepository,
    SessionRepository,
    RoleRepository,
    AssignRoleRepository,
    AssignPermissionRepository,
    CategoryRepository,
    CaseSimpleRepository,
    OrganizationUnitRepository,
    CaseAnswerRepository,
    CaseAdvancedRepository,
    ProcessRepository,
    WorkTargetRepository,
    WorkflowRepository,
    WorkflowsSubtasksRepository,
    WorkflowHistoryRepository,
    AssignWorkTargetGroupRepository,
    UploadSessionRepository,
    MediaRepository,
  ],
  exports: [
    AccountRepository,
    AssignPermissionRepository,
    AssignRoleRepository,
    CaseAdvancedRepository,
    CaseAnswerRepository,
    CaseSimpleRepository,
    CategoryRepository,
    EFormRepository,
    TrainingClassRepository,
    DatabaseService,
    OrganizationUnitRepository,
    ProcessRepository,
    RoleRepository,
    SessionRepository,
    StaffCardHistoryRepository,
    StaffContractRepository,
    StaffOrganizationHistoryRepository,
    StaffOrgEventRepository,
    StaffOrgHistoryActionRepository,
    StaffOrgRoleRepository,
    StaffPreviousPositionRepository,
    StaffRepository,
    StaffSpecializationRepository,
    StaffTrainingHistoryRepository,
    UserRepository,
    VerificationTokenRepository,
    AccountRepository,
    SessionRepository,
    RoleRepository,
    AssignRoleRepository,
    AssignPermissionRepository,
    CategoryRepository,
    CaseSimpleRepository,
    OrganizationUnitRepository,
    CaseAnswerRepository,
    CaseAdvancedRepository,
    ProcessRepository,
    WorkTargetRepository,
    WorkflowRepository,
    WorkflowsSubtasksRepository,
    WorkflowHistoryRepository,
    AssignWorkTargetGroupRepository,
    UploadSessionRepository,
    MediaRepository,
  ],
})
export class DatabaseModule {}
