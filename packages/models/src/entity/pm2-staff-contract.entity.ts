import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm'

import { DB_TYPE } from './db.fn'
import { Media } from './media.entity'
import { OrganizationUnit } from './organization-unit.entity'
import { Staff } from './pm2-staff.entity'
import { StaffContractStatus, StaffContractType } from './types'

@Entity('staff_contract')
@Index(['staffId'])
@Index(['organizationUnitId'])
@Index(['contractNo', 'organizationUnitId'], { unique: true })
@Index(['status'])
export class StaffContract {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string // Oracle: ID

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation() })
  staffId!: string // Oracle: STAFF_ID

  @ManyToOne(() => Staff, { nullable: false })
  @JoinColumn({ name: 'staffId' })
  staff!: Staff

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation() })
  organizationUnitId!: string // Oracle: ORGANIZATION_UNIT_ID

  @ManyToOne(() => OrganizationUnit, { nullable: false })
  @JoinColumn({ name: 'organizationUnitId' })
  organizationUnit!: OrganizationUnit

  @Column(DB_TYPE.VARCHAR)
  contractNo!: string // Oracle: CONTRACT_NO

  @Column(DB_TYPE.VARCHAR)
  contractType!: StaffContractType // Oracle: CONTRACT_TYPE

  @Column(DB_TYPE.TIMESTAMP)
  startDate!: Date

  @Column(DB_TYPE.TIMESTAMP, { nullable: true })
  endDate?: Date

  @Column(DB_TYPE.VARCHAR, { default: StaffContractStatus.ACTIVE })
  status!: StaffContractStatus // Oracle: STATUS

  // Optional terms
  // @Column(DB_TYPE.VARCHAR, { nullable: true })
  // position?: string // Oracle: POSITION

  // @Column(DB_TYPE.VARCHAR, { nullable: true })
  // salaryScheme?: string // Oracle: SALARY_SCHEME

  // @Column(DB_TYPE.VARCHAR, { nullable: true })
  // workPattern?: string // Oracle: WORK_PATTERN (Full-time / Part-time)

  // Attachments & notes
  @Column(DB_TYPE.VARCHAR, { nullable: true })
  fileRef?: string // Oracle: FILE_REF

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  fileId?: string // Oracle: FILE_ID

  @ManyToOne(() => Media, { nullable: true })
  @JoinColumn({ name: 'fileId' })
  file?: Media

  @Column(DB_TYPE.TEXT, { nullable: true })
  note?: string // Oracle: NOTE

  @CreateDateColumn()
  createdAt!: Date

  @UpdateDateColumn()
  updatedAt!: Date
}
