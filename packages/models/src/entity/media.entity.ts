import { <PERSON>umn, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany, PrimaryColumn } from 'typeorm'

import { BaseTimestampEntity } from './base-timestamp.entity'
import { DB_TYPE, jsonTransformer, transformer } from './db.fn'
import { OrganizationUnit } from './organization-unit.entity'
import { StaffContract } from './pm2-staff-contract.entity'
import { UploadSession } from './upload-session.entity'
import { User } from './user.entity'

@Entity('medias')
@Index(['ownerId'])
@Index(['sessionId'])
@Index(['orgUnitId'])
export class Media extends BaseTimestampEntity {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string

  @Column(DB_TYPE.VARCHAR)
  bucket!: string

  @Column(DB_TYPE.VARCHAR, { unique: true })
  objectKey!: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  fileName?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  contentType?: string

  @Column({ type: DB_TYPE.BIGINT, nullable: true, transformer: transformer.bigint })
  contentLength?: number

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  checksum?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  type?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  eTag?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  publicUrl?: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  ownerId?: string

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'ownerId' })
  owner?: User

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  sessionId?: string

  @ManyToOne(() => UploadSession, { nullable: true })
  @JoinColumn({ name: 'sessionId' })
  session?: UploadSession

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  orgUnitId?: string

  @ManyToOne(() => OrganizationUnit, { nullable: true })
  @JoinColumn({ name: 'orgUnitId' })
  orgUnit?: OrganizationUnit

  @Column(DB_TYPE.TEXT, { nullable: true, transformer: jsonTransformer })
  metadata?: Record<string, unknown>

  @OneToMany('StaffContract', (contract: any) => contract.file)
  contracts?: StaffContract[]
}
