import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm'

import { Category } from './category.entity'
import { DB_TYPE, jsonTransformer } from './db.fn'
import { User } from './user.entity'

@Entity('organization_units')
export class OrganizationUnit {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string // ID đơn vị tổ chức

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'ID cũ của đơn vị' })
  oldId?: number

  @Column(DB_TYPE.VARCHAR, { comment: 'Mã đơn vị' })
  code!: string

  @Column(DB_TYPE.VARCHAR, { comment: 'Tên đơn vị' })
  name!: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: '<PERSON>ên đầy đủ của đơn vị' })
  fullName?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Địa chỉ' })
  address?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Người đại diện' })
  representative?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Số điện thoại' })
  phone?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Số fax' })
  fax?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Email' })
  email?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Website' })
  website?: string

  // Thông tin địa phương (dữ liệu cũ)
  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID tỉnh (dữ liệu cũ)' })
  oldProvinceId?: string

  @Column(DB_TYPE.UUID, { nullable: true, comment: 'ID thành phố' })
  cityId?: number

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID huyện (dữ liệu cũ)' })
  oldDistrictId?: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID xã (dữ liệu cũ)' })
  oldWardId?: string

  // Cấu trúc cây
  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID cơ quan cha' })
  parentId?: string

  @ManyToOne(() => OrganizationUnit, ou => ou.children, { nullable: true })
  @JoinColumn({ name: 'parentId' })
  parent?: OrganizationUnit

  @OneToMany(() => OrganizationUnit, ou => ou.parent)
  children?: OrganizationUnit[]

  // Quan hệ với nhân sự thuộc đơn vị
  @OneToMany('Staff', 'organizationUnit')
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  staff?: Array<any> // Staff[] at runtime - using any to avoid circular dependency

  @Column(DB_TYPE.NUMBER, { default: 1, comment: 'Trạng thái' })
  status!: number

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'Loại đơn vị' })
  type?: number

  @Column(DB_TYPE.VARCHAR, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID nhóm tổ chức' })
  organizationGroupId?: string

  // Thông tin thành lập
  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày thành lập' })
  establishmentDate?: Date

  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày ra quyết định' })
  decisionDate?: Date

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Số quyết định' })
  decisionNumber?: string

  // Lĩnh vực pháp lý
  @Column(DB_TYPE.VARCHAR, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID lĩnh vực pháp lý' })
  legalFieldId?: string

  @Column(DB_TYPE.TEXT, { nullable: true, transformer: jsonTransformer })
  legalFormId?: string[]

  // Phạm vi hoạt động
  @Column(DB_TYPE.VARCHAR, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID phạm vi hoạt động' })
  rangeActivitiesId?: string

  // Thông tin tổ chức
  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'Số lượng nhân viên' })
  payrollNumber?: number

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'Số phòng' })
  roomNumber?: number

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'Số câu lạc bộ' })
  clubNumber?: number

  // Quản trị
  @Column(DB_TYPE.VARCHAR, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID nhóm quản trị' })
  adminGroupId?: string

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'ID trạng thái quản trị' })
  adminStatusId?: number

  @Column(DB_TYPE.TEXT, { nullable: true, comment: 'Kinh nghiệm' })
  experience?: string

  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày hết hạn hợp đồng' })
  deadlineContractDate?: Date

  @Column(DB_TYPE.TEXT, { nullable: true, comment: 'Ghi chú' })
  note?: string

  @CreateDateColumn()
  createdAt!: Date

  @UpdateDateColumn()
  updatedAt!: Date

  // Relationships to Category entity
  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'oldProvinceId', referencedColumnName: 'categoryId' })
  oldProvince?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'oldDistrictId', referencedColumnName: 'categoryId' })
  oldDistrict?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'oldWardId', referencedColumnName: 'categoryId' })
  oldWard?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'organizationGroupId', referencedColumnName: 'categoryId' })
  organizationGroup?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'legalFieldId', referencedColumnName: 'categoryId' })
  legalField?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'rangeActivitiesId', referencedColumnName: 'categoryId' })
  rangeActivities?: Category

  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'adminGroupId', referencedColumnName: 'categoryId' })
  adminGroup?: Category

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID người tạo' })
  createById?: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true, comment: 'ID người cập nhật' })
  updateById?: string

  // Relations to User entity for audit
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'createById' })
  createdBy?: User

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updateById' })
  updatedBy?: User

  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày đăng ký tham gia TGPL / ngày hiệu lực hợp đồng' })
  issueDate?: Date

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Đơn vị cấp cho tổ chức tham gia TGPL' })
  issuingAgency?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Lý do cấp giấy đăng ký tham gia' })
  participationReason?: string

  @Column(DB_TYPE.TEXT, { nullable: true, comment: 'Lý do chấm dứt đăng ký tham gia TGPL' })
  registrationTerminationReason?: string

  @Column(DB_TYPE.TEXT, { nullable: true, comment: 'Lý do chấm dứt hợp đồng' })
  contractTerminationReason?: string

  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày hết hạn hợp đồng' })
  expiryDate?: Date

  @Column(DB_TYPE.NUMBER, { nullable: true, comment: 'Số tháng gia hạn hợp đồng' })
  renewalMonths?: number

  @Column(DB_TYPE.TIMESTAMP, { nullable: true, comment: 'Ngày hết hạn khi gia hạn' })
  renewedExpiryDate?: Date

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Số hợp đồng' })
  contractNumber?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Số đăng ký tham gia TGPL' })
  registrationNumber?: string

  @Column(DB_TYPE.TEXT, { nullable: true, comment: 'Từ khóa tìm kiếm' })
  keyword?: string

  // Thông tin file hợp đồng TGPL
  @Column(DB_TYPE.UUID, { nullable: true, comment: 'ID file hợp đồng TGPL' })
  contractFileId?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, comment: 'Đường dẫn hoặc key file hợp đồng TGPL' })
  contractFileRef?: string

  // Danh sách file đính kèm
  @Column(DB_TYPE.TEXT, {
    nullable: true,
    transformer: jsonTransformer,
    comment: 'Danh sách file đính kèm ',
  })
  attachments?: { fileId?: string; fileKey?: string; url?: string; name?: string }[]
}
