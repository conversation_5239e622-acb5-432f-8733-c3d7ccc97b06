import { UploadSessionStatus } from '@ac/data-types'
import { Colum<PERSON>, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany, PrimaryColumn } from 'typeorm'

import { BaseTimestampEntity } from './base-timestamp.entity'
import { DB_TYPE, jsonTransformer, transformer } from './db.fn'
import { UploadPart } from './upload-part.entity'
import { User } from './user.entity'

@Entity('upload_sessions')
export class UploadSession extends BaseTimestampEntity {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string

  @Column(DB_TYPE.VARCHAR)
  bucket!: string

  @Column(DB_TYPE.VARCHAR, { unique: true })
  objectKey!: string

  @Column(DB_TYPE.VARCHAR)
  uploadId!: string

  @Column(DB_TYPE.VARCHAR, { length: 32, default: UploadSessionStatus.INITIATED })
  status!: UploadSessionStatus

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  contentType?: string

  @Column({ type: DB_TYPE.BIGINT, nullable: true, transformer: transformer.bigint })
  contentLength?: number

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  checksum?: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  ownerId?: string

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'ownerId' })
  owner?: User

  @Column(DB_TYPE.TEXT, { nullable: true, transformer: jsonTransformer })
  metadata?: Record<string, unknown>

  @Column(DB_TYPE.TIMESTAMP, { nullable: true })
  expiresAt?: Date

  @OneToMany(() => UploadPart, part => part.session)
  parts?: UploadPart[]
}
