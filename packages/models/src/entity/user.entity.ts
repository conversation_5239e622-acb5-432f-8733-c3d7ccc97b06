import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm'

import { Account } from './account.entity'
import { AssignRole } from './assign-role.entity'
import { Authenticator } from './authenticator.entity'
import { DB_TYPE } from './db.fn'
import { OrganizationUnit } from './organization-unit.entity'
import { Staff } from './pm2-staff.entity'
import { Role } from './role.entity'
import { Session } from './session.entity'
import { UserStatus } from './types'

@Entity('users')
@Index('uq_user_staff_when_present', ['staffId'], { unique: true })
export class User {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  name?: string

  @Column(DB_TYPE.VARCHAR, { unique: true })
  email!: string

  @Column(DB_TYPE.VARCHAR, { nullable: true, select: false })
  password?: string

  @Column(DB_TYPE.NUMBER, { nullable: false, default: UserStatus.ACTIVE })
  status!: UserStatus

  @Column(DB_TYPE.TIMESTAMP, { nullable: true })
  emailVerified?: Date

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  image?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  phone?: string

  @Column(DB_TYPE.VARCHAR, { nullable: true })
  keyword?: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  staffId?: string | null

  @ManyToOne(() => OrganizationUnit, { nullable: true })
  @JoinColumn({ name: 'orgUnitId' })
  orgUnit?: OrganizationUnit

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: true })
  orgUnitId?: string

  @ManyToOne(() => Staff, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffId' })
  staff?: Staff

  @CreateDateColumn()
  createdAt!: Date

  @UpdateDateColumn()
  updatedAt!: Date

  // Relations
  @OneToMany(() => Account, account => account.user)
  accounts!: Account[]

  @OneToMany(() => Session, session => session.user)
  sessions!: Session[]

  @OneToMany(() => Authenticator, authenticator => authenticator.user)
  authenticators!: Authenticator[]

  @OneToMany(() => Role, role => role.createBy)
  createdRoles!: Role[]

  @OneToMany(() => Role, role => role.updateBy)
  updatedRoles!: Role[]

  @OneToMany(() => AssignRole, ar => ar.user)
  assignRoles!: AssignRole[]
}
