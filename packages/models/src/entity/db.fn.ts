import { Any } from '@ac/common'
import { AC_ENVS } from '@ac/utils'
import { PrimaryColumnOptions, ValueTransformer } from 'typeorm'
import { WithLengthColumnType } from 'typeorm/driver/types/ColumnTypes'
import { WithPrecisionColumnType } from 'typeorm/driver/types/ColumnTypes'

const generateUuidOracle = () => {
  return `LOWER(REGEXP_REPLACE(RAWTOHEX(SYS_GUID()), '(.{8})(.{4})(.{4})(.{4})(.{12})', '\\1-\\2-\\3-\\4-\\5'))`
}

export const transformer: Record<'date' | 'bigint', ValueTransformer> = {
  date: {
    from: (date: string | null) => date && new Date(parseInt(date, 10)),
    to: (date?: Date) => date?.valueOf().toString(),
  },
  bigint: {
    from: (bigInt: string | null) => bigInt && parseInt(bigInt, 10),
    to: (bigInt?: number) => bigInt?.toString(),
  },
}

export const getExpires = () => {
  return new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
}

const getUuidGenerate = (): PrimaryColumnOptions => {
  switch (AC_ENVS.DB_TYPE) {
    case 'oracle':
      return { length: 36, default: generateUuidOracle }
    case 'postgres':
      return { generated: 'uuid' }
    default:
      return { generated: 'uuid' }
  }
}

const getUuidRelation = () => {
  switch (AC_ENVS.DB_TYPE) {
    case 'oracle':
      return { length: 36 }
    case 'postgres':
      return {}
    default:
      return {}
  }
}

export const jsonTransformer: ValueTransformer = {
  to: (value: Any) => {
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }

    return value
  },
  from: (value: Any) => {
    try {
      if (typeof value !== 'string') {
        return value
      }

      return JSON.parse(value)
    } catch (error) {
      console.error('Error parsing JSON:', error)
      return value
    }
  },
}

export const splitStringTransformer: ValueTransformer = {
  to: (value: string[]) => value?.join(','),
  from: (value: string) => value?.split(',').filter(Boolean) || [],
}

const DB_TYPE_CONFIG = {
  oracle: {
    VARCHAR: 'varchar2',
    TEXT: 'clob',
    NUMBER: 'number' as WithPrecisionColumnType,
    TIMESTAMP: 'timestamp',
    UUID: 'char' as WithLengthColumnType,
    BIGINT: 'number',
    getUuidGenerate,
    getUuidRelation,
  },
  postgres: {
    VARCHAR: 'varchar',
    TEXT: 'text',
    NUMBER: 'integer' as WithPrecisionColumnType,
    TIMESTAMP: 'timestamp',
    UUID: 'uuid' as WithLengthColumnType,
    BIGINT: 'bigint',
    getUuidGenerate,
    getUuidRelation,
  },
} as const

// Freeze only the top level to prevent reassignment
export const DB_TYPE = DB_TYPE_CONFIG[AC_ENVS.DB_TYPE]
