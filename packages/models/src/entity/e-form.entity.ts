import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm'

import { AcBaseEntity } from './ac-base-entity'
import { DB_TYPE, jsonTransformer } from './db.fn'
import { OrganizationUnit } from './organization-unit.entity'
import { ModelStatus } from './types'

@Entity('e-forms')
export class EForm extends AcBaseEntity {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string

  @Column(DB_TYPE.VARCHAR)
  name?: string

  @Column(DB_TYPE.VARCHAR)
  code!: string

  @Column(DB_TYPE.VARCHAR, { default: ModelStatus.ACTIVE })
  status!: ModelStatus

  @Column(DB_TYPE.TEXT, { transformer: jsonTransformer })
  jsonData!: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation(), nullable: false })
  orgUnitId: string

  // Relations
  @ManyToOne(() => OrganizationUnit, { nullable: true })
  @JoinColumn({ name: 'orgUnitId' })
  orgUnit?: OrganizationUnit

  @Column(DB_TYPE.TEXT, { nullable: true })
  keyword?: string
}
