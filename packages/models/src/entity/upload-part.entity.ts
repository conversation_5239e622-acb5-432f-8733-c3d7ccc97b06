import { AC_ENVS } from '@ac/utils'
import { Column, ColumnType, Entity, Index, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm'

import { BaseTimestampEntity } from './base-timestamp.entity'
import { DB_TYPE, transformer } from './db.fn'
import { UploadSession } from './upload-session.entity'

@Entity('upload_parts')
@Index(['sessionId', 'partNumber'], { unique: true })
export class UploadPart extends BaseTimestampEntity {
  @PrimaryColumn(DB_TYPE.UUID, DB_TYPE.getUuidGenerate())
  id!: string

  @Column(DB_TYPE.UUID, { ...DB_TYPE.getUuidRelation() })
  sessionId!: string

  @Column(DB_TYPE.NUMBER)
  partNumber!: number

  @Column(DB_TYPE.VARCHAR)
  eTag!: string

  @Column({ type: DB_TYPE.BIGINT, nullable: true, transformer: transformer.bigint })
  size?: number

  @ManyToOne(() => UploadSession, session => session.parts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sessionId' })
  session!: UploadSession
}
