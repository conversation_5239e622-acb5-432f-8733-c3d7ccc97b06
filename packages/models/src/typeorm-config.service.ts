import { Any } from '@ac/common'
import { AC_ENVS } from '@ac/utils'
import { Injectable } from '@nestjs/common'
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm'
import { addTransactionalDataSource } from 'typeorm-transactional'

import {
  Account,
  AssignPermission,
  AssignRole,
  AssignWorkTargetGroup,
  Authenticator,
  CaseAdvanced,
  CaseAnswer,
  Category,
  EForm,
  Media,
  OrganizationUnit,
  Process,
  Role,
  Session,
  Staff,
  StaffCardHistory,
  StaffContract,
  StaffOrganizationHistory,
  StaffOrgEvent,
  StaffOrgHistoryAction,
  StaffOrgRole,
  StaffPreviousPosition,
  StaffSpecialization,
  StaffTrainingHistory,
  TrainingClass,
  UploadPart,
  UploadSession,
  User,
  VerificationToken,
  Workflow,
  WorkflowHistory,
  WorkflowSubTask,
  WorkTargetGroup,
} from './entity'
import { CaseSimple } from './entity/case-simple.entity'

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  createTypeOrmOptions(): TypeOrmModuleOptions {
    let options = {}
    if (AC_ENVS.DB_TYPE === 'oracle') {
      options = {
        host: AC_ENVS.DB_HOST,
        port: AC_ENVS.DB_PORT,
        username: AC_ENVS.DB_USERNAME,
        password: AC_ENVS.DB_PASSWORD,
      }
      if (AC_ENVS.DB_CONNECTION_KIND === 'sid') {
        options = {
          ...options,
          sid: AC_ENVS.DB_NAME,
        }
      } else {
        options = {
          ...options,
          serviceName: AC_ENVS.DB_NAME,
        }
      }
    } else {
      options = {
        url: AC_ENVS.DB_URL,
      }
    }

    return {
      subscribers: [],
      type: AC_ENVS.DB_TYPE as Any,
      ...options,
      logging: AC_ENVS.DB_LOGGING,
      entities: [
        Account,
        AssignPermission,
        AssignRole,
        Authenticator,
        CaseAdvanced,
        WorkTargetGroup,
        CaseAnswer,
        CaseSimple,
        Category,
        EForm,
        OrganizationUnit,
        Role,
        Session,
        Staff,
        StaffCardHistory,
        StaffContract,
        StaffOrganizationHistory,
        StaffOrgEvent,
        StaffOrgHistoryAction,
        StaffOrgRole,
        StaffPreviousPosition,
        StaffSpecialization,
        TrainingClass,
        StaffTrainingHistory,
        User,
        VerificationToken,
        Media,
        UploadSession,
        UploadPart,
        Process,
        Workflow,
        WorkflowHistory,
        WorkflowSubTask,
        AssignWorkTargetGroup,
      ],
      synchronize: true,
      dropSchema: false,
    }
  }
}
