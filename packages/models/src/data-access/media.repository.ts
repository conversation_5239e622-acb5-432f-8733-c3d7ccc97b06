import { Injectable } from '@nestjs/common'
import { DeepPartial } from 'typeorm'

import { DatabaseService } from '../database.service'
import { Media } from '../entity'

export type UpsertMediaParams = {
  bucket: string
  objectKey: string
  fileName?: string
  contentType?: string
  contentLength?: number
  checksum?: string
  eTag?: string
  publicUrl?: string
  type?: string
  ownerId?: string
  sessionId?: string
  orgUnitId?: string
  metadata?: Record<string, unknown>
}

@Injectable()
export class MediaRepository {
  constructor(private readonly db: DatabaseService) {}

  async upsert(params: UpsertMediaParams) {
    const existing = await this.db.media.findOne({ where: { objectKey: params.objectKey } })

    const payload: DeepPartial<Media> = {
      id: existing?.id,
      bucket: params.bucket,
      objectKey: params.objectKey,
      fileName: params.fileName ?? existing?.fileName ?? params.objectKey.split('/').pop(),
      contentType: params.contentType ?? existing?.contentType,
      contentLength: params.contentLength ?? existing?.contentLength,
      checksum: params.checksum ?? existing?.checksum,
      eTag: params.eTag ?? existing?.eTag,
      publicUrl: params.publicUrl ?? existing?.publicUrl,
      type: params.type ?? existing?.type,
      ownerId: params.ownerId ?? existing?.ownerId,
      sessionId: params.sessionId ?? existing?.sessionId,
      orgUnitId: params.orgUnitId ?? existing?.orgUnitId,
      metadata: params.metadata ?? existing?.metadata,
    }

    return this.db.media.save(payload)
  }

  findByObjectKey(objectKey: string) {
    return this.db.media.findOne({ where: { objectKey } })
  }

  findById(id: string) {
    return this.db.media.findOne({ where: { id } })
  }
}
