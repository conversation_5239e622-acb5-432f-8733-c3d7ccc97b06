import {
  applyListQueryFilters,
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  GetListQueryHelperOptions,
  Injectable,
} from '@ac/common'
import { DeepPartial, FindOptionsWhere } from 'typeorm'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { DatabaseService } from '../database.service'
import { EForm } from '../entity'

@Injectable()
export class EFormRepository {
  constructor(private readonly db: DatabaseService) {}

  create(createEFormDto: DeepPartial<EForm>) {
    return this.db.eForm.save(createEFormDto)
  }

  find(where: FindOptionsWhere<EForm>) {
    return this.db.eForm.find({ where })
  }

  async findAll(query: GetListQueryBaseAllDto, options?: GetListQueryHelperOptions<EForm>) {
    const queryBuilder = this.db.eForm.createQueryBuilder('form')

    applyListQueryFilters({ queryBuilder, options: options || {}, query, alias: 'form' })

    const items = await queryBuilder.getMany()

    return items
  }

  async findAllAndCount(query: GetListQueryBaseDto, options?: GetListQueryHelperOptions<EForm>) {
    const queryBuilder = this.db.eForm.createQueryBuilder('form')

    applyListQueryFilters({ queryBuilder, options: options || {}, query, alias: 'form' })

    const items = await queryBuilder.getMany()
    const total = await queryBuilder.getCount()

    return {
      items,
      total,
      totalPages: Math.ceil(total / (query.pageSize || 10)),
    }
  }

  async loadOrgUnitForItems(items: EForm[]): Promise<EForm[]> {
    if (items.length === 0) return items

    const itemsWithOrgUnit = await this.db.eForm.find({
      where: items.map(item => ({ id: item.id })),
      relations: ['orgUnit'],
    })

    // Restore original order
    return items
      .map(item => itemsWithOrgUnit.find(itemWithOrgUnit => itemWithOrgUnit.id === item.id))
      .filter((item): item is EForm => item !== undefined)
  }

  findOne(where: FindOptionsWhere<EForm>) {
    return this.db.eForm.findOne({ where })
  }

  findById(id: string) {
    return this.db.eForm.findOne({ where: { id } })
  }

  findByCode(code: string) {
    return this.db.eForm.findOne({ where: { code } })
  }

  update(id: string, updateEFormDto: QueryDeepPartialEntity<EForm>) {
    return this.db.eForm.update(id, updateEFormDto)
  }

  updateMany(where: FindOptionsWhere<EForm>, updateEFormDto: QueryDeepPartialEntity<EForm>) {
    return this.db.eForm.update(where, updateEFormDto)
  }

  remove(id: string) {
    return this.db.eForm.delete(id)
  }
}
