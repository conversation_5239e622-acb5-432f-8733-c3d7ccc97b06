import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import {
  Account,
  AssignPermission,
  AssignRole,
  AssignWorkTargetGroup,
  Authenticator,
  CaseAdvanced,
  CaseAnswer,
  CaseSimple,
  Category,
  EForm,
  Media,
  OrganizationUnit,
  Process,
  Role,
  Session,
  Staff,
  StaffCardHistory,
  StaffContract,
  StaffOrganizationHistory,
  StaffOrgEvent,
  StaffOrgHistoryAction,
  StaffOrgRole,
  StaffPreviousPosition,
  StaffSpecialization,
  StaffTrainingHistory,
  TrainingClass,
  UploadPart,
  UploadSession,
  User,
  VerificationToken,
  Workflow,
  WorkflowHistory,
  WorkflowSubTask,
  WorkTargetGroup,
} from './entity'

@Injectable()
export class DatabaseService {
  constructor(
    @InjectRepository(User)
    public readonly user: Repository<User>,

    @InjectRepository(Account)
    public readonly account: Repository<Account>,

    @InjectRepository(Session)
    public readonly session: Repository<Session>,

    @InjectRepository(VerificationToken)
    public readonly verificationToken: Repository<VerificationToken>,

    @InjectRepository(Authenticator)
    public readonly authenticator: Repository<Authenticator>,

    @InjectRepository(CaseAnswer)
    public readonly caseAnswer: Repository<CaseAnswer>,

    @InjectRepository(Category)
    public readonly category: Repository<Category>,

    @InjectRepository(EForm)
    public readonly eForm: Repository<EForm>,

    @InjectRepository(Role)
    public readonly role: Repository<Role>,

    @InjectRepository(AssignRole)
    public readonly assignRole: Repository<AssignRole>,

    @InjectRepository(AssignPermission)
    public readonly assignPermission: Repository<AssignPermission>,

    @InjectRepository(Staff)
    public readonly staff: Repository<Staff>,

    @InjectRepository(StaffOrganizationHistory)
    public readonly staffOrganizationHistory: Repository<StaffOrganizationHistory>,

    @InjectRepository(StaffOrgRole)
    public readonly staffOrgRole: Repository<StaffOrgRole>,

    @InjectRepository(StaffContract)
    public readonly staffContract: Repository<StaffContract>,

    @InjectRepository(StaffCardHistory)
    public readonly staffCardHistory: Repository<StaffCardHistory>,

    @InjectRepository(StaffTrainingHistory)
    public readonly staffTrainingHistory: Repository<StaffTrainingHistory>,

    @InjectRepository(TrainingClass)
    public readonly trainingClass: Repository<TrainingClass>,

    @InjectRepository(StaffOrgEvent)
    public readonly staffOrgEvent: Repository<StaffOrgEvent>,

    @InjectRepository(StaffOrgHistoryAction)
    public readonly staffOrgHistoryAction: Repository<StaffOrgHistoryAction>,

    @InjectRepository(StaffPreviousPosition)
    public readonly staffPreviousPosition: Repository<StaffPreviousPosition>,

    @InjectRepository(StaffSpecialization)
    public readonly staffSpecialization: Repository<StaffSpecialization>,

    @InjectRepository(CaseSimple)
    public readonly caseSimple: Repository<CaseSimple>,

    @InjectRepository(OrganizationUnit)
    public readonly organizationUnit: Repository<OrganizationUnit>,

    @InjectRepository(CaseAdvanced)
    public readonly caseAdvanced: Repository<CaseAdvanced>,

    @InjectRepository(WorkTargetGroup)
    public readonly workTargetGroup: Repository<WorkTargetGroup>,

    @InjectRepository(Process)
    public readonly process: Repository<Process>,

    @InjectRepository(Workflow)
    public readonly workflow: Repository<Workflow>,

    @InjectRepository(WorkflowSubTask)
    public readonly workflowsSubtasks: Repository<WorkflowSubTask>,

    @InjectRepository(WorkflowHistory)
    public readonly workflowHistory: Repository<WorkflowHistory>,

    @InjectRepository(AssignWorkTargetGroup)
    public readonly assignWorkTargetGroup: Repository<AssignWorkTargetGroup>,

    @InjectRepository(UploadSession)
    public readonly uploadSession: Repository<UploadSession>,

    @InjectRepository(UploadPart)
    public readonly uploadPart: Repository<UploadPart>,

    @InjectRepository(Media)
    public readonly media: Repository<Media>
  ) {}
}
