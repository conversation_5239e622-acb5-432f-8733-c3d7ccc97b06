import { AC_ENVS } from '@ac/utils'
import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddStorageFileRelationToStaffContract1759309700368 implements MigrationInterface {
  name = 'AddStorageFileRelationToStaffContract1759309700368'

  public async up(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const tableName = 'staff_contract'

    if (!(await queryRunner.hasTable(tableName))) {
      throw new Error(
        '[AddStorageFileRelationToStaffContract1759309700368] Bảng "staff_contract" chưa tồn tại. Hãy chạy các migration khởi tạo (ví dụ 1758009796883-init-db-oracle hoặc 1758010926103-init-db-postgres) trước khi chạy migration này.'
      )
    }

    const hasFileIdColumn = await queryRunner.hasColumn(tableName, 'fileId')
    if (!hasFileIdColumn) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD ("fileId" char(36))`)
      } else {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD COLUMN "fileId" uuid`)
      }
    }

    const hasIndex = await queryRunner.query(
      isOracle
        ? `SELECT index_name FROM user_indexes WHERE index_name = :1`
        : `SELECT indexname FROM pg_indexes WHERE schemaname = current_schema() AND indexname = $1`,
      ['IDX_staff_contract_file']
    )

    if (!hasIndex.length) {
      await queryRunner.query(`CREATE INDEX "IDX_staff_contract_file" ON "${tableName}" ("fileId")`)
    }

    const constraintName = 'FK_staff_contract_storage_file'
    const hasConstraint = await queryRunner.query(
      isOracle
        ? `SELECT constraint_name FROM user_constraints WHERE constraint_name = :1`
        : `SELECT constraint_name FROM information_schema.table_constraints WHERE constraint_schema = current_schema() AND constraint_name = $1`,
      [constraintName]
    )

    if (!hasConstraint.length) {
      const referencedTable = (await queryRunner.hasTable('storage_files')) ? 'storage_files' : 'medias'

      try {
        const orphanQuery = isOracle
          ? `SELECT "FILEID" FROM "${tableName.toUpperCase()}" WHERE "FILEID" IS NOT NULL AND NOT EXISTS (SELECT 1 FROM "${referencedTable.toUpperCase()}" WHERE "${referencedTable.toUpperCase()}"."ID" = "${tableName.toUpperCase()}"."FILEID") FETCH FIRST 1 ROWS ONLY`
          : `SELECT "fileId" FROM "${tableName}" WHERE "fileId" IS NOT NULL AND NOT EXISTS (SELECT 1 FROM "${referencedTable}" WHERE "${referencedTable}"."id" = "${tableName}"."fileId") LIMIT 1`

        const orphan = await queryRunner.query(orphanQuery)

        if (orphan.length) {
          console.warn(
            '[AddStorageFileRelationToStaffContract1759309700368] Skip FK_staff_contract_storage_file due to orphaned fileId records'
          )
          return
        }
      } catch (error) {
        console.warn(
          '[AddStorageFileRelationToStaffContract1759309700368] Skip FK check because staff_contract table is not accessible:',
          error instanceof Error ? error.message : error
        )
        return
      }

      await queryRunner.query(
        `ALTER TABLE "${tableName}" ADD CONSTRAINT "${constraintName}" FOREIGN KEY ("fileId") REFERENCES "${referencedTable}" ("id") ON DELETE SET NULL`
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const tableName = 'staff_contract'

    if (!(await queryRunner.hasTable(tableName))) {
      throw new Error(
        '[AddStorageFileRelationToStaffContract1759309700368] Không thể revert vì bảng "staff_contract" không tồn tại. Kiểm tra lại trạng thái schema.'
      )
    }

    const constraintName = 'FK_staff_contract_storage_file'
    const hasConstraint = await queryRunner.query(
      isOracle
        ? `SELECT constraint_name FROM user_constraints WHERE constraint_name = :1`
        : `SELECT constraint_name FROM information_schema.table_constraints WHERE constraint_schema = current_schema() AND constraint_name = $1`,
      [constraintName]
    )

    if (hasConstraint.length) {
      await queryRunner.query(`ALTER TABLE "${tableName}" DROP CONSTRAINT "${constraintName}"`)
    }

    const hasIndex = await queryRunner.query(
      isOracle
        ? `SELECT index_name FROM user_indexes WHERE index_name = :1`
        : `SELECT indexname FROM pg_indexes WHERE schemaname = current_schema() AND indexname = $1`,
      ['IDX_staff_contract_file']
    )

    if (hasIndex.length) {
      await queryRunner.query(`DROP INDEX "IDX_staff_contract_file"`)
    }

    const hasFileIdColumn = await queryRunner.hasColumn(tableName, 'fileId')
    if (hasFileIdColumn) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${tableName}" DROP ("fileId")`)
      } else {
        await queryRunner.query(`ALTER TABLE "${tableName}" DROP COLUMN "fileId"`)
      }
    }
  }
}
