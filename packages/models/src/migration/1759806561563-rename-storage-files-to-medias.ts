import { MigrationInterface, QueryRunner } from 'typeorm'

export class RenameStorageFilesToMedias1759806561563 implements MigrationInterface {
  name = 'RenameStorageFilesToMedias1759806561563'

  public async up(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const legacyTableName = 'storage_files'
    const targetTableName = 'medias'

    const legacyTableExists = await this.hasTable(queryRunner, legacyTableName)
    const targetTableExists = await this.hasTable(queryRunner, targetTableName)

    if (!legacyTableExists && targetTableExists) {
      // Table already renamed, skip rename process
      await this.ensureColumnsAndRelations(queryRunner, targetTableName, isOracle)
      return
    }

    if (!legacyTableExists && !targetTableExists) {
      throw new Error('Neither "storage_files" nor "medias" tables exist. Migration cannot proceed.')
    }

    if (legacyTableExists) {
      await queryRunner.query(`ALTER TABLE "${legacyTableName}" RENAME TO "${targetTableName}"`)

      if (await this.hasConstraint(queryRunner, 'PK_storage_files_id', isOracle)) {
        await queryRunner.query(
          `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "PK_storage_files_id" TO "PK_medias_id"`
        )
      }

      if (await this.hasConstraint(queryRunner, 'UQ_storage_files_objectKey', isOracle)) {
        await queryRunner.query(
          `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "UQ_storage_files_objectKey" TO "UQ_medias_objectKey"`
        )
      }

      if (await this.hasConstraint(queryRunner, 'FK_storage_files_owner', isOracle)) {
        await queryRunner.query(
          `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "FK_storage_files_owner" TO "FK_medias_owner"`
        )
      }

      if (await this.hasConstraint(queryRunner, 'FK_storage_files_session', isOracle)) {
        await queryRunner.query(
          `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "FK_storage_files_session" TO "FK_medias_session"`
        )
      }

      if (await this.hasIndex(queryRunner, 'IDX_storage_files_owner', isOracle)) {
        await queryRunner.query(`ALTER INDEX "IDX_storage_files_owner" RENAME TO "IDX_medias_owner"`)
      }

      if (await this.hasIndex(queryRunner, 'IDX_storage_files_session', isOracle)) {
        await queryRunner.query(`ALTER INDEX "IDX_storage_files_session" RENAME TO "IDX_medias_session"`)
      }
    }

    await this.ensureColumnsAndRelations(queryRunner, targetTableName, isOracle)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const legacyTableName = 'storage_files'
    const targetTableName = 'medias'

    const targetTableExists = await this.hasTable(queryRunner, targetTableName)

    if (!targetTableExists) {
      // Nothing to revert
      return
    }

    if (await this.hasConstraint(queryRunner, 'FK_medias_org_unit', isOracle)) {
      await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP CONSTRAINT "FK_medias_org_unit"`)
    }

    if (await this.hasIndex(queryRunner, 'IDX_medias_org_unit', isOracle)) {
      if (isOracle) {
        await queryRunner.query(`DROP INDEX "IDX_medias_org_unit"`)
      } else {
        await queryRunner.query(`DROP INDEX "IDX_medias_org_unit"`)
      }
    }

    if (await this.hasColumn(queryRunner, targetTableName, 'orgUnitId')) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP ("orgUnitId")`)
      } else {
        await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP COLUMN "orgUnitId"`)
      }
    }

    if (await this.hasColumn(queryRunner, targetTableName, 'type')) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP ("type")`)
      } else {
        await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP COLUMN "type"`)
      }
    }

    if (await this.hasConstraint(queryRunner, 'FK_medias_session', isOracle)) {
      await queryRunner.query(
        `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "FK_medias_session" TO "FK_storage_files_session"`
      )
    }

    if (await this.hasConstraint(queryRunner, 'FK_medias_owner', isOracle)) {
      await queryRunner.query(
        `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "FK_medias_owner" TO "FK_storage_files_owner"`
      )
    }

    if (await this.hasConstraint(queryRunner, 'UQ_medias_objectKey', isOracle)) {
      await queryRunner.query(
        `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "UQ_medias_objectKey" TO "UQ_storage_files_objectKey"`
      )
    }

    if (await this.hasConstraint(queryRunner, 'PK_medias_id', isOracle)) {
      await queryRunner.query(
        `ALTER TABLE "${targetTableName}" RENAME CONSTRAINT "PK_medias_id" TO "PK_storage_files_id"`
      )
    }

    if (await this.hasIndex(queryRunner, 'IDX_medias_session', isOracle)) {
      await queryRunner.query(`ALTER INDEX "IDX_medias_session" RENAME TO "IDX_storage_files_session"`)
    }

    if (await this.hasIndex(queryRunner, 'IDX_medias_owner', isOracle)) {
      await queryRunner.query(`ALTER INDEX "IDX_medias_owner" RENAME TO "IDX_storage_files_owner"`)
    }

    const legacyTableExists = await this.hasTable(queryRunner, legacyTableName)

    if (!legacyTableExists) {
      await queryRunner.query(`ALTER TABLE "${targetTableName}" RENAME TO "${legacyTableName}"`)
    }
  }

  private async hasTable(queryRunner: QueryRunner, tableName: string): Promise<boolean> {
    try {
      return await queryRunner.hasTable(tableName)
    } catch {
      return false
    }
  }

  private async hasColumn(queryRunner: QueryRunner, tableName: string, columnName: string): Promise<boolean> {
    try {
      return await queryRunner.hasColumn(tableName, columnName)
    } catch {
      return false
    }
  }

  private async hasConstraint(queryRunner: QueryRunner, constraintName: string, isOracle: boolean): Promise<boolean> {
    if (isOracle) {
      const result = await queryRunner.query(
        `SELECT constraint_name FROM user_constraints WHERE constraint_name = :1`,
        [constraintName]
      )
      return result.length > 0
    }

    const result = await queryRunner.query(
      `SELECT constraint_name FROM information_schema.table_constraints WHERE constraint_schema = current_schema() AND constraint_name = $1`,
      [constraintName]
    )
    return result.length > 0
  }

  private async hasIndex(queryRunner: QueryRunner, indexName: string, isOracle: boolean): Promise<boolean> {
    if (isOracle) {
      const result = await queryRunner.query(`SELECT index_name FROM user_indexes WHERE index_name = :1`, [indexName])
      return result.length > 0
    }

    const result = await queryRunner.query(
      `SELECT indexname FROM pg_indexes WHERE schemaname = current_schema() AND indexname = $1`,
      [indexName]
    )
    return result.length > 0
  }

  private async ensureColumnsAndRelations(queryRunner: QueryRunner, tableName: string, isOracle: boolean) {
    const hasOrgUnitColumn = await this.hasColumn(queryRunner, tableName, 'orgUnitId')

    if (!hasOrgUnitColumn) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD ("orgUnitId" char(36))`)
      } else {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD COLUMN "orgUnitId" uuid`)
      }
    }

    const hasTypeColumn = await this.hasColumn(queryRunner, tableName, 'type')
    if (!hasTypeColumn) {
      if (isOracle) {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD ("type" varchar2(255))`)
      } else {
        await queryRunner.query(`ALTER TABLE "${tableName}" ADD COLUMN "type" character varying`)
      }
    }

    if (!(await this.hasIndex(queryRunner, 'IDX_medias_org_unit', isOracle))) {
      await queryRunner.query(`CREATE INDEX "IDX_medias_org_unit" ON "${tableName}" ("orgUnitId")`)
    }

    if (!(await this.hasConstraint(queryRunner, 'FK_medias_org_unit', isOracle))) {
      await queryRunner.query(
        `ALTER TABLE "${tableName}" ADD CONSTRAINT "FK_medias_org_unit" FOREIGN KEY ("orgUnitId") REFERENCES "organization_units" ("id") ON DELETE SET NULL`
      )
    }
  }
}
