import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddStorageFileTypeColumn1759465835246 implements MigrationInterface {
  name = 'AddStorageFileTypeColumn1759465835246'

  public async up(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const legacyTableName = 'storage_files'
    const targetTableName = (await queryRunner.hasTable(legacyTableName)) ? legacyTableName : 'medias'

    if (!(await queryRunner.hasTable(targetTableName))) {
      throw new Error(`Table "${legacyTableName}" or "medias" not found. Migration cannot proceed.`)
    }

    const hasTypeColumn = await queryRunner.hasColumn(targetTableName, 'type')
    if (hasTypeColumn) {
      return
    }

    if (isOracle) {
      await queryRunner.query(`ALTER TABLE "${targetTableName}" ADD ("type" varchar2(255))`)
      return
    }

    await queryRunner.query(`ALTER TABLE "${targetTableName}" ADD COLUMN "type" character varying`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const isOracle = queryRunner.connection.options.type === 'oracle'
    const legacyTableName = 'storage_files'
    const targetTableName = (await queryRunner.hasTable(legacyTableName)) ? legacyTableName : 'medias'

    if (!(await queryRunner.hasTable(targetTableName))) {
      return
    }

    const hasTypeColumn = await queryRunner.hasColumn(targetTableName, 'type')
    if (!hasTypeColumn) {
      return
    }

    if (isOracle) {
      await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP ("type")`)
      return
    }

    await queryRunner.query(`ALTER TABLE "${targetTableName}" DROP COLUMN "type"`)
  }
}
