import { AC_ENVS } from '@ac/utils'
import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFieldOfOrganizationUnitsTable1759675452745 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    if (AC_ENVS.DB_TYPE !== 'oracle') {
      return
    }

    await queryRunner.query(
      `ALTER TABLE "organization_units" ADD ("contractFileId" char(36) DEFAULT LOWER(REGEXP_REPLACE(RAWTOHEX(SYS_GUID()), '(.{8})(.{4})(.{4})(.{4})(.{12})', '\\1-\\2-\\3-\\4-\\5')))`
    )
    await queryRunner.query(`ALTER TABLE "organization_units" ADD ("contractFileRef" varchar2(255))`)
    await queryRunner.query(`ALTER TABLE "organization_units" ADD ("attachments" CLOB)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "organization_units" DROP COLUMN "contractFileId"`)
    await queryRunner.query(`ALTER TABLE "organization_units" DROP COLUMN "contractFileRef"`)
    await queryRunner.query(`ALTER TABLE "organization_units" DROP COLUMN "attachments"`)
  }
}
