#!/usr/bin/env tsx
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { existsSync, mkdirSync, readdirSync, readFileSync, writeFileSync } from 'fs'
import path, { join } from 'path'
import 'reflect-metadata'

// Import all entities for analysis
import * as entities from './entity'

const entityDir = join(process.cwd(), '../', '../', 'packages', 'models', 'src', 'entity')

// Logger class to collect logs
class GenerationLogger {
  private showConsole: boolean = false
  private logs: string[] = []

  constructor(showConsole: boolean = false) {
    this.showConsole = showConsole
  }

  log(message: string) {
    this.logs.push(message)

    if (this.showConsole) {
      console.log(message)
    }
  }

  error(message: string) {
    this.logs.push(`❌ ${message}`)

    if (this.showConsole) {
      console.error(`❌ ${message}`)
    }
  }

  warn(message: string) {
    this.logs.push(`⚠️ ${message}`)

    if (this.showConsole) {
      console.warn(`⚠️ ${message}`)
    }
  }

  getLogs(): string[] {
    return [...this.logs]
  }

  getLogsAsString(): string {
    return this.logs.join('\n')
  }

  clear() {
    this.logs = []
  }
}

const logger = new GenerationLogger()

function extractEnumTypesFromEntities(): Set<string> {
  const enumTypes = new Set<string>()

  for (const [entityName, EntityClass] of Object.entries(entities)) {
    try {
      // Tìm file entity tương ứng (same logic as analyzeEntity)
      const fileName = entityName
        .replace(/([A-Z])/g, (match, letter, index) => (index > 0 ? '-' : '') + letter.toLowerCase())
        .toLowerCase()

      // Alternative naming for compound words: WorkflowSubTask → workflow-subtask (without dash in SubTask)
      const fileNameAlt = entityName.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()

      // Special case for specific naming patterns: WorkflowSubTask → workflow-subtask (keep compound words)
      const fileNameSpecial = entityName
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .toLowerCase()
        .replace('-sub-task', '-subtask') // WorkflowSubTask -> workflow-subtask
        .replace('-sub-', '-sub') // any other SubXxx patterns

      const possiblePaths = [
        join(entityDir, `${fileName}.entity.ts`),
        join(entityDir, `${fileNameAlt}.entity.ts`), // alternative naming
        join(entityDir, `${fileNameSpecial}.entity.ts`), // special patterns
        join(entityDir, `pm2-${fileName}.entity.ts`),
      ]

      let entityPath = ''
      for (const path of possiblePaths) {
        if (existsSync(path)) {
          entityPath = path
          break
        }
      }

      if (entityPath) {
        const content = readFileSync(entityPath, 'utf-8')

        // Extract enum types from property declarations like: status!: CategoryStatus
        // Only match specific enum patterns, exclude entity types
        const enumMatches = content.matchAll(/:\s*([A-Z][a-zA-Z]*(?:Status|Type|Level|Result))\b/g)
        for (const match of enumMatches) {
          enumTypes.add(match[1] as string)
        }

        // Special case for business role enums (but not entity names)
        const businessRoleMatches = content.matchAll(/:\s*(Staff(?:Business)?Role|BusinessRole)\b/g)
        for (const match of businessRoleMatches) {
          enumTypes.add(match[1] as string)
        }

        // Extract enum types from default values like: { default: CategoryStatus.INIT }
        const defaultMatches = content.matchAll(/default:\s*([A-Z][a-zA-Z]*)\./g)
        for (const match of defaultMatches) {
          enumTypes.add(match[1] as string)
        }

        // Extract enum types from import statements
        // Only match specific enum patterns from types directory
        const importMatches = content.matchAll(
          /import\s*\{[^}]*?([A-Z][a-zA-Z]*(?:Status|Type|Level|Result))[^}]*?\}\s*from\s*['"][^'"]*types/g
        )
        for (const match of importMatches) {
          enumTypes.add(match[1] as string)
        }

        // Special case for business role enums from types
        const businessRoleImportMatches = content.matchAll(
          /import\s*\{[^}]*?(Staff(?:Business)?Role|BusinessRole)[^}]*?\}\s*from\s*['"][^'"]*types/g
        )
        for (const match of businessRoleImportMatches) {
          enumTypes.add(match[1] as string)
        }
      }
    } catch (error) {
      logger.warn(
        `Failed to extract enums from ${entityName}: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  return enumTypes
}

function parseEnumFromFile(filePath: string): Record<string, string> {
  try {
    const content = readFileSync(filePath, 'utf-8')
    const enums: Record<string, string> = {}

    // Find all enum declarations (with multiline support)
    const enumRegex = /export enum (\w+)\s*\{([^}]+)\}/gs
    let match

    while ((match = enumRegex.exec(content)) !== null) {
      const enumName = match[1] as string
      const enumBody = match[2]

      // Parse enum members using regex to match NAME = VALUE patterns
      let enumDef = `export enum ${enumName} {\n`

      // Use regex to find enum member patterns - supports both string and number values
      const memberRegex = /(\w+)\s*=\s*(?:['"]\w+['"]|\d+)(?:\s*,|\s*\/\/[^\n]*|\s*$)/g
      const members: string[] = []
      let memberMatch

      while ((memberMatch = memberRegex.exec(enumBody as string)) !== null) {
        const fullMatch = memberMatch[0]
        const cleanMember = fullMatch
          .replace(/\/\/.*$/, '')
          .replace(/,$/, '')
          .trim()
        if (cleanMember) {
          members.push(cleanMember)
        }
      }

      for (const member of members) {
        enumDef += `  ${member},\n`
      }
      enumDef += '}\n\n'

      enums[enumName] = enumDef
      logger.log(`📝 Parsed enum: ${enumName} from ${path.basename(filePath)}`)
    }

    return enums
  } catch (error) {
    logger.log(`❌ Error parsing enum file ${filePath}: ${error}`)
    return {}
  }
}

function loadEnumsFromTypesDirectory(): Record<string, string> {
  const typesDir = join(process.cwd(), '../', '../', 'packages', 'models', 'src', 'entity', 'types')
  const allEnums: Record<string, string> = {}

  logger.log(`🔍 Scanning types directory: ${typesDir}`)

  try {
    const files = readdirSync(typesDir).filter(file => file.endsWith('.enum.ts') || file.endsWith('.constance.ts'))

    logger.log(`📁 Found enum files: ${files.join(', ')}`)

    for (const file of files) {
      const filePath = join(typesDir, file)
      const fileEnums = parseEnumFromFile(filePath)
      Object.assign(allEnums, fileEnums)
    }

    logger.log(`✅ Loaded ${Object.keys(allEnums).length} enums total`)
    return allEnums
  } catch (error) {
    logger.error(`Error reading types directory: ${error}`)
    return {}
  }
}

// Generate enum definitions based on detected types
function generateEnumFromType(enumName: string, loadedEnums: Record<string, string>): string {
  // Check if we have this enum loaded from types directory
  if (loadedEnums[enumName]) {
    return loadedEnums[enumName]
  }

  // Fallback: generate basic enum structure
  logger.log(`⚠️ Enum ${enumName} not found in types directory, generating placeholder`)
  return `export enum ${enumName} {\n  // TODO: Add enum values\n}\n\n`
}

interface PropertyInfo {
  name: string
  type: string
  isOptional: boolean
  isArray: boolean
  description?: string
  transformerType?: string
}

// Base entity properties that should be inherited
const BASE_TIMESTAMP_PROPERTIES: PropertyInfo[] = [
  { name: 'createdAt', type: 'Date', isOptional: false, isArray: false },
  { name: 'updatedAt', type: 'Date', isOptional: false, isArray: false },
]

const AC_BASE_ENTITY_PROPERTIES: PropertyInfo[] = [
  { name: 'updatedById', type: 'string', isOptional: true, isArray: false },
  { name: 'createdById', type: 'string', isOptional: true, isArray: false },
  { name: 'updatedBy', type: 'User', isOptional: true, isArray: false },
  { name: 'createdBy', type: 'User', isOptional: true, isArray: false },
]

function parseEntityFile(entityPath: string): { name: string; properties: PropertyInfo[]; analysisMethod?: string } {
  const content = readFileSync(entityPath, 'utf-8')
  const lines = content.split('\n')

  // Find entity name from class declaration
  const classMatch = content.match(/export class (\w+)/)
  const entityName = classMatch ? classMatch[1] : 'Unknown'

  // Check if entity extends base classes
  const extendsAcBase = content.includes('extends AcBaseEntity')
  const extendsBaseTimestamp = content.includes('extends BaseTimestampEntity')

  const properties: PropertyInfo[] = []

  let currentProp: Partial<PropertyInfo> = {}
  let inPropertyDef = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines?.[i]?.trim() || ''

    // Skip comments and irrelevant decorators
    if (line.startsWith('//') || line.startsWith('*') || line.startsWith('/*')) continue

    // Skip imports, empty lines, JoinColumn decorators, and decorator options
    // BUT DO NOT skip lines that contain @Column decorators even if they have options
    if (
      line.startsWith('import') ||
      line === '' ||
      line.startsWith('}') ||
      (line.includes('@JoinColumn') && !line.includes('@Column')) ||
      (line.includes('nullable:') &&
        !line.includes('@Column') &&
        !line.includes('@ManyToOne') &&
        !line.includes('@OneToMany') &&
        !line.includes('@OneToOne') &&
        !line.includes('@ManyToMany')) ||
      (line.includes('onDelete:') && !line.includes('@Column')) ||
      (line.includes('onUpdate:') && !line.includes('@Column')) ||
      (line.includes('default:') && !line.includes('@Column')) ||
      (line.includes('comment:') && !line.includes('@Column')) ||
      (line.includes('to:') && !line.includes('@Column')) ||
      (line.includes('from:') && !line.includes('@Column')) ||
      line.startsWith('if ') ||
      line.startsWith('return ') ||
      line.startsWith('try ') ||
      line.includes('JSON.') ||
      line.includes('typeof ') ||
      line.includes('Array.isArray') ||
      line.includes('JSON.parse') ||
      line.includes('JSON.stringify')
    ) {
      continue
    }

    // Detect column/property decorators
    if (
      line.includes('@Column') ||
      line.includes('@PrimaryColumn') ||
      line.includes('@CreateDateColumn') ||
      line.includes('@UpdateDateColumn') ||
      line.includes('@DeleteDateColumn')
    ) {
      inPropertyDef = true
      currentProp = { isOptional: false, isArray: false }

      // Parse column type from decorator
      const typeMatch = line.match(/@(?:Primary)?Column\([^,)]*([^)]*)\)/)
      if (typeMatch) {
        const paramStr = typeMatch[1] || ''
        if (paramStr.includes('nullable: true')) {
          currentProp.isOptional = true
        }
      }

      // Check for transformer in next lines
      let checkNext = i + 1
      while (checkNext < lines.length && checkNext <= i + 3) {
        const nextLine = lines?.[checkNext]?.trim() || ''
        if (nextLine.includes('transformer:')) {
          if (nextLine.includes('splitStringTransformer')) {
            // Mark for string[] type
            currentProp.transformerType = 'string[]'
          } else if (nextLine.includes('jsonTransformer')) {
            // Mark for any type (JSON data)
            currentProp.transformerType = 'any'
          }
          // Skip custom transformer objects but continue parsing
          break
        }
        if (nextLine.includes('}') || nextLine.includes('@') || nextLine.includes('!:')) {
          break
        }
        checkNext++
      }
      continue
    }

    // Detect relation decorators

    if (
      line.includes('@OneToMany') ||
      line.includes('@ManyToOne') ||
      line.includes('@OneToOne') ||
      line.includes('@ManyToMany')
    ) {
      inPropertyDef = true
      currentProp = { isOptional: true, isArray: false }

      if (line.includes('@OneToMany') || line.includes('@ManyToMany')) {
        currentProp.isArray = true
      }
      continue
    }

    // Parse property declaration
    if (inPropertyDef && line.includes(':')) {
      // Skip if this is a decorator option (like transformer:, comment:, etc)
      if (
        line.includes('transformer:') ||
        line.includes('comment:') ||
        line.includes('nullable:') ||
        line.includes('default:')
      ) {
        continue
      }

      const propMatch = line.match(/(\w+)([!?])?\s*:\s*([^;=\n]+)/)

      if (propMatch) {
        const [, propName, optional, typeStr] = propMatch

        currentProp.name = propName
        // Handle TypeScript modifiers: ! = non-null, ? = optional
        if (optional === '?') {
          currentProp.isOptional = true
        } else if (optional === '!') {
          currentProp.isOptional = false // Explicitly non-null
        }
        // Keep existing optional flag from decorator if no modifier

        // Parse type
        let typeName = typeStr?.trim() || ''

        // Check if we have transformer type override
        if (currentProp.transformerType) {
          currentProp.type = currentProp.transformerType
          if (currentProp.transformerType === 'string[]') {
            currentProp.isArray = true
          }
        } else {
          // Handle arrays
          if (typeName.endsWith('[]')) {
            currentProp.isArray = true
            typeName = typeName.replace('[]', '')
          }

          // Handle union types like "string | null", "number | undefined"
          if (typeName.includes(' | ')) {
            const unionTypes = typeName.split(' | ').map(t => t.trim())
            // Get the main type (not null or undefined)
            const mainType = unionTypes.find(t => t !== 'null' && t !== 'undefined') || unionTypes[0]
            typeName = mainType || ''
            // If null or undefined is in the union, then the property is optional
            if (unionTypes.includes('null') || unionTypes.includes('undefined')) {
              currentProp.isOptional = true
            }
          }

          // Map common types
          if (typeName === 'string') {
            currentProp.type = 'string'
          } else if (typeName === 'number') {
            currentProp.type = 'number'
          } else if (typeName === 'boolean') {
            currentProp.type = 'boolean'
          } else if (typeName === 'Date') {
            currentProp.type = 'Date'
          } else if (typeName.includes('Status') || typeName.includes('Type') || typeName.includes('Result')) {
            // Enums
            currentProp.type = typeName
          } else if (typeName === 'Any' || typeName === 'any') {
            currentProp.type = 'any'
          } else {
            // Entity references - need to check if it's a built-in type
            // If it starts with uppercase, it might be an Entity class
            if (typeName[0] && typeName[0] === typeName[0].toUpperCase()) {
              currentProp.type = typeName
            } else {
              // Fallback for undefined types
              currentProp.type = typeName
            }
          }
        }

        properties.push(currentProp as PropertyInfo)
        currentProp = {}
        inPropertyDef = false
      }
    }

    // Reset if we hit a new decorator or end of property
    if (
      line.includes('@') &&
      !line.includes('@Column') &&
      !line.includes('@PrimaryColumn') &&
      !line.includes('DateColumn') &&
      !line.includes('ToMany') &&
      !line.includes('ToOne') &&
      !line.includes('@JoinColumn') // Skip @JoinColumn decorator
    ) {
      inPropertyDef = false
      currentProp = {}
    }
  }

  // Add base class properties if entity extends them
  if (extendsAcBase) {
    // Add both AcBaseEntity and BaseTimestampEntity properties
    properties.push(...BASE_TIMESTAMP_PROPERTIES, ...AC_BASE_ENTITY_PROPERTIES)
    logger.log(`✅ Added base properties for ${entityName} (extends AcBaseEntity)`)
  } else if (extendsBaseTimestamp) {
    // Only add BaseTimestampEntity properties
    properties.push(...BASE_TIMESTAMP_PROPERTIES)
    logger.log(`✅ Added timestamp properties for ${entityName} (extends BaseTimestampEntity)`)
  }

  return { name: entityName || '', properties, analysisMethod: 'file-parser' }
}

function analyzeEntity(EntityClass: any): { name: string; properties: PropertyInfo[]; analysisMethod?: string } {
  const entityName = EntityClass.name

  // Find corresponding entity file
  // Convert CamelCase to kebab-case: Account → account, CaseAdvanced → case-advanced
  const fileName = entityName
    .replace(/([A-Z])/g, (match: any, letter: any, index: any) => (index > 0 ? '-' : '') + letter.toLowerCase())
    .toLowerCase()

  // Alternative naming for compound words: WorkflowSubTask → workflow-subtask (without dash in SubTask)
  const fileNameAlt = entityName.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()

  // Special case for specific naming patterns: WorkflowSubTask → workflow-subtask (keep compound words)
  const fileNameSpecial = entityName
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .toLowerCase()
    .replace('-sub-task', '-subtask') // WorkflowSubTask -> workflow-subtask
    .replace('-sub-', '-sub') // any other SubXxx patterns
  // Try multiple possible paths
  const possiblePaths = [
    join(entityDir, `${fileName}.entity.ts`),
    join(entityDir, `${fileNameAlt}.entity.ts`), // alternative naming
    join(entityDir, `${fileNameSpecial}.entity.ts`), // special patterns
    join(entityDir, `pm2-${fileName}.entity.ts`), // for Staff entities
  ]

  let entityPath = ''
  for (const path of possiblePaths) {
    logger.log(`🔍 Checking: ${path} (exists: ${existsSync(path)})`)
    if (existsSync(path)) {
      entityPath = path
      break
    }
  }

  if (entityPath) {
    logger.log(`✅ Using file parser for ${entityName}: ${entityPath}`)
    try {
      const result = parseEntityFile(entityPath)
      return { ...result, analysisMethod: 'file-parser' }
    } catch (error) {
      logger.error(
        `❌ File parser failed for ${entityName}: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
      logger.warn(`📝 Falling back to reflection for ${entityName}`)
    }
  } else {
    logger.warn(`No file found for ${entityName}, using reflection fallback`)
  }

  // Fallback: parse from reflection metadata if file not found
  const properties: PropertyInfo[] = []
  const instance = new EntityClass()
  const propertyNames = Object.getOwnPropertyNames(instance)

  for (const propName of propertyNames) {
    if (propName === 'constructor') continue

    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    const propType = Reflect.getMetadata('design:type', instance, propName)
    let typeName = 'any'

    if (propType === String) typeName = 'string'
    else if (propType === Number) typeName = 'number'
    else if (propType === Boolean) typeName = 'boolean'
    else if (propType === Date) typeName = 'Date'
    else if (propType?.name) typeName = propType.name

    properties.push({
      name: propName,
      type: typeName,
      isOptional: true,
      isArray: propName.endsWith('s') && !propName.endsWith('ss'),
    })
  }

  // Check inheritance even in reflection mode by checking file if available
  if (entityPath) {
    try {
      const content = readFileSync(entityPath, 'utf-8')
      const extendsAcBase = content.includes('extends AcBaseEntity')
      const extendsBaseTimestamp = content.includes('extends BaseTimestampEntity')

      if (extendsAcBase) {
        properties.push(...BASE_TIMESTAMP_PROPERTIES, ...AC_BASE_ENTITY_PROPERTIES)
        logger.log(`✅ Added base properties for ${entityName} (extends AcBaseEntity) [reflection mode]`)
      } else if (extendsBaseTimestamp) {
        properties.push(...BASE_TIMESTAMP_PROPERTIES)
        logger.log(`✅ Added timestamp properties for ${entityName} (extends BaseTimestampEntity) [reflection mode]`)
      }
    } catch (error) {
      logger.warn(
        `Could not check inheritance for ${entityName}: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  return { name: entityName, properties, analysisMethod: 'reflection' }
}

function generateTypeDefinition(entityInfo: { name: string; properties: PropertyInfo[] }): string {
  const { name, properties } = entityInfo

  let typeDefinition = `export interface ${name} {\n`

  for (const prop of properties) {
    const optional = prop.isOptional ? '?' : ''
    const arrayNotation = prop.isArray ? '[]' : ''
    const type = prop.type + arrayNotation

    if (prop.description) {
      typeDefinition += `  /** ${prop.description} */\n`
    }

    typeDefinition += `  ${prop.name}${optional}: ${type}\n`
  }

  typeDefinition += '}\n\n'

  return typeDefinition
}

function generateTypesFromEntities(showConsole: boolean = false): {
  success: boolean
  logs: string[]
  summary?: any
  outputDir?: string
  outputDirCustom?: string
} {
  logger.clear() // Clear previous logs
  logger.log('🚀 Starting to generate types from TypeORM entities...')

  try {
    const modelsDir = join('packages', 'models', 'src', 'generated-types')
    const outputDir = join(process.cwd(), modelsDir)

    const webDir = join('apps', 'web', 'lib', 'types')
    const outputDirCustom = join(process.cwd(), '../', '../', webDir)

    if (!existsSync(outputDirCustom)) {
      mkdirSync(outputDirCustom, { recursive: true })
      logger.log(`📁 Directory created: ${outputDir}`)
    }

    const loadedEnums = loadEnumsFromTypesDirectory()

    logger.log('🔍 Discovering enum types from entities...')
    const discoveredEnums = extractEnumTypesFromEntities()
    logger.log(`📦 Found ${discoveredEnums.size} enum types: ${Array.from(discoveredEnums).join(', ')}`)

    const sortedEnumNames = Array.from(discoveredEnums).sort()

    // Generate enums first
    let enumsContent = `export * from '@ac/data-types'\n`

    if (outputDirCustom) {
      const enumsPathCustom = join(outputDirCustom, 'enums.ts')
      logger.log(`📁 Enums path: ${enumsPathCustom}`)
      logger.log(`📁 Enums enumsContent: ${enumsContent}`)
      // writeFileSync(enumsPathCustom, enumsContent)
    }

    let interfacesContent = `/* eslint-disable @typescript-eslint/no-explicit-any */
// Auto-generated interfaces from TypeORM entities
// DO NOT EDIT MANUALLY\n`

    // Import all discovered enums
    if (discoveredEnums.size > 0) {
      const enumImports = sortedEnumNames
      interfacesContent += 'import {\n'
      enumImports.forEach((enumName, index) => {
        const isLast = index === enumImports.length - 1
        interfacesContent += `  ${enumName}${isLast ? '' : ','}`

        if (index === enumImports.length - 1) {
          interfacesContent += ',\n'
        } else {
          interfacesContent += '\n'
        }
      })
      interfacesContent += "} from '@ac/data-types'\n\n"
    }

    const entityInfos: Array<{ name: string; properties: PropertyInfo[] }> = []

    for (const [entityName, EntityClass] of Object.entries(entities)) {
      try {
        logger.log(`🔍 Analyzing entity: ${entityName}`)
        const entityInfo = analyzeEntity(EntityClass)
        entityInfos.push(entityInfo)
        logger.log(`✅ Generated ${entityName} with ${entityInfo.properties.length} properties`)

        // Generate basic type definition first
        interfacesContent += generateTypeDefinition(entityInfo)
      } catch (error) {
        logger.error(`Error analyzing ${entityName}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Remove trailing newlines and add exactly one
    interfacesContent = interfacesContent.trimEnd() + '\n'

    if (outputDirCustom) {
      const interfacesPathCustom = join(outputDirCustom, 'interfaces.ts')
      writeFileSync(interfacesPathCustom, interfacesContent.replaceAll(': Date', ': string'))
    }

    // Generate index file
    let indexContent = `// Auto-generated index file
// DO NOT EDIT MANUALLY
export * from '@ac/data-types'
export * from './interfaces'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type Any = any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyError = any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyKeyValue = any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyRecord = Record<string, any>
`

    // Remove trailing newlines and add exactly one
    indexContent = indexContent.trimEnd() + '\n'

    if (outputDirCustom) {
      const indexPathCustom = join(outputDirCustom, 'index.ts')
      writeFileSync(indexPathCustom, indexContent)
    }

    // Generate summary
    const summaryContent = `# Generated Types

These types are automatically generated from TypeORM entities.

## How to use

\`\`\`typescript
import { User, Category, CategoryStatus } from './generated-types'

// Using interface
const user: User = {
  id: '123',
  name: 'John Doe',
  email: '<EMAIL>',
  // ...
}

// Using enum
const status = CategoryStatus.PUBLISHED
\`\`\`

## Generated entities (${entityInfos.length} entities)

${entityInfos.map(e => `- ${e.name} (${e.properties.length} properties)`).join('\n')}

## Notes

- This file is automatically generated, DO NOT edit directly
- To update types, run script again: \`curl -X 'GET' 'http://localhost:3000/ac-workflow-apis/workflows/dev/generate-frontend-types'\`
- These types only include structure, not validation logic
`

    if (outputDirCustom) {
      const summaryPathCustom = join(outputDirCustom, 'GENERATED_TYPES_SUMMARY.md')
      writeFileSync(summaryPathCustom, summaryContent)
    }

    const summary = {
      totalEntities: entityInfos.length,
      outputDirectory: outputDir,
      entities: entityInfos.map(e => ({ name: e.name, properties: e.properties.length })),
    }

    logger.log('\n🎉 Types generation completed!')
    logger.log(`📊 Summary: ${summary.totalEntities} entities`)

    if (showConsole) {
      console.log(logger.getLogsAsString())
    }

    return { success: true, logs: logger.getLogs(), summary, outputDir: modelsDir, outputDirCustom: webDir }
  } catch (error) {
    logger.error(`Fatal error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return { success: false, logs: logger.getLogs() }
  }
}

// Run script
if (require.main === module) {
  generateTypesFromEntities()
}

export { generateTypesFromEntities }
