// prettier-ignore
import { AC_ENVS } from '@ac/utils';
import { join } from 'path'
import { DataSource, DataSourceOptions } from 'typeorm'
import { SeederOptions } from 'typeorm-extension'

import { InitDbOracle1758009796883 } from './migration/1758009796883-init-db-oracle'
import { InitDbPostgres1758010926103 } from './migration/1758010926103-init-db-postgres'
import { AddStaffToUser1758094315454 } from './migration/1758094315454-AddStaffToUser'
import { InitTrainingClassesTable1758162839653 } from './migration/1758162839653-init-training-classes-table'
import { InitStaffTrainingHistoryTable1758165972424 } from './migration/1758165972424-init-staff-training-history-table'
import { MoveLegalAidFormToOrgHistory1758170578000 } from './migration/1758170578000-move-legal-aid-form-to-org-history'
import { InitWorkTargetGroupsTable1758852734127 } from './migration/1758852734127-init-work-target-groups-table'
import { InitAssignWorkTargetGroupsTable1758868127867 } from './migration/1758868127867-init-assign-work-target-groups-table'
import { InitUploadSessionsTable1759197500293 } from './migration/1759197500293-init-upload-sessions-table'
import { AddUserKeywordColumn1759199000000 } from './migration/1759199000000-add-user-keyword-column'
import { AddRoleKeywordColumn1759220786572 } from './migration/1759220786572-add-role-keyword-column'
import { CreateStorageFilesTable1759308244671 } from './migration/1759308244671-create-storage-filest-table'
import { AddStorageFileRelationToStaffContract1759309700368 } from './migration/1759309700368-add-storage-file-relation-to-staff-contract'
import { AddStorageFileTypeColumn1759465835246 } from './migration/1759465835246-add-storage-file-type-column'
import { UpdateFieldOfOrganizationUnitsTable1759675452745 } from './migration/1759675452745-update-field-of-organization-units-table'
import { RenameStorageFilesToMedias1759806561563 } from './migration/1759806561563-rename-storage-files-to-medias'

const migrationPostgres = [
  InitDbPostgres1758010926103,
  AddStaffToUser1758094315454,
  MoveLegalAidFormToOrgHistory1758170578000,
  CreateStorageFilesTable1759308244671,
  AddStorageFileRelationToStaffContract1759309700368,
  AddStorageFileTypeColumn1759465835246,
  RenameStorageFilesToMedias1759806561563,
  AddRoleKeywordColumn1759220786572,
]
const migrationOracle = [
  AddRoleKeywordColumn1759220786572,
  AddStaffToUser1758094315454,
  AddStorageFileRelationToStaffContract1759309700368,
  AddStorageFileTypeColumn1759465835246,
  AddUserKeywordColumn1759199000000,
  CreateStorageFilesTable1759308244671,
  InitAssignWorkTargetGroupsTable1758868127867,
  InitDbOracle1758009796883,
  InitStaffTrainingHistoryTable1758165972424,
  InitTrainingClassesTable1758162839653,
  InitUploadSessionsTable1759197500293,
  InitWorkTargetGroupsTable1758852734127,
  MoveLegalAidFormToOrgHistory1758170578000,
  RenameStorageFilesToMedias1759806561563,
  UpdateFieldOfOrganizationUnitsTable1759675452745,
]

const migrations = AC_ENVS.DB_TYPE === 'oracle' ? migrationOracle : migrationPostgres

type Options = DataSourceOptions & SeederOptions
let options: Partial<DataSourceOptions & SeederOptions> = {
  seeds: ['packages/models/src/seeds/**/*.{ts,js}'],
}

if (AC_ENVS.DB_TYPE === 'oracle') {
  options = {
    ...options,
    host: AC_ENVS.DB_HOST,
    port: AC_ENVS.DB_PORT,
    username: AC_ENVS.DB_USERNAME,
    password: AC_ENVS.DB_PASSWORD,
  } as Options

  if (AC_ENVS.DB_CONNECTION_KIND === 'sid') {
    options = {
      ...options,
      sid: AC_ENVS.DB_NAME,
    } as Options
  } else {
    options = {
      ...options,
      serviceName: AC_ENVS.DB_NAME,
    } as Options
  }
} else {
  options = {
    ...options,
    url: AC_ENVS.DB_URL,
  }
}

export const AppDataSource = new DataSource({
  migrations,
  subscribers: [],
  type: AC_ENVS.DB_TYPE,
  ...options,
  logging: false,
  migrationsTableName: 'ac_migrations',
  entities: [join(__dirname, 'entity', '*.entity.ts')],
  synchronize: false,
} as Options)
