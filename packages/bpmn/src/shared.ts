// Hằng số cho Task Queue, giúp dễ hiểu các cấu hình
// R: Repository
// L: Label
// C: Constant
// Example: RLoaiBuocXuLy, LT<PERSON>ThaiHoSo, CHan<PERSON>DongCoBan, RH<PERSON>hDongHoSo, ...
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type Any = any
// Hằng số cho Task Queue, giúp tránh lỗi gõ sai
export const VU_VIEC_TASK_QUEUE = 'vu-viec-task-queue'

export const RLoaiBuocXuLy = {
  TIEP_NHAN: 'TIEP_NHAN' as const,
  XU_LY: 'XU_LY' as const,
  TRA_KET_QUA: 'TRA_KET_QUA' as const,
}

export type ILoaiBuocXuLy = keyof typeof RLoaiBuocXuLy

export const RTrangThaiHoSo = {
  DANG_XU_LY: 'DANG_XU_LY' as const,
  CHO_THANH_TOAN: 'CHO_THANH_TOAN' as const,
  CHO_TIEP_NHAN: 'CHO_TIEP_NHAN' as const,
  CHO_CD_BO_SUNG: 'CHO_CD_BO_SUNG' as const,
  CHO_CB_BO_SUNG: 'CHO_CB_BO_SUNG' as const,
  CHO_DUYET_PHAN_CONG_TGPL: 'CHO_DUYET_PHAN_CONG_TGPL' as const,
  CHO_THAM_MUU: 'CHO_THAM_MUU' as const,
  CHO_NHAP_KET_QUA_TGPL: 'CHO_NHAP_KET_QUA_TGPL' as const,
  CHO_DUYET_KET_QUA_TGPL: 'CHO_DUYET_KET_QUA_TGPL' as const,
  CHO_NOP_LUU_TRU_TGPL: 'CHO_NOP_LUU_TRU_TGPL' as const,
  CHO_DUYET_THAM_DINH_TGPL: 'CHO_DUYET_THAM_DINH_TGPL' as const,
  CHO_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'CHO_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN' as const,
  CHO_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'CHO_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG' as const,
  CHO_DONG_TGPL: 'CHO_DONG_TGPL' as const,
  HOAN_THANH: 'HOAN_THANH' as const,
  DA_HUY: 'DA_HUY' as const,
}

export const LTrangThaiHoSo: Record<TrangThaiHoSo, string> = {
  DANG_XU_LY: 'Đang xử lý',
  CHO_THANH_TOAN: 'Chờ thanh toán',
  CHO_TIEP_NHAN: 'Chờ tiếp nhận',
  CHO_CD_BO_SUNG: 'Chờ công dân bổ sung',
  CHO_CB_BO_SUNG: 'Chờ cán bộ bổ sung',
  CHO_DUYET_PHAN_CONG_TGPL: 'Chờ duyệt phân công TGPL',
  CHO_THAM_MUU: 'Chờ tham mưu',
  CHO_NHAP_KET_QUA_TGPL: 'Chờ nhập kết quả TGPL',
  CHO_DUYET_KET_QUA_TGPL: 'Chờ duyệt kết quả TGPL',
  CHO_NOP_LUU_TRU_TGPL: 'Chờ nộp lưu trữ TGPL',
  CHO_DUYET_THAM_DINH_TGPL: 'Chờ duyệt thẩm định TGPL',
  CHO_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'Chờ nhập KQ thẩm định thời gian',
  CHO_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'Chờ nhập KQ thẩm định chất lượng',
  CHO_DONG_TGPL: 'Chờ đóng',
  HOAN_THANH: 'Hoàn thành',
  DA_HUY: 'Đã hủy',
}

export const LHanhDongHoSo: Record<IHanhDongHoSo, string> = {
  // Hành động chung
  TU_CHOI: 'Từ chối',
  YEU_CAU_CD_NOP_BO_SUNG: 'Yêu cầu công dân nộp bổ sung',
  YEU_CAU_CB_NOP_BO_SUNG: 'Yêu cầu cán bộ nộp bổ sung',
  CB_NOP_BO_SUNG: 'Cán bộ nộp bổ sung',

  //Hành động tiếp nhận
  DONG_Y_TIEP_NHAN: 'Đồng ý tiếp nhận',

  //Hành động xử lý
  // Phân công
  YEU_CAU_DUYET_PHAN_CONG_TGPL: 'Yêu cầu duyệt phân công TGPL', // Cán bộ nhập người phân công và gửi lãnh đạo duyệt
  YEU_CAU_THAM_MUU: 'Yêu cầu tham mưu', // Cán bộ nhập người phân công và gửi người tham mưu trước khi tham mưu gửi lãnh đạo duyệt
  NHAP_KQ_THAM_MUU: 'Nhập kết quả tham mưu', // Cán bộ nhập kết quả tham mưu và gửi lãnh đạo duyệt

  // Duyệt
  DUYET_PHAN_CONG_TGPL: 'Duyệt phân công TGPL',
  PHAN_CONG_THAM_DINH_TGPL: 'Phân công thẩm định TGPL',
  // Trợ giúp
  NHAP_KET_QUA_TGPL: 'Nhập kết quả TGPL',
  DUYET_KET_QUA_TGPL: 'Duyệt kết quả TGPL', // Xem xét và chuyển đến lưu hồ sơ
  NOP_LUU_TRU_TGPL: 'Nộp lưu trữ TGPL', // Nộp lưu trữ hồ sơ vào hệ thống

  DUYET_THAM_DINH_TGPL: 'Duyệt thẩm định TGPL',
  NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'Nhập KQ thẩm định thời gian TGPL',
  NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'Nhập KQ thẩm định chất lượng TGPL',

  //Hành động trả kết quả
  DONG_TGPL: 'Đóng TGPL',
}

// const x = Object.keys(LHanhDongHoSo).reduce((acc, key) => {
//   const name = `PM01_${key}_CASE_ADVANCED`
//   acc[key] = name
//   return acc
// }, {})

export const RHanhDongHoSo = {
  TU_CHOI: 'TU_CHOI' as const,
  YEU_CAU_CD_NOP_BO_SUNG: 'YEU_CAU_CD_NOP_BO_SUNG' as const,
  YEU_CAU_CB_NOP_BO_SUNG: 'YEU_CAU_CB_NOP_BO_SUNG' as const,
  CB_NOP_BO_SUNG: 'CB_NOP_BO_SUNG' as const,

  //Hành động tiếp nhận
  DONG_Y_TIEP_NHAN: 'DONG_Y_TIEP_NHAN' as const,

  //Hành động xử lý
  // Phân công
  YEU_CAU_DUYET_PHAN_CONG_TGPL: 'YEU_CAU_DUYET_PHAN_CONG_TGPL' as const, // Cán bộ nhập người phân công và gửi lãnh đạo duyệt
  YEU_CAU_THAM_MUU: 'YEU_CAU_THAM_MUU' as const, // Cán bộ nhập người phân công và gửi người tham mưu trước khi tham mưu gửi lãnh đạo duyệt
  NHAP_KQ_THAM_MUU: 'NHAP_KQ_THAM_MUU' as const, // Cán bộ nhập kết quả tham mưu và gửi lãnh đạo duyệt

  // Duyệt
  DUYET_PHAN_CONG_TGPL: 'DUYET_PHAN_CONG_TGPL' as const,
  // Trợ giúp
  NHAP_KET_QUA_TGPL: 'NHAP_KET_QUA_TGPL' as const,
  DUYET_KET_QUA_TGPL: 'DUYET_KET_QUA_TGPL' as const, // Xem xét và chuyển đến lưu hồ sơ
  PHAN_CONG_THAM_DINH_TGPL: 'PHAN_CONG_THAM_DINH_TGPL' as const,
  NOP_LUU_TRU_TGPL: 'NOP_LUU_TRU_TGPL' as const, // Nộp lưu trữ hồ sơ vào hệ thống

  DUYET_THAM_DINH_TGPL: 'DUYET_THAM_DINH_TGPL' as const,
  NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN' as const,
  NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG' as const,

  //Hành động trả kết quả
  DONG_TGPL: 'DONG_TGPL' as const,
}

export const CHanhDongCoBan: IHanhDongHoSo[] = [RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG, RHanhDongHoSo.TU_CHOI]

export const CHanhDongXuLy: IHanhDongHoSo[] = (Object.keys(RHanhDongHoSo) as IHanhDongHoSo[]).filter(
  i => !CHanhDongCoBan.includes(i)
)

export const CHanhDongTraKetQua: IHanhDongHoSo[] = [RHanhDongHoSo.DONG_TGPL]
export const CHanhDongTiepNhan: IHanhDongHoSo[] = [
  RHanhDongHoSo.DONG_Y_TIEP_NHAN,
  RHanhDongHoSo.TU_CHOI,
  RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG,
]

export type IIdBuocTiepTheo = string

// Định nghĩa các trạng thái có thể có của hồ sơ
export type TrangThaiHoSo = keyof typeof RTrangThaiHoSo

// Định nghĩa các hành động nghiệp vụ
export type IHanhDongHoSo = keyof typeof RHanhDongHoSo

// Định nghĩa bước xử lý trong quy trình BPMN
export interface BuocXuLy {
  id: string
  ten: string
  loai: keyof typeof RLoaiBuocXuLy
  vaiTro: string
  hanhDongChoPhep: IHanhDongHoSo[]
  buocTiepTheo?: {
    [key in IHanhDongHoSo]?: IIdBuocTiepTheo
  }
}

// Định nghĩa quy trình mẫu (template)
export interface QuyTrinhMau {
  id: string
  ten: string
  moTa: string
  buocDau: string // ID bước đầu tiên
  cacBuoc: BuocXuLyMau[]
  thuTucId: string
  macDinh: boolean
}

export interface BuocXuLyMau {
  id: string
  ten: string
  loai: ILoaiBuocXuLy
  isParallelProcess?: boolean
  // vaiTro: string
  hanhDongChoPhep: IHanhDongHoSo[]
  buocTiepTheo?: {
    [key in IHanhDongHoSo]?: string
  }
  ketNoiDen?: {
    targetId: string
    label?: string
    hanhDong?: IHanhDongHoSo
    canBoIds?: string[]
  }[]
  thoiGianXuLy?: number // Thời gian xử lý tính bằng giờ
}

// Định nghĩa quy trình thực hiện (instance)
export interface QuyTrinhThucHien {
  id: string
  ten: string
  moTa: string
  buocDau: string
  cacBuoc: BuocXuLyThucTe[]
  thuTucId: string
  donViId: string
}

export interface BuocXuLyThucTe {
  id: string
  ten: string
  loai: ILoaiBuocXuLy
  isParallelProcess?: boolean
  vaiTro: string
  hanhDongChoPhep: IHanhDongHoSo[]
  hanhDongTienLen?: IHanhDongHoSo[]
  hanhDongLuiLai?: IHanhDongHoSo[]
  buocTiepTheo?: {
    [key in IHanhDongHoSo]?: IIdBuocTiepTheo
  }
  ketNoiDen?: {
    targetId: string
    label?: string
    hanhDong: IHanhDongHoSo
    canBoIds: string[]
  }[]
  idPhongBan: string
  idCanBoXuLy: string
  thoiGianXuLy?: number // Thời gian xử lý tính bằng giờ
}

// Lịch sử chuyển trạng thái và thời gian
export interface LichSuTrangThai {
  trangThai: TrangThaiHoSo | IHanhDongHoSo
  buocHienTai?: string // ID bước hiện tại trong quy trình
  thoiGianBatDau: Date
  thoiGianKetThuc?: Date
  hanhDong?: IHanhDongHoSo
  actor?: CanBo
  // ghiChu?: string
  content: Any
}

// Dữ liệu chính của hồ sơ
export interface HoSoData {
  id: string
  citizenId: string
  files: string[]
  history: { timestamp: Date; message: string }[]
  lichSuTrangThai?: LichSuTrangThai[]
  quyTrinhId: string // ID quy trình được áp dụng
  quyTrinh: QuyTrinhThucHien
  buocHienTai: string // ID bước hiện tại trong quy trình
  hanhDongDaHoanThanh?: { [buocId: string]: IHanhDongHoSo[] } // Theo dõi hành động đã hoàn thành cho mỗi bước
}

// Dữ liệu cho các Signal
export interface YeuCauCbBoSungPayload {
  historyId: string
  buocHienTai: string
  actor: CanBo
  content: Any
  fixedForm: string
}

export interface CbNopBoSungPayload {
  historyId: string
  actor: CanBo
  newFiles: string[]
  content: Any
}

export interface YeuCauDuyetPhanCongTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface YeuCauThamMuuPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface NhapKqThamMuuPayload {
  historyId: string
  actor: CanBo
  assignedTo: CanBo
  content: Any
}

export interface DuyetPhanCongTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface NhapKetQuaTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface DuyetKetQuaTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}
export interface NopLuuTruTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface PhanCongThamDinhTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface NhapKqThamDinhTgplThoiGianPayload {
  historyId: string
  actor: CanBo
  content: Any
  newFiles: string[]
}

export interface NhapKqThamDinhTgplChatLuongPayload {
  historyId: string
  actor: CanBo
  content: Any
  newFiles: string[]
}

export interface DuyetThamDinhTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface DongTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface DongYTiepNhanPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface NopLuuTruTgplPayload {
  historyId: string
  actor: CanBo
  content: Any
}

export interface CanBo {
  id: string
  name: string
}

// Mock dữ liệu cán bộ, đơn vị, thủ tục, phòng ban
export const CAN_BO = [
  {
    id: 'CB1',
    name: 'Nguyễn Văn A',
    vaiTro: 'Cán bộ tiếp nhận',
    donViId: 'DV1',
    phongBanId: 'PB1',
  },
  {
    id: 'CB2',
    name: 'Trần Thị B',
    vaiTro: 'Cán bộ thẩm định',
    donViId: 'DV1',
    phongBanId: 'PB2',
  },
  {
    id: 'CB3',
    name: 'Lê Văn C',
    vaiTro: 'Cán bộ phê duyệt',
    donViId: 'DV1',
    phongBanId: 'PB3',
  },
  {
    id: 'CB4',
    name: 'Phạm Thị D',
    vaiTro: 'Cán bộ ký duyệt',
    donViId: 'DV1',
    phongBanId: 'PB4',
  },
  {
    id: 'CB5',
    name: 'Hoàng Văn E',
    vaiTro: 'Cán bộ trả kết quả',
    donViId: 'DV1',
    phongBanId: 'PB5',
  },
  {
    id: 'CB6',
    name: 'Thẩm Văn Mưu',
    vaiTro: 'Cán bộ tham mưu',
    donViId: 'DV1',
    phongBanId: 'PB6',
  },
  {
    id: 'CB7',
    name: 'Trợ Văn Giúp',
    vaiTro: 'Cán bộ trợ giúp',
    donViId: 'DV1',
    phongBanId: 'PB7',
  },
  {
    id: 'CB8',
    name: 'Hoàng Văn Công',
    vaiTro: 'Cán bộ phân công xử lý',
    donViId: 'DV1',
    phongBanId: 'PB8',
  },
]

export const DON_VI = [{ id: 'DV1', name: 'UBND Phường 1' }]

export const PHONG_BAN = [
  { id: 'PB1', name: 'Bộ phận tiếp nhận', donViId: 'DV1' },
  { id: 'PB2', name: 'Bộ phận thẩm định', donViId: 'DV1' },
  { id: 'PB3', name: 'Bộ phận phê duyệt', donViId: 'DV1' },
  { id: 'PB4', name: 'Bộ phận ký duyệt', donViId: 'DV1' },
  { id: 'PB5', name: 'Bộ phận trả kết quả', donViId: 'DV1' },
  { id: 'PB6', name: 'Bộ phận tham mưu', donViId: 'DV1' },
  { id: 'PB7', name: 'Bộ phận trợ giúp', donViId: 'DV1' },
  { id: 'PB8', name: 'Bộ phận phân công xử lý', donViId: 'DV1' },
]

export const THU_TUC = [
  {
    id: 'TT1',
    name: 'Chứng thực bản sao',
    moTa: 'Chứng thực bản sao từ bản chính',
  },
]
