export const PM01_PROCESS_PERMISSION = {
  PM01_READ_PROCESSES_ALL: 'PM01_READ_PROCESSES_ALL',
  PM01_READ_PROCESSES: 'PM01_READ_PROCESSES',
  PM01_CREATE_PROCESSES: 'PM01_CREATE_PROCESSES',
  PM01_UPDATE_PROCESSES: 'PM01_UPDATE_PROCESSES',
  PM01_DELETE_PROCESSES: 'PM01_DELETE_PROCESSES',
} as const

export const PM01_WORKFLOW_PERMISSION = {
  //
  PM01_WORKFLOW_TU_CHOI: 'PM01_WORKFLOW_TU_CHOI',
  PM01_WORKFLOW_YEU_CAU_CD_NOP_BO_SUNG: 'PM01_WORKFLOW_YEU_CAU_CD_NOP_BO_SUNG',
  PM01_WORKFLOW_YEU_CAU_CB_NOP_BO_SUNG: 'PM01_WORKFLOW_YEU_CAU_CB_NOP_BO_SUNG',
  PM01_WORKFLOW_CB_NOP_BO_SUNG: 'PM01_WORKFLOW_CB_NOP_BO_SUNG',
  PM01_WORKFLOW_DONG_Y_TIEP_NHAN: 'PM01_WORKFLOW_DONG_Y_TIEP_NHAN',
  PM01_WORKFLOW_YEU_CAU_DUYET_PHAN_CONG_TGPL: 'PM01_WORKFLOW_YEU_CAU_DUYET_PHAN_CONG_TGPL',
  PM01_WORKFLOW_YEU_CAU_THAM_MUU: 'PM01_WORKFLOW_YEU_CAU_THAM_MUU',
  PM01_WORKFLOW_NHAP_KQ_THAM_MUU: 'PM01_WORKFLOW_NHAP_KQ_THAM_MUU',
  PM01_WORKFLOW_DUYET_PHAN_CONG_TGPL: 'PM01_WORKFLOW_DUYET_PHAN_CONG_TGPL',
  PM01_WORKFLOW_PHAN_CONG_THAM_DINH_TGPL: 'PM01_WORKFLOW_PHAN_CONG_THAM_DINH_TGPL',
  PM01_WORKFLOW_NHAP_KET_QUA_TGPL: 'PM01_WORKFLOW_NHAP_KET_QUA_TGPL',
  PM01_WORKFLOW_DUYET_KET_QUA_TGPL: 'PM01_WORKFLOW_DUYET_KET_QUA_TGPL',
  PM01_WORKFLOW_NOP_LUU_TRU_TGPL: 'PM01_WORKFLOW_NOP_LUU_TRU_TGPL',
  PM01_WORKFLOW_DUYET_THAM_DINH_TGPL: 'PM01_WORKFLOW_DUYET_THAM_DINH_TGPL',
  PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN',
  PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG',
  PM01_WORKFLOW_DONG_TGPL: 'PM01_WORKFLOW_DONG_TGPL',
} as const

export const PM01_CASE_ADVANCED_PERMISSION = {
  PM01_READ_CASE_ADVANCED_ALL: 'PM01_READ_CASE_ADVANCED_ALL',
  PM01_READ_CASE_ADVANCED: 'PM01_READ_CASE_ADVANCED',
  PM01_CREATE_CASE_ADVANCED: 'PM01_CREATE_CASE_ADVANCED',
  PM01_UPDATE_CASE_ADVANCED: 'PM01_UPDATE_CASE_ADVANCED',
  PM01_DELETE_CASE_ADVANCED: 'PM01_DELETE_CASE_ADVANCED',
} as const

export const PM01_CASE_SIMPLE_PERMISSION = {
  PM01_CASE_SIMPLE_ALL: 'PM01_CASE_SIMPLE_ALL',
  PM01_READ_CASE_SIMPLE_ALL: 'PM01_READ_CASE_SIMPLE_ALL',
  PM01_CASE_SIMPLE_READ: 'PM01_CASE_SIMPLE_READ',
  PM01_CASE_SIMPLE_CREATE: 'PM01_CASE_SIMPLE_CREATE',
  PM01_CASE_SIMPLE_UPDATE: 'PM01_CASE_SIMPLE_UPDATE',
  PM01_CASE_SIMPLE_DELETE: 'PM01_CASE_SIMPLE_DELETE',
} as const

export const PM01_CASE_ANSWER_PERMISSION = {
  PM01_READ_CASE_ANSWER_ALL: 'PM01_READ_CASE_ANSWER_ALL',
  PM01_CASE_ANSWER_READ: 'PM01_CASE_ANSWER_READ',
  PM01_CASE_ANSWER_CREATE: 'PM01_CASE_ANSWER_CREATE',
  PM01_CASE_ANSWER_UPDATE: 'PM01_CASE_ANSWER_UPDATE',
  PM01_CASE_ANSWER_DELETE: 'PM01_CASE_ANSWER_DELETE',
} as const

export const PM01_PERMISSION = {
  ...PM01_CASE_ADVANCED_PERMISSION,
  ...PM01_CASE_ANSWER_PERMISSION,
  ...PM01_CASE_SIMPLE_PERMISSION,
  ...PM01_PROCESS_PERMISSION,
  ...PM01_WORKFLOW_PERMISSION,
} as const

export const PM01_CATEGORY_LABEL: Record<keyof typeof PM01_PERMISSION_TREE, string> = {
  CASE_SIMPLE: 'Quản lý việc TGPL',
  CASE_ADVANCED: 'Quản lý vụ việc TGPL',
  PROCESS: 'Quản lý quy trình',
  WORKFLOW: 'Xử lý vụ việc',
  CASE_ANSWER: 'Quản lý câu trả lời',
}

export const PM01_PERMISSION_LABEL: Record<keyof typeof PM01_PERMISSION | keyof typeof PM01_CATEGORY_LABEL, string> = {
  ...PM01_CATEGORY_LABEL,
  PM01_CASE_SIMPLE_ALL: 'Quyền quản lý việc và vụ việc TGPL',

  PM01_READ_CASE_SIMPLE_ALL: 'Xem danh sách việc TGPL',
  PM01_CASE_SIMPLE_READ: 'Xem chi tiết việc TGPL',
  PM01_CASE_SIMPLE_CREATE: 'Tạo mới việc TGPL',
  PM01_CASE_SIMPLE_UPDATE: 'Cập nhật việc TGPL',
  PM01_CASE_SIMPLE_DELETE: 'Xoá việc TGPL',

  PM01_READ_CASE_ADVANCED_ALL: 'Xem danh sách vụ việc trợ giúp pháp lý',
  PM01_READ_CASE_ADVANCED: 'Xem chi tiết vụ việc trợ giúp pháp lý',
  PM01_CREATE_CASE_ADVANCED: 'Tạo mới vụ việc trợ giúp pháp lý',
  PM01_UPDATE_CASE_ADVANCED: 'Cập nhật vụ việc trợ giúp pháp lý',
  PM01_DELETE_CASE_ADVANCED: 'Xoá vụ việc trợ giúp pháp lý',

  PM01_READ_PROCESSES_ALL: 'Xem danh sách quy trình',
  PM01_READ_PROCESSES: 'Xem chi tiết quy trình',
  PM01_CREATE_PROCESSES: 'Tạo mới quy trình',
  PM01_UPDATE_PROCESSES: 'Cập nhật quy trình',
  PM01_DELETE_PROCESSES: 'Xoá quy trình',

  PM01_READ_CASE_ANSWER_ALL: 'Xem danh sách câu hỏi và câu trả lời việc TGPL',
  PM01_CASE_ANSWER_READ: 'Xem chi tiết câu hỏi và câu trả lời việc TGPL',
  PM01_CASE_ANSWER_CREATE: 'Tạo mới câu hỏi và câu trả lời việc TGPL',
  PM01_CASE_ANSWER_UPDATE: 'Cập nhật câu hỏi và câu trả lời việc TGPL',
  PM01_CASE_ANSWER_DELETE: 'Xoá câu hỏi và câu trả lời việc TGPL',

  PM01_WORKFLOW_TU_CHOI: 'Từ chối vụ việc',
  PM01_WORKFLOW_YEU_CAU_CD_NOP_BO_SUNG: 'Yêu cầu công dân nộp bổ sung',
  PM01_WORKFLOW_YEU_CAU_CB_NOP_BO_SUNG: 'Yêu cầu cán bộ nộp bổ sung',
  PM01_WORKFLOW_CB_NOP_BO_SUNG: 'Cán bộ nộp bổ sung',
  PM01_WORKFLOW_DONG_Y_TIEP_NHAN: 'Đồng ý tiếp nhận',
  PM01_WORKFLOW_YEU_CAU_DUYET_PHAN_CONG_TGPL: 'Yêu cầu duyệt phân công TGPL',
  PM01_WORKFLOW_YEU_CAU_THAM_MUU: 'Yêu cầu tham mưu',
  PM01_WORKFLOW_NHAP_KQ_THAM_MUU: 'Nhập kết quả tham mưu',
  PM01_WORKFLOW_DUYET_PHAN_CONG_TGPL: 'Duyệt phân công TGPL',
  PM01_WORKFLOW_PHAN_CONG_THAM_DINH_TGPL: 'Phân công thẩm định TGPL',
  PM01_WORKFLOW_NHAP_KET_QUA_TGPL: 'Nhập kết quả TGPL',
  PM01_WORKFLOW_DUYET_KET_QUA_TGPL: 'Duyệt kết quả TGPL',
  PM01_WORKFLOW_NOP_LUU_TRU_TGPL: 'Nộp lưu trữ TGPL',
  PM01_WORKFLOW_DUYET_THAM_DINH_TGPL: 'Duyệt thẩm định TGPL',
  PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN: 'Nhập kết quả thẩm định thời gian TGPL',
  PM01_WORKFLOW_NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG: 'Nhập kết quả thẩm định chất lượng TGPL',
  PM01_WORKFLOW_DONG_TGPL: 'Đóng TGPL',
}

export const PM01_PERMISSION_TREE = {
  CASE_ANSWER: PM01_CASE_ANSWER_PERMISSION,
  CASE_SIMPLE: PM01_CASE_SIMPLE_PERMISSION,
  CASE_ADVANCED: PM01_CASE_ADVANCED_PERMISSION,
  PROCESS: PM01_PROCESS_PERMISSION,
  WORKFLOW: PM01_WORKFLOW_PERMISSION,
}
