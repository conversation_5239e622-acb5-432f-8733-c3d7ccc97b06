export enum EnumCaseAdvancedStatus {
  CHO_TIEP_NHAN = 0,
  DANG_XU_LY = 1,
  // TIEP_NHAN = 2,
  // CHO_PHAN_CONG = 3,
  HOAN_THANH = 4,
  TU_CHOI = 5,
}

export enum EnumCaseAdvancedStatus_TEXT {
  CHO_TIEP_NHAN = 'Chờ tiếp nhận',
  DANG_XU_LY = 'Đang xử lý',
  // TIEP_NHAN = 'Tiếp nhận',
  // CHO_PHAN_CONG = 'Chờ phân công',
  HOAN_THANH = 'Hoàn thành',
  TU_CHOI = 'Từ chối',
}

export const mapCaseAdvancedStatusText: Record<EnumCaseAdvancedStatus, string> = {
  [EnumCaseAdvancedStatus.CHO_TIEP_NHAN]: EnumCaseAdvancedStatus_TEXT.CHO_TIEP_NHAN,
  [EnumCaseAdvancedStatus.DANG_XU_LY]: EnumCaseAdvancedStatus_TEXT.DANG_XU_LY,
  // [EnumCaseAdvancedStatus.TIEP_NHAN]: EnumCaseAdvancedStatus_TEXT.TIEP_NHAN,
  // [EnumCaseAdvancedStatus.CHO_PHAN_CONG]: EnumCaseAdvancedStatus_TEXT.CHO_PHAN_CONG,
  [EnumCaseAdvancedStatus.HOAN_THANH]: EnumCaseAdvancedStatus_TEXT.HOAN_THANH,
  [EnumCaseAdvancedStatus.TU_CHOI]: EnumCaseAdvancedStatus_TEXT.TU_CHOI,
}

// Enums và constants cho các trạng thái và loại
export const ASSIGNMENT_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  CANCELLED: 3,
} as const

export const VERIFY_STATUS = {
  NOT_VERIFIED: 0,
  VERIFIED: 1,
  REJECTED: 2,
} as const

export const PAY_STATUS = {
  NOT_PAID: 0,
  PAID: 1,
  PARTIAL_PAID: 2,
} as const

export const GENDER = {
  MALE: 1,
  FEMALE: 2,
  OTHER: 3,
} as const

export const ASSIGNMENT_STATUS_LABELS = {
  [ASSIGNMENT_STATUS.PENDING]: 'Chờ xử lý',
  [ASSIGNMENT_STATUS.IN_PROGRESS]: 'Đang xử lý',
  [ASSIGNMENT_STATUS.COMPLETED]: 'Hoàn thành',
  [ASSIGNMENT_STATUS.CANCELLED]: 'Hủy bỏ',
} as const

export const VERIFY_STATUS_LABELS = {
  [VERIFY_STATUS.NOT_VERIFIED]: 'Chưa xác minh',
  [VERIFY_STATUS.VERIFIED]: 'Đã xác minh',
  [VERIFY_STATUS.REJECTED]: 'Từ chối',
} as const

export const PAY_STATUS_LABELS = {
  [PAY_STATUS.NOT_PAID]: 'Chưa chi trả',
  [PAY_STATUS.PAID]: 'Đã chi trả',
  [PAY_STATUS.PARTIAL_PAID]: 'Chi trả một phần',
} as const

export const GENDER_LABELS = {
  [GENDER.MALE]: 'Nam',
  [GENDER.FEMALE]: 'Nữ',
  [GENDER.OTHER]: 'Khác',
} as const

export enum WorkflowHistoryType {
  KET_THUC = 'KET_THUC',
}

export type AssignmentStatus = keyof typeof ASSIGNMENT_STATUS_LABELS
export type VerifyStatus = keyof typeof VERIFY_STATUS_LABELS
export type PayStatus = keyof typeof PAY_STATUS_LABELS
export type Gender = keyof typeof GENDER_LABELS

/**
 * Type for case advance status values
 */
export type CaseAdvancedStatus = keyof typeof EnumCaseAdvancedStatus

/**
 * Type for case advance status text values
 */
export type CaseAdvancedStatusText = keyof typeof EnumCaseAdvancedStatus_TEXT

/**
 * Status colors for UI display
 */
export const CASE_ADVANCED_STATUS_COLORS = {
  [EnumCaseAdvancedStatus.CHO_TIEP_NHAN]: 'blue',
  [EnumCaseAdvancedStatus.DANG_XU_LY]: 'blue',
  // [EnumCaseAdvancedStatus.TIEP_NHAN]: 'green',
  // [EnumCaseAdvancedStatus.CHO_PHAN_CONG]: 'yellow',
  [EnumCaseAdvancedStatus.HOAN_THANH]: 'emerald',
  [EnumCaseAdvancedStatus.TU_CHOI]: 'red',
} as const

/**
 * Status icons for UI display - sử dụng Lucide React icons
 */
export const CASE_ADVANCED_STATUS_ICONS = {
  [EnumCaseAdvancedStatus.CHO_TIEP_NHAN]: 'egg', // Icon clipboard với checkmark
  [EnumCaseAdvancedStatus.DANG_XU_LY]: 'refresh-cw', // Icon mũi tên xoay tròn
  // [EnumCaseAdvancedStatus.TIEP_NHAN]: 'clipboard-check', // Icon clipboard với checkmark
  // [EnumCaseAdvancedStatus.CHO_PHAN_CONG]: 'clock', // Icon đồng hồ
  [EnumCaseAdvancedStatus.HOAN_THANH]: 'check-circle', // Icon check trong vòng tròn
  [EnumCaseAdvancedStatus.TU_CHOI]: 'x-circle', // Icon X trong vòng tròn
} as const
