import { devEnv, getEnvStr } from '@ac/utils'

import { BASE_CONFIG } from '../base-config'
import { S3ServerConfigType } from '../types'

function getS3Config(): S3ServerConfigType[] {
  try {
    const s3Servers = JSON.parse(process.env['STORAGE_CORE_S3_SERVERS'] || '')
    if (!Array.isArray(s3Servers)) {
      throw new Error('STORAGE_CORE_S3_SERVERS must is an array')
    }
    if (!s3Servers.length) {
      throw new Error('STORAGE_CORE_S3_SERVERS must not an empty array')
    }
    return s3Servers
  } catch (error) {
    console.log('Error getS3Config', error)
    throw new Error('Please config STORAGE_CORE_S3_SERVERS')
  }
}

const DEFAULT = {
  UPLOAD: {
    MAX_SIZE: '10mb',
  },
}

export const STORAGE_CORE_CONFIG = {
  BASE: BASE_CONFIG,
  S3_SERVICES: getS3Config(),
  UPLOAD: {
    MAX_SIZE: getEnvStr('DOCUMENT_CORE_SERVICE_CONFIG_UPLOAD_MAX', devEnv(DEFAULT.UPLOAD.MAX_SIZE)),
  },
}
