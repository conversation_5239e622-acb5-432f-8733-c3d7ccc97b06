import { PERMISSIONS } from '@workspace/ui/constants'
import { Box, FileText, Folder, GitPullRequestArrow, HouseIcon, MessageCircleQuestion } from 'lucide-react'

export const PM1_SIDEBAR = [
  {
    title: '<PERSON>ảng điều khiển',
    url: '/dashboard/pm01',
    icon: HouseIcon,
    // TODO: thangkt: Do chưa tách nên tạm để như thế này
    permissions: [PERMISSIONS.PM02_WORK_TARGET_GROUP_CREATE],
  },
  {
    title: 'Quản lý vụ việc',
    url: '/case-advance',
    icon: FileText,
    permissions: [PERMISSIONS.PM01_READ_CASE_ADVANCED_ALL],
    items: [
      {
        title: '<PERSON>h sách vụ việc',
        url: '/case-advance',
        icon: Box,
        permissions: [PERMISSIONS.PM01_READ_CASE_ADVANCED_ALL],
      },
      {
        title: '<PERSON><PERSON>o mới vụ việc',
        url: '/case-advance/create',
        icon: Box,
        permissions: [PERMISSIONS.PM01_CREATE_CASE_ADVANCED],
      },
    ],
  },
  {
    title: 'Quản lý việc',
    url: '/case-simple',
    icon: MessageCircleQuestion,
    permissions: [PERMISSIONS.PM01_READ_CASE_SIMPLE_ALL],
    items: [
      {
        title: 'Danh sách việc TGPL',
        url: '/case-simple/list',
        icon: Box,
        permissions: [PERMISSIONS.PM01_READ_CASE_SIMPLE_ALL],
      },
      {
        title: 'Thêm mới việc TGPL',
        url: '/case-simple/create',
        icon: Box,
        permissions: [PERMISSIONS.PM01_CASE_SIMPLE_CREATE],
      },
    ],
  },
  {
    title: 'Quy trình xử lý',
    url: '/processes',
    icon: GitPullRequestArrow,
    permissions: [PERMISSIONS.PM01_READ_PROCESSES_ALL],
    items: [
      {
        title: 'Danh sách quy trình',
        url: '/processes',
        icon: Box,
        permissions: [PERMISSIONS.PM01_READ_PROCESSES_ALL],
      },
      {
        title: 'Tạo quy trình',
        url: '/processes/create',
        icon: Box,
        permissions: [PERMISSIONS.PM01_CREATE_PROCESSES],
      },
    ],
  },
  {
    title: 'Danh mục',
    url: '/categories',
    icon: Folder,
    permissions: [PERMISSIONS.PM02_READ_CATEGORIES_ALL],
    items: [
      // PM01
      {
        title: 'Các loại giấy tờ chứng minh',
        url: '/categories/cac-loai-giay-to-chung-minh',
      },
      {
        title: 'Loại đơn vị phối hợp',
        url: '/categories/loai-don-vi-phoi-hop',
      },
      {
        title: 'CSDL/Hệ thống xác minh',
        url: '/categories/csdl-he-thong-xac-minh',
      },
      {
        title: 'Đơn vị phối hợp cấp TW',
        url: '/categories/don-vi-phoi-hop-cap-tw',
      },
      {
        title: 'Đơn vị phối hợp tại đơn vị',
        url: '/categories/don-vi-phoi-hop-tai-don-vi',
      },
      {
        title: 'Giai đoạn theo hình thức TGPL',
        url: '/categories/giai-doan-theo-hinh-thuc-tgpl',
      },
      {
        title: 'Chỉ tiêu khảo sát lấy ý kiến của người được TGPL',
        url: '/categories/chi-tieu-khao-sat-lay-y-kien-cua-nguoi-duoc-tgpl',
      },
      {
        title: 'Nhóm loại tài liệu',
        url: '/categories/nhom-loai-tai-lieu',
      },
      {
        title: 'Loại tài liệu',
        url: '/categories/loai-tai-lieu',
      },
      {
        title: 'Loại tài liệu theo hình thức TGPL',
        url: '/categories/loai-tai-lieu-theo-hinh-thuc-tgpl',
      },
      {
        title: 'Các loại hình tiếp nhận yêu cầu',
        url: '/categories/cac-loai-hinh-tiep-nhan-yeu-cau',
      },
      {
        title: 'Kênh tiếp nhận yêu cầu TGPL ban đầu',
        url: '/categories/kenh-tiep-nhan-yeu-cau-tgpl-ban-dau',
      },
      {
        title: 'Loại hình công việc thực hiện TGPL',
        url: '/categories/loai-hinh-cong-viec-thuc-hien-tgpl',
      },
      {
        title: 'Loại công việc thực hiện TGPL',
        url: '/categories/loai-cong-viec-thuc-hien-tgpl',
      },
      {
        title: 'Công việc theo lĩnh vực/hình thức thực hiện TGPL',
        url: '/categories/cong-viec-theo-linh-vuc-hinh-thuc-thuc-hien-tgpl',
      },
      {
        title: 'Tài liệu bắt buộc khi kết thúc',
        url: '/categories/tai-lieu-bat-buoc-khi-ket-thuc',
      },
      {
        title: 'Loại liên kết vụ việc',
        url: '/categories/loai-lien-ket-vu-viec',
      },
    ],
  },
  {
    title: 'Tiện ích',
    url: '/extensions',
    icon: Box,
    permissions: [PERMISSIONS.PM01_READ_CASE_ANSWER_ALL],
    items: [
      {
        title: 'Danh sách câu hỏi và trả lời việc TGPL',
        url: '/extensions/case-answer',
        permissions: [PERMISSIONS.PM01_READ_CASE_ANSWER_ALL],
        icon: Box,
      },
    ],
  },
]
