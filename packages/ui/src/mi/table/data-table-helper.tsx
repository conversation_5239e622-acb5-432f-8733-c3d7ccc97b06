import { Column, Row, Table } from '@tanstack/react-table'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Checkbox } from '@workspace/ui/components/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'
import { ChevronDown } from 'lucide-react'

import { DataTableComboboxFilter } from './data-table-combobox-filter'
import { DataTableDateRangeFilter } from './data-table-date-range-filter'
import { DataTableFacetedFilter } from './data-table-faceted-filter'
import { DataTableTextFilter } from './data-table-text-filter'

export const TABLE_SIZE = {
  SELECT: 40,
  ACTIONS: 100,
  DATETIME: 130,
  STATUS: 120,
  S120: 120,
  S200: 200,
}

export const TABLE_ALIGN = {
  ACTIONS: 'flex flex-row gap-2',
}

export const selectColumn = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  t: any,
  { size = TABLE_SIZE.SELECT }: { size?: number } = {}
) => ({
  size: size,
  id: 'select',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  header: ({ table }: { table: Table<any> }) => {
    const allSelected = table.getIsAllPageRowsSelected()
    const someSelected = table.getIsSomePageRowsSelected()

    return (
      <div className="flex items-center gap-1">
        <Checkbox
          checked={allSelected || (someSelected && 'indeterminate')}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem
              onClick={() => {
                const newSelection: Record<string, boolean> = {}
                table.getRowModel().rows.forEach(row => {
                  newSelection[row.id] = true
                })
                table.setRowSelection(newSelection)
              }}
            >
              {t('table.select.all')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                const newSelection: Record<string, boolean> = {}
                table.getRowModel().rows.forEach(row => {
                  if (!row.getIsSelected()) {
                    newSelection[row.id] = true
                  }
                })
                table.setRowSelection(newSelection)
              }}
            >
              {t('table.select.invert')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cell: ({ row }: { row: Row<any> }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={value => row.toggleSelected(!!value)}
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const renderFilter = (filters: Column<any, any>[]) => {
  if (filters.length === 0) {
    return null
  }

  return filters.map(column => {
    const meta = column.columnDef.meta

    switch (meta?.filter?.type) {
      case 'combobox':
        return (
          <DataTableComboboxFilter
            key={column.id}
            column={column}
            title={meta.filter.label || meta.title}
            apiRequest={meta.filter.apiRequest}
            options={meta.filter.options || []}
            placeholder={meta.filter.placeholder}
            multiple={!!meta.filter.multiple}
          />
        )

      case 'select':
        return (
          <DataTableFacetedFilter
            key={column.id}
            column={column}
            placeholder={meta.filter.placeholder}
            title={meta.filter.label || meta.title}
            options={meta.filter.options || []}
            multiple={!!meta.filter.multiple}
          />
        )

      case 'date-range':
        return <DataTableDateRangeFilter key={column.id} column={column} title={meta.filter.label || meta.title} />

      case 'text':
        return (
          <DataTableTextFilter
            key={column.id}
            column={column}
            title={meta.filter.label || meta.title}
            value={column.getFilterValue()}
          />
        )

      default:
        return null
    }
  })
}
