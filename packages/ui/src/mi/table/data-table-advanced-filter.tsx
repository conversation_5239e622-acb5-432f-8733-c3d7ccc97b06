'use client'

import { Table } from '@tanstack/react-table'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import { renderFilter } from './data-table-helper'

interface DataTableAdvancedFilterProps<T> {
  loading: boolean
  table: Table<T>
  renderAdvancedFilter?: (table: Table<T>) => React.ReactNode
}

export function DataTableAdvancedFilter<T>({ table, renderAdvancedFilter }: DataTableAdvancedFilterProps<T>) {
  const show = table.options.meta?.uiState?.showAdvancedFilter ?? false
  const contentRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState(0)

  useEffect(() => {
    if (contentRef.current) {
      setHeight(contentRef.current.scrollHeight)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getState()]) // hoặc deps khác khi nội dung filter thay đổi

  const renderFilterAdvanced = () => {
    const filters = table.getAllColumns().filter(column => {
      const filter = column.columnDef.meta?.filter

      return filter?.position === 'advanced'
    })

    if (filters.length === 0) {
      return null
    }

    return renderFilter(filters)
  }

  const countFilterAdvanced = table.getAllColumns().filter(column => {
    const filter = column.columnDef.meta?.filter

    return filter?.position === 'advanced'
  }).length

  if (countFilterAdvanced === 0) {
    return null
  }

  return (
    <AnimatePresence initial={false}>
      {show && (
        <motion.div
          key="advanced-filter"
          initial={{ height: 0, opacity: 0 }}
          animate={{ height, opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="overflow-hidden"
        >
          <div ref={contentRef}>
            <div className="flex flex-col justify-between gap-3 md:gap-4">
              <div className="ac-table-advanced-filter grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5">
                {renderAdvancedFilter ? renderAdvancedFilter(table) : renderFilterAdvanced()}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
