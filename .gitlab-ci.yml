stages:
  - dev
  - staging
  - cache

.base-dev-job:
  variables:
    BUILD_STAGE: dev
  before_script:
    # - oc registry login --token=$OC_KEY --server=$OC_SERVER > /dev/null 2>&1
  after_script:
    - echo "Build ${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} complete."

.base-be-job:
  variables:
    BUILD_STAGE: dev
  before_script:
    - source $HOME/.bashrc
    - nvm use v20
    - export CI=true
    - export NODE_ENV=development
    - pnpm install --frozen-lockfile
    # - oc registry login --token=$OC_KEY --server=$OC_SERVER > /dev/null 2>&1
  after_script:
    - echo "Build ${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} complete."

ac-web-build:
  stage: dev
  variables:
    SERVICE: ac-web
    BUILD_STAGE: dev
  extends: .base-dev-job
  when: manual
  only:
    - build
  cache:
    key: ${CI_COMMIT_REF_SLUG}
  script:
    - echo "Build ac-web-build"
    - echo "BASE_IMAGE ${BASE_IMAGE}"
    - docker build --platform=linux/amd64 --build-arg BASE_IMAGE=${BASE_IMAGE} -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileWeb .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}

ac-web-cache-build:
  stage: cache
  variables:
    SERVICE: ac-web-cache
    BUILD_STAGE: dev
  extends: .base-dev-job
  when: manual
  only:
    - build
  script:
    - echo "Build ac-web-cache-build"
    - docker build --platform=linux/amd64 -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileWebCache .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}

ac-backend-cache-build:
  stage: cache
  variables:
    SERVICE: ac-backend-cache
    BUILD_STAGE: dev
  extends: .base-be-job
  when: manual
  only:
    - build
  script:
    - echo "Build ac-web-cache-build"
    - docker build --platform=linux/amd64 -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileBackendCache .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}

ac-api-dev:
  stage: dev
  variables:
    SERVICE: ac-apis
    BUILD_STAGE: dev
  extends: .base-be-job
  when: manual
  only:
    - build
  script:
    - echo "Build api"
    - pnpm build:api
    - cp ./apps/api/package.json ./apps/api/dist/package.json
    - cp -r ./packages/i18n/src ./apps/api/dist/i18n
    - cp -r ./pnpm-lock.yaml ./apps/api/dist/pnpm-lock.yaml
    - docker build --platform=linux/amd64 --progress=plain -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileApi .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}

ac-workflow-api-dev:
  stage: dev
  variables:
    SERVICE: ac-workflow-api
    BUILD_STAGE: dev
  extends: .base-be-job
  when: manual
  only:
    - build
  script:
    - echo "Build workflow"
    - pnpm build:workflow
    - cp ./apps/workflow/package.json ./apps/workflow/dist/package.json
    - cp -r ./packages/i18n/src ./apps/workflow/dist/i18n
    - cp -r ./pnpm-lock.yaml ./apps/workflow/dist/pnpm-lock.yaml
    - docker build --platform=linux/amd64 -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileWorkflow .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}

ac-worker-dev:
  stage: dev
  variables:
    SERVICE: ac-worker
    BUILD_STAGE: dev
  extends: .base-be-job
  when: manual
  only:
    - build
  script:
    - echo "Build worker"
    - pnpm build:worker
    - pnpm build:workflow:prod
    - cp ./apps/worker/package.json ./apps/worker/dist/package.json
    - cp -r ./packages/i18n/src ./apps/worker/dist/i18n
    - cp -r ./pnpm-lock.yaml ./apps/worker/dist/pnpm-lock.yaml
    - docker build --platform=linux/amd64 -t default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE} -f ./docker/DockerfileWorker .
    - docker push default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/${K8S_NAMESPACE}-${BUILD_STAGE}/${SERVICE}:${BUILD_STAGE}
