FROM --platform=linux/amd64 default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/fis-mbf-digilocker-dev/ac-backend-cache:dev AS builder
WORKDIR /app
COPY ./apps/api/dist .
RUN ls -liah
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then npm install -g pnpm && CI=true pnpm install --prod --no-frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

FROM default-route-openshift-image-registry.apps.prod01.fis-cloud.fpt.com/openshift/nodejs:20-ubi8-minimal
WORKDIR /app
COPY --from=builder /app ./
EXPOSE 8080
ENV NODE_ENV production
ENV PORT 8080

CMD ["node", "main.js"]
