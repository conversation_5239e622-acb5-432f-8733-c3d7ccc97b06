import { FIXED_LOCALE, ISupportedLocale, PREFERRED_LOCALE, SUPPORTED_LOCALES } from '@/constants/base'
import { set } from 'lodash'
import { getRequestConfig } from 'next-intl/server'
import { cookies, headers } from 'next/headers'

const loadMessages = async () => {
  const locales: Record<string, Record<string, string>> = {}

  for (const locale of SUPPORTED_LOCALES) {
    locales[locale] = Object.entries({
      ...(await import(`../messages/site/${locale}.json`)).default,
      ...(await import(`../messages/nav/${locale}.json`)).default,
      ...(await import(`../messages/common/${locale}.json`)).default,
      ...(await import(`../messages/auth/${locale}.json`)).default,
      ...(await import(`../messages/dashboard/${locale}.json`)).default,
      // TODO: Add other messages here
      ...(await import(`../messages/user-management/${locale}.json`)).default,
      ...(await import(`../messages/case-answer/${locale}.json`)).default,
      ...(await import(`../messages/case-advance/${locale}.json`)).default,
      ...(await import(`../messages/processes/${locale}.json`)).default,
      ...(await import(`../messages/case-simple/${locale}.json`)).default,
      ...(await import(`../messages/work-target-group/${locale}.json`)).default,
      ...(await import(`../messages/zod/${locale}.json`)).default,
      ...(await import(`../app/(admin)/roles/locale/${locale}.json`)).default,
      ...(await import(`../app/(full-screen)/forms/locale/${locale}.json`)).default,
    }).reduce((acc, [key, value]) => set(acc, key, value), {})
  }

  return locales
}

const messagesCache = await loadMessages()

// Helper function to detect language
function detectLanguage(acceptLanguage: string | null): string {
  if (!acceptLanguage) {
    return 'en'
  }

  const preferredLocale = acceptLanguage?.split(',')[0]?.split('-')[0]?.toLowerCase() || 'en'

  if (SUPPORTED_LOCALES.includes(preferredLocale as ISupportedLocale)) {
    return preferredLocale
  }

  return PREFERRED_LOCALE
}

export default getRequestConfig(async () => {
  const headersList = await headers()
  const acceptLanguage = headersList.get('Accept-Language')

  // Get language from cookie if exists
  const cookieStore = await cookies()
  const cookieLocale = cookieStore.get('NEXT_LOCALE')?.value

  // Determine locale with fallback chain
  let locale = cookieLocale || detectLanguage(acceptLanguage) || 'en'

  if (FIXED_LOCALE) {
    locale = FIXED_LOCALE
  }

  const messages = messagesCache[locale as ISupportedLocale]

  try {
    return {
      locale,
      messages,
      timeZone: 'Asia/Ho_Chi_Minh',
      now: new Date(),
    }
  } catch {
    return {
      locale: 'en',
      messages,
      timeZone: 'Asia/Ho_Chi_Minh',
      now: new Date(),
    }
  }
})
