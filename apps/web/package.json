{"name": "ac-web", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "next build", "check-unused-code": "ts-prune --project tsconfig.json --ignore '.next|.turbo|build|dist|jest.config.ts|packages|page.tsx|app/api|layout.tsx|constants|lib/types|i18n|lib/services|server/auth|lib/workflow|stores|lib/utils/index.ts|lib/hooks'", "dev": "next dev", "lint": "next lint", "lint:fix": "next lint --fix", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@ac/bpmn": "workspace:*", "@ac/data-types": "workspace:*", "@ac/permissions": "workspace:*", "@auth/typeorm-adapter": "^2.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@workspace/ui": "workspace:*", "@xyflow/react": "^12.8.3", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cookies": "^0.9.1", "date-fns": "^4.1.0", "docx-preview": "^0.3.7", "file-saver": "^2.0.5", "idb": "^8.0.3", "ioredis": "^5.6.1", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "moment": "^2.30.1", "next": "^15.2.3", "next-auth": "5.0.0-beta.29", "next-intl": "^4.3.4", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "reactflow": "^11.11.4", "reflect-metadata": "^0.2.2", "socket.io-client": "^4.8.1", "typeorm": "^0.3.25", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@types/cookies": "^0.9.1", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}}