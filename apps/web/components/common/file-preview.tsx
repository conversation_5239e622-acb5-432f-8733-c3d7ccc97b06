import { Button } from '@workspace/ui/components/button'
import { toast } from '@workspace/ui/components/toast'
import { FileText } from 'lucide-react'
import Image from 'next/image'
import { useEffect, useState } from 'react'

const DOWNLOAD_ENDPOINT = (fileId: string) => `/ac-apis/medias/files/${fileId}/download-url`

interface Props {
  fileId: string
  fileName?: string
}

export function FilePreview({ fileId, fileName }: Props) {
  const [url, setUrl] = useState<string | null>(null)
  const [contentType, setContentType] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!fileId) return
    setLoading(true)
    fetch(DOWNLOAD_ENDPOINT(fileId))
      .then(res => res.json())
      .then(data => {
        setUrl(data.url)
        setContentType(data.contentType || null)
      })
      .catch(() => toast.error('Không thể lấy link file'))
      .finally(() => setLoading(false))
  }, [fileId])

  if (loading) {
    return <div>Đang tải file...</div>
  }

  if (!url) return null

  // Determine preview type
  const ext = fileId.split('.').pop()?.toLowerCase() || ''
  const isPdf = contentType?.includes('pdf') || ext === 'pdf'
  const isImage = contentType?.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)

  if (isPdf) {
    return (
      <div className="mb-4">
        <iframe
          src={url}
          title={fileName || 'PDF'}
          style={{ width: '100%', height: 600, border: '1px solid #ccc', borderRadius: 8 }}
        />
      </div>
    )
  }

  if (isImage) {
    return (
      <div className="mb-4 flex items-center justify-center">
        <div className="relative max-h-[600px] min-h-[300px] w-full overflow-hidden rounded border border-gray-300">
          <Image
            src={url}
            alt={fileName || 'Ảnh'}
            fill
            sizes="(max-width: 1024px) 100vw, 1024px"
            className="object-contain"
            unoptimized
          />
        </div>
      </div>
    )
  }
  // fallback: download

  return (
    <Button
      variant="outline"
      size="sm"
      className="mb-4 w-full justify-start"
      onClick={() => window.open(url, '_blank')}
    >
      <FileText className="mr-2 h-4 w-4" />
      Tải về: {fileName || fileId}
    </Button>
  )
}
