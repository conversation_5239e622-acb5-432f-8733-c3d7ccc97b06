import { Button } from '@workspace/ui/components/button'
import { X } from 'lucide-react'
import React, { useEffect, useState } from 'react'

import { StaffUploadDialog } from '../staff/upload/staff-upload-dialog'
import { StaffUploadOptions, UploadedFileSummary, UploadOverrides } from '../staff/upload/use-staff-upload'

interface UploadDocumentProps {
  config?: Partial<StaffUploadOptions>
  overrides?: UploadOverrides
  title?: string
  description?: string
  onUploadedFiles: (files: UploadedFileSummary[]) => void
  multiple?: boolean
}

export function UploadDocument(props: UploadDocumentProps) {
  const {
    config,
    overrides,
    title = 'Tải lên tệp',
    description = 'Chọn các tệp cần đính kèm.',
    onUploadedFiles,
    multiple = true,
  } = props

  const [uploadedFiles, setUploadedFiles] = useState<UploadedFileSummary[]>([])
  const [isSingleDialogOpen, setIsSingleDialogOpen] = useState(false)

  const handleSingleUploaded = (file: UploadedFileSummary) => {
    if (multiple) {
      setUploadedFiles(prev => [...prev, file])
    } else {
      setUploadedFiles([file])
    }
    setIsSingleDialogOpen(false)
  }

  const handleRemoveFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // Notify parent when files change
  useEffect(() => {
    onUploadedFiles(uploadedFiles)
  }, [uploadedFiles, onUploadedFiles])

  return (
    <div>
      <Button
        type="button"
        variant="outline"
        onClick={() => setIsSingleDialogOpen(true)}
        disabled={!multiple && uploadedFiles.length >= 1}
      >
        Thêm tệp
      </Button>
      <div className="mt-2 flex flex-col gap-2">
        {uploadedFiles.map((file, idx) => (
          <div key={file.fileId || file.url || idx} className="flex items-center gap-2 rounded border px-2 py-1">
            <span className="flex-1 truncate">{file.fileKey || file.url || file.fileId}</span>
            <Button type="button" size="icon" variant="ghost" onClick={() => handleRemoveFile(idx)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
      <StaffUploadDialog
        open={isSingleDialogOpen}
        onOpenChange={setIsSingleDialogOpen}
        config={config}
        overrides={overrides}
        title={title}
        description={description}
        onUploaded={handleSingleUploaded}
      />
    </div>
  )
}
