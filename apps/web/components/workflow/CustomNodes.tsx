'use client'

import { wfLog } from '@/lib/fn'
import { Any } from '@/lib/types'
import { BuocXuLyMau, IHanhDongHoSo, LHanhDongHoSo } from '@/lib/workflow/shared'
import { SKIP_MISSING_FROM_INDEX_COUNTING } from '@/lib/workflow/workflow-layout'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader } from '@workspace/ui/components/card'
import { ArrowLeft, ArrowRight, ClipboardCheck, FileCheck, UserCheck, X } from 'lucide-react'
import React from 'react'
import { Handle, NodeProps, Position } from 'reactflow'

// Helper function để format thời gian
const formatThoiGian = (soGio?: number) => {
  if (!soGio || soGio <= 0) return ''
  const ngay = Math.floor(soGio / 8)
  const gio = soGio % 8
  let result = ''

  if (ngay > 0) result += `${ngay} ngày`

  if (gio > 0) result += `${ngay > 0 ? ' ' : ''}${gio} giờ`

  return result
}

interface Task {
  id: string
  title: string
  done: boolean
}

// Base Node Component
interface BaseNodeData extends BuocXuLyMau {
  selected?: boolean
  isAssigned?: boolean
  phongBan?: string
  canBo?: string
  onDelete?: (id: string) => void
  isAssignmentMode?: boolean
  onMoveLeft?: (id: string) => void
  onMoveRight?: (id: string) => void
  canMoveLeft?: boolean
  canMoveRight?: boolean
  tasks?: Task[]
  // Handle visibility control
  hasDirectConnections?: boolean
  hasGatewayConnection?: boolean
  hasIncomingConnections?: boolean
  // Focus mode and gateway-specific data
  showOnlySelectedConnections?: boolean
  selectedNodeId?: string
  usedOutputHandles?: Set<string>
  sourceStepId?: string
}

// const TaskList = ({ initialTasks }: { initialTasks?: Task[] }) => {
//   const [collapsed, setCollapsed] = useState(true)
//   const [tasks, setTasks] = useState(
//     initialTasks || [
//       {
//         id: '1212',
//         done: false,
//         title: 'Thiếu hồ sơ',
//       },
//     ]
//   )

//   const toggleTask = (taskId: string) => {
//     setTasks(prev => prev.map(t => (t.id === taskId ? { ...t, done: !t.done } : t)))
//   }

//   return (
//     <div className="mt-2">
//       {tasks.length > 0 && (
//         <Button variant="ghost" size="sm" className="h-5 w-5 p-0" onClick={() => setCollapsed(!collapsed)}>
//           {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
//         </Button>
//       )}

//       {!collapsed && (
//         <div className="mt-1 space-y-1">
//           {tasks.length === 0 ? (
//             <p className="text-xs text-gray-400">Không có công việc</p>
//           ) : (
//             tasks.map(task => (
//               <div key={task.id} className="flex items-center space-x-2 text-xs">
//                 <Checkbox checked={task.done} onCheckedChange={() => toggleTask(task.id)} />
//                 <span className={`flex-1 ${task.done ? 'text-green-600 line-through' : ''}`}>{task.title}</span>
//                 {task.done && <CheckCircle className="h-3 w-3 text-green-500" />}
//               </div>
//             ))
//           )}
//         </div>
//       )}
//     </div>
//   )
// }

// Node cho Tiếp nhận
const TiepNhanNode = ({ data, selected }: NodeProps<BaseNodeData>) => {
  // const handleDelete = (e: React.MouseEvent) => {
  //   e.stopPropagation()
  //   if (data.onDelete && window.confirm('Bạn có chắc chắn muốn xóa bước này?')) {
  //     data.onDelete(data.id)
  //   }
  // }

  return (
    <div className="group relative w-[200px] cursor-pointer">
      {/* Handle cho Start node connection (cạnh trái - luôn hiển thị) */}
      <Handle
        type="target"
        position={Position.Left}
        id="input-start"
        style={{ background: '#10b981', width: 10, height: 10 }}
      />

      {/* Input handles ở đáy để nhận connections từ Gateway theo BPMN requirement */}
      {/* Handle ở vị trí 25% (1/4) để nhận nhánh tiến từ góc 3h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-25"
        style={{
          background: '#22c55e', // Xanh lá cây cho nhánh tiến lên
          width: 8,
          height: 8,
          left: '25%', // 1/4 từ trái cho nhánh tiến
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      {/* Handle ở vị trí 75% (3/4) để nhận nhánh lùi từ góc 9h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-75"
        style={{
          background: '#f97316', // Cam cho nhánh quay lại
          width: 8,
          height: 8,
          left: '75%', // 3/4 từ trái cho nhánh lùi
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      <Card
        className={`transition-all duration-200 ${selected || data.selected ? 'shadow-lg ring-2 ring-blue-500' : 'shadow-sm'}`}
      >
        {/* {!data.isAssignmentMode && (
          <Button
            variant="destructive"
            size="sm"
            className="absolute -right-2 -top-2 z-10 h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
            onClick={handleDelete}
          >
            <X className="h-3 w-3" />
          </Button>
        )} */}

        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <UserCheck className="h-4 w-4 text-blue-600" />
            <Badge variant="default" className="text-xs">
              Tiếp nhận
            </Badge>
            {data.isAssigned ? (
              <Badge variant="outline" className="border-green-600 text-xs text-green-600">
                Đã gán
              </Badge>
            ) : data.isAssignmentMode ? (
              <Badge variant="outline" className="border-orange-600 text-xs text-orange-600">
                Chưa gán
              </Badge>
            ) : null}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-1">
            <h4 className="text-sm font-medium">{data.ten || 'Chưa đặt tên'}</h4>
            {/* <p className="text-xs text-gray-600">{data.vaiTro}</p> */}
            {data.thoiGianXuLy && data.thoiGianXuLy > 0 && (
              <p className="text-xs font-medium text-orange-600">⏱️ {formatThoiGian(data.thoiGianXuLy)}</p>
            )}
            {data.phongBan && <p className="text-xs text-blue-600">📍 {data.phongBan}</p>}
            {data.canBo && <p className="text-xs text-green-600">👤 {data.canBo}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Handle output ở đáy cho kết nối đến gateway (giữa cạnh đáy) - luôn hiển thị */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output-to-gateway"
        style={{
          background: '#8b5cf6',
          width: 8,
          height: 8,
          bottom: -4,
          left: '50%', // Giữa cạnh đáy (2/4)
          transform: 'translateX(-50%)',
        }}
      />
    </div>
  )
}

// Node cho Xử lý
const XuLyNode = ({ data, selected }: NodeProps<BaseNodeData>) => {
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()

    if (data.onDelete && window.confirm('Bạn có chắc chắn muốn xóa bước này?')) {
      data.onDelete(data.id)
    }
  }

  const handleMoveLeft = (e: React.MouseEvent) => {
    e.stopPropagation()

    if (data.onMoveLeft) data.onMoveLeft(data.id)
  }

  const handleMoveRight = (e: React.MouseEvent) => {
    e.stopPropagation()

    if (data.onMoveRight) data.onMoveRight(data.id)
  }

  return (
    <div className="group relative w-[200px] cursor-pointer">
      {/* Input handles ở đáy để nhận connections từ Gateway theo BPMN requirement */}
      {/* Handle ở vị trí 25% (1/4) để nhận nhánh tiến từ góc 3h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-25"
        style={{
          background: '#22c55e', // Xanh lá cây cho nhánh tiến lên
          width: 8,
          height: 8,
          left: '25%', // 1/4 từ trái cho nhánh tiến
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      {/* Handle ở vị trí 75% (3/4) để nhận nhánh lùi từ góc 9h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-75"
        style={{
          background: '#f97316', // Cam cho nhánh quay lại
          width: 8,
          height: 8,
          left: '75%', // 3/4 từ trái cho nhánh lùi
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      <Card
        className={`transition-all duration-200 ${selected || data.selected ? 'shadow-lg ring-2 ring-blue-500' : 'shadow-sm'} ${!data.isAssigned ? 'border-blue-500' : 'border-green-300'}`}
      >
        {!data.isAssignmentMode && (selected || data.selected) && (
          <div className="absolute -top-2 right-8 z-20 flex space-x-1">
            {data.canMoveLeft && (
              <Button
                variant="outline"
                size="sm"
                className="h-6 w-6 border-blue-300 bg-blue-50 p-0 hover:bg-blue-100"
                onClick={handleMoveLeft}
              >
                <ArrowLeft className="h-3 w-3 text-blue-600" />
              </Button>
            )}
            {data.canMoveRight && (
              <Button
                variant="outline"
                size="sm"
                className="h-6 w-6 border-blue-300 bg-blue-50 p-0 hover:bg-blue-100"
                onClick={handleMoveRight}
              >
                <ArrowRight className="h-3 w-3 text-blue-600" />
              </Button>
            )}
          </div>
        )}

        {!data.isAssignmentMode && (
          <Button
            variant="destructive"
            size="sm"
            className="absolute -top-2 -right-2 z-10 h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
            onClick={handleDelete}
          >
            <X className="h-3 w-3" />
          </Button>
        )}

        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <FileCheck className="h-4 w-4 text-purple-600" />
            <Badge variant="default" className="text-xs">
              Xử lý
            </Badge>
            {data.isAssigned ? (
              <Badge variant="outline" className="border-green-600 text-xs text-green-600">
                Đã gán
              </Badge>
            ) : data.isAssignmentMode ? (
              <Badge variant="outline" className="border-orange-600 text-xs text-orange-600">
                Chưa gán
              </Badge>
            ) : null}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <h4 className="text-sm font-medium">{data.ten || 'Chưa đặt tên'}</h4>
          {/* <p className="text-xs text-gray-600">{data.vaiTro}</p> */}

          {data.thoiGianXuLy && data.thoiGianXuLy > 0 && (
            <p className="text-xs font-medium text-orange-600">
              ⏱️ {Math.floor(data.thoiGianXuLy / 8) > 0 ? `${Math.floor(data.thoiGianXuLy / 8)} ngày` : ''}
              {data.thoiGianXuLy % 8 > 0 ? ` ${data.thoiGianXuLy % 8} giờ` : ''}
            </p>
          )}

          <div className="mt-2 flex flex-wrap gap-1">
            {data.hanhDongChoPhep
              ?.filter(hd => ['DUYET_HO_SO', 'KY_HO_SO'].includes(hd))
              .map(hanhDong => (
                <Badge key={hanhDong} variant="outline" className="text-xs">
                  {LHanhDongHoSo[hanhDong]}
                </Badge>
              ))}
          </div>

          {data.phongBan && <p className="text-xs text-blue-600">📍 {data.phongBan}</p>}
          {data.canBo && <p className="text-xs text-green-600">👤 {data.canBo}</p>}

          {/* <TaskList initialTasks={data.tasks} /> */}
        </CardContent>
      </Card>

      {/* Handle output ở đáy cho kết nối đến gateway (giữa cạnh đáy) - luôn hiển thị */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output-to-gateway"
        style={{
          background: '#8b5cf6',
          width: 8,
          height: 8,
          bottom: -4,
          left: '50%', // Giữa cạnh đáy (2/4)
          transform: 'translateX(-50%)',
        }}
      />
    </div>
  )
}

// Node cho Trả kết quả
const TraKetQuaNode = ({ data, selected }: NodeProps<BaseNodeData>) => {
  // const handleDelete = (e: React.MouseEvent) => {
  //   e.stopPropagation()
  //   if (data.onDelete && window.confirm('Bạn có chắc chắn muốn xóa bước này?')) {
  //     data.onDelete(data.id)
  //   }
  // }

  return (
    <div className="group relative w-[200px] cursor-pointer">
      {/* Input handles ở đáy để nhận connections từ Gateway theo BPMN requirement */}
      {/* Handle ở vị trí 25% (1/4) để nhận nhánh tiến từ góc 3h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-25"
        style={{
          background: '#22c55e', // Xanh lá cây cho nhánh tiến lên
          width: 8,
          height: 8,
          left: '25%', // 1/4 từ trái cho nhánh tiến
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      {/* Handle ở vị trí 75% (3/4) để nhận nhánh lùi từ góc 9h Gateway */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="input-bottom-75"
        style={{
          background: '#f97316', // Cam cho nhánh quay lại
          width: 8,
          height: 8,
          left: '75%', // 3/4 từ trái cho nhánh lùi
          bottom: -4,
          transform: 'translateX(-50%)',
        }}
      />

      <Card
        className={`transition-all duration-200 ${selected || data.selected ? 'shadow-lg ring-2 ring-blue-500' : 'shadow-sm'}`}
      >
        {/* {!data.isAssignmentMode && (
          <Button
            variant="destructive"
            size="sm"
            className="absolute -right-2 -top-2 z-10 h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
            onClick={handleDelete}
          >
            <X className="h-3 w-3" />
          </Button>
        )} */}

        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ClipboardCheck className="h-4 w-4 text-green-600" />
              <Badge variant="outline" className="text-xs">
                Trả kết quả
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-1">
            <h4 className="text-sm font-medium">{data.ten || 'Chưa đặt tên'}</h4>
            {/* <p className="text-xs text-gray-600">{data.vaiTro}</p> */}
            {data.thoiGianXuLy && data.thoiGianXuLy > 0 && (
              <p className="text-xs font-medium text-orange-600">⏱️ {formatThoiGian(data.thoiGianXuLy)}</p>
            )}
            {data.phongBan && <p className="text-xs text-blue-600">📍 {data.phongBan}</p>}
            {data.canBo && <p className="text-xs text-green-600">👤 {data.canBo}</p>}
          </div>
          {/* <TaskList initialTasks={data.tasks} /> */}
        </CardContent>
      </Card>

      {/* Handle cho End node connection (cạnh phải - luôn hiển thị) */}
      <Handle
        type="source"
        position={Position.Right}
        id="output-end"
        style={{ background: '#10b981', width: 10, height: 10 }}
      />

      {/* Handle output ở đáy cho kết nối đến gateway (giữa cạnh đáy) - luôn hiển thị */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output-to-gateway"
        style={{
          background: '#8b5cf6',
          width: 8,
          height: 8,
          bottom: -4,
          left: '50%', // Giữa cạnh đáy (2/4)
          transform: 'translateX(-50%)',
        }}
      />
    </div>
  )
}

// Start Node
const StartNode = ({ selected }: NodeProps) => (
  <div className="w-[200px]">
    <Card
      className={`border-green-500 bg-green-50 transition-all duration-200 ${selected ? 'shadow-lg ring-2 ring-green-500' : 'shadow-sm'}`}
    >
      <CardContent className="p-3 text-center">
        <div className="flex items-center justify-center space-x-2">
          <div className="h-3 w-3 rounded-full bg-green-500"></div>
          <span className="text-sm font-medium text-green-700">Bắt đầu</span>
        </div>
      </CardContent>
    </Card>
    <Handle
      type="source"
      position={Position.Right}
      id="output"
      style={{ background: '#10b981', width: 10, height: 10 }}
    />
  </div>
)

// End Node
const EndNode = ({ selected }: NodeProps) => (
  <div className="w-[200px]">
    <Handle
      type="target"
      position={Position.Left}
      id="input"
      style={{ background: '#ef4444', width: 10, height: 10 }}
    />
    <Card
      className={`border-red-500 bg-red-50 transition-all duration-200 ${selected ? 'shadow-lg ring-2 ring-red-500' : 'shadow-sm'}`}
    >
      <CardContent className="p-3 text-center">
        <div className="flex items-center justify-center space-x-2">
          <div className="h-3 w-3 rounded-full bg-red-500"></div>
          <span className="text-sm font-medium text-red-700">Kết thúc</span>
        </div>
      </CardContent>
    </Card>
  </div>
)

// Gateway Node (BPMN-style exclusive gateway - hình thoi với X)
const GatewayNode = ({ data, selected }: NodeProps<BaseNodeData & { sourceStepId?: string }>) => {
  // Helper function để check handle visibility trong focus mode
  const shouldShowHandle = (handleId: string): boolean => {
    const isUsed = data.usedOutputHandles?.has(handleId) || false

    if (data.showOnlySelectedConnections) {
      // Trong focus mode, chỉ hiển thị handles của selected node hoặc related nodes
      const isSelectedNode = data.selectedNodeId === data.id
      const isRelatedNode = data.selectedNodeId === data.sourceStepId // Gateway liên quan đến selected step

      return isUsed && (isSelectedNode || isRelatedNode)
    }

    // Ngoài focus mode, chỉ hiển thị handles được sử dụng
    return isUsed
  }

  return (
    <div className="group relative cursor-pointer">
      {/* Input handle ở đỉnh gateway (12h) - nhận kết nối từ STEP */}
      <Handle
        type="target"
        position={Position.Top}
        id="input"
        style={{
          background: '#8b5cf6',
          width: 8,
          height: 8,
          top: -4,
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      />

      {/* Tạm thời ẩn input-8h handle - chỉ sử dụng input ở đỉnh theo yêu cầu BPMN */}

      {/* Hình thoi với X */}
      <div
        className={`relative h-16 w-16 rotate-45 border-2 transition-all duration-200 ${
          selected || data.selected
            ? 'border-purple-500 bg-purple-100 shadow-lg'
            : 'border-purple-400 bg-white shadow-sm'
        }`}
      >
        {/* Dấu X ở giữa hình thoi */}
        <div className="absolute inset-0 flex -rotate-45 items-center justify-center">
          <span className="text-lg font-bold text-purple-600">×</span>
        </div>
      </div>

      {/* 6 source handles cho 6 hướng khác nhau */}
      {/* Top */}
      <Handle
        type="source"
        position={Position.Top}
        id="output-top"
        style={{
          background: '#8b5cf6',
          width: 6,
          height: 6,
          top: -3,
          left: '50%',
          transform: 'translateX(-50%)',
          opacity: shouldShowHandle('output-top') ? 1 : 0,
          pointerEvents: shouldShowHandle('output-top') ? 'auto' : 'none',
        }}
      />

      {/* Góc 3h - Right corner (Nhánh tiến lên) */}
      <Handle
        type="source"
        position={Position.Right}
        id="output-top-right"
        style={{
          background: '#22c55e', // Xanh lá cây cho nhánh tiến lên
          width: 8,
          height: 8,
          top: '50%', // Center Y của hình thoi
          right: -4, // Đỉnh góc phải của hình thoi xoay 45°
          transform: 'translateY(-50%)',
          opacity: 1, // Luôn hiển thị để quan sát
          pointerEvents: 'auto',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          zIndex: 10,
        }}
      />

      {/* Bottom Right */}
      <Handle
        type="source"
        position={Position.Right}
        id="output-bottom-right"
        style={{
          background: '#22c55e', // Xanh lá cây cho nhánh tiến lên
          width: 6,
          height: 6,
          bottom: '25%',
          right: -3,
          opacity: shouldShowHandle('output-bottom-right') ? 1 : 0,
          pointerEvents: shouldShowHandle('output-bottom-right') ? 'auto' : 'none',
        }}
      />

      {/* Bottom */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output-bottom"
        style={{
          background: '#f97316', // Cam cho nhánh quay lại
          width: 6,
          height: 6,
          bottom: -3,
          left: '50%',
          transform: 'translateX(-50%)',
          opacity: shouldShowHandle('output-bottom') ? 1 : 0,
          pointerEvents: shouldShowHandle('output-bottom') ? 'auto' : 'none',
        }}
      />

      {/* Góc 9h - Left corner (Nhánh quay lại) */}
      <Handle
        type="source"
        position={Position.Left}
        id="output-bottom-left"
        style={{
          background: '#f97316', // Cam cho nhánh quay lại
          width: 8,
          height: 8,
          top: '50%', // Center Y của hình thoi
          left: -4, // Đỉnh góc trái của hình thoi xoay 45°
          transform: 'translateY(-50%)',
          opacity: 1, // Luôn hiển thị để quan sát
          pointerEvents: 'auto',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          zIndex: 10,
        }}
      />

      {/* Top Left */}
      <Handle
        type="source"
        position={Position.Left}
        id="output-top-left"
        style={{
          background: '#6b7280',
          width: 6,
          height: 6,
          top: '25%',
          left: -3,
          opacity: shouldShowHandle('output-top-left') ? 1 : 0,
          pointerEvents: shouldShowHandle('output-top-left') ? 'auto' : 'none',
        }}
      />

      {/* Tooltip hiển thị thông tin */}
      {(selected || data.selected) && (
        <div className="absolute -bottom-8 left-1/2 z-10 -translate-x-1/2 transform rounded bg-black px-2 py-1 text-xs whitespace-nowrap text-white">
          Gateway từ: {data.sourceStepId}
        </div>
      )}
    </div>
  )
}

export const nodeTypes = {
  tiepNhan: TiepNhanNode,
  xuLy: XuLyNode,
  traKetQua: TraKetQuaNode,
  start: StartNode,
  end: EndNode,
  gateway: GatewayNode,
}

// Custom Edge for BPMN Gateway Branch connections (A-B-C-D segments)
const BPMNBranchEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  data,
  sourceHandleId,
  targetHandleId,
}: Any) => {
  wfLog(`\n🔥 ========== BPMNBranchEdge RENDERING - ID: ${id} ==========`)
  wfLog(`🔥 Full edge data:`, data)
  wfLog(`⚙️ Global SKIP_MISSING_FROM_INDEX_COUNTING: ${SKIP_MISSING_FROM_INDEX_COUNTING}`)
  wfLog(`🔍 sourceHandleId: ${sourceHandleId}, targetHandleId: ${targetHandleId}`)
  wfLog(`🔍 sourcePosition: ${sourcePosition}, targetPosition: ${targetPosition}`)
  wfLog(`🔥 Edge coordinates: source(${sourceX}, ${sourceY}) -> target(${targetX}, ${targetY})`)

  // BPMN requirement parameters
  const rongMoiNhanh = 20 // Rộng mỗi nhánh (20px)
  const caoMoiLan = 25 // Cao mỗi làn (25px)
  const demDuoi = 60 // Đệm dưới tăng từ 40px lên 60px để tránh đè step
  const caoGateway = 60 // Chiều cao Gateway node

  // ⚙️ Global config ảnh hưởng đến spacing và segment calculations
  // SKIP_MISSING_FROM_INDEX_COUNTING = ${SKIP_MISSING_FROM_INDEX_COUNTING}
  // → Ảnh hưởng: spacing calculations, A-B-C-D segments, globalIndex distribution

  // Config để hiển thị debug info
  // Set true để hiển thị: "2 -> Yêu cầu bổ sung -> 1 (L1, G2)" - bắt đầu từ 1
  // Set false để chỉ hiển thị: "2 -> Yêu cầu bổ sung -> 1"
  const SHOW_DEBUG_INDICES = true

  // Debug: Check raw data structure
  wfLog(`🔍 Raw edge data received:`, data)
  wfLog(`🔍 Raw edge data keys:`, data ? Object.keys(data) : 'null')

  // Lấy thông tin từ data và sourceHandleId thực tế
  const branchIndex = data?.branchIndex || 0 // Index local trong gateway này
  const globalIndex = data?.globalIndex || 0 // Index cumulative trong toàn workflow
  const drawnIndex = data?.drawnIndex || 0 // Index thực sự của đường được vẽ (0-14)
  const totalConnections = data?.totalConnections || 0 // Tổng số connections
  const sourceStepIndex = data?.sourceStepIndex || 0 // Step index nguồn
  const targetStepIndex = data?.targetStepIndex || 0 // Step index đích
  const actionType = data?.actionType || 'Unknown' // Action type

  // Quyết định isForward dựa trên sourceHandleId thực tế thay vì data
  const isForward = sourceHandleId === 'output-top-right' // true nếu từ góc 3h, false nếu từ góc 9h

  wfLog(`🎯 Detected isForward: ${isForward} based on sourceHandleId: ${sourceHandleId}`)
  wfLog(
    `📊 branchIndex: ${branchIndex}, drawnIndex: ${drawnIndex}, globalIndex: ${globalIndex}, totalConnections: ${totalConnections}`
  )
  wfLog(
    `🧐 Extracted values: branchIndex=${data?.branchIndex}, globalIndex=${data?.globalIndex}, totalConnections=${data?.totalConnections}`
  )
  wfLog(
    `🔍 ActionType debug: data.actionType='${data?.actionType}', sourceStepIndex=${sourceStepIndex}, targetStepIndex=${targetStepIndex}`
  )

  // Function để format action name cho display
  const getActionDisplayName = (actionType?: string) => {
    return LHanhDongHoSo[actionType as IHanhDongHoSo] || actionType || 'Unknown'
  }

  // Tạo label cho connection
  const baseLabel = `${sourceStepIndex + 1} -> ${getActionDisplayName(actionType)} -> ${targetStepIndex + 1}`
  const debugInfo = SHOW_DEBUG_INDICES ? ` (L${branchIndex + 1}, D${drawnIndex}, G${globalIndex + 1})` : ''
  const connectionLabel = baseLabel + debugInfo

  wfLog(`🏷️ Connection label: ${connectionLabel}`)
  wfLog(`🎯 Debug values - branchIndex: ${branchIndex}, globalIndex: ${globalIndex}`)
  wfLog(`🎯 Debug values +1 - L${branchIndex + 1}, D${drawnIndex}, G${globalIndex + 1}`)
  wfLog(`🎯 SHOW_DEBUG_INDICES: ${SHOW_DEBUG_INDICES}`)
  wfLog(`🎯 Final connectionLabel: "${connectionLabel}"`)
  wfLog(`🔥 ========== END BPMNBranchEdge ID: ${id} ==========\n`)

  // Tính toán các đoạn A-B-C-D theo BPMN requirement
  // Đoạn A: Đi ngang ra từ Gateway
  const prefix = 20 // Khoảng cách an toàn từ gateway

  let doiDaiA: number

  if (isForward) {
    // Nhánh tiến: đi sang phải, dùng branchIndex local trong gateway này
    doiDaiA = (branchIndex + 1) * rongMoiNhanh + prefix
    wfLog(`🟢 FORWARD Đoạn A: branchIndex=${branchIndex}, prefix=${prefix} → doiDaiA=${doiDaiA}px`)
  } else {
    // Nhánh lùi: đi sang trái, dùng branchIndex local trong gateway này
    doiDaiA = (branchIndex + 1) * rongMoiNhanh + prefix
    wfLog(`🔶 BACKWARD Đoạn A: branchIndex=${branchIndex}, prefix=${prefix} → doiDaiA=${doiDaiA}px`)
  }

  // Đoạn B: Đi lên từ Gateway - tạo các làn đường ngang
  // drawnIndex càng nhỏ thì Y càng cao (gần Gateway)
  // drawnIndex = 0 → Y cao nhất, drawnIndex = 14 → Y thấp nhất (cho 15 đường)
  const chieuCaoB = caoGateway / 2 + demDuoi + drawnIndex * caoMoiLan

  // Sử dụng tọa độ thực tế từ ReactFlow handles
  // sourceX, sourceY đã là vị trí chính xác của handle được ReactFlow cung cấp
  // Không cần tính toán thêm offset vì ReactFlow đã tính sẵn

  // Điểm xuất phát chính xác từ handle position
  const startPointX = sourceX // Vị trí X của handle (output-top-right hoặc output-bottom-left)
  const startPointY = sourceY // Vị trí Y của handle

  // Điểm kết thúc đoạn A (đi ngang ra)
  const endAX = isForward
    ? startPointX + doiDaiA // Nhánh tiến: đi thêm ra bên phải
    : startPointX - doiDaiA // Nhánh lùi: đi thêm ra bên trái
  const endAY = startPointY

  // Điểm kết thúc đoạn B (đi lên tạo làn đường ngang)
  const endBX = endAX
  const endBY = sourceY - chieuCaoB

  // Đoạn C: Đi ngang để thẳng hàng với STEP đích
  const endCX = targetX
  const endCY = endBY

  // Đoạn D: Đi lên nối vào đáy STEP đích
  const endDX = targetX
  const endDY = targetY

  // Tạo path A-B-C-D từ góc Gateway
  const pathData = `
    M ${startPointX} ${startPointY}
    L ${endAX} ${endAY}
    L ${endBX} ${endBY}
    L ${endCX} ${endCY}
    L ${endDX} ${endDY}
  `

  // Debug logging
  wfLog(`📐 BPMN Branch ${id}:`)
  wfLog(`   Branch Index: local=${branchIndex}, drawn=${drawnIndex}, global=${globalIndex}, Forward: ${isForward}`)
  wfLog(`   Handle Position (${isForward ? 'output-top-right' : 'output-bottom-left'}): (${sourceX}, ${sourceY})`)
  wfLog(`   Target: (${targetX}, ${targetY})`)
  wfLog(`   === Path A-B-C-D ===`)
  wfLog(
    `   Y Level Calculation: drawnIndex=${drawnIndex} → Y offset=${drawnIndex * caoMoiLan}px (smaller index = higher Y)`
  )
  wfLog(
    `   Đoạn A (${isForward ? 'sang phải' : 'sang trái'}): ${doiDaiA}px từ (${startPointX},${startPointY}) → (${endAX},${endAY})`
  )
  wfLog(`   Đoạn B (lên): ${chieuCaoB}px từ (${endAX},${endAY}) → (${endBX},${endBY})`)
  wfLog(`   Đoạn C (ngang): từ (${endBX},${endBY}) → (${endCX},${endCY})`)
  wfLog(`   Đoạn D (lên): từ (${endCX},${endCY}) → (${endDX},${endDY})`)

  // Tính toán vị trí để đặt label ở giữa đoạn C (đường ngang)
  const labelX = (endBX + endCX) / 2 // Giữa đoạn C
  const labelY = endCY // Chính giữa đoạn C (như line-through)

  return (
    <>
      <path
        id={id}
        d={pathData}
        style={{
          ...style,
          fill: 'none',
          stroke: isForward ? '#22c55e' : '#f97316', // Xanh lá cây cho tiến lên, cam cho quay lại
          strokeWidth: 2,
        }}
        markerEnd={markerEnd}
      />

      {/* Background cho text để dễ đọc */}
      <rect
        x={labelX - connectionLabel.length * 2.7} // Ước tính width với padding cho debug info
        y={labelY - 8} // Centered around labelY
        width={connectionLabel.length * 5.4} // Tăng width để chứa debug info
        height={16}
        fill="white"
        fillOpacity={0.95}
        stroke="#e5e7eb"
        strokeWidth={0.5}
        rx={4}
        style={{
          pointerEvents: 'none',
        }}
      />

      {/* Label hiển thị thông tin connection */}
      <text
        x={labelX}
        y={labelY}
        fill={isForward ? '#16a34a' : '#ea580c'} // Màu tương tự nhưng đậm hơn
        fontSize="11"
        fontWeight="500"
        textAnchor="middle"
        dominantBaseline="middle"
        style={{
          pointerEvents: 'none', // Không block mouse events
          userSelect: 'none',
        }}
      >
        {connectionLabel}
        {/* {wfLog(
          `🎨 RENDERING TEXT LABEL: "${connectionLabel}" at (${labelX}, ${labelY})`
        )}
        {wfLog(`🎨 Label position - x: ${labelX}, y: ${labelY}`)} */}
      </text>
    </>
  )
}

// Custom Edge for STEP to EndNode connection
const StepToEndEdge = ({ id, sourceX, sourceY, targetX, targetY, style = {}, markerEnd, data }: Any) => {
  wfLog(`🚨 StepToEndEdge RENDERING - ID: ${id}`)
  // Calculate path that goes around STEP-Gateway vertical lines
  // Go down from source, then horizontal at the middle level between Gateway and EndNode, then down to target

  // Use actual Gateway Y from edge data (calculated dynamically)
  const actualGatewayY = data?.gatewayY || 880 // Fallback to 880 if not provided
  const endY = targetY
  const midY = actualGatewayY + (endY - actualGatewayY) * 0.5 // 50% khoảng cách từ Gateway (gần Gateway hơn)

  // Debug logging
  wfLog(`🔗 StepToEndEdge Debug:`)
  wfLog(`   Source STEP: (${sourceX}, ${sourceY})`)
  wfLog(`   Target EndNode: (${targetX}, ${targetY})`)
  wfLog(`   Gateway Y from data: ${data?.gatewayY} (using: ${actualGatewayY})`)
  wfLog(`   Horizontal line Y (midY): ${midY} (25% từ Gateway)`)
  wfLog(`   Distance from Gateway to EndNode: ${endY - actualGatewayY}px`)
  // Add horizontal offset to avoid being too close to nodes
  const verticalOffset = 30 // Distance from node edges
  const sourceVerticalX = sourceX + verticalOffset // Go slightly right from STEP
  const targetVerticalX = targetX - verticalOffset // Go slightly left to EndNode

  wfLog(`   Path: STEP(${sourceY})→horizontal(${midY})→EndNode(${endY})`)
  wfLog(
    `   Vertical offsets: sourceX+${verticalOffset}=${sourceVerticalX}, targetX-${verticalOffset}=${targetVerticalX}`
  )

  const path = `
    M ${sourceX} ${sourceY}
    L ${sourceVerticalX} ${sourceY}
    L ${sourceVerticalX} ${midY}
    L ${targetVerticalX} ${midY}
    L ${targetVerticalX} ${targetY}
    L ${targetX} ${targetY}
  `

  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          strokeWidth: 2,
          stroke: '#6b7280', // Gray color like Start-to-Step connection
          fill: 'none',
        }}
        className="react-flow__edge-path"
        d={path}
        markerEnd={markerEnd}
      />
    </>
  )
}

export const edgeTypes = {
  'step-to-end': StepToEndEdge,
  'bpmn-branch': BPMNBranchEdge,
}

export type { BaseNodeData }
