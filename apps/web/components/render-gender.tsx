import { EnumGender } from '@/lib/types'
import { GENDER_LABELS } from '@/lib/types'
import { Mars, Venus, VenusAndMars } from 'lucide-react'

export function RenderGender({ gender, defaultValue = 'N/A' }: { gender?: EnumGender; defaultValue?: string }) {
  return (
    <div className="flex flex-wrap items-center gap-1">
      {gender === EnumGender.MALE && <Mars className="h-4 w-4" />}
      {gender === EnumGender.FEMALE && <Venus className="h-4 w-4" />}
      {gender === EnumGender.OTHER && <VenusAndMars className="h-4 w-4" />}
      <span className="flex items-center gap-1">
        {GENDER_LABELS[gender as keyof typeof GENDER_LABELS] || defaultValue}
      </span>
    </div>
  )
}
