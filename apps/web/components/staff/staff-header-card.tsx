'use client'

import { STAFF_BUSINESS_ROLE_OPTIONS } from '@/constants/staff'
import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Circle, Crown } from 'lucide-react'
import React, { useState } from 'react'

import { FieldEditDialog } from './components/field-edit-dialog'
import { InlineEditButton } from './components/inline-edit-button'
import { STAFF_FIELD_CONFIGS } from './constants/field-configs'
import { useUpdateStaffFields } from './services/staff-update.service'
import { StaffDetailResponse } from './types/staff-detail.types'
import { EditableFieldConfig, StaffFieldUpdateDto } from './types/staff-field-update.types'

interface StaffHeaderCardProps {
  staff: StaffDetailResponse
}

export const StaffHeaderCard = ({ staff }: StaffHeaderCardProps) => {
  const [editingField, setEditingField] = useState<{
    field: EditableFieldConfig
    currentValue: Any
  } | null>(null)

  const updateMutation = useUpdateStaffFields()

  // Extract status information
  const activityStatus = staff.activityStatus?.name || 'Không xác định'
  const isActive = staff.activityStatus?.code === 'ANG_CONG_TACANG_LAM_VIEC'
  const isHead = staff.organizationUnit?.isHead === 1

  // Generate avatar placeholder or use existing avatar
  const avatarSrc =
    staff.avatarRef ||
    `https://ui-avatars.com/api/?name=${encodeURIComponent(staff.fullName)}&background=e5e7eb&color=6b7280&size=96`

  const handleEditField = (field: EditableFieldConfig, currentValue: Any) => {
    setEditingField({ field, currentValue })
  }

  const handleSaveField = async (value: Any) => {
    if (!editingField) return

    const updateData: StaffFieldUpdateDto = {
      [editingField.field.key]: value,
    }

    await updateMutation.mutateAsync({
      staffId: staff.id,
      data: updateData,
    })
  }

  const handleCloseDialog = () => {
    setEditingField(null)
  }

  const roles =
    staff.organizationUnit?.roles && staff.organizationUnit.roles.length > 0
      ? staff.organizationUnit.roles
      : staff.organizationHistory?.find(h => h.status === 1)?.roles || []

  return (
    <>
      <div className="rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-700 p-4 text-white lg:p-6">
        <div className="flex flex-col items-start gap-4 lg:flex-row lg:gap-6">
          {/* Avatar */}
          <div className="relative shrink-0 self-center lg:self-start">
            <div
              className="h-20 w-20 rounded-full border-4 border-indigo-300 bg-gray-100 bg-cover bg-center lg:h-24 lg:w-24"
              style={{ backgroundImage: `url('${avatarSrc}')` }}
            />
          </div>

          {/* Staff Information */}
          <div className="w-full flex-1 space-y-2 text-center lg:text-left">
            {/* Head Status Badge */}
            {isHead && (
              <div className="flex items-center justify-center gap-1 text-sm text-white/80 lg:justify-start">
                <Crown className="h-5 w-5 text-yellow-600" />
                <span>Người đứng đầu</span>
              </div>
            )}

            {/* Full Name with Edit Button */}
            <div className="group flex items-center justify-center gap-2 lg:justify-start">
              <h2 className="text-lg font-semibold tracking-wide uppercase lg:text-xl">{staff.fullName}</h2>
              <InlineEditButton
                fieldKey="fullName"
                currentValue={staff.fullName}
                fieldConfig={STAFF_FIELD_CONFIGS.fullName}
                onEdit={handleEditField}
                className="text-white/70 hover:text-white"
              />
            </div>

            {/* Staff ID and Type */}
            <div className="flex items-center justify-center gap-3 text-sm lg:justify-start">
              {roles.map((role: string, idx: number) => (
                <Badge key={idx} variant="default" className="text-xs">
                  {STAFF_BUSINESS_ROLE_OPTIONS.find(opt => opt.value === role)?.label || role}
                </Badge>
              ))}
            </div>

            {/* Contact Information with Edit Buttons */}
            <div className="space-y-1">
              {staff.email && (
                <div className="group flex items-center justify-center gap-2 text-sm lg:justify-start">
                  <span className="text-white/90">{staff.email}</span>
                  <InlineEditButton
                    fieldKey="email"
                    currentValue={staff.email}
                    fieldConfig={STAFF_FIELD_CONFIGS.email}
                    onEdit={handleEditField}
                    className="text-white/70 hover:text-white"
                  />
                </div>
              )}
              {staff.phone && (
                <div className="group flex items-center justify-center gap-2 text-sm lg:justify-start">
                  <span className="text-white/90">{staff.phone}</span>
                  <InlineEditButton
                    fieldKey="phone"
                    currentValue={staff.phone}
                    fieldConfig={STAFF_FIELD_CONFIGS.phone}
                    onEdit={handleEditField}
                    className="text-white/70 hover:text-white"
                  />
                </div>
              )}
            </div>

            {/* Activity Status with Edit Button */}
            <div className="group flex items-center justify-center gap-2 text-sm lg:justify-start">
              <Circle className={`h-2.5 w-2.5 fill-current ${isActive ? 'text-green-400' : 'text-gray-400'}`} />
              <span className="text-white">{activityStatus}</span>
              <InlineEditButton
                fieldKey="activityStatusId"
                currentValue={staff.activityStatusId}
                fieldConfig={STAFF_FIELD_CONFIGS.activityStatusId}
                onEdit={handleEditField}
                className="text-white/70 hover:text-white"
              />
            </div>

            {/* Organization Unit - Mobile Only */}
            {staff.organizationUnit && (
              <div className="border-t border-indigo-400/30 pt-2 lg:hidden">
                <div className="text-sm text-white/80">{staff.organizationUnit.name}</div>
                {staff.organizationUnit.position && (
                  <div className="text-xs text-white/60">{staff.organizationUnit.position.name}</div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Edit Dialog */}
      {editingField && (
        <FieldEditDialog
          isOpen={!!editingField}
          onClose={handleCloseDialog}
          field={editingField.field}
          currentValue={editingField.currentValue}
          onSave={handleSaveField}
          isLoading={updateMutation.isPending}
          staff={undefined}
        />
      )}
    </>
  )
}
