import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { UserListResponse, UserSearchFilter } from '../types/user.types'

// API service functions
const searchUsers = async (filter: UserSearchFilter = {}): Promise<UserListResponse> => {
  const params = new URLSearchParams()

  if (filter.search) {
    params.append('search', filter.search)
  }

  if (filter.staffId !== undefined) {
    if (filter.staffId === null) {
      // Search for unassigned users
      params.append('filter', JSON.stringify({ staffId: null }))
    } else {
      params.append('filter', JSON.stringify({ staffId: filter.staffId }))
    }
  }

  if (filter.status !== undefined) {
    params.append('status', filter.status.toString())
  }

  if (filter.page !== undefined) {
    params.append('page', filter.page.toString())
  }

  if (filter.pageSize !== undefined) {
    params.append('pageSize', filter.pageSize.toString())
  }

  const response = await fetch(`/ac-apis/users?${params.toString()}`)

  if (!response.ok) {
    throw new Error(`Failed to search users: ${response.statusText}`)
  }

  return response.json()
}

const assignUserToStaff = async (staffId: string, userId: string): Promise<void> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/fields`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to assign user to staff')
  }
}

const removeUserFromStaff = async (staffId: string): Promise<void> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/fields`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId: null }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to remove user from staff')
  }
}

export const useSearchUsersInfinite = (filter: Omit<UserSearchFilter, 'page'> = {}, pageSize: number = 20) => {
  return useInfiniteQuery({
    queryKey: ['users', 'search', 'infinite', filter, pageSize],
    queryFn: ({ pageParam = 1 }) =>
      searchUsers({
        ...filter,
        page: pageParam,
        pageSize,
      }),
    getNextPageParam: lastPage => {
      const { page, pageSize, total } = lastPage
      const totalPages = Math.ceil(total / pageSize)

      return page < totalPages ? page + 1 : undefined
    },
    initialPageParam: 1,
    staleTime: 30 * 1000, // 30 seconds
    retry: 2,
  })
}

export const useAssignUserToStaff = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ staffId, userId }: { staffId: string; userId: string }) => assignUserToStaff(staffId, userId),
    onSuccess: (_, { staffId }) => {
      // Invalidate staff detail and user queries
      queryClient.invalidateQueries({ queryKey: ['staff-detail', staffId] })
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

export const useRemoveUserFromStaff = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (staffId: string) => removeUserFromStaff(staffId),
    onSuccess: (_, staffId) => {
      // Invalidate staff detail and user queries
      queryClient.invalidateQueries({ queryKey: ['staff-detail', staffId] })
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}
