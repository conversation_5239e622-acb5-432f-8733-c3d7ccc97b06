import { useQuery } from '@tanstack/react-query'

import { StaffDetailResponse } from '../types/staff-detail.types'

// API service function to fetch staff detail
const getStaffDetail = async (staffId: string): Promise<StaffDetailResponse> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/detail`)

  if (!response.ok) {
    throw new Error(`Failed to fetch staff detail: ${response.statusText}`)
  }

  const data = await response.json()

  return data
}

// TanStack Query hook for staff detail
export const useStaffDetail = (staffId: string) => {
  return useQuery({
    queryKey: ['staff-detail', staffId],
    queryFn: () => getStaffDetail(staffId),
    enabled: !!staffId, // Only run query if staffId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Export query key factory for consistency
export const staffDetailQueryKeys = {
  all: ['staff-detail'] as const,
  detail: (staffId: string) => ['staff-detail', staffId] as const,
}
