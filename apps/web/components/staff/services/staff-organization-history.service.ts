import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/components/toast'
import { Any } from '@workspace/ui/types'

import { StaffDetailResponse } from '../types/staff-detail.types'

interface UpdateOrganizationHistoryDto {
  staffOrganizationHistoryId: string
  [key: string]: Any
}

// API service function to update organization history
const updateOrganizationHistory = async (data: UpdateOrganizationHistoryDto) => {
  const response = await fetch('/ac-apis/staffs/organization-history', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  if (!response.ok) {
    const errorData = await response.json().catch(() => null)
    throw new Error(errorData?.message || `Failed to update organization history: ${response.statusText}`)
  }

  return response.json()
}

// React Query mutation hook for updating organization history
export const useUpdateOrganizationHistory = (staffId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateOrganizationHistory,
    onMutate: async (data: UpdateOrganizationHistoryDto) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['staff-detail', staffId] })

      // Snapshot the previous value for rollback
      const previousStaff = queryClient.getQueryData<StaffDetailResponse>(['staff-detail', staffId])

      // Optimistically update the cache
      if (previousStaff) {
        const updatedStaff = {
          ...previousStaff,
          organizationHistory: previousStaff.organizationHistory?.map(history => {
            if (history.id === data.staffOrganizationHistoryId) {
              // Update the specific organization history record
              return {
                ...history,
                ...Object.fromEntries(Object.entries(data).filter(([key]) => key !== 'staffOrganizationHistoryId')),
                updatedAt: new Date().toISOString(),
              }
            }

            return history
          }),
        }

        queryClient.setQueryData<StaffDetailResponse>(['staff-detail', staffId], updatedStaff)
      }

      return { previousStaff }
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousStaff) {
        queryClient.setQueryData(['staff-detail', staffId], context.previousStaff)
      }

      // Show error toast
      toast.error('Cập nhật lịch sử tổ chức thất bại', {
        description: error instanceof Error ? error.message : 'Đã có lỗi xảy ra khi cập nhật thông tin',
      })

      console.error('Organization history update error:', error)
    },
    onSuccess: updatedResponse => {
      // Show success toast
      toast.success('Cập nhật lịch sử tổ chức thành công', {
        description: 'Thông tin đã được cập nhật thành công',
      })

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['staffs'] })

      console.log('Organization history updated successfully:', updatedResponse)
    },
  })
}
