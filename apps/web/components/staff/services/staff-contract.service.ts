'use client'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/components/toast'

import { StaffDetailResponse } from '../types/staff-detail.types'

// Types for contract operations
export interface CreateContractData {
  organizationUnitId: string
  contractNo: string
  contractType: 'LABOR' | 'SERVICE' | 'SECONDMENT' | 'OTHER'
  startDate: string
  endDate?: string
  terminateOldContracts?: boolean
  fileId?: string
  fileRef?: string
}

interface UpdateContractEndDateData {
  contractId: string
  endDate?: string
}

// API service function to create a contract
const createStaffContract = async (
  staffId: string,
  data: CreateContractData
): Promise<{ message: string; contractId: string }> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/contracts`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to create contract')
  }

  return response.json()
}

// API service function to update contract end date
const updateContractEndDate = async (data: UpdateContractEndDateData): Promise<{ message: string }> => {
  const response = await fetch(`/ac-apis/staffs/contracts/${data.contractId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ endDate: data.endDate }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to update contract')
  }

  return response.json()
}

// Hook for creating a staff contract
export const useCreateStaffContract = (staffId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateContractData) => createStaffContract(staffId, data),
    onMutate: async () => {
      // Cancel any outgoing refetches
      await Promise.all([
        queryClient.cancelQueries({ queryKey: ['staff-detail', staffId] }),
        queryClient.cancelQueries({ queryKey: ['staff-contracts', staffId] }),
      ])
    },
    onSuccess: () => {
      // Invalidate and refetch staff details
      queryClient.invalidateQueries({
        queryKey: ['staff-detail', staffId],
      })
      queryClient.invalidateQueries({
        queryKey: ['staff-contracts', staffId],
      })

      toast.success('Tạo hợp đồng thành công!')
    },
    onError: (error: Error) => {
      console.error('Error creating contract:', error)
      toast.error(error.message || 'Có lỗi xảy ra khi tạo hợp đồng')
    },
  })
}

// Hook for updating contract end date
export const useUpdateContractEndDate = (staffId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdateContractEndDateData) => updateContractEndDate(data),
    onMutate: async variables => {
      // Cancel any outgoing refetches
      await Promise.all([
        queryClient.cancelQueries({ queryKey: ['staff-detail', staffId] }),
        queryClient.cancelQueries({ queryKey: ['staff-contracts', staffId] }),
      ])

      // Snapshot the previous value
      const previousStaff = queryClient.getQueryData<StaffDetailResponse>(['staff-detail', staffId])

      // Optimistically update the contract end date
      if (previousStaff) {
        const updatedStaff = {
          ...previousStaff,
          contracts: previousStaff.contracts?.map(contract =>
            contract.id === variables.contractId
              ? { ...contract, endDate: variables.endDate ? new Date(variables.endDate) : undefined }
              : contract
          ),
        }

        queryClient.setQueryData(['staff-detail', staffId], updatedStaff)
      }

      return { previousStaff }
    },
    onSuccess: () => {
      // Invalidate and refetch to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ['staff-detail', staffId],
      })
      queryClient.invalidateQueries({
        queryKey: ['staff-contracts', staffId],
      })

      toast.success('Cập nhật ngày kết thúc hợp đồng thành công!')
    },
    onError: (error: Error, _variables, context) => {
      // Rollback on error
      if (context?.previousStaff) {
        queryClient.setQueryData(['staff-detail', staffId], context.previousStaff)
      }
      queryClient.invalidateQueries({ queryKey: ['staff-contracts', staffId] })

      console.error('Error updating contract end date:', error)
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật hợp đồng')
    },
  })
}
