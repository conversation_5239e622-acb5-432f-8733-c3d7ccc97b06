import { Any } from '@/lib/types'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/components/toast'

import { STAFF_FIELD_CONFIGS } from '../constants/field-configs'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { EditableFieldConfig, StaffFieldUpdateDto } from '../types/staff-field-update.types'

// Helper function to handle cascading field resets
const prepareCascadingUpdate = (fieldKey: keyof StaffFieldUpdateDto, fieldValue: Any): StaffFieldUpdateDto => {
  const fieldConfig = Object.values(STAFF_FIELD_CONFIGS).find(config => config.key === fieldKey) as
    | EditableFieldConfig
    | undefined

  const updateData: StaffFieldUpdateDto = {
    [fieldKey]: fieldValue,
  }

  // Handle cascading resets for dependent fields
  if (fieldConfig?.cascadeReset) {
    fieldConfig.cascadeReset.forEach((dependentField: keyof StaffFieldUpdateDto) => {
      // Reset dependent fields to empty when parent changes
      updateData[dependentField] = undefined
    })

    // Show user feedback about cascading changes
    if (fieldConfig.cascadeReset.length > 0) {
      const resetFieldNames = fieldConfig.cascadeReset
        .map((field: keyof StaffFieldUpdateDto) => {
          const dependentConfig = Object.values(STAFF_FIELD_CONFIGS).find(config => config.key === field) as
            | EditableFieldConfig
            | undefined

          return dependentConfig?.label || field
        })
        .join(', ')

      toast.info(`Đã cập nhật ${fieldConfig.label} và reset ${resetFieldNames}`)
    }
  }

  return updateData
}

// API service function to update staff fields
const updateStaffFields = async (staffId: string, data: StaffFieldUpdateDto): Promise<StaffDetailResponse> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/fields`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  if (!response.ok) {
    const errorData = await response.json().catch(() => null)
    throw new Error(errorData?.message || `Failed to update staff: ${response.statusText}`)
  }

  return response.json()
}

export const useUpdateStaffFields = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ staffId, data }: { staffId: string; data: StaffFieldUpdateDto }) => {
      return updateStaffFields(staffId, data)
    },
    onMutate: async ({ staffId, data }) => {
      // Cancel Any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['staff-detail', staffId] })

      // Snapshot the previous value for rollback
      const previousStaff = queryClient.getQueryData<StaffDetailResponse>(['staff-detail', staffId])

      // Optimistically update the cache
      if (previousStaff) {
        queryClient.setQueryData<StaffDetailResponse>(['staff-detail', staffId], {
          ...previousStaff,
          ...data,
          updatedAt: new Date(),
        })
      }

      return { previousStaff }
    },
    onError: (error, { staffId }, context) => {
      // Rollback on error
      if (context?.previousStaff) {
        queryClient.setQueryData(['staff-detail', staffId], context.previousStaff)
      }

      // Show error toast
      toast.error('Cập nhật thông tin thất bại', {
        description: error instanceof Error ? error.message : 'Đã có lỗi xảy ra khi cập nhật thông tin nhân viên',
      })

      console.error('Staff update error:', error)
    },
    onSuccess: (updatedStaff, { staffId }) => {
      // Update the cache with server response
      queryClient.setQueryData(['staff-detail', staffId], updatedStaff)

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['staffs'] })

      // Show success toast
      toast.success('Cập nhật thông tin thành công', {
        description: 'Thông tin nhân viên đã được cập nhật thành công',
      })
    },
    onSettled: (data, error, { staffId }) => {
      // Always refetch after mutation settles
      queryClient.invalidateQueries({ queryKey: ['staff-detail', staffId] })
    },
  })
}

// Enhanced hook for single field updates with cascading support
export const useUpdateStaffField = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      staffId,
      fieldKey,
      fieldValue,
    }: {
      staffId: string
      fieldKey: keyof StaffFieldUpdateDto
      fieldValue: Any
    }) => {
      // Get current staff data for cascading logic
      const currentStaff = queryClient.getQueryData<StaffDetailResponse>(['staff-detail', staffId])

      if (!currentStaff) {
        throw new Error('Staff data not found in cache')
      }

      // Prepare cascading update data
      const cascadingData = prepareCascadingUpdate(fieldKey, fieldValue)

      return updateStaffFields(staffId, cascadingData)
    },
    onMutate: async ({ staffId, fieldKey, fieldValue }) => {
      // Cancel Any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['staff-detail', staffId] })

      // Snapshot the previous value for rollback
      const previousStaff = queryClient.getQueryData<StaffDetailResponse>(['staff-detail', staffId])

      // Prepare optimistic update with cascading
      if (previousStaff) {
        const cascadingData = prepareCascadingUpdate(fieldKey, fieldValue)

        queryClient.setQueryData<StaffDetailResponse>(['staff-detail', staffId], {
          ...previousStaff,
          ...cascadingData,
          updatedAt: new Date(),
        })
      }

      return { previousStaff }
    },
    onError: (error, { staffId }, context) => {
      // Rollback on error
      if (context?.previousStaff) {
        queryClient.setQueryData(['staff-detail', staffId], context.previousStaff)
      }

      // Show error toast
      toast.error('Cập nhật thông tin thất bại', {
        description: error instanceof Error ? error.message : 'Đã có lỗi xảy ra khi cập nhật thông tin nhân viên',
      })

      console.error('Staff field update error:', error)
    },
    onSuccess: (updatedStaff, { staffId }) => {
      // Update the cache with server response
      queryClient.setQueryData(['staff-detail', staffId], updatedStaff)

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['staffs'] })
    },
    onSettled: (data, error, { staffId }) => {
      // Always refetch after mutation settles
      queryClient.invalidateQueries({ queryKey: ['staff-detail', staffId] })
    },
  })
}
