import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/lib/toast'

import {
  ChangeOrganizationUnitRequest,
  ChangeOrganizationUnitResponse,
  OrganizationUnitOption,
} from '../types/organization-move.types'

// API service for organization unit operations
const organizationUnitsApi = {
  // Get all organization units for dropdown
  getAll: async (): Promise<{ items: OrganizationUnitOption[] }> => {
    const response = await fetch('/ac-apis/organization-units/method/all?pageSize=1000')

    if (!response.ok) {
      throw new Error('Failed to fetch organization units')
    }

    return response.json()
  },

  // Get organization units by type
  getByType: async (type: number): Promise<OrganizationUnitOption[]> => {
    const response = await fetch(`/ac-apis/organization-units/method/all-by-type/${type}`)

    if (!response.ok) {
      throw new Error('Failed to fetch organization units by type')
    }

    return response.json()
  },

  // Create organization history record (move to new organization)
  changeOrganizationUnit: async (data: ChangeOrganizationUnitRequest): Promise<ChangeOrganizationUnitResponse> => {
    // Include businessRoles in the main API call - backend now handles this directly
    const response = await fetch('/ac-apis/staffs/change-organization-unit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data), // Send all data including businessRoles
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || 'Failed to change organization unit')
    }

    return response.json()
  },
}

// Query keys for caching
const organizationKeys = {
  all: ['organization-units'] as const,
  lists: () => [...organizationKeys.all, 'list'] as const,
  list: (filters: string) => [...organizationKeys.lists(), { filters }] as const,
  details: () => [...organizationKeys.all, 'detail'] as const,
  detail: (id: string) => [...organizationKeys.details(), id] as const,
  byType: (type: number) => [...organizationKeys.all, 'by-type', type] as const,
}

// Hooks for organization units
export const useOrganizationUnitsQuery = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: organizationKeys.lists(),
    queryFn: organizationUnitsApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: options?.enabled ?? true,
    select: data => data.items || [],
  })
}

export const useOrganizationUnitsByTypeQuery = (type: number, enabled = true) => {
  return useQuery({
    queryKey: organizationKeys.byType(type),
    queryFn: () => organizationUnitsApi.getByType(type),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Mutation hook for moving staff to another organization
export const useMoveToOrganizationMutation = (staffId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: organizationUnitsApi.changeOrganizationUnit,
    onSuccess: data => {
      toast.success(data.message || 'Chuyển đơn vị tổ chức thành công')

      // Invalidate and refetch staff details
      queryClient.invalidateQueries({
        queryKey: ['staffs', 'detail', staffId],
      })

      // Invalidate staff list queries
      queryClient.invalidateQueries({
        queryKey: ['staffs', 'list'],
      })
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi chuyển đơn vị tổ chức')
    },
  })
}
