import { Any } from '@workspace/ui/types'

import { FieldType } from '../../form-components/type'

export interface BaseFieldConfig {
  show: boolean
  required?: boolean
  disabled?: boolean
  label?: string
  placeholder?: string
  fieldType?: FieldType // Determines which component to render
  options?: { value: string | number; label: string }[]
  defaultValue?: Any
  validation?: Any // For custom zod validation
  min?: number
  max?: number
  minHeight?: string // For textarea

  // API-based options configuration (for api-select type)
  categoryType?: string
  fallbackOptions?: { value: string | number; label: string }[]
  apiOptions?: {
    enabled?: boolean
    staleTime?: number
    cacheTime?: number
    [key: string]: Any
  }

  // Production UI options (for api-select type)
  showDebugInfo?: boolean
  showOptionCount?: boolean
  allowRetry?: boolean

  // Additional field-specific options
  type?: string // For input type (email, tel, etc.)
  className?: string // Custom CSS classes
  onValueChange?: (values: Any, options?: Any) => void
}
