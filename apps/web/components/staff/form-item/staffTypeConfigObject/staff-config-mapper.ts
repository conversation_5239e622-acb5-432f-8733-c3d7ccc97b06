import {
  BASIC_STAFF_CONFIG,
  TC_ADVISOR_CONFIG,
  TC_LAWYER_CONFIG,
  TT_COLLABORATOR_CONFIG,
  TT_DRIVER_WORKER_CONFIG,
  TT_INTERN_CONFIG,
  TT_LAWYER_CONTRACT_CONFIG,
  TT_OFFICE_STAFF_CONFIG,
  TT_TGPL_OFFICER_CONFIG,
} from '.'

// Type definitions and utility functions
export type StaffFormType =
  | 'TT_STAFF'
  | 'TT_LAWYER_CONTRACT'
  | 'TT_COLLABORATOR'
  | 'LAWYER_ORG'
  | 'LEGAL_ADVISOR'
  | 'TC_OFFICE_STAFF'
  | 'TC_DRIVER_WORKER'
  | 'TGPL_OFFICER'
  | 'TGPL_INTERN'
  | 'ACCOUNTING_AND_OFFICE'
  | 'DRIVER_AND_WORKER'

const STAFF_TYPE_CONFIG_MAP = {
  TT_STAFF: BASIC_STAFF_CONFIG,
  TT_COLLABORATOR: TT_COLLABORATOR_CONFIG,
  TT_LAWYER_CONTRACT: TT_LAWYER_CONTRACT_CONFIG,
  LAWYER_ORG: TC_LAWYER_CONFIG,
  LEGAL_ADVISOR: TC_ADVISOR_CONFIG,
  TC_OFFICE_STAFF: TT_OFFICE_STAFF_CONFIG,
  TC_DRIVER_WORKER: TT_DRIVER_WORKER_CONFIG,
  TGPL_OFFICER: TT_TGPL_OFFICER_CONFIG,
  TGPL_INTERN: TT_INTERN_CONFIG,
  ACCOUNTING_AND_OFFICE: TT_OFFICE_STAFF_CONFIG,
  DRIVER_AND_WORKER: TT_DRIVER_WORKER_CONFIG,
} as const

export const getStaffConfig = (type: StaffFormType) => {
  return STAFF_TYPE_CONFIG_MAP[type] || BASIC_STAFF_CONFIG
}
