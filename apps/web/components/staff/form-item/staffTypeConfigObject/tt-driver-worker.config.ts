import { STAFF_TYPE_CONTRACTOR } from '@/constants/staff'

import { BASIC_STAFF_CONFIG, StaffFormConfiguration } from './base-staff-type.configs'

// Configuration for <PERSON><PERSON><PERSON> xe và người lao động khác (TC_DRIVER_WORKER)
// Based on CSV column 8 - shows ONLY fields marked with "1" in the CSV
// Strict CSV compliance - no extensions or interpretations
export const TT_DRIVER_WORKER_CONFIG: StaffFormConfiguration = {
  personalInfo: {
    ...BASIC_STAFF_CONFIG.personalInfo,
  },

  contactInfo: {
    ...BASIC_STAFF_CONFIG.contactInfo,
  },

  cccdInfo: {
    ...BASIC_STAFF_CONFIG.cccdInfo,
  },

  professionalInfo: {
    professionalLevelId: {
      ...BASIC_STAFF_CONFIG.professionalInfo.professionalLevelId,
      required: true,
      label: 'Trì<PERSON> độ nghiệp vụ',
      placeholder: '<PERSON>ọn tr<PERSON><PERSON> độ nghiệp vụ...',
    },

    staffType: {
      ...BASIC_STAFF_CONFIG.professionalInfo.staffType,
      show: true,
      required: true,
      defaultValue: STAFF_TYPE_CONTRACTOR.value,
      fallbackOptions: [STAFF_TYPE_CONTRACTOR],
    },

    organizationUnitId: {
      ...BASIC_STAFF_CONFIG.professionalInfo.organizationUnitId,
      show: true,
      required: true,
      label: 'Đơn vị quản lý',
      placeholder: 'Chọn đơn vị quản lý...',
    },

    activityStatusId: {
      ...BASIC_STAFF_CONFIG.professionalInfo.activityStatusId,
      show: false, // CSV column 8: empty
      required: false,
    },

    // Years of experience is NOT shown for drivers/workers (CSV: empty)
    yearsOfExperience: {
      ...BASIC_STAFF_CONFIG.professionalInfo.yearsOfExperience,
      show: false, // CSV column 8: empty
      required: false,
    },
  },

  // Appointment info is NOT enabled for drivers/workers (CSV: empty for appointment fields)
  appointmentInfo: {
    enabled: false,
    appointmentDate: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.appointmentDate,
      show: false, // CSV column 8: empty
      required: false,
    },
    appointmentYears: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.appointmentYears,
      show: false, // CSV column 8: empty
      required: false,
    },
    dismissalDate: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.dismissalDate,
      show: false,
      required: false,
    },
    reappointmentDate: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.reappointmentDate,
      show: false,
      required: false,
    },
    reappointmentYears: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.reappointmentYears,
      show: false,
      required: false,
    },
    transferDate: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.transferDate,
      show: false,
      required: false,
    },
    appointmentDecisionAttachment: {
      ...BASIC_STAFF_CONFIG.appointmentInfo.appointmentDecisionAttachment,
      show: false,
      required: false,
    },
  },

  // Contract section - CSV shows extensive contract fields marked as "1"
  contract: {
    enabled: true,
    contractNo: {
      ...BASIC_STAFF_CONFIG.contract.contractNo,
      show: true, // CSV: "Số hợp đồng" = 1
      required: true,
      label: 'Số hợp đồng',
    },
    contractType: {
      ...BASIC_STAFF_CONFIG.contract.contractType,
      show: true, // CSV: "Thông tin hợp đồng" = 1
      label: 'Loại hợp đồng',
    },
    startDate: {
      ...BASIC_STAFF_CONFIG.contract.startDate,
      show: true, // CSV: "Ngày có hiệu lực" = 1
      required: true,
      label: 'Ngày có hiệu lực hợp đồng',
    },
    endDate: {
      ...BASIC_STAFF_CONFIG.contract.endDate,
      show: true, // CSV: "Ngày hết hạn" = 1
      required: false,
      label: 'Ngày hết hạn hợp đồng',
    },
    fileRef: {
      ...BASIC_STAFF_CONFIG.contract.fileRef,
      show: true, // CSV: "File đính kèm" = 1
      required: false,
      label: 'File đính kèm',
    },
    note: {
      ...BASIC_STAFF_CONFIG.contract.note,
      show: true, // CSV: "Ghi chú" = 1
      required: false,
      label: 'Ghi chú',
    },
    status: {
      ...BASIC_STAFF_CONFIG.contract.status,
      show: true,
      required: true,
    },

    extensionMonths: {
      ...BASIC_STAFF_CONFIG.contract.extensionMonths,
      show: true, // CSV: "Số tháng gia hạn" = 1
      required: false,
      label: 'Số tháng gia hạn',
    },
    extendedExpiryDate: {
      ...BASIC_STAFF_CONFIG.contract.extendedExpiryDate,
      show: true, // CSV: "Ngày hết hạn khi gia hạn" = 1
      required: false,
      label: 'Ngày hết hạn khi gia hạn',
    },
    contractTerminationReason: {
      ...BASIC_STAFF_CONFIG.contract.contractTerminationReason,
      show: true, // CSV: "Lý do chấm dứt hợp đồng" = 1
      required: false,
      label: 'Lý do chấm dứt hợp đồng',
    },
  },

  // Organization history section - CSV shows minimal requirements
  organizationHistory: {
    enabled: true, // Minimal org history for drivers/workers
    // Position and organization info
    positionId: {
      ...BASIC_STAFF_CONFIG.organizationHistory.positionId,
      show: false, // CSV column 8: empty for "Chức danh, chức vụ"
      required: true,
    },
    levelId: {
      ...BASIC_STAFF_CONFIG.organizationHistory.levelId,
      show: false,
      required: false,
    },
    isHead: {
      ...BASIC_STAFF_CONFIG.organizationHistory.isHead,
      show: false, // CSV column 8: empty for "Người đứng đầu"
      required: false,
    },
    roles: {
      ...BASIC_STAFF_CONFIG.organizationHistory.roles,
      show: true,
      required: true,
    },
    specializedFields: {
      ...BASIC_STAFF_CONFIG.organizationHistory.specializedFields,
      show: false, // CSV column 8: empty for "Các lĩnh vực chuyên sâu"
      required: false,
    },
    legalAidFormId: {
      ...BASIC_STAFF_CONFIG.organizationHistory.legalAidFormId,
      show: false, // Not marked in CSV
      required: false,
    },
    civilServantRank: {
      ...BASIC_STAFF_CONFIG.organizationHistory.civilServantRank,
      show: false, // CSV column 8: empty for "Hạng viên chức"
      required: false,
    },
    awards: {
      ...BASIC_STAFF_CONFIG.organizationHistory.awards,
      show: false, // CSV column 8: empty for "Khen thưởng"
      required: false,
    },
    disciplinaryActions: {
      ...BASIC_STAFF_CONFIG.organizationHistory.disciplinaryActions,
      show: false, // CSV column 8: empty for "Kỷ luật"
      required: false,
    },
    // Timeline
    startDate: {
      ...BASIC_STAFF_CONFIG.organizationHistory.startDate,
      show: true, // Handled in contract section
      required: true,
    },
    endDate: {
      ...BASIC_STAFF_CONFIG.organizationHistory.endDate,
      show: true,
      required: false,
    },
    status: {
      ...BASIC_STAFF_CONFIG.organizationHistory.status,
      show: false,
      required: false,
    },
    // Decision metadata
    decisionNumber: {
      ...BASIC_STAFF_CONFIG.organizationHistory.decisionNumber,
      show: false,
      required: false,
    },
    decisionDate: {
      ...BASIC_STAFF_CONFIG.organizationHistory.decisionDate,
      show: false,
      required: false,
    },
    decisionAuthority: {
      ...BASIC_STAFF_CONFIG.organizationHistory.decisionAuthority,
      show: false,
      required: false,
    },
    reason: {
      ...BASIC_STAFF_CONFIG.organizationHistory.reason,
      show: false,
      required: false,
    },
    note: {
      ...BASIC_STAFF_CONFIG.organizationHistory.note,
      show: false,
      required: false,
    },
    // Probation/Internship fields
    isProbation: {
      ...BASIC_STAFF_CONFIG.organizationHistory.isProbation,
      show: false,
      required: false,
    },
    probationStartDate: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationStartDate,
      show: false,
      required: false,
    },
    probationDurationMonths: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationDurationMonths,
      show: false,
      required: false,
    },
    probationResult: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationResult,
      show: false,
      required: false,
    },
    probationAssessmentStatus: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationAssessmentStatus,
      show: false,
      required: false,
    },
    probationExemptionReason: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationExemptionReason,
      show: false,
      required: false,
    },
    probationAttachments: {
      ...BASIC_STAFF_CONFIG.organizationHistory.probationAttachments,
      show: false,
      required: false,
    },
  },

  // Card history is NOT enabled for drivers/workers (CSV: empty for card-related fields)
  cardHistory: {
    enabled: false,
    cardNumber: {
      ...BASIC_STAFF_CONFIG.cardHistory.cardNumber,
      show: false, // CSV column 8: empty
      required: false,
    },
    issuedAt: {
      ...BASIC_STAFF_CONFIG.cardHistory.issuedAt,
      show: false,
      required: false,
    },
    status: {
      ...BASIC_STAFF_CONFIG.cardHistory.status,
      show: false,
      required: false,
    },
    isCurrent: {
      ...BASIC_STAFF_CONFIG.cardHistory.isCurrent,
      show: false,
      required: false,
    },
    fileRefFront: {
      ...BASIC_STAFF_CONFIG.cardHistory.fileRefFront,
      show: false,
      required: false,
    },
    fileRefBack: {
      ...BASIC_STAFF_CONFIG.cardHistory.fileRefBack,
      show: false,
      required: false,
    },
    note: {
      ...BASIC_STAFF_CONFIG.cardHistory.note,
      show: false,
      required: false,
    },
    revokedAt: {
      ...BASIC_STAFF_CONFIG.cardHistory.revokedAt,
      show: false,
      required: false,
    },
    revocationReason: {
      ...BASIC_STAFF_CONFIG.cardHistory.revocationReason,
      show: false,
      required: false,
    },
    reissuedAt: {
      ...BASIC_STAFF_CONFIG.cardHistory.reissuedAt,
      show: false,
      required: false,
    },
    decisionAttachment: {
      ...BASIC_STAFF_CONFIG.cardHistory.decisionAttachment,
      show: false,
      required: false,
    },
    changeDescription: {
      ...BASIC_STAFF_CONFIG.cardHistory.changeDescription,
      show: false,
      required: false,
    },
    lawyerLicenseIssuedAt: {
      ...BASIC_STAFF_CONFIG.cardHistory.lawyerLicenseIssuedAt,
      show: false,
      required: false,
    },
  },
}
