import { Any } from '@/lib/types'
import { FormControl, FormField, FormItem, FormLabel } from '@workspace/ui/components/form'
import { Switch } from '@workspace/ui/components/switch'
import { FieldValues } from 'react-hook-form'

import { BaseFormFieldProps } from './type/base-field'

interface FormSwitchProps<T extends FieldValues> extends BaseFormFieldProps<T> {
  description?: string
  useNumericValues?: boolean
}

export function FormSwitch<T extends FieldValues>({
  control,
  name,
  label,
  description,
  disabled = false,
  className,
  useNumericValues = false,
}: FormSwitchProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const getValue = (value: Any): boolean => {
          if (useNumericValues) {
            return value === 1 || value === '1' || value === true
          }

          return Boolean(value)
        }

        const setValue = (checked: boolean) => {
          if (useNumericValues) {
            field.onChange(checked ? 1 : 0)
          } else {
            field.onChange(checked)
          }
        }

        return (
          <FormItem className={`flex flex-row items-center justify-between rounded-lg border p-4 ${className}`}>
            <div className="space-y-0.5">
              <FormLabel>{label}</FormLabel>
              {description && <div className="text-muted-foreground text-sm">{description}</div>}
            </div>
            <FormControl>
              <Switch checked={getValue(field.value)} onCheckedChange={setValue} disabled={disabled} />
            </FormControl>
          </FormItem>
        )
      }}
    />
  )
}
