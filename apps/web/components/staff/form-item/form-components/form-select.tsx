import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { FieldValues } from 'react-hook-form'

import { BaseFormFieldProps } from './type/base-field'

export interface SelectOption {
  value: string
  label: string
}

interface FormSelectProps<T extends FieldValues> extends BaseFormFieldProps<T> {
  placeholder?: string
  options: SelectOption[]
  onValueChange?: (value: string) => void
}

export function FormSelect<T extends FieldValues>({
  control,
  name,
  label,
  required = false,
  disabled = false,
  placeholder,
  options,
  className,
  onValueChange,
}: FormSelectProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>
            {label} {required && <span className="text-red-500">*</span>}
          </FormLabel>
          <Select
            onValueChange={value => {
              field.onChange(value)
              onValueChange?.(value)
            }}
            value={field.value}
            disabled={disabled}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {(() => {
                const validOptions = options.filter(option => option.value && option.value !== '')

                return validOptions.length > 0 ? (
                  validOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="__no_options__" disabled>
                    No options available
                  </SelectItem>
                )
              })()}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
