import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import MultipleSelector, { Option } from '@workspace/ui/components/multiselect'
import { Loader2 } from 'lucide-react'
import { FieldValues } from 'react-hook-form'

import { SelectOption } from './form-select'
import { BaseFormFieldProps } from './type/base-field'

interface FormMultiSelectProps<T extends FieldValues> extends BaseFormFieldProps<T> {
  placeholder?: string
  options: SelectOption[]
  maxSelected?: number
  hidePlaceholderWhenSelected?: boolean
  onValueChange?: (values: string[], options: SelectOption[]) => void
}

export function FormMultiSelect<T extends FieldValues>({
  control,
  name,
  label,
  required = false,
  disabled = false,
  placeholder,
  options,
  maxSelected,
  hidePlaceholderWhenSelected = true,
  className,
  onValueChange,
}: FormMultiSelectProps<T>) {
  const convertedOptions: Option[] = options.map(option => ({
    value: option.value,
    label: option.label,
  }))

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>
            {label} {required && <span className="text-red-500">*</span>}
          </FormLabel>
          <FormControl>
            <MultipleSelector
              value={
                field.value
                  ? field.value.map((val: string) => {
                      const option = convertedOptions.find(opt => opt.value === val)

                      return option || { value: val, label: val }
                    })
                  : []
              }
              onChange={selectedOptions => {
                const values = selectedOptions.map(option => option.value)
                const selectOptions = selectedOptions.map(option => ({
                  value: option.value,
                  label: option.label,
                }))
                field.onChange(values)
                onValueChange?.(values, selectOptions)
              }}
              options={convertedOptions}
              placeholder={placeholder}
              maxSelected={maxSelected}
              hidePlaceholderWhenSelected={hidePlaceholderWhenSelected}
              disabled={disabled}
              emptyIndicator={
                <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">Không có dữ liệu</p>
              }
              loadingIndicator={
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm">Đang tải...</span>
                </div>
              }
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
