import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Textarea } from '@workspace/ui/components/textarea'
import { FieldValues } from 'react-hook-form'

import { BaseFormFieldProps } from './type/base-field'

interface FormTextareaProps<T extends FieldValues> extends BaseFormFieldProps<T> {
  placeholder?: string
  minHeight?: string
}

export function FormTextarea<T extends FieldValues>({
  control,
  name,
  label,
  required = false,
  disabled = false,
  placeholder,
  minHeight = 'min-h-[100px]',
  className,
}: FormTextareaProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>
            {label} {required && <span className="text-red-500">*</span>}
          </FormLabel>
          <FormControl>
            <Textarea placeholder={placeholder} className={minHeight} disabled={disabled} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
