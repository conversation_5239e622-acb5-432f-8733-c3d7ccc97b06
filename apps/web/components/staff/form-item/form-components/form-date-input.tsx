import { DatePickerInput } from '@workspace/ui/components/date-picker-input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/mi'
import { FieldValues } from 'react-hook-form'

import { BaseFormFieldProps } from './type/base-field'

// Date Input Component
interface FormDateInputProps<T extends FieldValues> extends BaseFormFieldProps<T> {
  placeholder?: string
  disallowFuture?: boolean
  disallowPast?: boolean
  defaultValue?: string | Date | null
}

export function FormDateInput<T extends FieldValues>({
  control,
  name,
  label,
  required = false,
  disabled = false,
  placeholder = 'dd/mm/yyyy',
  className,
  disallowFuture = false,
  disallowPast = false,
  defaultValue,
}: FormDateInputProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const rawValue = field.value as unknown
        let pickerValue: string | undefined = ''

        const normalizedDefault = (() => {
          if (defaultValue === null || defaultValue === undefined) {
            return undefined
          }

          if (defaultValue instanceof Date) {
            return defaultValue.toISOString().split('T')[0]
          }

          if (typeof defaultValue === 'string') {
            const trimmed = defaultValue.trim()

            if (!trimmed) {
              return undefined
            }

            if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
              return trimmed
            }

            const parsed = new Date(trimmed)

            if (!Number.isNaN(parsed.getTime())) {
              return parsed.toISOString().split('T')[0]
            }
          }

          return undefined
        })()

        if (typeof rawValue === 'string') {
          pickerValue = rawValue
        } else if (rawValue instanceof Date) {
          pickerValue = rawValue.toISOString().split('T')[0]
        } else if ((rawValue === undefined || rawValue === null) && normalizedDefault) {
          pickerValue = normalizedDefault

          if (typeof window !== 'undefined') {
            Promise.resolve().then(() => field.onChange(normalizedDefault))
          } else {
            field.onChange(normalizedDefault)
          }
        }

        return (
          <FormItem className={className}>
            <FormLabel>
              {label} {required && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <DatePickerInput
                value={pickerValue}
                onChange={value => field.onChange(value ?? '')}
                placeholder={placeholder}
                disabled={disabled}
                disallowFuture={disallowFuture}
                disallowPast={disallowPast}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
