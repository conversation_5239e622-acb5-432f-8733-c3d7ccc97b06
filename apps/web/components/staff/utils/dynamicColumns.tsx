'use client'

import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { CATEGORY_TYPES, getCategoryDisplayName, Staff, STAFF_BUSINESS_ROLE_OPTIONS } from '@/constants/staff'
import { Any } from '@/lib/types'
import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { selectColumn, TABLE_SIZE } from '@workspace/ui/mi/table'
import { Building2, Calendar, Crown, Eye, FileText, Mail, Phone, User } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { StaffFormType } from '../form-item/staffTypeConfigObject/staff-config-mapper'
import { StaffFormConfig } from '../form/StaffFormConfigTypes'

interface DynamicColumnOptions {
  onEdit?: (lawyer: Staff) => void
  onDelete?: (lawyer: Staff) => void
  onViewDetail?: (lawyer: Staff) => void
  config: Partial<StaffFormConfig>
  staffType: StaffFormType
  t: ReturnType<typeof useTranslations>
  organizationFilter: string
}

export const generateDynamicColumns = (options: DynamicColumnOptions): ColumnDef<Staff>[] => {
  const { config, onViewDetail, t, organizationFilter } = options
  const columns: ColumnDef<Staff>[] = []

  // Selection column (always first)
  columns.push(selectColumn(t))

  // Personal Information Group
  if (config.fullName?.show || config.gender?.show || config.dateOfBirth?.show) {
    columns.push({
      id: 'personalInfo',
      header: 'Thông tin cá nhân',
      size: 280,
      enableSorting: false,
      cell: ({ row }) => {
        const lawyer = row.original

        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{lawyer.fullName}</span>
            </div>
            {config.gender?.show && (lawyer.gender || lawyer.genderId) && (
              <div className="text-muted-foreground text-sm">{getCategoryDisplayName(lawyer.gender)}</div>
            )}
            {config.dateOfBirth?.show && lawyer.dateOfBirth && (
              <div className="text-muted-foreground flex items-center gap-1 text-sm">
                <Calendar className="h-3 w-3" />
                {new Date(lawyer.dateOfBirth).toLocaleDateString('vi-VN')}
              </div>
            )}
            {config.ethnic?.show && lawyer.ethnic && (
              <div className="text-muted-foreground text-xs">Dân tộc: {lawyer.ethnic}</div>
            )}
            {lawyer.cccd && (
              <div className="text-muted-foreground flex items-center gap-1 text-xs">
                <FileText className="h-3 w-3" />
                CCCD: {lawyer.cccd}
              </div>
            )}
          </div>
        )
      },
      meta: {
        title: 'Thông tin cá nhân',
      },
    })
  }

  // Contact Information Group
  if (config.email?.show || config.phone?.show) {
    columns.push({
      id: 'contactInfo',
      header: 'Liên hệ & Địa chỉ',
      size: 280,
      enableSorting: false,
      cell: ({ row }) => {
        const lawyer = row.original

        return (
          <div className="space-y-1">
            {config.email?.show && lawyer.email && (
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3 text-blue-600" />
                <RenderInteractiveLink
                  onClick={() => (window.location.href = `mailto:${lawyer.email}`)}
                  className="text-sm"
                >
                  {lawyer.email}
                </RenderInteractiveLink>
              </div>
            )}
            {config.phone?.show && lawyer.phone && (
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3 text-green-600" />
                <RenderInteractiveLink
                  onClick={() => (window.location.href = `tel:${lawyer.phone}`)}
                  className="text-sm"
                >
                  {lawyer.phone}
                </RenderInteractiveLink>
              </div>
            )}
            {(lawyer.address || lawyer.permanentAddress) && (
              <div className="text-muted-foreground line-clamp-2 text-xs">
                {lawyer.address || lawyer.permanentAddress}
              </div>
            )}
            {(lawyer.province || lawyer.district || lawyer.ward) && (
              <div className="text-muted-foreground text-xs">
                {[lawyer.ward, lawyer.district, lawyer.province].filter(Boolean).join(', ')}
              </div>
            )}
          </div>
        )
      },
      meta: {
        title: 'Thông tin liên hệ',
      },
    })
  }

  // Job/Position Information Group
  if (config.organizationUnitId?.show || config.role?.show || config.professionalLevel?.show) {
    columns.push({
      id: 'organizationUnitId',
      header: 'Công việc & Trạng thái',
      size: 280,
      enableSorting: false,
      cell: ({ row }) => {
        const staff = row.original
        const roles =
          staff.organizationUnit?.roles && staff.organizationUnit.roles.length > 0
            ? staff.organizationUnit.roles
            : staff.organizationHistory?.find(h => h.status === 1)?.roles ||
              staff.staffRoles?.map((r: Any) => r.role) ||
              []

        // return (<>hello</>)
        return (
          <div className="space-y-1">
            {config.organizationUnitId?.show && staff.organizationUnit && (
              <div className="flex items-center gap-1">
                <Building2 className="text-muted-foreground h-3 w-3" />
                <span className="text-sm font-medium">
                  {/* Handle both new OrganizationUnitResponseDto and legacy structure */}
                  {staff.organizationUnit.name || staff.organizationUnit.code}
                </span>
                {(staff.organizationUnit?.isHead ||
                  staff.organizationHistory?.find(h => h.status === 1)?.isHead ||
                  staff.isHead) && <Crown className="h-3 w-3 text-yellow-600" />}
              </div>
            )}

            {staff.organizationUnit?.code && (
              <div className="text-muted-foreground text-xs">Mã: {staff.organizationUnit.code}</div>
            )}

            {config.role?.show && roles && roles.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {roles.map((role: string, idx: number) => (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {STAFF_BUSINESS_ROLE_OPTIONS.find(opt => opt.value === role)?.label || role}
                  </Badge>
                ))}
              </div>
            )}

            {config.professionalLevel?.show && (
              <div className="flex flex-wrap gap-1">
                {(() => {
                  const current = staff.organizationHistory?.find(h => h.status === 1)

                  if (current)
                    return (
                      <>
                        {current?.position?.name && (
                          <Badge variant="outline" className="text-xs">
                            {current.position.name}
                          </Badge>
                        )}
                        {current?.level?.name && (
                          <Badge variant="secondary" className="text-xs">
                            {current.level.name}
                          </Badge>
                        )}
                      </>
                    )
                })()}

                {staff.professionalLevel && (
                  <Badge variant="secondary" className="text-xs">
                    {staff.professionalLevel.name}
                  </Badge>
                )}
              </div>
            )}
          </div>
        )
      },
      meta: {
        title: 'Thông tin công việc',
        filter: {
          type: 'combobox',
          label: 'Đơn vị',
          apiRequest: async (keyword?: string) => {
            const params = new URLSearchParams()

            if (keyword) params.append('keyword', keyword)
            params.append('pageSize', '100')
            params.append('filter', organizationFilter)

            const response = await fetch(`/ac-apis/organization-units?${params}`)
            const data = await response.json()

            return (
              data.items?.map((item: Any) => ({
                label: `${item.code} - ${item.name}`,
                value: item.id,
              })) || []
            )
          },
          placeholder: 'Chọn đơn vị...',
        },
      },
    })
  }

  // Professional/License Information Group
  if (config.professionalLevel?.show || config.yearsOfExperience?.show) {
    columns.push({
      id: 'professionalInfo',
      header: 'Chuyên môn',
      size: 200,
      enableSorting: false,
      cell: ({ row }) => {
        const staff = row.original

        return (
          <div className="space-y-1">
            {staff.yearsOfExperience && (
              <div className="text-muted-foreground text-xs">Kinh nghiệm: {staff.yearsOfExperience} năm</div>
            )}

            {(() => {
              const current = staff.organizationHistory?.find(h => h.status === 1)
              const specs = current?.specializations || []

              if (!specs.length) return null

              return (
                <div className="flex flex-wrap gap-1">
                  {specs.map((s, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {s.specialization?.name}
                    </Badge>
                  ))}
                  {/* Also show staff professional level */}
                  {(staff as Any).professionalLevelCategory?.name && (
                    <Badge variant="secondary" className="text-xs">
                      {(staff as Any).professionalLevelCategory.name}
                    </Badge>
                  )}
                  {!(staff as Any).professionalLevel?.name && staff.professionalLevel && (
                    <Badge variant="secondary" className="text-xs">
                      {staff.professionalLevel.name}
                    </Badge>
                  )}
                </div>
              )
            })()}
          </div>
        )
      },
      meta: {
        title: 'Thông tin chuyên môn',
      },
    })
  }

  // // Contract/Card Information Group
  if (config.contractNumber?.show || config.cardNumber?.show || config.contractStatus?.show) {
    columns.push({
      id: 'contractInfo',
      header: 'Hợp đồng/Thẻ',
      size: 180,
      enableSorting: false,
      cell: ({ row }) => {
        const staff = row.original

        return (
          <div className="space-y-1">
            {config.contractNumber?.show && staff.contractNumber && (
              <div className="flex items-center gap-1">
                <FileText className="h-3 w-3 text-green-600" />
                <span className="text-sm font-medium">{staff.contractNumber}</span>
              </div>
            )}

            {config.contractStatus?.show && staff.contractStatus && (
              <div>
                <Badge variant="outline" className="text-xs">
                  {staff.contractStatus}
                </Badge>
              </div>
            )}

            {config.cardNumber?.show && staff.cardNumber && (
              <div className="text-muted-foreground text-xs">Thẻ: {staff.cardNumber}</div>
            )}

            {config.contractEffectiveDate?.show && staff.contractEffectiveDate && (
              <div className="text-muted-foreground text-xs">
                Hiệu lực: {new Date(staff.contractEffectiveDate).toLocaleDateString('vi-VN')}
              </div>
            )}
          </div>
        )
      },
      meta: {
        title: 'Hợp đồng & thẻ',
      },
    })
  }

  // // Activity Status Column
  if (config.activityStatusId?.show) {
    columns.push({
      id: 'activityStatusId',
      accessorKey: 'activityStatusId',
      header: 'Trạng thái',
      size: TABLE_SIZE.STATUS,
      cell: ({ row }) => {
        const staff = row.original
        const statusName = getCategoryDisplayName(staff.activityStatus)

        return (
          <Badge variant="secondary" className="text-xs">
            {statusName}
          </Badge>
        )
      },
      meta: {
        title: 'Trạng thái hoạt động',
        filter: {
          type: 'combobox',
          label: 'Trạng thái',
          apiRequest: async (keyword?: string) => {
            const baseFilter = { type: CATEGORY_TYPES.STAFF.ACTIVITY_STATUS, status: 'published' as const }
            const combinedFilter = keyword ? { ...baseFilter, keyword } : baseFilter
            const filterParams = encodeURIComponent(JSON.stringify(combinedFilter))

            const url = `/ac-apis/categories/method/all?filter=${filterParams}&pageSize=100`
            const response = await fetch(url, {
              headers: { Accept: 'application/json', 'Content-Type': 'application/json' },
            })
            const result = await response.json()
            const items = result.data || result.content || result.items || (Array.isArray(result) ? result : [])

            return items.map((item: Any) => ({ label: item.name, value: item.categoryId || item.id })) || []
          },
          placeholder: 'Chọn trạng thái...',
        },
      },
    })
  }

  // // Work Duration Information
  if (config.workStartDate?.show || config.legalWorkStartDate?.show) {
    columns.push({
      id: 'workDuration',
      header: 'Thời gian công tác',
      size: 160,
      enableSorting: false,
      cell: ({ row }) => {
        const staff = row.original

        return (
          <div className="space-y-1">
            {/* Prefer current start/end from organizationHistory */}
            {config.workStartDate?.show &&
              ((staff.organizationHistory?.find(h => h.status === 1)?.startDate as Any) || staff.workStartDate) && (
                <div className="text-sm">
                  Bắt đầu:{' '}
                  <RenderDateTime
                    datetime={
                      (staff.organizationHistory?.find(h => h.status === 1)?.startDate as Any) || staff.workStartDate
                    }
                  />
                </div>
              )}

            {config.legalWorkStartDate?.show && staff.legalWorkStartDate && (
              <div className="text-muted-foreground text-xs">
                Pháp luật: <RenderDateTime datetime={staff.legalWorkStartDate} />
              </div>
            )}

            {config.yearsInLegalField?.show && staff.yearsInLegalField && (
              <div className="text-muted-foreground text-xs">{staff.yearsInLegalField} năm PL</div>
            )}

            {(() => {
              const current = staff.organizationHistory?.find(h => h.status === 1)

              if (!current?.endDate) return null

              return (
                <div className="text-muted-foreground text-xs">
                  Kết thúc: <RenderDateTime datetime={current.endDate as Any} />
                </div>
              )
            })()}
          </div>
        )
      },
      meta: {
        title: 'Thời gian công tác',
      },
    })
  }

  // // Record Information (always show)
  columns.push({
    accessorKey: 'createdAt',
    header: 'Thông tin bản ghi',
    size: TABLE_SIZE.DATETIME + 20,
    cell: ({ row }) => {
      const staff = row.original

      return (
        <div className="space-y-1">
          <div className="text-sm">
            <div className="text-muted-foreground text-xs">Tạo:</div>
            <RenderDateTime datetime={staff.createdAt} />
          </div>
          {staff.updatedAt && staff.updatedAt !== staff.createdAt && (
            <div className="text-sm">
              <div className="text-muted-foreground text-xs">Cập nhật:</div>
              <RenderDateTime datetime={staff.updatedAt} />
            </div>
          )}
          <div className="text-muted-foreground text-xs">ID: {staff.id?.slice(-8)}</div>
        </div>
      )
    },
    meta: {
      title: 'Ngày tạo & cập nhật',
    },
  })

  // // Actions column (always last)
  columns.push({
    id: 'actions',
    header: 'Thao tác',
    size: TABLE_SIZE.ACTIONS,
    cell: ({ row }) => {
      const staff = row.original

      return (
        <div className="flex items-center gap-1">
          {onViewDetail && (
            <Button variant="ghost" size="sm" onClick={() => onViewDetail(staff)}>
              <Eye className="h-3 w-3" />
            </Button>
          )}
          {/* {onEdit && (
            <Button variant="ghost" size="sm" onClick={() => onEdit(staff)}>
              <Edit className="h-3 w-3" />
            </Button>
          )} */}
          {/* {onDelete && (
            <Button variant="ghost" size="sm" onClick={() => onDelete(staff)}>
              <Trash2 className="h-3 w-3 text-red-600" />
            </Button>
          )} */}
        </div>
      )
    },
    meta: {
      title: 'Thao tác',
    },
  })

  return columns
}
