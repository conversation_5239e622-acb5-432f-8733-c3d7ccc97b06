'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Switch } from '@workspace/ui/components/switch'
import { cn } from '@workspace/ui/lib/utils'
import { CalendarIcon, UploadCloud, X } from 'lucide-react'
import { useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { useOrganizationUnits } from '../hooks/use-organization-units'
import { CreateContractData, useCreateStaffContract } from '../services/staff-contract.service'
import { StaffUploadDialog } from '../upload/staff-upload-dialog'
import { UploadedFileSummary } from '../upload/use-staff-upload'

// Validation schema
const createContractSchema = z.object({
  organizationUnitId: z.string().min(1, 'Vui lòng chọn đơn vị'),
  contractNo: z.string().min(1, 'Số hợp đồng là bắt buộc'),
  contractType: z.enum(['LABOR', 'SERVICE', 'SECONDMENT', 'OTHER'], {
    error: 'Vui lòng chọn loại hợp đồng',
  }),
  startDate: z.date({
    error: 'Ngày bắt đầu là bắt buộc',
  }),
  endDate: z.date().optional(),
  terminateOldContracts: z.boolean(),
})

type CreateContractFormData = z.infer<typeof createContractSchema>

interface CreateContractModalProps {
  staffId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

const contractTypeLabels: Record<string, string> = {
  LABOR: 'Hợp đồng lao động',
  SERVICE: 'Hợp đồng dịch vụ',
  SECONDMENT: 'Hợp đồng điều động',
  OTHER: 'Khác',
}

export function CreateContractModal({ staffId, open, onOpenChange, onSuccess }: CreateContractModalProps) {
  const [startDateOpen, setStartDateOpen] = useState(false)
  const [endDateOpen, setEndDateOpen] = useState(false)
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<UploadedFileSummary | null>(null)

  const { data: organizationUnits } = useOrganizationUnits()
  const createContractMutation = useCreateStaffContract(staffId)

  const form = useForm<CreateContractFormData>({
    resolver: zodResolver(createContractSchema),
    defaultValues: {
      organizationUnitId: '',
      contractNo: '',
      contractType: 'LABOR',
      startDate: new Date(),
      endDate: undefined,
      terminateOldContracts: false,
    },
  })

  // Helper function to format date as YYYY-MM-DD
  const formatDateForAPI = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  }

  const onSubmit = async (data: CreateContractFormData) => {
    try {
      const submitData: CreateContractData = {
        ...data,
        startDate: formatDateForAPI(data.startDate),
        endDate: data.endDate ? formatDateForAPI(data.endDate) : undefined,
      }

      if (uploadedFile) {
        if (uploadedFile.fileId) {
          submitData.fileId = uploadedFile.fileId
        }

        if (uploadedFile.fileKey) {
          submitData.fileRef = uploadedFile.fileKey
        } else if (uploadedFile.url) {
          submitData.fileRef = uploadedFile.url
        }
      }

      await createContractMutation.mutateAsync(submitData)
      form.reset()
      setUploadedFile(null)
      onOpenChange(false)
      onSuccess?.()
    } catch (error) {
      // Error is handled by the mutation
      console.error('Error creating contract:', error)
    }
  }

  const handleCancel = () => {
    form.reset()
    setUploadedFile(null)
    onOpenChange(false)
  }

  const uploadOverrides = useMemo(() => ({ pathPrefix: `staff/contracts/${staffId}` }), [staffId])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Tạo hợp đồng mới</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Organization Unit */}
            <FormField
              control={form.control}
              name="organizationUnitId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Đơn vị <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn đơn vị" />
                      </SelectTrigger>
                      <SelectContent>
                        {organizationUnits?.map(unit => (
                          <SelectItem key={unit.id} value={unit.id}>
                            {unit.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contract Number */}
            <FormField
              control={form.control}
              name="contractNo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Số hợp đồng <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập số hợp đồng" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contract Type */}
            <FormField
              control={form.control}
              name="contractType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Loại hợp đồng <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(contractTypeLabels).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Start Date */}
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Ngày bắt đầu <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? field.value.toLocaleDateString('vi-VN') : <span>Chọn ngày</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={date => {
                            field.onChange(date)
                            setStartDateOpen(false)
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* End Date */}
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ngày kết thúc</FormLabel>
                  <FormControl>
                    <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? (
                            field.value.toLocaleDateString('vi-VN')
                          ) : (
                            <span>Chọn ngày (không bắt buộc)</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={date => {
                            field.onChange(date)
                            setEndDateOpen(false)
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Terminate Old Contracts */}
            <FormField
              control={form.control}
              name="terminateOldContracts"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Kết thúc hợp đồng cũ</FormLabel>
                    <div className="text-muted-foreground text-sm">
                      Tự động kết thúc các hợp đồng hiện tại khi tạo hợp đồng mới
                    </div>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Contract file */}
            <FormItem>
              <FormLabel>Tệp hợp đồng</FormLabel>
              {uploadedFile ? (
                <div className="border-muted flex flex-col gap-2 rounded-md border px-3 py-2 text-sm">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="font-semibold">{uploadedFile.fileKey ?? uploadedFile.url}</div>
                      <div className="text-muted-foreground text-xs">
                        {uploadedFile.url ? (
                          <a href={uploadedFile.url} target="_blank" rel="noreferrer" className="underline">
                            Mở tệp đã tải lên
                          </a>
                        ) : null}
                      </div>
                      {uploadedFile.fileId ? (
                        <div className="text-muted-foreground text-xs">File ID: {uploadedFile.fileId}</div>
                      ) : null}
                    </div>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      className="h-7 w-7"
                      onClick={() => setUploadedFile(null)}
                      aria-label="Xóa tệp"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsUploadDialogOpen(true)}
                  className="inline-flex items-center gap-2"
                >
                  <UploadCloud className="h-4 w-4" />
                  Tải tệp hợp đồng
                </Button>
              )}
              <FormMessage />
            </FormItem>

            {/* Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Hủy
              </Button>
              <Button type="submit" disabled={createContractMutation.isPending} className="min-w-[100px]">
                {createContractMutation.isPending ? 'Đang tạo...' : 'Tạo hợp đồng'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>

      <StaffUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        config={{ defaultPathPrefix: 'staff/contracts' }}
        overrides={uploadOverrides}
        title="Tải lên hợp đồng"
        description="Chọn tệp hợp đồng cần đính kèm. Tệp sẽ được lưu tự động theo đường dẫn của nhân sự."
        onUploaded={summary => {
          setUploadedFile(summary)
        }}
      />
    </Dialog>
  )
}
