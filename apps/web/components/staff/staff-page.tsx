'use client'

import { Staff } from '@/constants/staff'
import { Any } from '@/lib/types'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DataTable } from '@workspace/ui/mi/table'
import { PlusIcon, Users } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { getStaffConfig, StaffFormType } from './form-item/staffTypeConfigObject/staff-config-mapper'
import { adaptConfigForTable } from './form/ConfigAdapter'
import { CustomStaffForm } from './form/StaffForm'
import { generateDynamicColumns } from './utils/dynamicColumns'
import { getOrgUnitQueryParamsMapWithStaffType, getStaffTypeName } from './utils/staff-helper'

// Get default query parameters for each staff type - using new staff.ts constants
const getDefaultQueryParams = (staffType: StaffFormType) => {
  const params: Record<string, string | string[]> = {}

  switch (staffType) {
    case 'TT_STAFF':
      // TGPL Officers in TGPL Centers
      params.organizationTypes = '[3]'
      params.businessRoles = '["TGPL_OFFICER","TGPL_INTERN","ACCOUNTING_AND_OFFICE","DRIVER_AND_WORKER" ]'
      // Could also filter by staff type if needed
      // params.staffType = StaffType.IN_ORGANIZATION
      break

    case 'TT_LAWYER_CONTRACT':
      // Contract Lawyers (can be in any organization)
      params.organizationTypes = '[3]'
      params.businessRoles = '["LAWYER_CONTRACT"]'
      // Filter by contractor type to be more specific
      // params.staffType = StaffType.CONTRACTOR
      break

    case 'TT_COLLABORATOR':
      // TGPL Collaborators in TGPL Centers
      params.organizationTypes = '[3]'
      params.businessRoles = '["TGPL_COLLABORATOR"]'
      // Filter by collaborator type to be more specific
      // params.staffType = StaffType.COLLABORATOR
      break

    case 'LAWYER_ORG':
      // Organization Lawyers in Organizations
      params.organizationTypes = '[6,7]'
      params.businessRoles = '["LAWYER_ORG"]'
      break

    case 'LEGAL_ADVISOR':
      // Legal Consultants in Organizations
      params.organizationTypes = '[6,7]'
      params.businessRoles = '["LEGAL_ADVISOR"]'
      break

    default:
      // No default filters for unknown types
      break
  }

  // Convert to URLSearchParams format
  const urlParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => urlParams.append(key, v))
    } else {
      urlParams.append(key, value)
    }
  })

  return urlParams.toString()
}

export const StaffPage = ({
  type,
  tableTitle,
  tablePath,
}: {
  type: StaffFormType
  tableTitle: string
  tablePath: string
}) => {
  const t = useTranslations()
  const router = useRouter()
  const queryClient = useQueryClient()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCreateMenuOpen, setIsCreateMenuOpen] = useState(false)
  const [selectedBusinessRole, setSelectedBusinessRole] = useState<StaffFormType | null>(null)

  // Get configuration for current staff type
  const staffConfig = getStaffConfig(type)
  const orgUnitQueryParams = getOrgUnitQueryParamsMapWithStaffType(type)

  // Generate dynamic columns based on staff type configuration
  // Convert new config format to old format for table compatibility
  const columns = generateDynamicColumns({
    config: adaptConfigForTable(staffConfig),
    staffType: type,
    t,
    onViewDetail: (staff: Staff) => {
      router.push(`/staff-management/${staff.id}`)
    },
    organizationFilter: JSON.stringify(orgUnitQueryParams),
  })

  const {
    table,
    // isLoading, initialized, error, data
  } = useEnhancedTable<Staff>({
    columns,
    pageName: `staff-${type.toLowerCase()}`, // Unique page name per staff type
    keyObject: {},
    queryFn: async state => {
      // Get dynamic params from table state (pagination, sorting, search)
      const dynamicParams = generateQueryParams(state, {
        baseParams: {},
      })

      // Get default params for this staff type
      const defaultParams = getDefaultQueryParams(type)

      // Merge parameters properly to avoid duplicates
      const mergedParams = new URLSearchParams()

      // Add default params first
      if (defaultParams) {
        const defaultParamsObj = new URLSearchParams(defaultParams)
        defaultParamsObj.forEach((value, key) => {
          // Process parameter value - no special handling needed anymore
          mergedParams.append(key, value)
        })
      }

      // Add dynamic params (pagination, search, sorting, and filters)
      if (dynamicParams) {
        const dynamicParamsObj = new URLSearchParams(dynamicParams)
        dynamicParamsObj.forEach((value, key) => {
          // Don't override default filters with dynamic params, but allow column filters including organizationUnitId

          if (!['organizationType', 'organizationTypes', 'businessRole', 'businessRoles', 'staffType'].includes(key)) {
            let normalizedValue = value

            if (typeof value === 'string' && value.startsWith('combobox:')) {
              console.log('key', key)
              console.log('value', JSON.parse(value.replace('combobox:', '')))

              try {
                const payload = JSON.parse(value.replace('combobox:', ''))

                if (Array.isArray(payload)) {
                  const ids = payload.map((item: Any) => item?.value).filter(Boolean)
                  normalizedValue = ids.join(',')
                } else if (payload && typeof payload === 'object') {
                  normalizedValue = payload.value || ''
                }
              } catch (_e) {
                console.error('Failed to parse combobox value:', _e)
                // Fallback to original value if parse fails
              }
            }

            if (normalizedValue !== '') {
              // Ensure specific filters are nested under filter[...]
              if (key === 'activityStatusId' || key === 'organizationUnitId') {
                mergedParams.append(`filter[${key}]`, normalizedValue)
              } else {
                mergedParams.append(key, normalizedValue)
              }
            }
          }
        })
      }

      const finalParams = mergedParams.toString()

      const res = await fetch(`/ac-apis/staffs?${finalParams}`)

      if (!res.ok) {
        throw new Error('Failed to fetch staff data')
      }

      const data = await res.json()

      return data
    },
    initialState: {
      // Default sorting by creation date (replaced cardNumber after refactoring)
      sorting: [{ id: 'createdAt', desc: true }],
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
    },
    enabled: true,
    queryKey: ['staff', type], // Unique key per staff type
  })

  return (
    <>
      <AdminPageLayout breadcrumb={[{ label: tableTitle, href: tablePath }]}>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {tableTitle}
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => {
                    if (type === 'TT_STAFF') {
                      setIsCreateMenuOpen(true)
                    } else {
                      setSelectedBusinessRole(type)
                      setIsCreateDialogOpen(true)
                    }
                  }}
                >
                  <PlusIcon className="h-4 w-4" />
                  Thêm mới
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <DataTable table={table} />
            </div>
          </CardContent>
        </Card>
      </AdminPageLayout>

      {/* Create Menu for TT_STAFF */}
      {type === 'TT_STAFF' && isCreateMenuOpen && (
        <Dialog open={isCreateMenuOpen} onOpenChange={setIsCreateMenuOpen}>
          <DialogContent className="max-w-xs">
            <DialogHeader>
              <DialogTitle>Chọn loại nhân sự</DialogTitle>
              <DialogDescription />
            </DialogHeader>
            <div className="flex flex-col gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedBusinessRole('TGPL_OFFICER')
                  setIsCreateMenuOpen(false)
                  setIsCreateDialogOpen(true)
                }}
              >
                Trợ giúp viên pháp lý
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedBusinessRole('TGPL_INTERN')
                  setIsCreateMenuOpen(false)
                  setIsCreateDialogOpen(true)
                }}
              >
                Tập sự TGPL
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedBusinessRole('ACCOUNTING_AND_OFFICE')
                  setIsCreateMenuOpen(false)
                  setIsCreateDialogOpen(true)
                }}
              >
                Kế toán/văn thư/thủ quỹ/viên chức khác
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedBusinessRole('DRIVER_AND_WORKER')
                  setIsCreateMenuOpen(false)
                  setIsCreateDialogOpen(true)
                }}
              >
                Lái xe, lao động khác
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Create Dialog */}
      <Dialog
        open={isCreateDialogOpen}
        onOpenChange={open => {
          setIsCreateDialogOpen(open)

          if (!open) setSelectedBusinessRole(null)
        }}
      >
        <DialogContent className="flex h-[85vh] flex-col gap-0 p-0 sm:max-w-[800px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Thêm mới {selectedBusinessRole ? getStaffTypeName(selectedBusinessRole) : ''}</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <CustomStaffForm
              initialData={null}
              onSuccess={() => {
                setIsCreateDialogOpen(false)
                setSelectedBusinessRole(null)
                queryClient.invalidateQueries({ queryKey: ['staff', type] })

                if (table.options.meta?.reload) {
                  table.options.meta.reload()
                }
              }}
              staffType={selectedBusinessRole || 'TT_STAFF'}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
