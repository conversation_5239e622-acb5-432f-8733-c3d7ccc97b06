export interface ChangeOrganizationUnitRequest {
  staffId: string
  newOrganizationUnitId: string // Backend expects this field name
  categoryId?: string
  positionId?: string
  levelId?: string
  legalAidFormId?: string
  isHead?: number
  startDate: string // Backend expects this field name
  isProbation?: number
  probationStartDate?: string
  probationDurationMonths?: number
  decisionNumber?: string
  decisionDate?: string
  decisionAuthority?: string
  reason?: string
  note?: string
  // Business roles - now handled directly by backend
  businessRoles?: string[]
}

export interface ChangeOrganizationUnitResponse {
  success?: boolean
  message: string
  userId?: string
  staffId: string
  newOrganizationUnitId: string
  newOrganizationHistoryId: string
  startDate: string
  addedRoles?: string[]
  data?: {
    id: string
    staffId: string
    organizationUnitId: string
    startDate: string
    endDate?: string
  }
}

export interface OrganizationUnitOption {
  id: string
  name: string
  code: string
  type?: string
}
