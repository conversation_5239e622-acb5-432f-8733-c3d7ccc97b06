import { StaffBusinessRole, StaffType } from '@ac/data-types'

// Staff detail interfaces for comprehensive staff information
export interface StaffDetailResponse {
  // Basic staff information
  id: string
  fullName: string
  dateOfBirth?: Date
  genderId?: string
  gender?: CategoryInfo
  ethnicId?: string
  ethnic?: string
  email?: string
  phone?: string
  address?: string
  permanentAddress?: string
  provinceId?: string
  province?: string
  districtId?: string
  district?: string
  wardId?: string
  ward?: string
  cccd?: string
  cccdIssuanceDate?: Date
  cccdPlaceOfIssuance?: string
  professionalLevelId?: string
  professionalLevel?: CategoryInfo
  professionalLevelCategory?: CategoryInfo
  staffType?: StaffType
  maritalStatus?: string
  avatarRef?: string
  activityStatusId?: string
  activityStatus?: CategoryInfo
  yearsOfExperience?: number

  // Current organization (from Staff entity)
  organizationUnitId?: string
  organizationUnit?: OrganizationUnitInfo

  // Assigned user information
  assignedUser?: AssignedUserInfo

  // Related entities
  contracts?: StaffContractInfo[]
  organizationHistory?: StaffOrganizationHistoryInfo[]
  cardHistory?: StaffCardHistoryInfo[]
  trainingHistory?: StaffTrainingHistoryInfo[]
  previousPositions?: StaffPreviousPositionInfo[]

  // System fields
  createdAt: Date
  updatedAt: Date
  createdBy?: string
  updatedBy?: string
}

interface CategoryInfo {
  categoryId: string
  name: string
  code?: string
  type: string
}

interface AssignedUserInfo {
  id: string
  username: string
  email: string
  phone?: string
  isActive: boolean
  createdAt: Date
}

interface OrganizationUnitInfo {
  organizationUnitId: string
  name: string
  code: string
  type: string
  // Current position info (from organization history)
  positionId?: string
  position?: CategoryInfo
  levelId?: string
  level?: CategoryInfo
  isHead?: number
  startDate?: Date
  endDate?: Date
  status?: number
  roles?: StaffBusinessRole[]
}

interface StaffContractInfo {
  id: string
  contractNo: string
  contractType: string
  startDate: Date
  endDate?: Date
  status: string
  organizationUnitId: string
  organizationUnit?: {
    id: string
    name: string
    code: string
    type: number
  }
  fileRef?: string
  fileId?: string
  file?: {
    id: string
    bucket: string
    objectKey: string
    publicUrl?: string
    contentType?: string
  }
  note?: string
  createdAt: Date
  updatedAt: Date
}

interface StaffOrganizationHistoryInfo {
  id: string
  staffId: string
  organizationUnitId: string
  organizationUnit?: OrganizationUnitInfo
  positionId?: string
  position?: CategoryInfo
  levelId?: string
  level?: CategoryInfo
  legalAidFormId?: string
  legalAidForm?: CategoryInfo
  isHead: number
  startDate: Date
  endDate?: Date
  status: number
  roles?: StaffBusinessRole[]
  specializations?: StaffSpecializationInfo[]

  // Additional editable fields
  decisionNumber?: string
  decisionDate?: Date
  decisionAuthority?: string
  reason?: string
  note?: string

  // Probation fields
  isProbation?: number
  probationStartDate?: Date
  probationDurationMonths?: number
  probationResult?: string
  probationAssessmentStatus?: string
  probationExemptionReason?: string
  probationAttachments?: string
}

interface StaffSpecializationInfo {
  id: string
  staffOrganizationId: string
  categoryId: string
  specialization?: CategoryInfo
  level?: string
  certificateNo?: string
  certIssuedAt?: Date
  certExpiresAt?: Date
  note?: string
}

interface StaffCardHistoryInfo {
  cardNumber: string
  cardType: string
  createdAt: Date
  expiryDate?: Date
  id: string
  isCurrent: number
  issuedDate: Date
  note?: string
  status: string
  updatedAt: Date
}

// Internal interface for training history
interface StaffTrainingHistoryInfo {
  id: string
  staffId: string
  trainingClassId: string
  trainingClass?: {
    id: string
    courseName: string
    description?: string
    startDate: Date
    endDate: Date
  }
  registrationDate: Date
  note?: string
  createdAt: Date
  updatedAt: Date
}

// Internal interface for previous positions
interface StaffPreviousPositionInfo {
  id: string
  staffId: string
  categoryId: string
  category?: {
    categoryId: string
    name: string
    code: string
    type: string
  }
  organizationUnitId: string
  organizationUnit?: {
    id: string
    name: string
    code: string
    type: number
  }
  startDate: Date
  endDate?: Date
  decisionNumber?: string
  decisionDate?: Date
  evidenceFileRef?: string
  note?: string
  createdAt: Date
  updatedAt: Date
}
