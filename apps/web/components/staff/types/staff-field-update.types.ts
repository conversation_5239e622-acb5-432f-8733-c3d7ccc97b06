import { Any } from '@/lib/types'
import { StaffType } from '@ac/data-types'

import { FieldType } from '../form-item/form-components/type'

export type StaffFieldUpdateDto = {
  // Basic Information
  fullName?: string
  email?: string
  phone?: string
  address?: string
  permanentAddress?: string
  cccd?: string
  cccdIssuanceDate?: Date
  cccdPlaceOfIssuance?: string
  dateOfBirth?: Date
  yearsOfExperience?: number
  maritalStatus?: string
  staffType?: StaffType

  // Category-based fields
  genderId?: string
  ethnicId?: string
  provinceId?: string
  districtId?: string
  wardId?: string
  professionalLevelId?: string
  activityStatusId?: string

  // Organization
  organizationUnitId?: string

  // System fields
  updatedBy?: string
}

type OrganizationHistoryFieldUpdateDto = {
  // Category-based fields (like staff fields)
  positionId?: string
  levelId?: string
  legalAidFormId?: string

  // Decision fields
  decisionNumber?: string
  decisionDate?: Date
  decisionAuthority?: string
  reason?: string
  note?: string
  isHead?: number

  // Probation fields
  isProbation?: number
  probationStartDate?: Date
  probationDurationMonths?: number
  probationResult?: string
  probationAssessmentStatus?: string
  probationExemptionReason?: string
  probationAttachments?: string
}

export interface EditableFieldConfig {
  key: keyof StaffFieldUpdateDto
  label: string
  type: FieldType
  required?: boolean
  validation?: {
    pattern?: RegExp
    min?: number
    max?: number
    maxLength?: number
    minLength?: number
  }
  options?: Array<{ value: string | number; label: string }> | readonly { value: string | number; label: string }[]
  // API-specific properties
  categoryType?: string
  fallbackOptions?: Array<{ value: string; label: string }> | readonly { value: string; label: string }[]
  // Field dependency configuration for cascading relationships
  dependsOn?: keyof StaffFieldUpdateDto
  cascadeReset?: (keyof StaffFieldUpdateDto)[]
  getDynamicFilters?: (staff: Any) => Record<string, Any>
}

export interface OrganizationHistoryEditableFieldConfig {
  key: keyof OrganizationHistoryFieldUpdateDto
  label: string
  type: FieldType
  required?: boolean
  validation?: {
    pattern?: RegExp
    min?: number
    max?: number
    maxLength?: number
    minLength?: number
  }
  options?: Array<{ value: string | number; label: string }> | readonly { value: string | number; label: string }[]
  // API-specific properties
  categoryType?: string
  fallbackOptions?: Array<{ value: string; label: string }> | readonly { value: string; label: string }[]
  // Field dependency configuration for cascading relationships
  dependsOn?: keyof StaffFieldUpdateDto
  cascadeReset?: (keyof StaffFieldUpdateDto)[]
  getDynamicFilters?: (staff: Any) => Record<string, Any>
}
