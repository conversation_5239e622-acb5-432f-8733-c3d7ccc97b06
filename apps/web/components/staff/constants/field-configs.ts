import { APPOINTMENT_REASON_OPTIONS } from '@/constants/staff'
import { Any } from '@/lib/types'

import { StaffFieldUpdateDto } from '../types/staff-field-update.types'

export const STAFF_FIELD_CONFIGS = {
  fullName: {
    key: 'fullName' as keyof StaffFieldUpdateDto,
    label: 'Họ và tên',
    type: 'text' as const,
    required: true,
    validation: {
      minLength: 2,
      maxLength: 100,
    },
  },
  email: {
    key: 'email' as keyof StaffFieldUpdateDto,
    label: 'Email',
    type: 'email' as const,
    required: false,
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
  },
  phone: {
    key: 'phone' as keyof StaffFieldUpdateDto,
    label: 'Số điện thoại',
    type: 'phone' as const,
    required: false,
    validation: {
      pattern: /^[0-9+\-\s()]+$/,
      minLength: 10,
      maxLength: 15,
    },
  },
  address: {
    key: 'address' as keyof StaffFieldUpdateDto,
    label: 'Địa chỉ hiện tại',
    type: 'textarea' as const,
    required: false,
    validation: {
      maxLength: 500,
    },
  },
  permanentAddress: {
    key: 'permanentAddress' as keyof StaffFieldUpdateDto,
    label: 'Địa chỉ thường trú',
    type: 'textarea' as const,
    required: false,
    validation: {
      maxLength: 500,
    },
  },
  cccd: {
    key: 'cccd' as keyof StaffFieldUpdateDto,
    label: 'Căn cước công dân',
    type: 'text' as const,
    required: false,
    validation: {
      pattern: /^[0-9]{12}$/,
      minLength: 12,
      maxLength: 12,
    },
  },
  cccdPlaceOfIssuance: {
    key: 'cccdPlaceOfIssuance' as keyof StaffFieldUpdateDto,
    label: 'Nơi cấp CCCD',
    type: 'text' as const,
    required: false,
    validation: {
      maxLength: 200,
    },
  },
  dateOfBirth: {
    key: 'dateOfBirth' as keyof StaffFieldUpdateDto,
    label: 'Ngày sinh',
    type: 'date' as const,
    required: false,
  },
  cccdIssuanceDate: {
    key: 'cccdIssuanceDate' as keyof StaffFieldUpdateDto,
    label: 'Ngày cấp CCCD',
    type: 'date' as const,
    required: false,
  },
  yearsOfExperience: {
    key: 'yearsOfExperience' as keyof StaffFieldUpdateDto,
    label: 'Số năm kinh nghiệm',
    type: 'number' as const,
    required: false,
    validation: {
      min: 0,
      max: 50,
    },
  },
  maritalStatus: {
    key: 'maritalStatus' as keyof StaffFieldUpdateDto,
    label: 'Tình trạng hôn nhân',
    type: 'select' as const,
    required: false,
    options: [
      { value: 'single', label: 'Độc thân' },
      { value: 'married', label: 'Đã kết hôn' },
      { value: 'divorced', label: 'Đã ly hôn' },
      { value: 'widowed', label: 'Góa' },
    ],
  },
  activityStatusId: {
    key: 'activityStatusId' as keyof StaffFieldUpdateDto,
    label: 'Trạng thái hoạt động',
    type: 'api-select' as const,
    required: false,
    categoryType: 'TRANG_THAI_HOAT_DONG_CUA_CAN_BO',
  },
  genderId: {
    key: 'genderId' as keyof StaffFieldUpdateDto,
    label: 'Giới tính',
    type: 'api-select' as const,
    required: false,
    categoryType: 'GT',
    fallbackOptions: [
      { value: 'male', label: 'Nam' },
      { value: 'female', label: 'Nữ' },
    ],
  },
  ethnicId: {
    key: 'ethnicId' as keyof StaffFieldUpdateDto,
    label: 'Dân tộc',
    type: 'api-select' as const,
    required: false,
    categoryType: 'DTK',
    fallbackOptions: [],
  },
  provinceId: {
    key: 'provinceId' as keyof StaffFieldUpdateDto,
    label: 'Tỉnh/Thành phố',
    type: 'api-select' as const,
    required: false,
    categoryType: 'TP',
    fallbackOptions: [],
    // When province changes, reset dependent fields
    cascadeReset: ['districtId', 'wardId'] as (keyof StaffFieldUpdateDto)[],
  },
  districtId: {
    key: 'districtId' as keyof StaffFieldUpdateDto,
    label: 'Quận/Huyện',
    type: 'api-select' as const,
    required: false,
    categoryType: 'QH',
    fallbackOptions: [],
    // District depends on province selection
    dependsOn: 'provinceId',
    cascadeReset: ['wardId'] as (keyof StaffFieldUpdateDto)[],
    getDynamicFilters: (staff: Any) => ({
      parentId: staff?.provinceId || staff?.province?.categoryId,
    }),
  },
  wardId: {
    key: 'wardId' as keyof StaffFieldUpdateDto,
    label: 'Phường/Xã',
    type: 'api-select' as const,
    required: false,
    categoryType: 'PX',
    fallbackOptions: [],
    // Ward depends on district selection
    dependsOn: 'districtId',
    getDynamicFilters: (staff: Any) => ({
      parentId: staff?.districtId || staff?.district?.categoryId,
    }),
  },
  professionalLevelId: {
    key: 'professionalLevelId' as keyof StaffFieldUpdateDto,
    label: 'Trình độ chuyên môn',
    type: 'api-select' as const,
    required: false,
    categoryType: 'TDNV',
  },
  staffType: {
    key: 'staffType' as keyof StaffFieldUpdateDto,
    label: 'Loại nhân viên',
    type: 'select' as const,
    required: false,
    options: [
      { value: 'IN_ORGANIZATION', label: 'Cán bộ' },
      { value: 'FULL_TIME', label: 'Toàn thời gian' },
      { value: 'PART_TIME', label: 'Bán thời gian' },
      { value: 'CONTRACT', label: 'Hợp đồng' },
      { value: 'INTERN', label: 'Thực tập sinh' },
    ],
  },
  organizationUnitId: {
    key: 'organizationUnitId' as keyof StaffFieldUpdateDto,
    label: 'Đơn vị tổ chức',
    type: 'api-select' as const,
    required: false,
    categoryType: 'OU', // Organization Unit
    fallbackOptions: [],
  },
} as const

export const getContactInfoEditableFields = () => {
  return [
    STAFF_FIELD_CONFIGS.permanentAddress,
    STAFF_FIELD_CONFIGS.phone,
    STAFF_FIELD_CONFIGS.email,
    STAFF_FIELD_CONFIGS.provinceId,
    STAFF_FIELD_CONFIGS.districtId,
    STAFF_FIELD_CONFIGS.wardId,
  ]
}

export const getPersonalInfoEditableFields = () => {
  return [
    STAFF_FIELD_CONFIGS.dateOfBirth,
    STAFF_FIELD_CONFIGS.genderId,
    STAFF_FIELD_CONFIGS.ethnicId,
    STAFF_FIELD_CONFIGS.cccd,
    STAFF_FIELD_CONFIGS.cccdIssuanceDate,
    STAFF_FIELD_CONFIGS.cccdPlaceOfIssuance,
  ]
}

export const getProfessionalInfoEditableFields = () => {
  return [
    STAFF_FIELD_CONFIGS.professionalLevelId,
    STAFF_FIELD_CONFIGS.activityStatusId,
    STAFF_FIELD_CONFIGS.yearsOfExperience,
  ]
}

// Organization History field configurations
const ORGANIZATION_HISTORY_FIELD_CONFIGS = {
  decisionNumber: {
    key: 'decisionNumber' as const,
    label: 'Số quyết định',
    type: 'text' as const,
    required: false,
    validation: {
      maxLength: 100,
    },
  },
  decisionDate: {
    key: 'decisionDate' as const,
    label: 'Ngày quyết định',
    type: 'date' as const,
    required: false,
  },
  decisionAuthority: {
    key: 'decisionAuthority' as const,
    label: 'Cơ quan ban hành',
    type: 'text' as const,
    required: false,
    validation: {
      maxLength: 200,
    },
  },
  reason: {
    key: 'reason' as const,
    label: 'Lý do',
    type: 'select' as const,
    required: false,
    options: APPOINTMENT_REASON_OPTIONS,
  },
  note: {
    key: 'note' as const,
    label: 'Ghi chú',
    type: 'textarea' as const,
    required: false,
    validation: {
      maxLength: 1000,
    },
  },
  isHead: {
    key: 'isHead' as const,
    label: 'Là người đứng đầu',
    type: 'select' as const,
    required: false,
    options: [
      { value: 0, label: 'Không' },
      { value: 1, label: 'Có' },
    ],
  },
  isProbation: {
    key: 'isProbation' as const,
    label: 'Tập sự',
    type: 'select' as const,
    required: false,
    options: [
      { value: 0, label: 'Không' },
      { value: 1, label: 'Có' },
    ],
  },
  probationStartDate: {
    key: 'probationStartDate' as const,
    label: 'Ngày bắt đầu tập sự',
    type: 'date' as const,
    required: false,
  },
  probationDurationMonths: {
    key: 'probationDurationMonths' as const,
    label: 'Thời gian tập sự (tháng)',
    type: 'number' as const,
    required: false,
    validation: {
      min: 1,
      max: 24,
    },
  },
  probationResult: {
    key: 'probationResult' as const,
    label: 'Kết quả tập sự',
    type: 'select' as const,
    required: false,
    options: [
      { value: '', label: 'Chưa có' },
      { value: 'ĐẠT', label: 'Đạt' },
      { value: 'KHÔNG ĐẠT', label: 'Không đạt' },
    ],
  },
  probationAssessmentStatus: {
    key: 'probationAssessmentStatus' as const,
    label: 'Trạng thái đánh giá tập sự',
    type: 'select' as const,
    required: false,
    options: [
      { value: '', label: 'Chưa đánh giá' },
      { value: 'PENDING', label: 'Đang chờ đánh giá' },
      { value: 'COMPLETED', label: 'Đã hoàn thành đánh giá' },
    ],
  },
  probationExemptionReason: {
    key: 'probationExemptionReason' as const,
    label: 'Lý do miễn tập sự',
    type: 'select' as const,
    required: false,
    validation: {
      maxLength: 500,
    },
  },
  probationAttachments: {
    key: 'probationAttachments' as const,
    label: 'File đính kèm tập sự',
    type: 'text' as const,
    required: false,
    validation: {
      maxLength: 500,
    },
  },
  legalAidFormId: {
    key: 'legalAidFormId' as const,
    label: 'Hình thức trợ giúp pháp lý',
    type: 'api-select' as const,
    required: false,
    apiEndpoint: '/categories',
    categoryType: 'CONG_VIEC_THEO_LINH_VUC_HINH_THUC_THUC_HIEN_TGPL',
    validation: {
      maxLength: 100,
    },
  },
  positionId: {
    key: 'positionId' as const,
    label: 'Chức danh',
    type: 'api-select' as const,
    required: false,
    apiEndpoint: '/categories',
    categoryType: 'CDH',

    validation: {
      maxLength: 100,
    },
  },
  levelId: {
    key: 'levelId' as const,
    label: 'Cấp bậc',
    type: 'api-select' as const,
    required: false,
    apiEndpoint: '/categories',
    categoryType: 'HANG_CHUC_DANH_NGHE_NGHIEP_VIEN_CHUC',
    validation: {
      maxLength: 100,
    },
  },
} as const

export const getOrganizationHistoryEditableFields = () => {
  return [
    ORGANIZATION_HISTORY_FIELD_CONFIGS.positionId,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.levelId,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.legalAidFormId,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.decisionNumber,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.decisionDate,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.decisionAuthority,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.isHead,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.reason,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.note,
  ]
}

export const getOrganizationHistoryProbationFields = () => {
  return [
    ORGANIZATION_HISTORY_FIELD_CONFIGS.isProbation,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationStartDate,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationDurationMonths,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationResult,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationAssessmentStatus,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationExemptionReason,
    ORGANIZATION_HISTORY_FIELD_CONFIGS.probationAttachments,
  ]
}
