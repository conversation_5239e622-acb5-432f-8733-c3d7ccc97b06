'use client'

import { useCallback, useMemo, useState } from 'react'

export type UploadedFileSummary = {
  url: string
  bucket?: string
  fileKey?: string
  fileId?: string
  type?: string
}

export type StaffUploadOptions = {
  kind: string
  server: string
  defaultPathPrefix: string
  maxConcurrentUploads: number
}

export type UploadOverrides = {
  kind?: string
  pathPrefix?: string
}

const DEFAULT_OPTIONS: StaffUploadOptions = {
  kind: 'STAFF_DOCUMENT',
  server: 'default',
  defaultPathPrefix: 'staff/documents',
  maxConcurrentUploads: 4,
}

const DEFAULT_PART_SIZE = 5 * 1024 * 1024

const normalizePathPrefix = (value: string) =>
  value
    .split('/')
    .map(segment => segment.trim())
    .filter(Boolean)
    .map(segment => segment.replace(/[^a-zA-Z0-9._-]/g, '_'))
    .join('/')

const createFileParts = (file: File, partSize: number) => {
  const parts: { partNumber: number; chunk: Blob }[] = []
  let partNumber = 1

  for (let start = 0; start < file.size; start += partSize, partNumber += 1) {
    const end = Math.min(start + partSize, file.size)
    parts.push({ partNumber, chunk: file.slice(start, end) })
  }

  return parts
}

type PresignSingleResponse = {
  strategy: 'single' | 'multipart'
  bucket?: string
  fileKey?: string
  expiresIn?: number
  presignedUrl?: string
  publicUrl?: string
  pathPrefix?: string
}

type InitiateMultipartResponse = {
  strategy: 'multipart'
  sessionId: string
  uploadId: string
  fileKey: string
  bucket: string
  expiresIn: number
  partSize: number
  publicUrl?: string
  pathPrefix?: string
}

type PresignPartResponse = {
  presignedUrl: string
  expiresIn: number
}

type CompleteMultipartResponse = {
  publicUrl?: string
  bucket?: string
  fileKey?: string
  eTag?: string
  fileId?: string
  type?: string
  id?: string
}

type CompleteSingleUploadResponse = {
  bucket?: string
  fileKey?: string
  publicUrl?: string
  eTag?: string
  fileId?: string
  type?: string
  id?: string
}

type UploadResult = {
  summary: UploadedFileSummary
  sessionId?: string
}

const mergeOptions = (options?: Partial<StaffUploadOptions>): StaffUploadOptions => ({
  ...DEFAULT_OPTIONS,
  ...(options ?? {}),
})

export const useStaffUpload = (options?: Partial<StaffUploadOptions>) => {
  const resolvedOptions = useMemo(() => mergeOptions(options), [options])
  const defaultPathPrefix = useMemo(
    () => normalizePathPrefix(resolvedOptions.defaultPathPrefix),
    [resolvedOptions.defaultPathPrefix]
  )

  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [result, setResult] = useState<UploadResult | null>(null)

  const reset = useCallback(() => {
    setUploading(false)
    setUploadProgress(0)
    setErrorMessage(null)
    setResult(null)
  }, [])

  const uploadFile = useCallback(
    async (file: File, overrides?: UploadOverrides): Promise<UploadResult> => {
      if (!file) {
        throw new Error('File is required')
      }

      const mimeType = file.type || 'application/octet-stream'
      const size = file.size
      const server = resolvedOptions.server
      const kind = overrides?.kind ?? resolvedOptions.kind
      const pathPrefix = overrides?.pathPrefix ? normalizePathPrefix(overrides.pathPrefix) : defaultPathPrefix

      setUploading(true)
      setUploadProgress(0)
      setErrorMessage(null)
      setResult(null)

      let initiatedSession: InitiateMultipartResponse | null = null
      let uploadedBytes = 0

      const updateProgress = (delta: number) => {
        uploadedBytes += delta
        const percent = Math.min(100, Math.round((uploadedBytes / size) * 100))
        setUploadProgress(percent)
      }

      try {
        const requestBody = {
          kind,
          server,
          fileName: file.name,
          mimeType,
          fileSize: size,
          pathPrefix: pathPrefix || undefined,
        }

        const presignResponse = await fetch('/ac-apis/medias/uploads/presign-single', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody),
        })

        if (!presignResponse.ok) {
          throw new Error('Không thể khởi tạo đường dẫn tải lên')
        }

        const presignPayload = (await presignResponse.json()) as PresignSingleResponse

        if (presignPayload.strategy === 'single' && presignPayload.presignedUrl) {
          const { eTag } = await uploadSinglePart(file, presignPayload.presignedUrl, mimeType, updateProgress)

          const completeSingle = await finalizeSingleUpload({
            server,
            bucket: presignPayload.bucket,
            fileKey: presignPayload.fileKey!,
            contentType: mimeType,
            contentLength: size,
            publicUrl: presignPayload.publicUrl,
            eTag,
            pathPrefix: presignPayload.pathPrefix,
            type: kind,
          })

          const url =
            completeSingle.publicUrl ??
            presignPayload.publicUrl ??
            buildFallbackUrl(
              completeSingle.bucket ?? presignPayload.bucket,
              completeSingle.fileKey ?? presignPayload.fileKey
            )

          if (!url) {
            throw new Error('Không thể xác định đường dẫn tệp sau khi tải lên (single).')
          }

          const summary: UploadedFileSummary = {
            url,
            fileKey: completeSingle.fileKey ?? presignPayload.fileKey,
            bucket: completeSingle.bucket ?? presignPayload.bucket,
            fileId: completeSingle.fileId ?? completeSingle.id,
            type: completeSingle.type ?? kind,
          }

          const output: UploadResult = { summary }
          setResult(output)

          return output
        }

        const initPayload = await initiateMultipart({
          kind,
          server,
          mimeType,
          fileSize: size,
          fileName: file.name,
          pathPrefix: pathPrefix || undefined,
        })

        initiatedSession = initPayload

        const partSize = initPayload.partSize || DEFAULT_PART_SIZE
        const parts = createFileParts(file, partSize)
        const partUploads: { partNumber: number; eTag: string; size: number }[] = new Array(parts.length)
        let nextIndex = 0

        const worker = async () => {
          while (true) {
            const currentIndex = nextIndex
            nextIndex += 1

            if (currentIndex >= parts.length) {
              break
            }

            const part = parts[currentIndex]

            if (!part) {
              continue
            }

            const signPayload = await presignMultipartPart({
              server,
              fileKey: initPayload.fileKey,
              uploadId: initPayload.uploadId,
              partNumber: part.partNumber,
            })

            const eTag = await uploadChunk(signPayload.presignedUrl, part.chunk, mimeType, updateProgress)
            partUploads[currentIndex] = { partNumber: part.partNumber, eTag, size: part.chunk.size }
          }
        }

        const concurrency = Math.min(resolvedOptions.maxConcurrentUploads, parts.length || 1)
        await Promise.all(Array.from({ length: concurrency }, () => worker()))

        const finalizedParts = partUploads.filter(Boolean) as {
          partNumber: number
          eTag: string
          size: number
        }[]

        if (!finalizedParts.length) {
          throw new Error('Không thể tải lên các phần của tệp.')
        }

        const completePayload = await completeMultipart({
          server,
          fileKey: initPayload.fileKey,
          uploadId: initPayload.uploadId,
          parts: finalizedParts,
          totalSize: file.size,
          type: kind,
        })

        const url = completePayload.publicUrl ?? buildFallbackUrl(completePayload.bucket, completePayload.fileKey)

        if (!url) {
          throw new Error('Không thể xác định đường dẫn tệp sau khi tải lên (multipart).')
        }

        const summary: UploadedFileSummary = {
          url,
          fileKey: completePayload.fileKey,
          bucket: completePayload.bucket,
          fileId: completePayload.fileId ?? completePayload.id,
          type: completePayload.type ?? kind,
        }

        const output: UploadResult = {
          summary,
          sessionId: initPayload.sessionId,
        }

        setResult(output)

        return output
      } catch (error) {
        if (initiatedSession) {
          try {
            await abortMultipart({
              server,
              fileKey: initiatedSession.fileKey,
              uploadId: initiatedSession.uploadId,
            })
          } catch (abortError) {
            console.warn('[useStaffUpload] abort multipart failed', abortError)
          }
        }

        setErrorMessage(error instanceof Error ? error.message : 'Không thể tải lên tệp, vui lòng thử lại.')
        throw error
      } finally {
        setUploading(false)
      }
    },
    [defaultPathPrefix, resolvedOptions]
  )

  return {
    uploading,
    uploadProgress,
    errorMessage,
    result,
    uploadFile,
    reset,
  }
}

const uploadSinglePart = async (
  file: File,
  presignedUrl: string,
  mimeType: string,
  onUploaded?: (bytes: number) => void
) => {
  const response = await fetch(presignedUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': mimeType,
    },
  })

  if (!response.ok) {
    throw new Error('Không thể tải lên tệp (single-part).')
  }

  onUploaded?.(file.size)

  const eTagHeader = response.headers.get('ETag') || response.headers.get('etag')
  const eTag = eTagHeader ? eTagHeader.replace(/"/g, '') : undefined

  return { eTag }
}

const uploadChunk = async (
  presignedUrl: string,
  chunk: Blob,
  mimeType: string,
  onUploaded?: (bytes: number) => void
) => {
  const response = await fetch(presignedUrl, {
    method: 'PUT',
    body: chunk,
    headers: {
      'Content-Type': mimeType,
    },
  })

  if (!response.ok) {
    throw new Error('Không thể tải lên phần dữ liệu.')
  }

  const etag = response.headers.get('ETag') || response.headers.get('etag')

  if (!etag) {
    throw new Error('Không thể lấy ETag từ phản hồi tải lên.')
  }

  onUploaded?.(chunk.size)

  return etag.replace(/"/g, '')
}

const initiateMultipart = async ({
  kind,
  server,
  fileName,
  fileSize,
  mimeType,
  pathPrefix,
}: {
  kind: string
  server: string
  fileName: string
  fileSize: number
  mimeType: string
  pathPrefix?: string
}): Promise<InitiateMultipartResponse> => {
  const response = await fetch('/ac-apis/medias/uploads/initiate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      kind,
      server,
      fileName,
      fileSize,
      mimeType,
      pathPrefix,
    }),
  })

  if (!response.ok) {
    throw new Error('Không thể khởi tạo multipart upload.')
  }

  return response.json()
}

const presignMultipartPart = async ({
  server,
  fileKey,
  uploadId,
  partNumber,
}: {
  server: string
  fileKey: string
  uploadId: string
  partNumber: number
}): Promise<PresignPartResponse> => {
  const response = await fetch('/ac-apis/medias/uploads/presign-part', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      fileKey,
      uploadId,
      partNumber,
      server,
    }),
  })

  if (!response.ok) {
    throw new Error(`Không thể lấy URL tải lên cho phần #${partNumber}.`)
  }

  return response.json()
}

const completeMultipart = async ({
  server,
  fileKey,
  uploadId,
  parts,
  totalSize,
  type,
}: {
  server: string
  fileKey: string
  uploadId: string
  parts: { partNumber: number; eTag: string; size: number }[]
  totalSize: number
  type?: string
}): Promise<CompleteMultipartResponse> => {
  const response = await fetch('/ac-apis/medias/uploads/complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      fileKey,
      uploadId,
      server,
      checksum: undefined,
      totalSize,
      type,
      parts: parts.map(part => ({
        partNumber: part.partNumber,
        eTag: part.eTag,
        size: part.size,
      })),
    }),
  })

  if (!response.ok) {
    throw new Error('Không thể hoàn tất multipart upload.')
  }

  return response.json()
}

const abortMultipart = async ({ server, fileKey, uploadId }: { server: string; fileKey: string; uploadId: string }) => {
  const response = await fetch('/ac-apis/medias/uploads/abort', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ fileKey, uploadId, server }),
  })

  if (!response.ok) {
    throw new Error('Không thể huỷ phiên tải lên chưa hoàn tất.')
  }
}

const buildFallbackUrl = (bucket?: string, fileKey?: string) => {
  if (!bucket || !fileKey) return undefined

  return `${bucket}/${fileKey}`
}

const finalizeSingleUpload = async ({
  server,
  bucket,
  fileKey,
  contentType,
  contentLength,
  publicUrl,
  eTag,
  pathPrefix,
  type,
}: {
  server: string
  bucket?: string
  fileKey: string
  contentType: string
  contentLength: number
  publicUrl?: string
  eTag?: string
  pathPrefix?: string
  type?: string
}): Promise<CompleteSingleUploadResponse> => {
  const response = await fetch('/ac-apis/medias/uploads/complete-single', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      server,
      bucket,
      fileKey,
      contentType,
      contentLength,
      publicUrl,
      eTag,
      pathPrefix,
      type,
    }),
  })

  if (!response.ok) {
    throw new Error('Không thể ghi nhận tệp đã tải lên.')
  }

  return response.json()
}
