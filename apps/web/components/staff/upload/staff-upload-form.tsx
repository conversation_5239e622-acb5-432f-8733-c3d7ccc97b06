'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Progress } from '@workspace/ui/components/progress'
import { toast } from '@workspace/ui/components/toast'
import { Loader2, UploadCloud } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import { StaffUploadOptions, UploadedFileSummary, UploadOverrides, useStaffUpload } from './use-staff-upload'

type Props = {
  open: boolean
  onUploaded?: (summary: UploadedFileSummary) => void
  config?: Partial<StaffUploadOptions>
  overrides?: UploadOverrides
  onClose?: () => void
}

export const StaffUploadForm = ({ open, onUploaded, config, overrides, onClose }: Props) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const { uploading, uploadProgress, errorMessage, result, uploadFile, reset } = useStaffUpload(config)

  useEffect(() => {
    if (!open) {
      setSelectedFile(null)
      reset()

      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }, [open, reset])

  const handleFileChange: React.ChangeEventHandler<HTMLInputElement> = event => {
    const file = event.target.files?.[0]
    setSelectedFile(file ?? null)
    reset()
  }

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Vui lòng chọn tệp cần tải lên')

      return
    }

    try {
      const { summary } = await uploadFile(selectedFile, overrides)
      toast.success('Tải lên thành công')
      onUploaded?.(summary)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Không thể tải lên tệp, vui lòng thử lại.')
    }
  }

  const handleClose = () => {
    onClose?.()
  }

  return (
    <div className="flex flex-col gap-4 py-2">
      <div className="space-y-2">
        <Label htmlFor="staff-upload-file">Chọn tệp</Label>
        <Input id="staff-upload-file" ref={fileInputRef} type="file" onChange={handleFileChange} />
        {selectedFile && (
          <div className="border-muted rounded-md border px-3 py-2 text-sm">
            <div className="font-semibold">{selectedFile.name}</div>
            <div className="text-muted-foreground text-xs">
              {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB · {selectedFile.type || 'application/octet-stream'}
            </div>
          </div>
        )}
      </div>

      {(uploading || uploadProgress > 0) && (
        <div className="space-y-2">
          <Label>Tiến độ</Label>
          <Progress value={uploadProgress} />
          <p className="text-muted-foreground text-xs">{uploadProgress}%</p>
        </div>
      )}

      {errorMessage && <p className="text-destructive text-sm">{errorMessage}</p>}

      {result?.summary && (
        <div className="border-muted rounded-md border px-3 py-2 text-sm">
          <div className="font-semibold">Đường dẫn đã tạo</div>
          <a href={result.summary.url} className="text-primary underline" target="_blank" rel="noreferrer">
            <span className="text-xs break-all sm:text-sm">{result.summary.url}</span>
          </a>
          {result.summary.fileKey ? (
            <div className="text-muted-foreground text-xs">
              File key:
              <span className="ml-1 align-middle text-xs break-all">{result.summary.fileKey}</span>
            </div>
          ) : null}
          {result.summary.fileId ? (
            <div className="text-muted-foreground text-xs">
              File ID:
              <span className="ml-1 align-middle text-xs break-all">{result.summary.fileId}</span>
            </div>
          ) : null}
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={handleClose} disabled={uploading}>
          Đóng
        </Button>
        <Button onClick={handleUpload} disabled={uploading || !selectedFile}>
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang tải lên
            </>
          ) : (
            <>
              <UploadCloud className="mr-2 h-4 w-4" /> Tải lên
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
