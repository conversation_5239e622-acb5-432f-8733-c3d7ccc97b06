'use client'

import { <PERSON><PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader } from '@workspace/ui/components/card'
import { Skeleton } from '@workspace/ui/components/skeleton'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@workspace/ui/components/tabs'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { ArrowLeft, FileText, GraduationCap, History, IdCard, Users } from 'lucide-react'
import { useParams, useRouter } from 'next/navigation'

import { CardHistoryTab } from './components/staff-detail-tab-card-history'
import { ContractsTab } from './components/staff-detail-tab-contract'
import { OrganizationHistoryTab } from './components/staff-detail-tab-organization-history'
import { PersonalInfoTab } from './components/staff-detail-tab-personal-info'
import { PreviousPositionsTab } from './components/staff-detail-tab-previous-position'
import { TrainingHistoryTab } from './components/staff-detail-tab-training-history'
import { UserAssignmentWrapper } from './components/user-assignment-wrapper'
import { useStaffDetail } from './services/staff-detail.service'
import { StaffHeaderCard } from './staff-header-card'

// Loading Component
const LoadingSkeleton = () => (
  <div className="space-y-6">
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-64" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
)

// Main Staff Detail Page Component
export const StaffDetailPage = () => {
  const params = useParams()
  const router = useRouter()

  const staffId = params.staffId as string
  const { data: staff, isLoading, error } = useStaffDetail(staffId)

  if (isLoading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý cán bộ', href: '/staff-management' },
          { label: 'Chi tiết cán bộ', href: '#' },
        ]}
      >
        <LoadingSkeleton />
      </AdminPageLayout>
    )
  }

  if (error || !staff) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý cán bộ', href: '/staff-management' },
          { label: 'Chi tiết cán bộ', href: '#' },
        ]}
      >
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-red-500">Không thể tải thông tin cán bộ. Vui lòng thử lại.</p>
            <Button variant="outline" className="mt-4" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </CardContent>
        </Card>
      </AdminPageLayout>
    )
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Quản lý cán bộ', href: '/staff-management' },
        { label: staff.fullName, href: '#' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with Back Button and Actions */}
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
            <h1 className="text-xl font-bold lg:text-2xl">Chi tiết cán bộ</h1>
          </div>
        </div>

        {/* Main Content Layout */}
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-4">
          {/* Left Sidebar - Staff Header Card */}
          <div className="xl:col-span-1">
            <div className="space-y-6 xl:sticky xl:top-6">
              <StaffHeaderCard staff={staff} />
              <UserAssignmentWrapper
                staff={staff}
                onStaffUpdated={() => {
                  // Refetch staff data when user assignment changes
                  // window.location.reload()
                }}
              />
            </div>
          </div>

          {/* Right Content - Tabs */}
          <div className="xl:col-span-3">
            <Tabs defaultValue="personal" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2 gap-1 sm:grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="personal" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <IdCard className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Cá nhân</span>
                </TabsTrigger>
                <TabsTrigger value="contracts" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <FileText className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Hợp đồng</span>
                </TabsTrigger>
                <TabsTrigger value="organization" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <Users className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Tổ chức</span>
                </TabsTrigger>
                <TabsTrigger value="cards" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <IdCard className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Thẻ</span>
                </TabsTrigger>
                <TabsTrigger value="training" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <GraduationCap className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Đào tạo</span>
                </TabsTrigger>
                <TabsTrigger value="previous" className="flex items-center gap-1 px-2 lg:gap-2 lg:px-3">
                  <History className="h-3 w-3 lg:h-4 lg:w-4" />
                  <span className="text-xs lg:text-sm">Kinh nghiệm</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="personal">
                <PersonalInfoTab staff={staff} />
              </TabsContent>

              <TabsContent value="contracts">
                <ContractsTab staff={staff} />
              </TabsContent>

              <TabsContent value="organization">
                <OrganizationHistoryTab staff={staff} />
              </TabsContent>

              <TabsContent value="cards">
                <CardHistoryTab staff={staff} />
              </TabsContent>

              <TabsContent value="training">
                <TrainingHistoryTab staff={staff} />
              </TabsContent>

              <TabsContent value="previous">
                <PreviousPositionsTab staff={staff} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AdminPageLayout>
  )
}
