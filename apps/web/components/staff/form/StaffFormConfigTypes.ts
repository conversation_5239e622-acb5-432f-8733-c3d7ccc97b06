import type { UseCategoriesOptions } from '@/lib/hooks'
import { Any } from '@/lib/types'
import type * as z from 'zod'

// Field Configuration Types
interface SelectOption {
  value: string
  label: string
}

interface FieldConfig {
  show: boolean
  required?: boolean
  disabled?: boolean
  defaultValue?: string | number | boolean | string[] | number[] | boolean[] | Date | null | undefined | SelectOption
  label?: string
  placeholder?: string
  description?: string
  options?: SelectOption[]
  min?: number
  max?: number
  minHeight?: string
  validation?: z.ZodType<Any>
  useApi?: boolean
  categoryType?: string
  fallbackOptions?: SelectOption[]
  apiOptions?: Omit<UseCategoriesOptions, 'type'>
}

export interface StaffFormConfig {
  // Personal Information
  fullName: FieldConfig
  gender: FieldConfig
  dateOfBirth: FieldConfig
  ethnic: FieldConfig

  // Contact Information
  email: FieldConfig
  phone: FieldConfig
  permanentAddress: FieldConfig
  address: FieldConfig
  province: FieldConfig
  ward: FieldConfig

  // CCCD Information
  cccd: FieldConfig
  cccdIssuanceDate: FieldConfig
  cccdPlaceOfIssuance: FieldConfig

  // Work Organization
  organizationUnitId: FieldConfig
  role: FieldConfig
  staffTitle: FieldConfig
  previousPositions: FieldConfig
  isHead: FieldConfig

  // Professional Information
  professionalLevel: FieldConfig
  activityStatusId: FieldConfig
  workStartDate: FieldConfig
  workEndDate: FieldConfig
  startDurationDate: FieldConfig
  endDurationDate: FieldConfig

  contractTerminationReason: FieldConfig

  // Career Information
  appointmentDate: FieldConfig
  appointmentTerm: FieldConfig
  recruitmentDate: FieldConfig
  contractStartDate: FieldConfig
  contractEndDate: FieldConfig
  legalWorkStartDate: FieldConfig
  legalWorkEndDate: FieldConfig
  yearsInLegalField: FieldConfig

  // Specialized Fields
  specializedFields: FieldConfig
  civilServantRank: FieldConfig
  awards: FieldConfig
  disciplinaryActions: FieldConfig

  // Additional Information
  tgplAppointmentInfo: FieldConfig
  attachmentUrl: FieldConfig
  notes: FieldConfig
  internshipStart: FieldConfig
  internshipEnd: FieldConfig

  // Contract Information
  contractNote: FieldConfig
  contractNumber: FieldConfig
  contractType: FieldConfig
  contractStatus: FieldConfig
  contractPosition: FieldConfig
  contractSalaryScheme: FieldConfig
  contractWorkPattern: FieldConfig
  contractEffectiveDate: FieldConfig
  contractExpiryDate: FieldConfig
  extensionMonths: FieldConfig
  extendedExpiryDate: FieldConfig

  // Card Management (CollabCardHistory fields)
  cardNumber: FieldConfig
  cardIssuedAt: FieldConfig
  cardStatus: FieldConfig
  cardIsCurrent: FieldConfig
  cardFileRefFront: FieldConfig
  cardFileRefBack: FieldConfig
  cardNote: FieldConfig

  // Legal Information (for lawyers)
  lawyerLicenseCode: FieldConfig
  lawyerLicenseIssuedAt: FieldConfig
  lawyerLicenseExpiresAt: FieldConfig
  barAssociation: FieldConfig
  assistanceForms: FieldConfig

  // Experience
  yearsOfExperience: FieldConfig

  // Type
  type: FieldConfig
}
