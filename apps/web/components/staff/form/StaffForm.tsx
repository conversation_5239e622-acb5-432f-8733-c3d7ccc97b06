import { Any, Staff } from '@/lib/types'

import { BASIC_STAFF_CONFIG, StaffFormConfiguration } from '../form-item/staffTypeConfigObject/base-staff-type.configs'
import { StaffFormType } from '../form-item/staffTypeConfigObject/staff-config-mapper'
import { TC_LAWYER_CONFIG } from '../form-item/staffTypeConfigObject/tc-lawer.config'
import { TC_ADVISOR_CONFIG } from '../form-item/staffTypeConfigObject/tc-legal-consultant.config'
import { TT_COLLABORATOR_CONFIG } from '../form-item/staffTypeConfigObject/tt-collaborator.config'
import { TT_DRIVER_WORKER_CONFIG } from '../form-item/staffTypeConfigObject/tt-driver-worker.config'
import { TT_INTERN_CONFIG } from '../form-item/staffTypeConfigObject/tt-intern.config'
import { TT_LAWYER_CONTRACT_CONFIG } from '../form-item/staffTypeConfigObject/tt-lawyer-contract.config'
import { TT_OFFICE_STAFF_CONFIG } from '../form-item/staffTypeConfigObject/tt-office-staff.config'
import { TT_TGPL_OFFICER_CONFIG } from '../form-item/staffTypeConfigObject/tt-tgpl_officer.config'
import { getOrgUnitQueryParamsMapWithStaffType } from '../utils/staff-helper'
import { NewStaffForm } from './NewStaffForm'

export function CustomStaffForm({
  staffType,
  initialData,
  onSuccess,
  onCancel,
}: {
  staffType: StaffFormType
  initialData?: Staff | null
  onSuccess?: (data: Any) => void
  onCancel?: () => void
}) {
  const getConfig = (type: StaffFormType): StaffFormConfiguration => {
    switch (type) {
      case 'TGPL_OFFICER':
        return TT_TGPL_OFFICER_CONFIG
      case 'TGPL_INTERN':
        return TT_INTERN_CONFIG
      case 'ACCOUNTING_AND_OFFICE':
        return TT_OFFICE_STAFF_CONFIG
      case 'DRIVER_AND_WORKER':
        return TT_DRIVER_WORKER_CONFIG
      case 'TT_LAWYER_CONTRACT':
        return TT_LAWYER_CONTRACT_CONFIG
      case 'TT_COLLABORATOR':
        return TT_COLLABORATOR_CONFIG
      case 'LAWYER_ORG':
        return TC_LAWYER_CONFIG
      case 'LEGAL_ADVISOR':
        return TC_ADVISOR_CONFIG
      default:
        return BASIC_STAFF_CONFIG
    }
  }

  return (
    <NewStaffForm
      config={getConfig(staffType)}
      initialData={initialData}
      onSuccess={onSuccess}
      onCancel={onCancel}
      filterParam={getOrgUnitQueryParamsMapWithStaffType(staffType)}
    />
  )
}
