import { Any } from '@/lib/types'
import { useEffect, useState } from 'react'

export interface OrganizationUnit {
  id: string
  name: string
  code: string
}

interface UseOrganizationsResult {
  organizations: OrganizationUnit[]
  isLoading: boolean
  error: Error | null
}

export function useOrganizations(filterParam: Record<string, Any> = { type: 3 }): UseOrganizationsResult {
  const [organizations, setOrganizations] = useState<OrganizationUnit[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchOrganizations = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const filterParamsString = encodeURIComponent(JSON.stringify(filterParam))
        const response = await fetch(`/ac-apis/organization-units?filter=${filterParamsString}&pageSize=100`)

        if (!response.ok) {
          throw new Error('Failed to fetch organizations')
        }

        const data = await response.json()

        if (isMounted) {
          setOrganizations(data.items || [])
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Failed to fetch organizations'))
          setOrganizations([])
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchOrganizations()

    return () => {
      isMounted = false
    }
  }, [filterParam])

  return { organizations, isLoading, error }
}
