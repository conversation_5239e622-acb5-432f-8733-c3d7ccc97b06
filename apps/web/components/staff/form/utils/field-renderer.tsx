import { Any } from '@/lib/types'

import { CategoryApiMultiSelect, CategoryApiSelect } from '..'
import {
  FormDateInput,
  FormInput,
  FormMultiSelect,
  FormSelect,
  FormSwitch,
  FormTextarea,
} from '../../form-item/form-components'
import { OrganizationUnit } from './use-organizations'

interface RenderFieldProps {
  fieldName: string
  fieldConfig: Any
  control: Any
  organizations?: OrganizationUnit[]
  namePrefix?: string
  selectedProvince?: string
  selectedDistrict?: string
  onProvinceChange?: (value: string) => void
  onDistrictChange?: (value: string) => void
  form?: Any
  isHydrated?: boolean
}

export function renderField({
  fieldName,
  fieldConfig,
  control,
  organizations = [],
  namePrefix,
  selectedProvince,
  selectedDistrict,
  onProvinceChange,
  onDistrictChange,
  form,
  isHydrated = false,
}: RenderFieldProps) {
  if (!fieldConfig.show) return null

  const actualFieldName = namePrefix ? `${namePrefix}_${fieldName}` : fieldName
  const baseProps = {
    control,
    name: actualFieldName as Any,
    label: fieldConfig.label || fieldName,
    placeholder: fieldConfig.placeholder,
    required: fieldConfig.required,
    disabled: fieldConfig.disabled,
    className: fieldConfig.className || '',
  }

  switch (fieldConfig.fieldType) {
    case 'email': {
      return <FormInput key={actualFieldName} {...baseProps} type="email" />
    }

    case 'number': {
      return (
        <FormInput key={actualFieldName} {...baseProps} type="number" min={fieldConfig.min} max={fieldConfig.max} />
      )
    }

    case 'date': {
      return (
        <FormDateInput
          key={actualFieldName}
          {...baseProps}
          defaultValue={(fieldConfig.defaultValue ?? undefined) as string | Date | null}
        />
      )
    }

    case 'textarea': {
      return (
        <FormTextarea
          key={actualFieldName}
          {...baseProps}
          minHeight={fieldConfig.minHeight || 'min-h-[100px]'}
          className={fieldConfig.className || (fieldName === 'note' ? 'md:col-span-2' : '')}
        />
      )
    }

    case 'select': {
      if (fieldName === 'organizationUnitId') {
        const orgOptions = organizations.map(org => ({
          value: org.id,
          label: `${org.code} - ${org.name}`,
        }))

        return (
          <FormSelect
            key={actualFieldName}
            {...baseProps}
            options={orgOptions}
            placeholder={fieldConfig.placeholder || 'Chọn đơn vị tổ chức...'}
          />
        )
      }

      const selectOptions = fieldConfig.options || fieldConfig.fallbackOptions || []

      return <FormSelect key={actualFieldName} {...baseProps} options={selectOptions} />
    }

    case 'multiselect': {
      if (fieldName === 'organizationUnitIds') {
        const orgOptions = organizations.map(org => ({
          value: org.id,
          label: `${org.code} - ${org.name}`,
        }))

        return (
          <FormMultiSelect
            key={actualFieldName}
            {...baseProps}
            options={orgOptions}
            placeholder={fieldConfig.placeholder || 'Chọn các đơn vị tổ chức...'}
            maxSelected={fieldConfig.maxSelected}
            hidePlaceholderWhenSelected={fieldConfig.hidePlaceholderWhenSelected ?? true}
            onValueChange={(values, options) => {
              fieldConfig.onValueChange?.(values, options)
            }}
          />
        )
      }

      const multiSelectOptions = fieldConfig.options || fieldConfig.fallbackOptions || []

      return (
        <FormMultiSelect
          key={actualFieldName}
          {...baseProps}
          options={multiSelectOptions}
          maxSelected={fieldConfig.maxSelected}
          hidePlaceholderWhenSelected={fieldConfig.hidePlaceholderWhenSelected ?? true}
          onValueChange={(values, options) => {
            fieldConfig.onValueChange?.(values, options)
          }}
        />
      )
    }

    case 'api-multiselect': {
      const multiSelectAdditionalFilters: Record<string, Any> = {}
      let multiSelectOnValueChangeHandler: ((values: string[], options: Any[]) => void) | undefined

      if (fieldName === 'provinceIds') {
        multiSelectOnValueChangeHandler = values => {
          const districtFieldName = namePrefix ? `${namePrefix}_districtIds` : 'districtIds'
          const wardFieldName = namePrefix ? `${namePrefix}_wardIds` : 'wardIds'

          form?.setValue(districtFieldName, [])
          form?.setValue(wardFieldName, [])

          if (values.length > 0 && values[0]) {
            onProvinceChange?.(values[0])
          } else {
            onProvinceChange?.('')
          }
        }
      } else if (fieldName === 'districtIds') {
        if (isHydrated && selectedProvince) {
          multiSelectAdditionalFilters.parentId = selectedProvince
        }

        multiSelectOnValueChangeHandler = values => {
          const wardFieldName = namePrefix ? `${namePrefix}_wardIds` : 'wardIds'
          form?.setValue(wardFieldName, [])

          if (values.length > 0 && values[0]) {
            onDistrictChange?.(values[0])
          } else {
            onDistrictChange?.('')
          }
        }
      } else if (fieldName === 'wardIds') {
        if (isHydrated && selectedDistrict) {
          multiSelectAdditionalFilters.parentId = selectedDistrict
        }
      }

      let multiSelectEnhancedPlaceholder = baseProps.placeholder

      if (isHydrated) {
        if (fieldName === 'districtIds' && !selectedProvince) {
          multiSelectEnhancedPlaceholder = 'Vui lòng chọn tỉnh/thành phố trước'
        } else if (fieldName === 'wardIds' && !selectedDistrict) {
          multiSelectEnhancedPlaceholder = 'Vui lòng chọn quận/huyện trước'
        }
      }

      const isMultiSelectDisabled =
        baseProps.disabled ||
        (isHydrated && fieldName === 'districtIds' && !selectedProvince) ||
        (isHydrated && fieldName === 'wardIds' && !selectedDistrict)

      return (
        <CategoryApiMultiSelect
          key={actualFieldName}
          {...baseProps}
          placeholder={multiSelectEnhancedPlaceholder}
          categoryType={fieldConfig.categoryType || ''}
          fallbackOptions={fieldConfig.fallbackOptions || []}
          additionalFilters={multiSelectAdditionalFilters}
          onValueChange={multiSelectOnValueChangeHandler}
          disabled={isMultiSelectDisabled}
          maxSelected={fieldConfig.maxSelected}
          showDebugInfo={false}
          showOptionCount={fieldConfig.showOptionCount ?? false}
          allowRetry={fieldConfig.allowRetry ?? true}
          enableInfiniteScroll={true}
          pageSize={20}
          searchKeyword=""
        />
      )
    }

    case 'api-select': {
      const additionalFilters: Record<string, Any> = {}
      let onValueChangeHandler: ((value: string, option: Any) => void) | undefined

      if (fieldName === 'provinceId') {
        onValueChangeHandler = value => {
          const districtFieldName = namePrefix ? `${namePrefix}_districtId` : 'districtId'
          const wardFieldName = namePrefix ? `${namePrefix}_wardId` : 'wardId'

          form?.setValue(districtFieldName, '')
          form?.setValue(wardFieldName, '')

          onProvinceChange?.(value)
        }
      } else if (fieldName === 'districtId') {
        if (isHydrated && selectedProvince) {
          additionalFilters.parentId = selectedProvince
        }

        onValueChangeHandler = value => {
          const wardFieldName = namePrefix ? `${namePrefix}_wardId` : 'wardId'
          form?.setValue(wardFieldName, '')

          onDistrictChange?.(value)
        }
      } else if (fieldName === 'wardId') {
        if (isHydrated && selectedDistrict) {
          additionalFilters.parentId = selectedDistrict
        }
      }

      let enhancedPlaceholder = baseProps.placeholder

      if (isHydrated) {
        if (fieldName === 'districtId' && !selectedProvince) {
          enhancedPlaceholder = 'Vui lòng chọn tỉnh/thành phố trước'
        } else if (fieldName === 'wardId' && !selectedDistrict) {
          enhancedPlaceholder = 'Vui lòng chọn quận/huyện trước'
        }
      }

      const isDisabled =
        baseProps.disabled ||
        (isHydrated && fieldName === 'districtId' && !selectedProvince) ||
        (isHydrated && fieldName === 'wardId' && !selectedDistrict)

      return (
        <CategoryApiSelect
          key={actualFieldName}
          {...baseProps}
          placeholder={enhancedPlaceholder}
          categoryType={fieldConfig.categoryType || ''}
          fallbackOptions={fieldConfig.fallbackOptions || []}
          additionalFilters={additionalFilters}
          onValueChange={onValueChangeHandler}
          disabled={isDisabled}
          showDebugInfo={false}
          showOptionCount={fieldConfig.showOptionCount ?? false}
          allowRetry={fieldConfig.allowRetry ?? true}
          enableInfiniteScroll={true}
          pageSize={20}
          searchKeyword=""
        />
      )
    }

    case 'switch': {
      const shouldUseNumericValues =
        fieldName === 'isHead' || fieldName === 'status' || fieldName === 'isCurrent' || fieldName === 'isProbation'

      return <FormSwitch key={actualFieldName} {...baseProps} useNumericValues={shouldUseNumericValues} />
    }

    case 'text':
    default:
      return <FormInput key={actualFieldName} {...baseProps} type={fieldConfig.type || 'text'} />
  }
}
