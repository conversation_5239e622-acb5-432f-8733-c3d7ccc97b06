'use client'

import { Any } from '@/lib/types'
import { toast } from '@workspace/ui/components/toast'
import { useCallback, useState } from 'react'
import { Path, PathValue, UseFormReturn } from 'react-hook-form'

import { type CitizenData } from '../../../citizen-lookup'
import { StaffFormConfiguration } from '../../form-item/staffTypeConfigObject/base-staff-type.configs'

export function useCitizenPrefill<FormValues extends Record<string, Any>>(
  config: StaffFormConfiguration,
  form: UseFormReturn<FormValues>
) {
  const [isCitizenLookupOpen, setIsCitizenLookupOpen] = useState(false)

  const openCitizenLookup = useCallback(() => setIsCitizenLookupOpen(true), [])
  const closeCitizenLookup = useCallback(() => setIsCitizenLookupOpen(false), [])

  const handleCitizenSelect = useCallback(
    (citizenData: CitizenData) => {
      const assignValue = <TFieldName extends Path<FormValues>>(fieldName: TFieldName, value?: Any) => {
        if (value === undefined || value === null || value === '') return
        form.setValue(fieldName, value as PathValue<FormValues, TFieldName>, {
          shouldDirty: true,
          shouldTouch: true,
        })
      }

      if (config.personalInfo.fullName?.show) {
        assignValue('fullName' as Path<FormValues>, citizenData.fullName)
      }

      if (config.personalInfo.dateOfBirth?.show && citizenData.birthDate) {
        const dateParts = citizenData.birthDate.split('/')

        if (dateParts.length === 3 && dateParts[0] && dateParts[1] && dateParts[2]) {
          const formattedDate = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`
          assignValue('dateOfBirth' as Path<FormValues>, formattedDate)
        }
      }

      if (config.cccdInfo.cccd?.show) {
        assignValue('cccd' as Path<FormValues>, citizenData.idNumber)
      }

      if (config.contactInfo.permanentAddress?.show) {
        assignValue('permanentAddress' as Path<FormValues>, citizenData.permanentAddress)
      }

      toast.success('Đã điền thông tin từ CSDL Quốc gia')
      closeCitizenLookup()
    },
    [closeCitizenLookup, config, form]
  )

  return {
    isCitizenLookupOpen,
    openCitizenLookup,
    closeCitizenLookup,
    handleCitizenSelect,
  }
}
