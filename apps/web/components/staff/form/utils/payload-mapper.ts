import { Any } from '@/lib/types'

import { StaffFormConfiguration } from '../../form-item/staffTypeConfigObject/base-staff-type.configs'

const MULTI_TYPES = new Set(['multiselect', 'api-multiselect'])

type SpecialHandlerResult = {
  key?: string
  value: Any
}

function assignSimpleFields(
  payload: Any,
  configGroup: Record<string, Any>,
  values: Record<string, Any>,
  options: { prefix?: string; numericFields?: string[]; specialHandlers?: Record<string, (value: Any) => Any> } = {}
) {
  const { prefix, numericFields = [], specialHandlers = {} } = options

  Object.entries(configGroup).forEach(([fieldName, fieldConfig]) => {
    if (!fieldConfig.show) return

    if (fieldName === 'enabled') return

    const key = prefix ? `${prefix}_${fieldName}` : fieldName
    const rawValue = values[key]

    if (rawValue === undefined) return

    if (specialHandlers[fieldName]) {
      const handled = specialHandlers[fieldName](rawValue)

      if (handled === undefined || handled === null) {
        return
      }

      if (typeof handled === 'object' && 'value' in (handled as SpecialHandlerResult)) {
        const { key: targetKey = fieldName, value } = handled as SpecialHandlerResult

        if (value !== undefined && value !== null && value !== '') {
          payload[targetKey] = value
        }
      } else if (handled !== '') {
        payload[fieldName] = handled
      }

      return
    }

    if (MULTI_TYPES.has(fieldConfig.fieldType)) {
      if (Array.isArray(rawValue) && rawValue.length > 0) {
        payload[fieldName] = rawValue
      }

      return
    }

    if (rawValue === '') return

    if (numericFields.includes(fieldName)) {
      const numericValue = Number(rawValue)

      if (!Number.isNaN(numericValue)) {
        payload[fieldName] = numericValue
      }

      return
    }

    payload[fieldName] = rawValue
  })
}

export function mapFormValuesToPayload<FormValues extends Record<string, Any>>(
  config: StaffFormConfiguration,
  values: FormValues
) {
  const payload: Any = {}

  assignSimpleFields(payload, config.personalInfo, values)
  assignSimpleFields(payload, config.contactInfo, values)
  assignSimpleFields(payload, config.cccdInfo, values)
  assignSimpleFields(payload, config.professionalInfo, values, {
    specialHandlers: {
      yearsOfExperience: (value: Any) => {
        const parsed = parseInt(value as string, 10)

        return Number.isNaN(parsed) ? undefined : parsed
      },
    },
  })

  if (config.contract.enabled) {
    const contract: Any = {}
    assignSimpleFields(contract, config.contract, values, {
      prefix: 'contract',
    })

    if (Object.keys(contract).length > 0) {
      payload.contract = contract
    }
  }

  if (config.organizationHistory.enabled) {
    const organizationHistory: Any = {}
    assignSimpleFields(organizationHistory, config.organizationHistory, values, {
      prefix: 'organizationHistory',
      numericFields: ['isHead', 'status', 'isProbation', 'probationDurationMonths'],
      specialHandlers: {
        specializedFields: (value: Any[]) => {
          if (!Array.isArray(value) || value.length === 0) {
            return undefined
          }

          return {
            key: 'specializations',
            value: value.map(categoryId => ({
              categoryId,
            })),
          }
        },
      },
    })

    if (Array.isArray(organizationHistory.roles)) {
      const allowedRoles = new Set([
        'TGPL_OFFICER',
        'TGPL_COLLABORATOR',
        'TGPL_INTERN',
        'LAWYER_CONTRACT',
        'LAWYER_ORG',
        'LEGAL_ADVISOR',
        'ACCOUNTING_AND_OFFICE',
        'DRIVER_AND_WORKER',
      ])

      organizationHistory.roles = organizationHistory.roles
        .map((role: string) => (role === 'TGPL_OFFICER' ? 'TGPL_OFFICER' : role))
        .filter((role: string) => allowedRoles.has(role))

      if (organizationHistory.roles.length === 0) {
        delete organizationHistory.roles
      }
    }

    if (Object.keys(organizationHistory).length > 0) {
      payload.organizationHistory = organizationHistory
    }
  }

  if (config.cardHistory.enabled) {
    const cardHistory: Any = {}
    assignSimpleFields(cardHistory, config.cardHistory, values, {
      prefix: 'cardHistory',
      numericFields: ['isCurrent'],
    })

    if (Object.keys(cardHistory).length > 0) {
      payload.cardHistory = cardHistory
    }
  }

  return payload
}
