import { Any } from '@/lib/types'

import { StaffFormConfiguration } from '../../form-item/staffTypeConfigObject/base-staff-type.configs'

const MULTI_TYPES = new Set(['multiselect', 'api-multiselect'])

interface BuildOptions {
  prefix?: string
}

function normalizeValue(defaultValue: Any, fieldType: string) {
  if (!MULTI_TYPES.has(fieldType)) {
    return defaultValue
  }

  if (Array.isArray(defaultValue)) {
    return defaultValue
  }

  if (defaultValue === undefined || defaultValue === null || defaultValue === '') {
    return []
  }

  return [defaultValue]
}

function ensureDefaultArray(fieldType: string) {
  return MULTI_TYPES.has(fieldType) ? [] : undefined
}

function buildSectionDefaults(
  accumulator: Record<string, Any>,
  group: Record<string, Any>,
  options: BuildOptions = {}
) {
  const { prefix } = options

  Object.entries(group).forEach(([fieldName, fieldConfig]) => {
    if (fieldName === 'enabled') return

    const key = prefix ? `${prefix}_${fieldName}` : fieldName

    if (fieldConfig.show && fieldConfig.defaultValue !== undefined) {
      accumulator[key] = normalizeValue(fieldConfig.defaultValue, fieldConfig.fieldType)
    } else {
      const fallback = ensureDefaultArray(fieldConfig.fieldType)

      if (fallback !== undefined) {
        accumulator[key] = fallback
      }
    }
  })
}

export function buildDefaultValues(config: StaffFormConfiguration) {
  const defaults: Record<string, Any> = {}

  buildSectionDefaults(defaults, config.personalInfo)
  buildSectionDefaults(defaults, config.professionalInfo)
  buildSectionDefaults(defaults, config.contactInfo)
  buildSectionDefaults(defaults, config.cccdInfo)

  if (config.contract.enabled) {
    buildSectionDefaults(defaults, config.contract, { prefix: 'contract' })
  }

  if (config.organizationHistory.enabled) {
    buildSectionDefaults(defaults, config.organizationHistory, { prefix: 'organizationHistory' })
  }

  if (config.cardHistory.enabled) {
    buildSectionDefaults(defaults, config.cardHistory, { prefix: 'cardHistory' })
  }

  return defaults
}
