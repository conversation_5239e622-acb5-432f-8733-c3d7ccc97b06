import { Any } from '@/lib/types'
import * as z from 'zod'

import { StaffFormConfiguration } from '../../form-item/staffTypeConfigObject/base-staff-type.configs'

type SectionOptions = {
  prefix?: string
  emailField?: string
  numericFields?: string[]
  yearsOfExperienceField?: string
}

const MULTI_TYPES = new Set(['multiselect', 'api-multiselect'])

function buildSectionSchema(
  schemaFields: Record<string, z.ZodType<Any>>,
  group: Record<string, Any>,
  options: SectionOptions = {}
) {
  const { prefix, emailField, numericFields = [], yearsOfExperienceField } = options

  Object.entries(group).forEach(([fieldName, fieldConfig]) => {
    if (fieldName === 'enabled' || !fieldConfig.show) return

    let fieldSchema: z.ZodType<Any>

    if (fieldConfig.validation) {
      fieldSchema = fieldConfig.validation
    } else if (MULTI_TYPES.has(fieldConfig.fieldType)) {
      fieldSchema = fieldConfig.required
        ? z.array(z.string()).min(1, `${fieldConfig.label || fieldName} là bắt buộc`)
        : z.array(z.string()).optional()
    } else if (emailField && fieldName === emailField) {
      fieldSchema = fieldConfig.required
        ? z
            .string()
            .email('Email không hợp lệ')
            .min(1, `${fieldConfig.label || fieldName} là bắt buộc`)
        : z.string().email('Email không hợp lệ').optional().or(z.literal(''))
    } else if (yearsOfExperienceField && fieldName === yearsOfExperienceField) {
      fieldSchema = fieldConfig.required ? z.string().min(1, 'Số năm kinh nghiệm là bắt buộc') : z.string().optional()
    } else if (numericFields.includes(fieldName)) {
      fieldSchema = z.number().optional()
    } else {
      fieldSchema = fieldConfig.required
        ? z.string().min(1, `${fieldConfig.label || fieldName} là bắt buộc`)
        : z.string().optional()
    }

    const key = prefix ? `${prefix}_${fieldName}` : fieldName
    schemaFields[key] = fieldSchema
  })
}

export function createFormSchema(config: StaffFormConfiguration) {
  const schemaFields: Record<string, z.ZodType<Any>> = {}

  buildSectionSchema(schemaFields, config.personalInfo)
  buildSectionSchema(schemaFields, config.contactInfo, { emailField: 'email' })
  buildSectionSchema(schemaFields, config.cccdInfo)
  buildSectionSchema(schemaFields, config.professionalInfo, { yearsOfExperienceField: 'yearsOfExperience' })

  if (config.contract.enabled) {
    buildSectionSchema(schemaFields, config.contract, { prefix: 'contract' })
  }

  if (config.organizationHistory.enabled) {
    buildSectionSchema(schemaFields, config.organizationHistory, {
      prefix: 'organizationHistory',
      numericFields: ['isHead', 'status', 'isProbation'],
    })
  }

  if (config.cardHistory.enabled) {
    buildSectionSchema(schemaFields, config.cardHistory, {
      prefix: 'cardHistory',
      numericFields: ['isCurrent'],
    })
  }

  return z.object(schemaFields)
}
