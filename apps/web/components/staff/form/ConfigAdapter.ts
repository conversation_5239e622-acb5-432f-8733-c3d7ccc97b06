// Configuration adapter that converts new hierarchical StaffFormConfiguration
// to old flat StaffFormConfig for table compatibility
import { StaffFormConfiguration } from '../form-item/staffTypeConfigObject/base-staff-type.configs'
import { StaffFormConfig } from './StaffFormConfigTypes'

/**
 * Converts new hierarchical StaffFormConfiguration to old flat StaffFormConfig
 * This adapter enables table components to work with the new configuration structure
 */
export function adaptConfigForTable(newConfig: StaffFormConfiguration): Partial<StaffFormConfig> {
  const flatConfig: Partial<StaffFormConfig> = {}

  // Personal Information
  if (newConfig.personalInfo?.fullName?.show) {
    flatConfig.fullName = {
      show: newConfig.personalInfo.fullName.show,
      required: newConfig.personalInfo.fullName.required,
      label: newConfig.personalInfo.fullName.label,
    }
  }

  if (newConfig.personalInfo?.genderId?.show) {
    flatConfig.gender = {
      show: newConfig.personalInfo.genderId.show,
      required: newConfig.personalInfo.genderId.required,
      label: newConfig.personalInfo.genderId.label,
      useApi: newConfig.personalInfo.genderId.fieldType === 'api-select',
      categoryType: newConfig.personalInfo.genderId.categoryType,
      fallbackOptions: newConfig.personalInfo.genderId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.personalInfo?.dateOfBirth?.show) {
    flatConfig.dateOfBirth = {
      show: newConfig.personalInfo.dateOfBirth.show,
      required: newConfig.personalInfo.dateOfBirth.required,
      label: newConfig.personalInfo.dateOfBirth.label,
    }
  }

  if (newConfig.personalInfo?.ethnicId?.show) {
    flatConfig.ethnic = {
      show: newConfig.personalInfo.ethnicId.show,
      required: newConfig.personalInfo.ethnicId.required,
      label: newConfig.personalInfo.ethnicId.label,
      useApi: newConfig.personalInfo.ethnicId.fieldType === 'api-select',
      categoryType: newConfig.personalInfo.ethnicId.categoryType,
      fallbackOptions: newConfig.personalInfo.ethnicId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  // Contact Information
  if (newConfig.contactInfo?.email?.show) {
    flatConfig.email = {
      show: newConfig.contactInfo.email.show,
      required: newConfig.contactInfo.email.required,
      label: newConfig.contactInfo.email.label,
    }
  }

  if (newConfig.contactInfo?.phone?.show) {
    flatConfig.phone = {
      show: newConfig.contactInfo.phone.show,
      required: newConfig.contactInfo.phone.required,
      label: newConfig.contactInfo.phone.label,
    }
  }

  if (newConfig.contactInfo?.permanentAddress?.show) {
    flatConfig.permanentAddress = {
      show: newConfig.contactInfo.permanentAddress.show,
      required: newConfig.contactInfo.permanentAddress.required,
      label: newConfig.contactInfo.permanentAddress.label,
    }
  }

  if (newConfig.contactInfo?.provinceId?.show) {
    flatConfig.province = {
      show: newConfig.contactInfo.provinceId.show,
      required: newConfig.contactInfo.provinceId.required,
      label: newConfig.contactInfo.provinceId.label,
      useApi: newConfig.contactInfo.provinceId.fieldType === 'api-select',
      categoryType: newConfig.contactInfo.provinceId.categoryType,
      fallbackOptions: newConfig.contactInfo.provinceId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.contactInfo?.wardId?.show) {
    flatConfig.ward = {
      show: newConfig.contactInfo.wardId.show,
      required: newConfig.contactInfo.wardId.required,
      label: newConfig.contactInfo.wardId.label,
      useApi: newConfig.contactInfo.wardId.fieldType === 'api-select',
      categoryType: newConfig.contactInfo.wardId.categoryType,
      fallbackOptions: newConfig.contactInfo.wardId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  // CCCD Information
  if (newConfig.cccdInfo?.cccd?.show) {
    flatConfig.cccd = {
      show: newConfig.cccdInfo.cccd.show,
      required: newConfig.cccdInfo.cccd.required,
      label: newConfig.cccdInfo.cccd.label,
    }
  }

  if (newConfig.cccdInfo?.cccdIssuanceDate?.show) {
    flatConfig.cccdIssuanceDate = {
      show: newConfig.cccdInfo.cccdIssuanceDate.show,
      required: newConfig.cccdInfo.cccdIssuanceDate.required,
      label: newConfig.cccdInfo.cccdIssuanceDate.label,
    }
  }

  if (newConfig.cccdInfo?.cccdPlaceOfIssuance?.show) {
    flatConfig.cccdPlaceOfIssuance = {
      show: newConfig.cccdInfo.cccdPlaceOfIssuance.show,
      required: newConfig.cccdInfo.cccdPlaceOfIssuance.required,
      label: newConfig.cccdInfo.cccdPlaceOfIssuance.label,
    }
  }

  // Professional Information
  if (newConfig.professionalInfo?.organizationUnitId?.show) {
    flatConfig.organizationUnitId = {
      show: newConfig.professionalInfo.organizationUnitId.show,
      required: newConfig.professionalInfo.organizationUnitId.required,
      label: newConfig.professionalInfo.organizationUnitId.label,
    }
  }

  if (newConfig.professionalInfo?.professionalLevelId?.show) {
    flatConfig.professionalLevel = {
      show: newConfig.professionalInfo.professionalLevelId.show,
      required: newConfig.professionalInfo.professionalLevelId.required,
      label: newConfig.professionalInfo.professionalLevelId.label,
      options: newConfig.professionalInfo.professionalLevelId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
      useApi: newConfig.professionalInfo.professionalLevelId.fieldType === 'api-select',
      categoryType: newConfig.professionalInfo.professionalLevelId.categoryType,
      fallbackOptions: newConfig.professionalInfo.professionalLevelId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.professionalInfo?.activityStatusId?.show) {
    flatConfig.activityStatusId = {
      show: newConfig.professionalInfo.activityStatusId.show,
      required: newConfig.professionalInfo.activityStatusId.required,
      label: newConfig.professionalInfo.activityStatusId.label,
      options: newConfig.professionalInfo.activityStatusId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
      useApi: newConfig.professionalInfo.activityStatusId.fieldType === 'api-select',
      categoryType: newConfig.professionalInfo.activityStatusId.categoryType,
      fallbackOptions: newConfig.professionalInfo.activityStatusId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.professionalInfo?.yearsOfExperience?.show) {
    flatConfig.yearsOfExperience = {
      show: newConfig.professionalInfo.yearsOfExperience.show,
      required: newConfig.professionalInfo.yearsOfExperience.required,
      label: newConfig.professionalInfo.yearsOfExperience.label,
    }
  }

  // Organization history and position/role information
  if (newConfig.organizationHistory?.roles?.show) {
    flatConfig.role = {
      show: newConfig.organizationHistory.roles.show,
      required: newConfig.organizationHistory.roles.required,
      label: newConfig.organizationHistory.roles.label,
      options: newConfig.organizationHistory.roles.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.positionId?.show) {
    flatConfig.staffTitle = {
      show: newConfig.organizationHistory.positionId.show,
      required: newConfig.organizationHistory.positionId.required,
      label: newConfig.organizationHistory.positionId.label,
      useApi: newConfig.organizationHistory.positionId.fieldType === 'api-select',
      categoryType: newConfig.organizationHistory.positionId.categoryType,
      fallbackOptions: newConfig.organizationHistory.positionId.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.isHead?.show) {
    flatConfig.isHead = {
      show: newConfig.organizationHistory.isHead.show,
      required: newConfig.organizationHistory.isHead.required,
      label: newConfig.organizationHistory.isHead.label,
      options: newConfig.organizationHistory.isHead.options?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.startDate?.show) {
    flatConfig.workStartDate = {
      show: newConfig.organizationHistory.startDate.show,
      required: newConfig.organizationHistory.startDate.required,
      label: newConfig.organizationHistory.startDate.label,
    }
  }

  if (newConfig.organizationHistory?.endDate?.show) {
    flatConfig.workEndDate = {
      show: newConfig.organizationHistory.endDate.show,
      required: newConfig.organizationHistory.endDate.required,
      label: newConfig.organizationHistory.endDate.label,
    }
  }

  if (newConfig.organizationHistory?.specializedFields?.show) {
    flatConfig.specializedFields = {
      show: newConfig.organizationHistory.specializedFields.show,
      required: newConfig.organizationHistory.specializedFields.required,
      label: newConfig.organizationHistory.specializedFields.label,
      useApi: newConfig.organizationHistory.specializedFields.fieldType === 'api-multiselect',
      categoryType: newConfig.organizationHistory.specializedFields.categoryType,
      fallbackOptions: newConfig.organizationHistory.specializedFields.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.civilServantRank?.show) {
    flatConfig.civilServantRank = {
      show: newConfig.organizationHistory.civilServantRank.show,
      required: newConfig.organizationHistory.civilServantRank.required,
      label: newConfig.organizationHistory.civilServantRank.label,
      useApi: newConfig.organizationHistory.civilServantRank.fieldType === 'api-select',
      categoryType: newConfig.organizationHistory.civilServantRank.categoryType,
      fallbackOptions: newConfig.organizationHistory.civilServantRank.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.awards?.show) {
    flatConfig.awards = {
      show: newConfig.organizationHistory.awards.show,
      required: newConfig.organizationHistory.awards.required,
      label: newConfig.organizationHistory.awards.label,
      useApi: newConfig.organizationHistory.awards.fieldType === 'api-multiselect',
      categoryType: newConfig.organizationHistory.awards.categoryType,
      fallbackOptions: newConfig.organizationHistory.awards.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.disciplinaryActions?.show) {
    flatConfig.disciplinaryActions = {
      show: newConfig.organizationHistory.disciplinaryActions.show,
      required: newConfig.organizationHistory.disciplinaryActions.required,
      label: newConfig.organizationHistory.disciplinaryActions.label,
      useApi: newConfig.organizationHistory.disciplinaryActions.fieldType === 'api-multiselect',
      categoryType: newConfig.organizationHistory.disciplinaryActions.categoryType,
      fallbackOptions: newConfig.organizationHistory.disciplinaryActions.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.organizationHistory?.note?.show) {
    flatConfig.notes = {
      show: newConfig.organizationHistory.note.show,
      required: newConfig.organizationHistory.note.required,
      label: newConfig.organizationHistory.note.label,
    }
  }

  // Appointment Information
  if (newConfig.appointmentInfo?.appointmentDate?.show) {
    flatConfig.appointmentDate = {
      show: newConfig.appointmentInfo.appointmentDate.show,
      required: newConfig.appointmentInfo.appointmentDate.required,
      label: newConfig.appointmentInfo.appointmentDate.label,
    }
  }

  if (newConfig.appointmentInfo?.appointmentYears?.show) {
    flatConfig.appointmentTerm = {
      show: newConfig.appointmentInfo.appointmentYears.show,
      required: newConfig.appointmentInfo.appointmentYears.required,
      label: newConfig.appointmentInfo.appointmentYears.label,
    }
  }

  // Contract Information
  if (newConfig.contract?.enabled && newConfig.contract.contractNo?.show) {
    flatConfig.contractNumber = {
      show: newConfig.contract.contractNo.show,
      required: newConfig.contract.contractNo.required,
      label: newConfig.contract.contractNo.label,
    }
  }

  if (newConfig.contract?.enabled && newConfig.contract.contractType?.show) {
    flatConfig.contractType = {
      show: newConfig.contract.contractType.show,
      required: newConfig.contract.contractType.required,
      label: newConfig.contract.contractType.label,
      options: (newConfig.contract.contractType.fallbackOptions || newConfig.contract.contractType.options)?.map(
        opt => ({
          value: String(opt.value),
          label: opt.label,
        })
      ),
      useApi: newConfig.contract.contractType.fieldType === 'api-select',
      categoryType: newConfig.contract.contractType.categoryType,
      fallbackOptions: newConfig.contract.contractType.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.contract?.enabled && newConfig.contract.startDate?.show) {
    flatConfig.contractStartDate = {
      show: newConfig.contract.startDate.show,
      required: newConfig.contract.startDate.required,
      label: newConfig.contract.startDate.label,
    }
    // Duplicate mapping for compatibility
    flatConfig.contractEffectiveDate = {
      show: newConfig.contract.startDate.show,
      required: newConfig.contract.startDate.required,
      label: newConfig.contract.startDate.label,
    }
  }

  if (newConfig.contract?.enabled && newConfig.contract.endDate?.show) {
    flatConfig.contractEndDate = {
      show: newConfig.contract.endDate.show,
      required: newConfig.contract.endDate.required,
      label: newConfig.contract.endDate.label,
    }
    // Duplicate mapping for compatibility
    flatConfig.contractExpiryDate = {
      show: newConfig.contract.endDate.show,
      required: newConfig.contract.endDate.required,
      label: newConfig.contract.endDate.label,
    }
  }

  if (newConfig.contract?.enabled && newConfig.contract.status?.show) {
    flatConfig.contractStatus = {
      show: newConfig.contract.status.show,
      required: newConfig.contract.status.required,
      label: newConfig.contract.status.label,
      options: newConfig.contract.status.options?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.contract?.enabled && newConfig.contract.note?.show) {
    flatConfig.contractNote = {
      show: newConfig.contract.note.show,
      required: newConfig.contract.note.required,
      label: newConfig.contract.note.label,
    }
  }

  // Card History
  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.cardNumber?.show) {
    flatConfig.cardNumber = {
      show: newConfig.cardHistory.cardNumber.show,
      required: newConfig.cardHistory.cardNumber.required,
      label: newConfig.cardHistory.cardNumber.label,
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.issuedAt?.show) {
    flatConfig.cardIssuedAt = {
      show: newConfig.cardHistory.issuedAt.show,
      required: newConfig.cardHistory.issuedAt.required,
      label: newConfig.cardHistory.issuedAt.label,
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.status?.show) {
    flatConfig.cardStatus = {
      show: newConfig.cardHistory.status.show,
      required: newConfig.cardHistory.status.required,
      label: newConfig.cardHistory.status.label,
      options: newConfig.cardHistory.status.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.isCurrent?.show) {
    flatConfig.cardIsCurrent = {
      show: newConfig.cardHistory.isCurrent.show,
      required: newConfig.cardHistory.isCurrent.required,
      label: newConfig.cardHistory.isCurrent.label,
      options: newConfig.cardHistory.isCurrent.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.fileRefFront?.show) {
    flatConfig.cardFileRefFront = {
      show: newConfig.cardHistory.fileRefFront.show,
      required: newConfig.cardHistory.fileRefFront.required,
      label: newConfig.cardHistory.fileRefFront.label,
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.fileRefBack?.show) {
    flatConfig.cardFileRefBack = {
      show: newConfig.cardHistory.fileRefBack.show,
      required: newConfig.cardHistory.fileRefBack.required,
      label: newConfig.cardHistory.fileRefBack.label,
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.note?.show) {
    flatConfig.cardNote = {
      show: newConfig.cardHistory.note.show,
      required: newConfig.cardHistory.note.required,
      label: newConfig.cardHistory.note.label,
    }
  }

  if (newConfig.cardHistory?.enabled && newConfig.cardHistory.lawyerLicenseIssuedAt?.show) {
    flatConfig.lawyerLicenseIssuedAt = {
      show: newConfig.cardHistory.lawyerLicenseIssuedAt.show,
      required: newConfig.cardHistory.lawyerLicenseIssuedAt.required,
      label: newConfig.cardHistory.lawyerLicenseIssuedAt.label,
    }
  }

  // Staff type
  if (newConfig.professionalInfo?.staffType?.show) {
    flatConfig.type = {
      show: newConfig.professionalInfo.staffType.show,
      required: newConfig.professionalInfo.staffType.required,
      label: newConfig.professionalInfo.staffType.label,
      options: newConfig.professionalInfo.staffType.fallbackOptions?.map(opt => ({
        value: String(opt.value),
        label: opt.label,
      })),
    }
  }

  return flatConfig
}
