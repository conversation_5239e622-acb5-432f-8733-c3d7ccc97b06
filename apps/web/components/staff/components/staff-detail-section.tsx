'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import React, { useState } from 'react'

import { useUpdateStaffField } from '../services/staff-update.service'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { EditableFieldConfig, StaffFieldUpdateDto } from '../types/staff-field-update.types'
import { FieldEditDialog } from './field-edit-dialog'
import { InlineEditButton } from './inline-edit-button'

interface StaffDetailSectionProps {
  title: string
  staff: StaffDetailResponse
  fieldConfigs: EditableFieldConfig[]
  className?: string
  mainButton?: React.ReactNode
}

export const StaffDetailSection: React.FC<StaffDetailSectionProps> = ({
  title,
  staff,
  fieldConfigs,
  className = '',
  mainButton,
}) => {
  const [editingField, setEditingField] = useState<{
    field: EditableFieldConfig
    currentValue: Any
  } | null>(null)

  const updateMutation = useUpdateStaffField()

  const handleEditField = (field: EditableFieldConfig, currentValue: Any) => {
    setEditingField({ field, currentValue })
  }

  const handleSaveField = async (value: Any) => {
    if (!editingField) return

    await updateMutation.mutateAsync({
      staffId: staff.id,
      fieldKey: editingField.field.key,
      fieldValue: value,
    })
  }

  const handleCloseDialog = () => {
    setEditingField(null)
  }

  const getFieldValue = (fieldKey: keyof StaffFieldUpdateDto) => {
    // Handle nested fields and display values
    switch (fieldKey) {
      case 'genderId':
        return staff.gender?.name || staff.genderId
      case 'ethnicId':
        return staff.ethnic || staff.ethnicId
      case 'provinceId':
        return staff.province || staff.provinceId
      case 'districtId':
        return staff.district || staff.districtId
      case 'wardId':
        return staff.ward || staff.wardId
      case 'professionalLevelId':
        return staff.professionalLevel?.name
      case 'activityStatusId':
        return staff.activityStatus?.name || staff.activityStatusId
      case 'organizationUnitId':
        return staff.organizationUnit?.name || staff.organizationUnitId
      default:
        return (staff as Any)[fieldKey]
    }
  }

  const getFieldDisplayValue = (fieldKey: keyof StaffFieldUpdateDto) => {
    const value = getFieldValue(fieldKey)

    if (!value) return 'Chưa cập nhật'

    // Special formatting for certain field types
    if (fieldKey === 'dateOfBirth' || fieldKey === 'cccdIssuanceDate') {
      return value ? new Date(value).toLocaleDateString('vi-VN') : 'Chưa cập nhật'
    }

    return value
  }

  const getFieldRawValue = (fieldKey: keyof StaffFieldUpdateDto) => {
    // Return the actual ID value for editing (not the display name)
    switch (fieldKey) {
      case 'genderId':
        return staff.genderId
      case 'ethnicId':
        return staff.ethnicId
      case 'provinceId':
        return staff.provinceId
      case 'districtId':
        return staff.districtId
      case 'wardId':
        return staff.wardId
      case 'professionalLevelId':
        return staff.professionalLevelId
      case 'activityStatusId':
        return staff.activityStatusId
      case 'organizationUnitId':
        return staff.organizationUnitId
      default:
        return (staff as Any)[fieldKey]
    }
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">{title}</CardTitle>
            {mainButton}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {fieldConfigs.map(fieldConfig => {
              const displayValue = getFieldDisplayValue(fieldConfig.key)
              const rawValue = getFieldRawValue(fieldConfig.key)
              const hasValue = rawValue && rawValue !== ''

              return (
                <div key={fieldConfig.key} className="group flex flex-col space-y-1">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">
                      {fieldConfig.label}
                      {fieldConfig.required && <span className="ml-1 text-red-500">*</span>}
                    </label>
                    <InlineEditButton
                      fieldKey={fieldConfig.key}
                      currentValue={rawValue}
                      fieldConfig={fieldConfig}
                      onEdit={handleEditField}
                      className="opacity-0 transition-opacity group-hover:opacity-100"
                      showOnHover={false}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    {hasValue ? (
                      <span className="text-sm text-gray-900">{displayValue}</span>
                    ) : (
                      <span className="text-sm text-gray-500 italic">Chưa cập nhật</span>
                    )}
                    {fieldConfig.type === 'api-select' && hasValue && (
                      <Badge variant="secondary" className="text-xs">
                        API
                      </Badge>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {editingField && (
        <FieldEditDialog
          isOpen={!!editingField}
          onClose={handleCloseDialog}
          field={editingField.field}
          currentValue={editingField.currentValue}
          onSave={handleSaveField}
          isLoading={updateMutation.isPending}
          staff={staff}
        />
      )}
    </>
  )
}
