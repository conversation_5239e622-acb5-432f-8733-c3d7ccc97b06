'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { cn } from '@workspace/ui/lib/utils'
import { CalendarIcon, Check, Edit2, X } from 'lucide-react'
import { useState } from 'react'

import { useUpdateContractEndDate } from '../services/staff-contract.service'

interface ContractEndDateEditorProps {
  contractId: string
  currentEndDate: Date | string | undefined
  staffId: string
}

export function ContractEndDateEditor({ contractId, currentEndDate, staffId }: ContractEndDateEditorProps) {
  const parsedCurrentEndDate = currentEndDate ? new Date(currentEndDate) : undefined
  const [isEditing, setIsEditing] = useState(false)
  const [tempDate, setTempDate] = useState<Date | undefined>(parsedCurrentEndDate)
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)

  const updateEndDateMutation = useUpdateContractEndDate(staffId)

  const handleEdit = () => {
    setIsEditing(true)
    setTempDate(parsedCurrentEndDate)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setTempDate(parsedCurrentEndDate)
    setIsCalendarOpen(false)
  }

  // Helper function to format date as YYYY-MM-DD
  const formatDateForAPI = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  }

  const handleSave = async () => {
    try {
      await updateEndDateMutation.mutateAsync({
        contractId,
        endDate: tempDate ? formatDateForAPI(tempDate) : undefined,
      })
      setIsEditing(false)
      setIsCalendarOpen(false)
    } catch (error) {
      console.error('Error updating contract end date:', error)
      // Error is handled by the mutation
    }
  }

  const handleRemoveEndDate = () => {
    setTempDate(undefined)
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn('min-w-[140px] justify-start text-left font-normal', !tempDate && 'text-muted-foreground')}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {tempDate ? tempDate.toLocaleDateString('vi-VN') : <span>Chọn ngày</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={tempDate}
              onSelect={date => {
                setTempDate(date)
                setIsCalendarOpen(false)
              }}
              initialFocus
            />
            <div className="border-t p-3">
              <Button variant="outline" size="sm" onClick={handleRemoveEndDate} className="w-full">
                Không thời hạn
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        <Button size="sm" onClick={handleSave} disabled={updateEndDateMutation.isPending} className="h-8 w-8 p-0">
          <Check className="h-4 w-4" />
        </Button>

        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          disabled={updateEndDateMutation.isPending}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="group flex items-center gap-2">
      <span className="text-sm">
        {parsedCurrentEndDate ? parsedCurrentEndDate.toLocaleDateString('vi-VN') : 'Không thời hạn'}
      </span>
      <Button
        size="sm"
        variant="ghost"
        onClick={handleEdit}
        className="h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
      >
        <Edit2 className="h-3 w-3" />
      </Button>
    </div>
  )
}
