'use client'

import { addStaffAction } from '@/services/staff-organization-relations'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { CategoryApiSelect } from '../form'

const addActionSchema = z.object({
  actionType: z.enum(['HINH_THUC_KHEN_THUONG', 'HINH_THUC_KY_LUAT'], {
    error: 'Vui lòng chọn loại hành động',
  }),
  rewardDisciplineId: z.string().min(1, 'Vui lòng chọn loại khen thưởng/kỷ luật'),
})

type AddActionFormData = z.infer<typeof addActionSchema>

interface AddActionDialogProps {
  isOpen: boolean
  onClose: () => void
  staffId: string
  onSuccess: () => void
  initialActionType?: 'HINH_THUC_KHEN_THUONG' | 'HINH_THUC_KY_LUAT'
}

export const AddActionDialog: React.FC<AddActionDialogProps> = ({
  isOpen,
  onClose,
  staffId,
  onSuccess,
  initialActionType,
}) => {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<AddActionFormData>({
    resolver: zodResolver(addActionSchema),
    defaultValues: {
      actionType: initialActionType,
      rewardDisciplineId: '',
    },
  })

  // Update form when initialActionType changes
  React.useEffect(() => {
    if (initialActionType) {
      form.setValue('actionType', initialActionType)
      // Reset the reward/discipline selection when action type changes
      form.setValue('rewardDisciplineId', '')
    }
  }, [initialActionType, form])

  const selectedActionType = form.watch('actionType')

  const onSubmit = async (data: AddActionFormData) => {
    setIsLoading(true)

    try {
      await addStaffAction({
        staffId,
        rewardDisciplineId: data.rewardDisciplineId,
      })

      onSuccess()
      onClose()
      form.reset()
    } catch (error) {
      console.error('Error adding action:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {selectedActionType === 'HINH_THUC_KHEN_THUONG'
              ? 'Thêm khen thưởng'
              : selectedActionType === 'HINH_THUC_KY_LUAT'
                ? 'Thêm kỷ luật'
                : 'Thêm khen thưởng/kỷ luật'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Action Type Selection */}
            <FormField
              control={form.control}
              name="actionType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Loại hành động <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={value => {
                      field.onChange(value)
                      // Reset the reward/discipline selection when type changes
                      form.setValue('rewardDisciplineId', '')
                    }}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn loại hành động..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HINH_THUC_KHEN_THUONG">Khen thưởng</SelectItem>
                      <SelectItem value="HINH_THUC_KY_LUAT">Kỷ luật</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reward/Discipline Category Selection */}
            {selectedActionType && (
              <CategoryApiSelect
                control={form.control}
                name="rewardDisciplineId"
                label={selectedActionType === 'HINH_THUC_KHEN_THUONG' ? 'Loại khen thưởng' : 'Hình thức kỷ luật'}
                placeholder={
                  selectedActionType === 'HINH_THUC_KHEN_THUONG'
                    ? 'Chọn loại khen thưởng...'
                    : 'Chọn hình thức kỷ luật...'
                }
                categoryType={
                  selectedActionType === 'HINH_THUC_KHEN_THUONG' ? 'HINH_THUC_KHEN_THUONG' : 'HINH_THUC_KY_LUAT'
                }
                required
                enableInfiniteScroll={true}
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? 'Đang lưu...'
                  : selectedActionType === 'HINH_THUC_KHEN_THUONG'
                    ? 'Thêm khen thưởng'
                    : selectedActionType === 'HINH_THUC_KY_LUAT'
                      ? 'Thêm kỷ luật'
                      : 'Thêm hành động'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
