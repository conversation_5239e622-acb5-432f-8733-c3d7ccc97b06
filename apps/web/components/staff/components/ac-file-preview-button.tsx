'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { renderAsync } from 'docx-preview'
import Image from 'next/image'
import { ComponentProps, useCallback, useEffect, useRef, useState } from 'react'

const STREAM_ENDPOINT = (fileId: string) => `/ac-apis/medias/files/${fileId}/stream`
const DOWNLOAD_ENDPOINT = (fileId: string) => `/ac-apis/medias/files/${fileId}/download-url`

const enum PreviewType {
  Video = 'video',
  Audio = 'audio',
  Pdf = 'pdf',
  Docx = 'docx',
  Image = 'image',
}

type PreviewState = {
  downloadUrl: string
  previewUrl: string
  fileName?: string
  contentType?: string
  type: PreviewType
  docxBlob?: Blob
}

type ButtonBaseProps = Omit<ComponentProps<typeof Button>, 'onClick'>

interface AcFilePreviewButtonProps extends ButtonBaseProps {
  fileId: string
  label?: string
}

const resolvePreviewType = (contentType?: string | null): PreviewType | null => {
  if (!contentType) return null

  if (contentType.startsWith('video/')) return PreviewType.Video

  if (contentType.startsWith('audio/')) return PreviewType.Audio

  if (contentType.startsWith('application/pdf')) return PreviewType.Pdf

  if (
    contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    contentType === 'application/msword' ||
    contentType === 'application/vnd.ms-word.document.macroEnabled.12'
  ) {
    return PreviewType.Docx
  }

  if (contentType.startsWith('image/')) return PreviewType.Image

  return null
}

const buildPreviewUrl = (type: PreviewType, fileId: string, downloadUrl: string) => {
  switch (type) {
    case PreviewType.Video:
    case PreviewType.Audio:
      return STREAM_ENDPOINT(fileId)
    case PreviewType.Pdf:
      return downloadUrl
    case PreviewType.Docx:
      return downloadUrl
    default:
      return downloadUrl
  }
}

const DOCX_PREVIEW_STYLES = `
  .docx-wrapper {
    min-height: 100%;
    background: #f9fafb;
    padding: 24px;
    width: fit-content;
    min-width: 100%;
  }

  .docx-wrapper > section {
    background: white;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    min-width: 210mm;
    min-height: 297mm;
    padding: 2.54cm;
    margin: 0 auto;
  }

  .docx {
    font-family: 'Calibri', 'Arial', sans-serif;
    font-size: 11pt;
    line-height: 1.5;
    color: #000;
  }

  .docx-wrapper table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
  }

  .docx-wrapper table td,
  .docx-wrapper table th {
    border: 1px solid #d1d5db;
    padding: 8px 12px;
    text-align: left;
  }

  .docx-wrapper table th {
    font-weight: 600;
  }

  .docx-wrapper p {
    margin: 0.5em 0;
  }

  .docx-wrapper h1,
  .docx-wrapper h2,
  .docx-wrapper h3 {
    margin: 1em 0 0.5em 0;
    font-weight: 600;
  }

  .docx-wrapper a {
    color: #2563eb;
    text-decoration: underline;
  }

  .docx-wrapper ul,
  .docx-wrapper ol {
    margin: 0.5em 0;
    padding-left: 2em;
  }
`

export const AcFilePreviewButton = ({
  fileId,
  label = 'Xem tài liệu',
  className,
  variant = 'link',
  ...buttonProps
}: AcFilePreviewButtonProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [preview, setPreview] = useState<PreviewState | null>(null)
  const docxContainerRef = useRef<HTMLDivElement | null>(null)
  const [docxContainer, setDocxContainer] = useState<HTMLDivElement | null>(null)

  const cleanupPreview = (state: PreviewState | null) => {
    if (state?.type === PreviewType.Docx && docxContainerRef.current) {
      docxContainerRef.current.innerHTML = ''
    }
  }
  const handleDocxContainerRef = useCallback((node: HTMLDivElement | null) => {
    docxContainerRef.current = node
    setDocxContainer(node)
  }, [])

  // Render DOCX when blob is available
  useEffect(() => {
    if (preview?.type !== PreviewType.Docx || !preview.docxBlob || !docxContainer) {
      return
    }

    docxContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; min-height: 200px;">
        <div style="text-align: center;">
          <div style="border: 3px solid #f3f4f6; border-top: 3px solid #3b82f6; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 12px;"></div>
          <p style="color: #6b7280; font-size: 14px;">Đang tải tài liệu...</p>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `

    const { docxBlob } = preview
    let isCancelled = false

    const render = async () => {
      try {
        if (!docxBlob) return

        await renderAsync(docxBlob, docxContainer, undefined, {
          className: 'docx-preview',
          inWrapper: true,
          ignoreWidth: false,
          ignoreHeight: false,
          ignoreFonts: false,
          breakPages: true,
          debug: false,
          experimental: false,
        })
      } catch (error) {
        console.error('[DOCX Preview] Render error:', error)

        if (!isCancelled) {
          docxContainer.innerHTML = `
            <div class="p-4 text-center">
              <p class="text-red-500 mb-2">Không thể hiển thị tài liệu.</p>
              <p class="text-sm text-gray-500">Lỗi: ${error instanceof Error ? error.message : 'Không xác định'}</p>
            </div>
          `
        }
      }
    }

    render()

    return () => {
      isCancelled = true
    }
  }, [docxContainer, preview])

  useEffect(() => {
    return () => {
      cleanupPreview(preview)
    }
  }, [preview])

  const handleOpen = async () => {
    if (isLoading) return

    setIsLoading(true)

    try {
      const response = await fetch(DOWNLOAD_ENDPOINT(fileId))

      if (!response.ok) {
        throw new Error('Không thể lấy liên kết tải tệp hợp đồng')
      }

      const payload: {
        url?: string
        fileName?: string
        contentType?: string
      } = await response.json()

      if (!payload?.url) {
        throw new Error('Không tìm thấy liên kết tải tệp hợp đồng')
      }

      const type = resolvePreviewType(payload.contentType)

      if (!type) {
        window.open(payload.url, '_blank', 'noopener')

        return
      }

      if (type === PreviewType.Docx) {
        // Use docx-preview for native rendering
        cleanupPreview(preview)

        try {
          // Fetch the file
          const fileResponse = await fetch(payload.url)

          if (!fileResponse.ok) {
            throw new Error('Failed to fetch DOCX file')
          }

          const blob = await fileResponse.blob()

          setPreview({
            downloadUrl: payload.url,
            previewUrl: payload.url,
            fileName: payload.fileName,
            contentType: payload.contentType,
            type,
            docxBlob: blob,
          })
        } catch (error) {
          console.error('DOCX fetch error:', error)
          toast.error('Không thể tải tệp DOCX. Vui lòng tải xuống để xem.')
          cleanupPreview(preview)
          setPreview(null)
        }

        return
      }

      const previewUrl = buildPreviewUrl(type, fileId, payload.url)

      cleanupPreview(preview)

      setPreview({
        downloadUrl: payload.url,
        previewUrl,
        fileName: payload.fileName,
        contentType: payload.contentType,
        type,
      })
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Không thể mở tệp hợp đồng')
    } finally {
      setIsLoading(false)
    }
  }

  const closePreview = () => {
    setPreview(null)
  }

  const renderPreviewContent = () => {
    if (!preview) return null

    switch (preview.type) {
      case PreviewType.Video:
        return (
          <video
            controls
            className="border-border h-[60vh] w-full rounded border bg-black"
            src={preview.previewUrl}
            preload="metadata"
          >
            Trình duyệt của bạn không hỗ trợ phát video.
          </video>
        )
      case PreviewType.Audio:
        return (
          <audio controls className="w-full" src={preview.previewUrl} preload="metadata">
            Trình duyệt của bạn không hỗ trợ phát âm thanh.
          </audio>
        )
      case PreviewType.Pdf:
        return (
          <div className="border-border h-[70vh] w-full overflow-hidden rounded border">
            <iframe
              src={preview.previewUrl}
              title={preview.fileName ?? 'Tài liệu hợp đồng'}
              className="h-full w-full"
            />
          </div>
        )
      case PreviewType.Docx:
        if (!preview.docxBlob) {
          return (
            <div className="flex flex-col items-center justify-center gap-4 py-8">
              <p className="text-muted-foreground text-sm">Không thể tải tài liệu.</p>
              <Button variant="default" onClick={() => window.open(preview.downloadUrl, '_blank', 'noopener')}>
                Tải xuống
              </Button>
            </div>
          )
        }

        return (
          <div className="h-full w-full overflow-auto rounded bg-gray-50">
            <style>{DOCX_PREVIEW_STYLES}</style>
            <div ref={handleDocxContainerRef} className="docx-wrapper" />
          </div>
        )
      case PreviewType.Image:
        return (
          <div className="relative h-[70vh] w-full overflow-hidden rounded border bg-gray-50">
            <Image
              src={preview.previewUrl}
              alt={preview.fileName ?? 'Ảnh hợp đồng'}
              fill
              sizes="(max-width: 1024px) 100vw, 70vh"
              className="object-contain"
              unoptimized
            />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <>
      <Button
        type="button"
        onClick={handleOpen}
        disabled={isLoading}
        variant={variant}
        className={cn('h-auto', variant === 'link' ? 'text-primary p-0' : '', className)}
        {...buttonProps}
      >
        {isLoading ? 'Đang mở...' : label}
      </Button>

      <Dialog open={!!preview} onOpenChange={isOpen => !isOpen && closePreview()}>
        <DialogContent className="flex max-h-[95vh] w-[95vw] !max-w-none flex-col">
          <DialogHeader>
            <DialogTitle>Xem tài liệu </DialogTitle>
          </DialogHeader>
          <DialogDescription>{preview?.fileName ?? 'Xem tài liệu hợp đồng'}</DialogDescription>

          {preview ? (
            <div className="flex min-h-0 flex-1 flex-col gap-4">
              <div className="min-h-0 flex-1 overflow-auto">{renderPreviewContent()}</div>

              <div className="flex shrink-0 justify-end gap-2">
                <Button variant="outline" onClick={() => window.open(preview.downloadUrl, '_blank', 'noopener')}>
                  Tải xuống
                </Button>
                <Button onClick={closePreview}>Đóng</Button>
              </div>
            </div>
          ) : null}
        </DialogContent>
      </Dialog>
    </>
  )
}
