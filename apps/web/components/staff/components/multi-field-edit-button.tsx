'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { ChevronDown, Edit3 } from 'lucide-react'
import React, { useState } from 'react'

import { FIELD_GROUPS } from '../configs/field-groups'
import { useUpdateStaffFields } from '../services/staff-update.service'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { MultiFieldEditDialog } from './multi-field-edit-dialog'

interface MultiFieldEditButtonProps {
  staff: StaffDetailResponse
  onUpdate?: () => void
}

export const LocationInfoMultiFieldEditButton: React.FC<MultiFieldEditButtonProps> = ({ staff }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { mutateAsync: updateStaffFields, isPending } = useUpdateStaffFields()
  const fieldGroup = FIELD_GROUPS.find(group => group.id === 'location-info')

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="h-8 border-blue-200 px-3 text-xs font-medium text-blue-700 hover:border-blue-300 hover:bg-blue-50"
        onClick={() => setIsDialogOpen(true)}
      >
        <Edit3 className="mr-1.5 h-3 w-3" />
        Chỉnh sửa nhiều trường
        <ChevronDown className="ml-1.5 h-3 w-3" />
      </Button>
      <MultiFieldEditDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        fields={fieldGroup?.fields || []}
        staff={staff}
        onSave={async data => {
          await updateStaffFields({ staffId: staff.id, data })
        }}
        isLoading={isPending}
        title={fieldGroup?.title || ''}
      />
    </>
  )
}
