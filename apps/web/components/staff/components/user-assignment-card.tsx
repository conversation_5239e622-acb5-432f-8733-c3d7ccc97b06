'use client'

import { Badge } from '@workspace/ui/components/badge'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { User, UserPlus, UserX } from 'lucide-react'

import { StaffDetailResponse } from '../types/staff-detail.types'
import UserSearchSelection from './user-search-selection'

interface UserAssignmentCardProps {
  staff: StaffDetailResponse
  onUserAssigned?: () => void
  onUserRemoved?: () => void
}

export const UserAssignmentCard = ({ staff, onUserAssigned, onUserRemoved }: UserAssignmentCardProps) => {
  const hasAssignedUser = !!staff.assignedUser
  const handleUserSelected = () => {
    onUserAssigned?.()
  }

  const handleRemoveUser = () => {
    onUserRemoved?.()
  }

  return (
    <Card className="w-full">
      {/* Header matching Figma design */}
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-indigo-600">
          <User className="h-5 w-5" />
          Thông tin tài khoản
        </CardTitle>
      </CardHeader>

      <CardContent className="px-3 pb-3">
        {hasAssignedUser && staff.assignedUser ? (
          /* Display assigned user */
          <div className="space-y-4">
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <h4 className="font-medium text-gray-900">{staff.assignedUser.username}</h4>
                    <Badge variant={staff.assignedUser.isActive ? 'default' : 'secondary'}>
                      {staff.assignedUser.isActive ? 'Hoạt động' : 'Tạm ngưng'}
                    </Badge>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    {staff.assignedUser.email && (
                      <p className="break-all">
                        <span className="font-medium">Email:</span> {staff.assignedUser.email}
                      </p>
                    )}
                    {staff.assignedUser.phone && (
                      <p>
                        <span className="font-medium">Điện thoại:</span> {staff.assignedUser.phone}
                      </p>
                    )}
                    <p>
                      <span className="font-medium">Ngày tạo:</span>{' '}
                      {new Date(staff.assignedUser.createdAt).toLocaleDateString('vi-VN')}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <UserSearchSelection
                    staffId={staff.id}
                    onUserSelected={handleUserSelected}
                    triggerButton={
                      <Button variant="outline" size="sm" className="flex items-center justify-center gap-2">
                        <UserPlus className="h-4 w-4" />
                        Thay đổi
                      </Button>
                    }
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRemoveUser}
                    className="flex items-center justify-center gap-2 text-red-600 hover:text-red-700"
                  >
                    <UserX className="h-4 w-4" />
                    Huỷ liên kết
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* No assigned user or selecting new user */
          <div className="space-y-4">
            {!hasAssignedUser && (
              <div className="justify-center bg-red-300 py-8 text-center">
                <User className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <p className="mb-4 text-gray-500">Chưa có tài khoản được gán cho nhân viên này</p>
                <UserSearchSelection
                  staffId={staff.id}
                  onUserSelected={handleUserSelected}
                  triggerButton={
                    <Button className="flex items-center gap-2">
                      <UserPlus className="h-4 w-4" />
                      Gán tài khoản
                    </Button>
                  }
                />
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
