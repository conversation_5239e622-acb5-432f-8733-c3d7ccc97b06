'use client'

import { STAFF_BUSINESS_ROLE_OPTIONS } from '@/constants/staff'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from '@workspace/ui/components/dialog'
import { Form } from '@workspace/ui/components/form'
import { toast } from '@workspace/ui/lib/toast'
import { Building2, Users } from 'lucide-react'
import React from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { FormSelect } from '../form-item/form-components'
import { StaffDetailResponse } from '../types/staff-detail.types'

// ========================================
// ORGANIZATION TYPE AND ROLE SELECTION DIALOG
// ========================================
//
// This dialog appears before the move organization dialog
// to let users choose:
// 1. Organization Type (Trung tâm = 3, Tổ chức = 6,7)
// 2. Staff Business Roles (multiple selection)
//
// These values are then passed to MoveToOrganizationDialog
//
// ========================================

// Form Schema
const organizationTypeRoleSchema = z.object({
  organizationType: z.string().min(1, 'Vui lòng chọn loại tổ chức'),
  businessRoles: z.string().min(1, 'Vui lòng chọn vai trò nghiệp vụ'),
})

type OrganizationTypeRoleFormValues = z.infer<typeof organizationTypeRoleSchema>

// Organization Type Options
const ORGANIZATION_TYPE_OPTIONS = [
  { label: 'Trung tâm', value: '3' },
  { label: 'Tổ chức', value: '6,7' },
]

interface OrganizationTypeRoleSelectionDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (data: { organizationType: string; businessRoles: string[] }) => void
  staff: StaffDetailResponse
}

export function OrganizationTypeRoleSelectionDialog({
  isOpen,
  onClose,
  onConfirm,
  staff,
}: OrganizationTypeRoleSelectionDialogProps) {
  // No hydration detection needed for FormSelect

  // Initialize form
  const form = useForm<OrganizationTypeRoleFormValues>({
    resolver: zodResolver(organizationTypeRoleSchema),
    defaultValues: {
      organizationType: '',
      businessRoles: '',
    },
  })

  // Handle form submission
  const onSubmit = (values: OrganizationTypeRoleFormValues) => {
    try {
      onConfirm({
        organizationType: values.organizationType,
        businessRoles: [values.businessRoles], // Convert single value to array
      })
      form.reset()
    } catch (error) {
      console.error('Error confirming organization type and role:', error)
      toast.error('Có lỗi xảy ra khi xử lý dữ liệu')
    }
  }

  // Handle dialog close
  const handleClose = () => {
    form.reset()
    onClose()
  }

  // Handle business roles change - no longer needed for single select
  // const handleBusinessRolesChange = (values: string[]) => {
  //   form.setValue('businessRoles', values)
  // }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Chọn loại tổ chức và vai trò
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              {/* Organization Type Selection */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Building2 className="h-4 w-4" />
                  Loại tổ chức đích
                </div>
                <FormSelect
                  control={form.control}
                  name="organizationType"
                  label="Chọn loại tổ chức"
                  placeholder="Chọn loại tổ chức..."
                  options={ORGANIZATION_TYPE_OPTIONS}
                  required
                />
                <p className="text-xs text-gray-500">
                  • Trung tâm: Trung tâm Trợ giúp pháp lý
                  <br />• Tổ chức: Tổ chức hành nghề luật sư, phòng ban, chi nhánh
                </p>
              </div>

              {/* Staff Business Roles Selection */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Users className="h-4 w-4" />
                  Vai trò nghiệp vụ
                </div>
                <FormSelect
                  control={form.control}
                  name="businessRoles"
                  label="Chọn vai trò nghiệp vụ"
                  placeholder="Chọn vai trò nghiệp vụ..."
                  options={STAFF_BUSINESS_ROLE_OPTIONS}
                  required
                />

                <p className="text-xs text-gray-500">Chọn vai trò chính của nhân viên trong đơn vị mới.</p>
              </div>

              {/* Staff Info Display */}
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 text-sm font-medium text-gray-700">Thông tin nhân viên</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">Họ tên:</span> {staff.fullName}
                  </div>
                  <div>
                    <span className="font-medium">ID nhân viên:</span> {staff.id}
                  </div>
                  {staff.organizationUnit && (
                    <div>
                      <span className="font-medium">Đơn vị hiện tại:</span> {staff.organizationUnit.name}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Hủy
              </Button>
              <Button type="submit" disabled={!form.formState.isValid}>
                Tiếp tục chuyển đơn vị
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
