'use client'

import { addStaffSpecializations } from '@/services/staff-organization-relations'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { CategoryApiMultiSelect } from '../form'

const addSpecializationSchema = z.object({
  specializations: z.array(z.string()).min(1, 'Vui lòng chọn ít nhất một chuyên môn'),
  level: z.string().optional(),
  certificateNo: z.string().optional(),
  certIssuedAt: z.string().optional(),
  certExpiresAt: z.string().optional(),
  note: z.string().optional(),
})

type AddSpecializationFormData = z.infer<typeof addSpecializationSchema>

interface AddSpecializationDialogProps {
  isOpen: boolean
  onClose: () => void
  staffId: string
  onSuccess: () => void
}

export const AddSpecializationDialog: React.FC<AddSpecializationDialogProps> = ({
  isOpen,
  onClose,
  staffId,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<AddSpecializationFormData>({
    resolver: zodResolver(addSpecializationSchema),
    defaultValues: {
      specializations: [],
      level: '',
      certificateNo: '',
      certIssuedAt: '',
      certExpiresAt: '',
      note: '',
    },
  })

  const onSubmit = async (data: AddSpecializationFormData) => {
    setIsLoading(true)

    try {
      // Transform the data to match API expectations
      const specializationsToAdd = data.specializations.map(categoryId => ({
        categoryId,
        level: data.level || undefined,
        certificateNo: data.certificateNo || undefined,
        certIssuedAt: data.certIssuedAt || undefined,
        certExpiresAt: data.certExpiresAt || undefined,
        note: data.note || undefined,
      }))

      await addStaffSpecializations({
        staffId,
        specializations: specializationsToAdd,
      })

      onSuccess()
      onClose()
      form.reset()
    } catch (error) {
      console.error('Error adding specializations:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Thêm chuyên môn cho nhân viên</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Specialization Selection */}
            <CategoryApiMultiSelect
              control={form.control}
              name="specializations"
              label="Chuyên môn"
              placeholder="Chọn chuyên môn..."
              categoryType="LINH_VUC_TGPL"
              required
              enableInfiniteScroll={true}
              showOptionCount={true}
            />

            {/* Additional Fields */}
            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cấp độ chuyên môn</FormLabel>
                  <Input {...field} placeholder="VD: Junior, Mid, Senior, Expert..." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="certificateNo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số chứng chỉ</FormLabel>
                  <Input {...field} placeholder="Nhập số chứng chỉ chuyên môn..." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="certIssuedAt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày cấp chứng chỉ</FormLabel>
                    <Input {...field} type="date" />
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="certExpiresAt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày hết hạn</FormLabel>
                    <Input {...field} type="date" />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ghi chú</FormLabel>
                  <Input {...field} placeholder="Ghi chú thêm về chuyên môn..." />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Đang lưu...' : 'Thêm chuyên môn'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
