'use client'

import { Any } from '@/lib/types'
import { But<PERSON> } from '@workspace/ui/components/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { Edit2 } from 'lucide-react'
import React from 'react'
import { useForm } from 'react-hook-form'

import { CategoryApiSelect } from '../form'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { EditableFieldConfig } from '../types/staff-field-update.types'

interface FormData {
  value: Any
}

interface FieldEditDialogProps {
  isOpen: boolean
  onClose: () => void
  field: EditableFieldConfig
  currentValue: Any
  onSave: (value: Any) => Promise<void>
  isLoading: boolean
  staff?: StaffDetailResponse // Add staff context for dependency resolution
}

export const FieldEditDialog: React.FC<FieldEditDialogProps> = ({
  isOpen,
  onClose,
  field,
  currentValue,
  onSave,
  isLoading,
  staff,
}) => {
  const form = useForm<FormData>({
    defaultValues: {
      value: currentValue || '',
    },
  })

  const handleSubmit = async (data: FormData) => {
    try {
      await onSave(data.value)
      onClose()
    } catch (error) {
      console.error('Error saving field:', error)
    }
  }

  // Get dynamic filters for API-based fields with dependencies
  const getDynamicFilters = () => {
    if (field.getDynamicFilters && staff) {
      return field.getDynamicFilters(staff)
    }

    return {}
  }

  // Check if field has unmet dependencies
  const hasUnmetDependencies = () => {
    if (!field.dependsOn || !staff) return false

    const dependentValue = (staff as Any)[field.dependsOn]

    return !dependentValue || dependentValue === ''
  }

  const renderField = () => {
    const validation = field.validation || {}

    // Check for unmet dependencies for API select fields
    if (field.type === 'api-select' && hasUnmetDependencies()) {
      return (
        <div className="space-y-2">
          <div className="rounded-md border border-amber-200 bg-amber-50 p-3">
            <p className="text-sm text-amber-700">
              Vui lòng chọn <strong>{getFieldLabel(field.dependsOn!)}</strong> trước khi chỉnh sửa trường này.
            </p>
          </div>
          <Input disabled placeholder={`Chọn ${getFieldLabel(field.dependsOn!)} trước`} className="bg-gray-50" />
        </div>
      )
    }

    switch (field.type) {
      case 'api-select':
        return (
          <CategoryApiSelect
            control={form.control}
            name="value"
            label=""
            categoryType={field.categoryType!}
            fallbackOptions={Array.from(field.fallbackOptions || [])}
            placeholder={`Chọn ${field.label.toLowerCase()}`}
            additionalFilters={getDynamicFilters()}
            className="w-full"
          />
        )

      case 'select':
        return (
          <Select value={form.watch('value')} onValueChange={value => form.setValue('value', value)}>
            <SelectTrigger>
              <SelectValue placeholder={`Chọn ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map(option => (
                <SelectItem key={option.value} value={`${option.value}`}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'textarea':
        return (
          <Textarea
            value={form.watch('value') || ''}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => form.setValue('value', e.target.value)}
            placeholder={`Nhập ${field.label.toLowerCase()}`}
            maxLength={validation?.maxLength}
            rows={4}
          />
        )

      case 'date': {
        const dateValue = form.watch('value')
        const formattedValue = dateValue instanceof Date ? dateValue.toISOString().split('T')[0] : dateValue || ''

        return (
          <Input
            type="date"
            value={formattedValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              form.setValue('value', e.target.value ? new Date(e.target.value) : null)
            }
          />
        )
      }

      case 'number':
        return (
          <Input
            type="number"
            value={form.watch('value') || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              form.setValue('value', e.target.value ? Number(e.target.value) : null)
            }
            placeholder={`Nhập ${field.label.toLowerCase()}`}
            min={validation?.min}
            max={validation?.max}
          />
        )

      case 'email':
        return (
          <Input
            type="email"
            value={form.watch('value') || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => form.setValue('value', e.target.value)}
            placeholder={`Nhập ${field.label.toLowerCase()}`}
          />
        )

      case 'phone':
        return (
          <Input
            type="tel"
            value={form.watch('value') || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => form.setValue('value', e.target.value)}
            placeholder={`Nhập ${field.label.toLowerCase()}`}
            pattern={validation?.pattern?.source}
          />
        )

      default: // text
        return (
          <Input
            type="text"
            value={form.watch('value') || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => form.setValue('value', e.target.value)}
            placeholder={`Nhập ${field.label.toLowerCase()}`}
            maxLength={validation?.maxLength}
            minLength={validation?.minLength}
          />
        )
    }
  }

  // Helper function to get field label by key
  const getFieldLabel = (fieldKey: string): string => {
    const fieldLabels: Record<string, string> = {
      provinceId: 'Tỉnh/Thành phố',
      districtId: 'Quận/Huyện',
      wardId: 'Phường/Xã',
      genderId: 'Giới tính',
      ethnicId: 'Dân tộc',
      professionalLevelId: 'Trình độ chuyên môn',
      activityStatusId: 'Trạng thái hoạt động',
    }

    return fieldLabels[fieldKey] || fieldKey
  }

  // Check if save should be disabled
  const isSaveDisabled = () => {
    return isLoading || (field.type === 'api-select' && hasUnmetDependencies())
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit2 className="h-4 w-4" />
            Chỉnh sửa {field.label}
          </DialogTitle>
          <DialogDescription>
            Cập nhật thông tin {field.label.toLowerCase()} của nhân viên
            {field.dependsOn && hasUnmetDependencies() && (
              <span className="mt-1 block text-sm text-amber-600">
                Trường này phụ thuộc vào {getFieldLabel(field.dependsOn)}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="value"
              rules={{
                required: field.required ? `${field.label} là bắt buộc` : false,
                pattern: field.validation?.pattern
                  ? {
                      value: field.validation.pattern,
                      message: `${field.label} không đúng định dạng`,
                    }
                  : undefined,
                minLength: field.validation?.minLength
                  ? {
                      value: field.validation.minLength,
                      message: `${field.label} phải có ít nhất ${field.validation.minLength} ký tự`,
                    }
                  : undefined,
                maxLength: field.validation?.maxLength
                  ? {
                      value: field.validation.maxLength,
                      message: `${field.label} không được quá ${field.validation.maxLength} ký tự`,
                    }
                  : undefined,
                min: field.validation?.min
                  ? {
                      value: field.validation.min,
                      message: `${field.label} phải lớn hơn hoặc bằng ${field.validation.min}`,
                    }
                  : undefined,
                max: field.validation?.max
                  ? {
                      value: field.validation.max,
                      message: `${field.label} phải nhỏ hơn hoặc bằng ${field.validation.max}`,
                    }
                  : undefined,
              }}
              render={() => (
                <FormItem>
                  <FormLabel>
                    {field.label}
                    {field.required && <span className="ml-1 text-red-500">*</span>}
                  </FormLabel>
                  <FormControl>{renderField()}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Hủy
              </Button>
              <Button type="submit" disabled={isSaveDisabled()} className="min-w-[80px]">
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Đang lưu...
                  </div>
                ) : (
                  'Lưu'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
