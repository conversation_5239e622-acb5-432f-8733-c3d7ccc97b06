import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/mi'
import { GraduationCap } from 'lucide-react'

import { StaffDetailResponse } from '../types/staff-detail.types'

export const TrainingHistoryTab = ({ staff }: { staff: StaffDetailResponse }) => {
  if (!staff.trainingHistory || staff.trainingHistory.length === 0) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <GraduationCap className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <p className="text-gray-500">Chưa có lịch sử đào tạo</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {staff.trainingHistory.map(training => (
        <Card key={training.id}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              {training.trainingClass?.courseName || 'Lớp đào tạo'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium text-gray-600">Ngày đăng ký</label>
                <p className="text-sm">
                  {training.registrationDate
                    ? new Date(training.registrationDate).toLocaleDateString('vi-VN')
                    : 'Chưa có'}
                </p>
              </div>
              {training.trainingClass?.startDate && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Ngày bắt đầu</label>
                  <p className="text-sm">{new Date(training.trainingClass.startDate).toLocaleDateString('vi-VN')}</p>
                </div>
              )}
              {training.trainingClass?.endDate && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Ngày kết thúc</label>
                  <p className="text-sm">{new Date(training.trainingClass.endDate).toLocaleDateString('vi-VN')}</p>
                </div>
              )}
              {training.trainingClass?.description && (
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-600">Mô tả</label>
                  <p className="text-sm">{training.trainingClass.description}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
