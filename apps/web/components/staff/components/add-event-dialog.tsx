'use client'

import { addStaffEvent } from '@/services/staff-organization-relations'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const addEventSchema = z.object({
  eventType: z.enum(['APPOINTMENT', 'REAPPOINTMENT', 'DISMISSAL', 'TRANSFER'], {
    error: '<PERSON>ui lòng chọn loại sự kiện',
  }),
  eventDate: z.string().min(1, '<PERSON>ui lòng chọn ngày sự kiện'),
  termYears: z.string().optional(),
  decisionNumber: z.string().optional(),
  decisionDate: z.string().optional(),
  decisionAuthority: z.string().optional(),
  reason: z.string().optional(),
  note: z.string().optional(),
})

type AddEventFormData = z.infer<typeof addEventSchema>

interface AddEventDialogProps {
  isOpen: boolean
  onClose: () => void
  staffId: string
  onSuccess: () => void
}

const EVENT_TYPE_OPTIONS = [
  { value: 'APPOINTMENT', label: 'Bổ nhiệm' },
  { value: 'REAPPOINTMENT', label: 'Bổ nhiệm lại' },
  { value: 'DISMISSAL', label: 'Miễn nhiệm' },
  { value: 'TRANSFER', label: 'Chuyển công tác' },
]

export const AddEventDialog: React.FC<AddEventDialogProps> = ({ isOpen, onClose, staffId, onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<AddEventFormData>({
    resolver: zodResolver(addEventSchema),
    defaultValues: {
      eventType: undefined,
      eventDate: '',
      termYears: '',
      decisionNumber: '',
      decisionDate: '',
      decisionAuthority: '',
      reason: '',
      note: '',
    },
  })

  const selectedEventType = form.watch('eventType')

  const onSubmit = async (data: AddEventFormData) => {
    setIsLoading(true)

    try {
      await addStaffEvent({
        staffId,
        eventType: data.eventType,
        eventDate: data.eventDate,
        termYears: data.termYears ? parseInt(data.termYears) : undefined,
        decisionNumber: data.decisionNumber || undefined,
        decisionDate: data.decisionDate || undefined,
        decisionAuthority: data.decisionAuthority || undefined,
        reason: data.reason || undefined,
        note: data.note || undefined,
      })

      onSuccess()
      onClose()
      form.reset()
    } catch (error) {
      console.error('Error adding event:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Thêm sự kiện tổ chức</DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Event Type */}
            <FormField
              control={form.control}
              name="eventType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Loại sự kiện <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn loại sự kiện..." />
                    </SelectTrigger>
                    <SelectContent>
                      {EVENT_TYPE_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Event Date and Term Years */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="eventDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Ngày sự kiện <span className="text-red-500">*</span>
                    </FormLabel>
                    <Input {...field} type="date" />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {(selectedEventType === 'APPOINTMENT' || selectedEventType === 'REAPPOINTMENT') && (
                <FormField
                  control={form.control}
                  name="termYears"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nhiệm kỳ (năm)</FormLabel>
                      <Input {...field} type="number" placeholder="Số năm..." />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Decision Information */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="decisionNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số quyết định</FormLabel>
                    <Input {...field} placeholder="VD: QD-001/2024..." />
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="decisionDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày quyết định</FormLabel>
                    <Input {...field} type="date" />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="decisionAuthority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cơ quan quyết định</FormLabel>
                  <Input {...field} placeholder="VD: UBND Tỉnh Hà Nội..." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Lý do</FormLabel>
                  <Input {...field} placeholder="Lý do thay đổi..." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ghi chú</FormLabel>
                  <Textarea {...field} placeholder="Ghi chú thêm..." rows={3} />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Đang lưu...' : 'Thêm sự kiện'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
