'use client'

import { STAFF_BUSINESS_ROLE_OPTIONS } from '@/constants/staff'
import { StaffBusinessRole } from '@ac/data-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { DialogFooter } from '@workspace/ui/components/dialog'
import { Form } from '@workspace/ui/components/form'
import { toast } from '@workspace/ui/lib/toast'
import { Any } from '@workspace/ui/types'
import { Loader2 } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import type { BaseFieldConfig, StaffOrganizationHistoryConfig } from '../form-item/staffTypeConfigObject'
// Direct imports to avoid circular dependencies during SSR
import { BASIC_STAFF_CONFIG } from '../form-item/staffTypeConfigObject/base-staff-type.configs'
import { TC_LAWYER_CONFIG } from '../form-item/staffTypeConfigObject/tc-lawer.config'
import { TC_ADVISOR_CONFIG } from '../form-item/staffTypeConfigObject/tc-legal-consultant.config'
import { TT_COLLABORATOR_CONFIG } from '../form-item/staffTypeConfigObject/tt-collaborator.config'
import { TT_DRIVER_WORKER_CONFIG } from '../form-item/staffTypeConfigObject/tt-driver-worker.config'
import { TT_INTERN_CONFIG } from '../form-item/staffTypeConfigObject/tt-intern.config'
import { TT_LAWYER_CONTRACT_CONFIG } from '../form-item/staffTypeConfigObject/tt-lawyer-contract.config'
import { TT_OFFICE_STAFF_CONFIG } from '../form-item/staffTypeConfigObject/tt-office-staff.config'
import { TT_TGPL_OFFICER_CONFIG } from '../form-item/staffTypeConfigObject/tt-tgpl_officer.config'
import { renderField } from '../form/utils/field-renderer'
import {
  useMoveToOrganizationMutation,
  useOrganizationUnitsByTypeQuery,
  useOrganizationUnitsQuery,
} from '../services/organization-move.service'
import { ChangeOrganizationUnitRequest, StaffDetailResponse } from '../types'

// ========================================
// MOVE ORGANIZATION FORM HELPERS
// ========================================

interface MoveOrganizationHistoryConfig extends StaffOrganizationHistoryConfig {
  organizationUnitId: BaseFieldConfig
}

interface MoveOrganizationFormConfiguration {
  organizationHistory: MoveOrganizationHistoryConfig
}

function getTodayDate(): string {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

function getMoveConfig(businessRole: string): MoveOrganizationFormConfiguration {
  try {
    switch (businessRole as StaffBusinessRole) {
      case StaffBusinessRole.TGPL_OFFICER:
        return {
          organizationHistory: {
            ...TT_TGPL_OFFICER_CONFIG.organizationHistory,
            organizationUnitId: TT_TGPL_OFFICER_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.TGPL_COLLABORATOR:
        return {
          organizationHistory: {
            ...TT_COLLABORATOR_CONFIG.organizationHistory,
            organizationUnitId: TT_COLLABORATOR_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.TGPL_INTERN:
        return {
          organizationHistory: {
            ...TT_INTERN_CONFIG.organizationHistory,
            organizationUnitId: TT_INTERN_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.LAWYER_CONTRACT:
        return {
          organizationHistory: {
            ...TT_LAWYER_CONTRACT_CONFIG.organizationHistory,
            organizationUnitId: TT_LAWYER_CONTRACT_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.LAWYER_ORG:
        return {
          organizationHistory: {
            ...TC_LAWYER_CONFIG.organizationHistory,
            organizationUnitId: TC_LAWYER_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.LEGAL_ADVISOR:
        return {
          organizationHistory: {
            ...TC_ADVISOR_CONFIG.organizationHistory,
            organizationUnitId: TC_ADVISOR_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.ACCOUNTING_AND_OFFICE:
        return {
          organizationHistory: {
            ...TT_OFFICE_STAFF_CONFIG.organizationHistory,
            organizationUnitId: TT_OFFICE_STAFF_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      case StaffBusinessRole.DRIVER_AND_WORKER:
        return {
          organizationHistory: {
            ...TT_DRIVER_WORKER_CONFIG.organizationHistory,
            organizationUnitId: TT_DRIVER_WORKER_CONFIG.professionalInfo.organizationUnitId,
          },
        }
      default:
        if (!BASIC_STAFF_CONFIG) {
          console.error('❌ BASIC_STAFF_CONFIG is undefined!')
          throw new Error('Configuration not loaded')
        }

        return {
          organizationHistory: {
            ...BASIC_STAFF_CONFIG.organizationHistory,
            organizationUnitId: BASIC_STAFF_CONFIG.professionalInfo.organizationUnitId,
          },
        }
    }
  } catch (error) {
    console.error('❌ Error in getMoveConfig:', error, 'for role:', businessRole)
    throw error
  }
}

function createMoveFormSchema(config: MoveOrganizationFormConfiguration) {
  const schemaFields: Record<string, z.ZodType<Any>> = {}

  Object.entries(config.organizationHistory).forEach(([fieldName, fieldConfig]) => {
    if (fieldName === 'enabled') return

    if (fieldConfig && typeof fieldConfig === 'object' && 'show' in fieldConfig && fieldConfig.show) {
      let fieldSchema: z.ZodType<Any>

      if (fieldConfig.validation) {
        fieldSchema = fieldConfig.validation
      } else if (fieldConfig.fieldType === 'multiselect' || fieldConfig.fieldType === 'api-multiselect') {
        fieldSchema = fieldConfig.required
          ? z.array(z.string()).min(1, `${fieldConfig.label || fieldName} là bắt buộc`)
          : z.array(z.string()).optional()
      } else if (fieldConfig.fieldType === 'number') {
        fieldSchema = fieldConfig.required
          ? z.number({ error: `${fieldConfig.label || fieldName} là bắt buộc` })
          : z.number().optional()
      } else if (fieldConfig.fieldType === 'switch') {
        fieldSchema = z.number().min(0).max(1).optional()
      } else if (fieldConfig.required) {
        fieldSchema = z.string().min(1, `${fieldConfig.label || fieldName} là bắt buộc`)
      } else {
        fieldSchema = z.string().optional()
      }

      schemaFields[fieldName] = fieldSchema
    }
  })

  return z.object(schemaFields)
}

const BASE_DEFAULT_VALUES = {
  startDate: '',
  status: 1,
  isHead: 0,
  isProbation: 0,
  organizationUnitId: '',
  positionId: '',
  levelId: '',
  decisionNumber: '',
  decisionDate: '',
  decisionAuthority: '',
  reason: '',
  note: '',
}

interface MoveToOrganizationFormProps {
  staff: StaffDetailResponse
  organizationType?: string
  businessRoles?: string[]
  onCancel: () => void
  onSuccess: () => void
}

export function MoveToOrganizationForm({
  staff,
  organizationType,
  businessRoles = [],
  onCancel,
  onSuccess,
}: MoveToOrganizationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isHydrated, setIsHydrated] = useState(false)

  // Early return before any hooks that might access undefined configs
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  const effectiveBusinessRole = businessRoles[0] || 'TT_STAFF'
  const config = useMemo(() => getMoveConfig(effectiveBusinessRole), [effectiveBusinessRole])
  const formSchema = useMemo(() => createMoveFormSchema(config), [config])
  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: BASE_DEFAULT_VALUES as Partial<FormValues>,
  })

  const shouldUseFilteredQuery = organizationType !== undefined && organizationType !== null
  const organizationTypeAsNumber = organizationType
    ? organizationType === '3'
      ? 3
      : organizationType === '6,7'
        ? 6
        : 3
    : 3

  const { data: organizationUnitsData, isLoading: loadingOrgsAll } = useOrganizationUnitsQuery({
    enabled: isHydrated && !shouldUseFilteredQuery,
  })
  const { data: filteredOrganizationUnitsData, isLoading: loadingOrgsFiltered } = useOrganizationUnitsByTypeQuery(
    organizationTypeAsNumber,
    isHydrated && shouldUseFilteredQuery
  )

  const organizationUnits = shouldUseFilteredQuery ? filteredOrganizationUnitsData || [] : organizationUnitsData || []
  const loadingOrgs = shouldUseFilteredQuery ? loadingOrgsFiltered : loadingOrgsAll

  const moveToOrganizationMutation = useMoveToOrganizationMutation(staff.id)
  const isProbationEnabled = Boolean(form.watch('isProbation'))

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  useEffect(() => {
    if (isHydrated && !form.getValues('startDate')) {
      form.setValue('startDate', getTodayDate())
    }
  }, [form, isHydrated])

  // Don't render anything until hydrated to avoid hydration mismatch
  if (!isHydrated) {
    return (
      <>
        <div className="flex flex-1 items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
        <DialogFooter className="shrink-0 border-t px-6 py-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="button" disabled variant="secondary">
            Đang tải...
          </Button>
        </DialogFooter>
      </>
    )
  }

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      const payload: ChangeOrganizationUnitRequest = {
        staffId: staff.id,
        newOrganizationUnitId: values.organizationUnitId!,
        positionId: values.positionId,
        levelId: values.levelId,
        legalAidFormId: values.legalAidFormId,
        isHead: values.isHead,
        startDate: values.startDate!,
        isProbation: values.isProbation,
        probationStartDate: values.probationStartDate,
        probationDurationMonths: values.probationDurationMonths,
        decisionNumber: values.decisionNumber,
        decisionDate: values.decisionDate,
        decisionAuthority: values.decisionAuthority,
        reason: values.reason,
        note: values.note,
        businessRoles: values.roles,
      }

      await moveToOrganizationMutation.mutateAsync(payload)
      toast.success('Đã chuyển đơn vị thành công')
      onSuccess()
    } catch (error) {
      console.error('❌ Submit error:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <div className="flex-1 overflow-y-auto">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 p-6">
            <div className="rounded-lg border bg-gray-50 p-4">
              <h4 className="mb-3 font-medium">Thông tin nhân viên hiện tại</h4>
              <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                <div>
                  <span className="font-medium">Họ và tên:</span> {staff.fullName}
                </div>
                <div>
                  <span className="font-medium">Đơn vị hiện tại:</span> {staff.organizationUnit?.name || 'Chưa có'}
                </div>
                <div>
                  <span className="font-medium">Chức danh:</span> {staff.organizationUnit?.position?.name || 'Chưa có'}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {staff.email || 'Chưa có'}
                </div>
              </div>
            </div>

            {(organizationType || businessRoles.length > 0) && (
              <div className="rounded-lg border bg-blue-50 p-4">
                <h4 className="mb-3 font-medium text-blue-800">Loại tổ chức và vai trò đã chọn</h4>
                <div className="space-y-2 text-sm">
                  {organizationType && (
                    <div>
                      <span className="font-medium text-blue-700">Loại tổ chức:</span>{' '}
                      <span className="text-blue-800">{organizationType === '3' ? 'Trung tâm' : 'Tổ chức'}</span>
                    </div>
                  )}
                  {businessRoles.length > 0 && (
                    <div>
                      <span className="font-medium text-blue-700">Vai trò nghiệp vụ:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {businessRoles.map(role => {
                          const roleOption = STAFF_BUSINESS_ROLE_OPTIONS.find(opt => opt.value === role)

                          return (
                            <span key={role} className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                              {roleOption?.label || role}
                            </span>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-6">
              <h4 className="text-lg font-medium">Chi tiết chuyển đơn vị</h4>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {Object.entries(config.organizationHistory).map(([fieldName, fieldConfig]) => {
                  if (fieldName === 'enabled' || !fieldConfig || typeof fieldConfig !== 'object' || !fieldConfig.show) {
                    return null
                  }

                  if (
                    (fieldName === 'probationStartDate' || fieldName === 'probationDurationMonths') &&
                    !isProbationEnabled
                  ) {
                    return null
                  }

                  return (
                    <div key={fieldName}>
                      {renderField({
                        fieldName,
                        fieldConfig,
                        control: form.control,
                        organizations: organizationUnits,
                        form,
                        isHydrated,
                      })}
                    </div>
                  )
                })}
              </div>
            </div>
          </form>
        </Form>
      </div>
      <DialogFooter className="shrink-0 border-t px-6 py-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Hủy
        </Button>
        <Button type="submit" disabled={isSubmitting || loadingOrgs} onClick={form.handleSubmit(handleSubmit)}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Đang xử lý...
            </>
          ) : (
            'Chuyển đơn vị'
          )}
        </Button>
      </DialogFooter>
    </>
  )
}
