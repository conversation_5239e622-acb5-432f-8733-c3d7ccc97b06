'use client'

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import React from 'react'

import { StaffDetailResponse } from '../types'
import { MoveToOrganizationForm } from './move-to-organization-form'

interface MoveToOrganizationDialogProps {
  isOpen: boolean
  onClose: () => void
  staff: StaffDetailResponse
  organizationType?: string
  businessRoles?: string[]
}

export const MoveToOrganizationDialog: React.FC<MoveToOrganizationDialogProps> = ({
  isOpen,
  onClose,
  staff,
  organizationType,
  businessRoles,
}) => {
  return (
    <Dialog
      open={isOpen}
      onOpenChange={open => {
        if (!open) {
          onClose()
        }
      }}
    >
      <DialogContent className="flex h-[85vh] flex-col gap-0 p-0 sm:max-w-[800px]">
        <DialogHeader className="shrink-0 border-b px-6 py-4">
          <DialogTitle>Chuyển đơn vị tổ chức</DialogTitle>
          <DialogDescription>Chuyển nhân viên {staff.fullName} đến đơn vị mới</DialogDescription>
        </DialogHeader>
        <MoveToOrganizationForm
          staff={staff}
          organizationType={organizationType}
          businessRoles={businessRoles ?? []}
          onCancel={onClose}
          onSuccess={onClose}
        />
      </DialogContent>
    </Dialog>
  )
}
