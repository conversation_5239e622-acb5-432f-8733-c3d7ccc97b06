'use client'

import { Any } from '@/lib/types'
import { Button } from '@workspace/ui/components/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip'
import { cn } from '@workspace/ui/lib/utils'
import { Edit2 } from 'lucide-react'
import React from 'react'

import { EditableFieldConfig } from '../types/staff-field-update.types'

interface InlineEditButtonProps {
  fieldKey: string
  currentValue: Any
  fieldConfig: EditableFieldConfig
  onEdit: (fieldConfig: EditableFieldConfig, currentValue: Any) => void
  className?: string
  showOnHover?: boolean
}

export const InlineEditButton: React.FC<InlineEditButtonProps> = ({
  fieldKey,
  currentValue,
  fieldConfig,
  onEdit,
  className,
  showOnHover = true,
}) => {
  const handleClick = () => {
    onEdit(fieldConfig, currentValue)
  }

  return (
    <TooltipProvider key={fieldKey}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            className={cn(
              'text-muted-foreground hover:text-foreground h-6 w-6 p-0',
              showOnHover && 'opacity-0 transition-opacity group-hover:opacity-100',
              className
            )}
          >
            <Edit2 className="h-3 w-3" />
            <span className="sr-only">Chỉnh sửa {fieldConfig.label}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Chỉnh sửa {fieldConfig.label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
