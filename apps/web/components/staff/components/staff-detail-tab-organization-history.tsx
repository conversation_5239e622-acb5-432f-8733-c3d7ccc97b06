import { useQueryClient } from '@tanstack/react-query'
import { But<PERSON> } from '@workspace/ui/components/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@workspace/ui/mi'
import { Any } from '@workspace/ui/types'
import { History as HistoryIcon, Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useState } from 'react'

import { getOrganizationHistoryEditableFields, getOrganizationHistoryProbationFields } from '../constants/field-configs'
import { staffDetailQueryKeys } from '../services/staff-detail.service'
import { useUpdateOrganizationHistory } from '../services/staff-organization-history.service'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { MoveToOrganizationDialog } from './move-to-organization-config-dialog'
import { OrganizationHistoryDetailSection } from './organization-history-detail-section'
import { OrganizationTypeRoleSelectionDialog } from './organization-type-role-selection-dialog'

// Organization History Tab Component with Edit Functionality
export const OrganizationHistoryTab = ({ staff }: { staff: StaffDetailResponse }) => {
  const params = useParams()
  const staffId = params.staffId as string
  const queryClient = useQueryClient()
  const updateOrganizationHistoryMutation = useUpdateOrganizationHistory(staffId)
  const [isTypeRoleDialogOpen, setIsTypeRoleDialogOpen] = useState(false)
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false)
  const [selectedOrgTypeAndRoles, setSelectedOrgTypeAndRoles] = useState<{
    organizationType: string
    businessRoles: string[]
  } | null>(null)

  const handleUpdateHistory = async (historyId: string, fieldKey: string, value: Any) => {
    updateOrganizationHistoryMutation.mutate({
      staffOrganizationHistoryId: historyId,
      [fieldKey]: value,
    })
  }

  const handleRefreshStaff = () => {
    queryClient.invalidateQueries({
      queryKey: staffDetailQueryKeys.detail(staffId),
    })
  }

  const handleTypeRoleConfirm = (data: { organizationType: string; businessRoles: string[] }) => {
    setSelectedOrgTypeAndRoles(data)
    setIsTypeRoleDialogOpen(false)
    setIsMoveDialogOpen(true)
  }

  const handleMoveDialogClose = () => {
    setIsMoveDialogOpen(false)
    setSelectedOrgTypeAndRoles(null)
  }

  return (
    <div className="space-y-4">
      {/* Header with Add Organization Button */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <HistoryIcon className="h-5 w-5" />
              Lịch sử tổ chức
            </CardTitle>
            <Button onClick={() => setIsTypeRoleDialogOpen(true)} size="sm" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Chuyển đơn vị
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Organization History Records */}
      {staff.organizationHistory && staff.organizationHistory.length > 0 ? (
        <div className="space-y-4">
          {staff.organizationHistory
            // Sort: status = 1 (active) first, then by startDate descending
            .sort((a, b) => {
              // First priority: status = 1 comes first
              if (a.status === 1 && b.status !== 1) return -1

              if (a.status !== 1 && b.status === 1) return 1

              // Second priority: sort by startDate (newest first)
              const dateA = a.startDate ? new Date(a.startDate).getTime() : 0
              const dateB = b.startDate ? new Date(b.startDate).getTime() : 0

              return dateB - dateA
            })
            .map(history => (
              <OrganizationHistoryDetailSection
                key={history.id}
                title={history.organizationUnit?.name || 'Đơn vị không xác định'}
                history={history}
                fieldConfigs={getOrganizationHistoryEditableFields()}
                probationFieldConfigs={getOrganizationHistoryProbationFields()}
                onUpdate={handleUpdateHistory}
                onRefreshStaff={handleRefreshStaff}
                staff={staff}
              />
            ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <HistoryIcon className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <p className="text-gray-500">Chưa có lịch sử tổ chức</p>
            <Button onClick={() => setIsTypeRoleDialogOpen(true)} variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" />
              Tạo bản ghi đầu tiên
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Organization Type & Role Selection Dialog */}
      <OrganizationTypeRoleSelectionDialog
        isOpen={isTypeRoleDialogOpen}
        onClose={() => setIsTypeRoleDialogOpen(false)}
        onConfirm={handleTypeRoleConfirm}
        staff={staff}
      />

      {/* Move to Organization Dialog */}
      <MoveToOrganizationDialog
        isOpen={isMoveDialogOpen}
        onClose={handleMoveDialogClose}
        staff={staff}
        organizationType={selectedOrgTypeAndRoles?.organizationType}
        businessRoles={selectedOrgTypeAndRoles?.businessRoles}
      />
    </div>
  )
}
