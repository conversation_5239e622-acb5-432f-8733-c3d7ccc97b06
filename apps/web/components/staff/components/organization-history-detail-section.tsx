'use client'

import { STAFF_BUSINESS_ROLE_OPTIONS } from '@/constants/staff'
import { Any } from '@/lib/types'
import { deleteStaffAction, deleteStaffEvent, deleteStaffSpecialization } from '@/services/staff-organization-relations'
import { Badge } from '@workspace/ui/components/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Separator } from '@workspace/ui/components/separator'
import { Users } from 'lucide-react'
import React, { useState } from 'react'

import { StaffDetailResponse } from '../types/staff-detail.types'
import { OrganizationHistoryEditableFieldConfig } from '../types/staff-field-update.types'
import { AddActionDialog } from './add-action-dialog'
import { AddEventDialog } from './add-event-dialog'
import { AddSpecializationDialog } from './add-specialization-dialog'
import { FieldEditDialog } from './field-edit-dialog'
import { InlineEditButton } from './inline-edit-button'

interface OrganizationHistoryDetailSectionProps {
  title: string
  history: Any // Will contain organization history data
  fieldConfigs: OrganizationHistoryEditableFieldConfig[]
  probationFieldConfigs: OrganizationHistoryEditableFieldConfig[]
  onUpdate?: (historyId: string, fieldKey: string, value: Any) => Promise<void>
  onRefreshStaff?: () => void // Add callback to refresh staff data
  className?: string
  staff: StaffDetailResponse // Add staff context for field editing
}

export const OrganizationHistoryDetailSection: React.FC<OrganizationHistoryDetailSectionProps> = ({
  title,
  history,
  fieldConfigs,
  probationFieldConfigs,
  onUpdate,
  onRefreshStaff,
  className = '',
  staff,
}) => {
  const [editingField, setEditingField] = useState<{
    field: OrganizationHistoryEditableFieldConfig
    currentValue: Any
  } | null>(null)

  // Dialog states
  const [isAddSpecializationOpen, setIsAddSpecializationOpen] = useState(false)
  const [isAddActionOpen, setIsAddActionOpen] = useState(false)
  const [selectedActionType, setSelectedActionType] = useState<'HINH_THUC_KHEN_THUONG' | 'HINH_THUC_KY_LUAT' | null>(
    null
  )
  const [isAddEventOpen, setIsAddEventOpen] = useState(false)

  const handleEditField = (field: OrganizationHistoryEditableFieldConfig, currentValue: Any) => {
    console.log(field, 'field')
    setEditingField({ field, currentValue })
  }

  const handleSaveField = async (value: Any) => {
    if (!editingField || !onUpdate) return

    await onUpdate(history.id, editingField.field.key, value)
  }

  const handleCloseDialog = () => {
    setEditingField(null)
  }

  const getFieldValue = (fieldKey: string) => {
    // Handle nested fields and display values like staff detail section
    switch (fieldKey) {
      case 'positionId':
        return history.position?.name || history.positionId

      case 'levelId':
        return history.level?.name || history.levelId

      case 'legalAidFormId':
        return history.legalAidForm?.name || history.legalAidFormId

      case 'isHead':
        return history.isHead === 1 ? 'Có' : 'Không'

      case 'isProbation': {
        return history.isProbation === 1 ? 'Có' : 'Không'
      }

      case 'probationStartDate': {
        const dateValue = (history as Any)[fieldKey]

        if (!dateValue) {
          return null
        }
        const date = new Date(dateValue)

        return isNaN(date.getTime()) ? null : date.toLocaleDateString('vi-VN')
      }
      default:
        return (history as Any)[fieldKey]
    }
  }

  const getFieldDisplayValue = (fieldKey: string) => {
    const value = getFieldValue(fieldKey)

    if (!value) return 'Chưa cập nhật'

    // For date fields, getFieldValue already handles the formatting
    return value
  }

  const getFieldRawValue = (fieldKey: string) => {
    // Return the actual ID value for editing (not the display name) like staff detail section
    switch (fieldKey) {
      case 'positionId':
        return history.positionId

      case 'levelId':
        return history.levelId

      case 'legalAidFormId':
        return history.legalAidFormId

      case 'isProbation':
        return (history as Any)[fieldKey] || 0

      case 'probationStartDate': {
        const dateValue = (history as Any)[fieldKey]

        if (!dateValue) return ''
        const date = new Date(dateValue)

        if (isNaN(date.getTime())) return ''
        // Format date for input without timezone conversion
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        return `${year}-${month}-${day}`
      }

      default:
        return (history as Any)[fieldKey] || ''
    }
  }

  const shouldShowProbationFields = () => {
    return history.isProbation === 1
  }

  // Utility function to get event type labels
  const getEventTypeLabel = (eventType: string) => {
    const eventTypes: Record<string, string> = {
      APPOINTMENT: 'Bổ nhiệm',
      REAPPOINTMENT: 'Bổ nhiệm lại',
      DISMISSAL: 'Miễn nhiệm',
      TRANSFER: 'Chuyển công tác',
    }

    return eventTypes[eventType] || eventType
  }

  // Handlers for specializations
  const handleAddSpecialization = () => {
    setIsAddSpecializationOpen(true)
  }

  const handleDeleteSpecialization = async (specializationId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa chuyên môn này?')) {
      try {
        await deleteStaffSpecialization(staff.id, specializationId)
        onRefreshStaff?.() // Refresh staff data after successful deletion
        console.log('Specialization deleted:', specializationId)
      } catch (error) {
        console.error('Error deleting specialization:', error)
      }
    }
  }

  // Handlers for actions
  const handleAddAction = (actionType: 'HINH_THUC_KHEN_THUONG' | 'HINH_THUC_KY_LUAT') => {
    setSelectedActionType(actionType)
    setIsAddActionOpen(true)
  }

  const handleDeleteAction = async (actionId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa hành động này?')) {
      try {
        await deleteStaffAction(staff.id, actionId)
        onRefreshStaff?.() // Refresh staff data after successful deletion
        console.log('Action deleted:', actionId)
      } catch (error) {
        console.error('Error deleting action:', error)
      }
    }
  }

  // Handlers for events
  const handleAddEvent = () => {
    setIsAddEventOpen(true)
  }

  const handleDeleteEvent = async (eventId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa sự kiện này?')) {
      try {
        await deleteStaffEvent(staff.id, eventId)
        onRefreshStaff?.() // Refresh staff data after successful deletion
        console.log('Event deleted:', eventId)
      } catch (error) {
        console.error('Error deleting event:', error)
      }
    }
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {title}
            </span>
            <div className="flex items-center gap-2">
              <Badge variant={history.status === 1 ? 'default' : 'secondary'}>
                {history.status === 1 ? 'Hiện tại' : 'Đã kết thúc'}
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium text-gray-600">Mã đơn vị</label>
                <p className="text-sm">{history.organizationUnit?.code || 'Chưa có'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Ngày bắt đầu</label>
                <p className="text-sm">
                  {history.startDate ? new Date(history.startDate).toLocaleDateString('vi-VN') : 'Chưa có'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Ngày kết thúc</label>
                <p className="text-sm">
                  {history.endDate ? new Date(history.endDate).toLocaleDateString('vi-VN') : ''}
                </p>
              </div>
              <div>
                {history.roles.map((role: string, idx: number) => (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {STAFF_BUSINESS_ROLE_OPTIONS.find(opt => opt.value === role)?.label || role}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <Separator />

          {/* Editable Fields */}
          <div>
            <h4 className="mb-3 text-sm font-medium text-gray-700">Thông tin bổ sung</h4>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {fieldConfigs.map(fieldConfig => {
                const displayValue = getFieldDisplayValue(fieldConfig.key)
                const rawValue = getFieldRawValue(fieldConfig.key)
                const hasValue = rawValue && rawValue !== ''

                return (
                  <div key={fieldConfig.key} className="group flex flex-col space-y-1">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-gray-700">
                        {fieldConfig.label}
                        {fieldConfig.required && <span className="ml-1 text-red-500">*</span>}
                      </label>
                      {history.status == 1 && (
                        <InlineEditButton
                          fieldKey={fieldConfig.key as Any}
                          currentValue={rawValue}
                          fieldConfig={fieldConfig as Any}
                          onEdit={handleEditField as Any}
                          className="opacity-0 transition-opacity group-hover:opacity-100"
                          showOnHover={false}
                        />
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {hasValue || fieldConfig.key === 'isHead' ? (
                        fieldConfig.key === 'isHead' ? (
                          <Badge variant={history.isHead === 1 ? 'default' : 'secondary'}>{displayValue}</Badge>
                        ) : (
                          <span className="text-sm text-gray-900">{displayValue}</span>
                        )
                      ) : (
                        <span className="text-sm text-gray-500 italic">Chưa cập nhật</span>
                      )}
                      {fieldConfig.type === 'api-select' && hasValue && (
                        <Badge variant="secondary" className="text-xs">
                          API
                        </Badge>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Probation Section */}
          {shouldShowProbationFields() && (
            <>
              <Separator />
              <div>
                <h4 className="mb-3 text-sm font-medium text-gray-700">Thông tin tập sự</h4>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {probationFieldConfigs.map(fieldConfig => {
                    // Skip isProbation field since we already know it's 1
                    if (fieldConfig.key === 'isProbation') return null

                    const displayValue = getFieldDisplayValue(fieldConfig.key)
                    const rawValue = getFieldRawValue(fieldConfig.key)
                    const hasValue = rawValue && rawValue !== ''

                    return (
                      <div key={fieldConfig.key} className="group flex flex-col space-y-1">
                        <div className="flex items-center justify-between">
                          <label className="text-sm font-medium text-gray-700">
                            {fieldConfig.label}
                            {fieldConfig.required && <span className="ml-1 text-red-500">*</span>}
                          </label>
                          <InlineEditButton
                            fieldKey={fieldConfig.key as Any}
                            currentValue={rawValue}
                            fieldConfig={fieldConfig as Any}
                            onEdit={handleEditField as Any}
                            className="opacity-0 transition-opacity group-hover:opacity-100"
                            showOnHover={false}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          {hasValue ? (
                            <span className="text-sm text-gray-900">{displayValue}</span>
                          ) : (
                            <span className="text-sm text-gray-500 italic">Chưa cập nhật</span>
                          )}
                          {fieldConfig.type === 'api-select' && hasValue && (
                            <Badge variant="secondary" className="text-xs">
                              API
                            </Badge>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </>
          )}

          {/* Show specializations - Editable Section */}
          <Separator />
          <div>
            <div className="mb-3 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">Chuyên môn trong giai đoạn này</h4>
              {history.status === 1 && (
                <button
                  onClick={() => handleAddSpecialization()}
                  className="rounded bg-blue-50 px-2 py-1 text-xs text-blue-600 hover:bg-blue-100"
                >
                  + Thêm chuyên môn
                </button>
              )}
            </div>

            {history.specializations && history.specializations.length > 0 ? (
              <div className="space-y-2">
                {history.specializations.map((spec: Any) => (
                  <div key={spec.id} className="flex items-center justify-between rounded bg-gray-50 p-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{spec.specialization?.name}</Badge>
                      {spec.level && <span className="text-xs text-gray-500">({spec.level})</span>}
                      {spec.certificateNo && (
                        <span className="text-xs text-gray-500">- Chứng chỉ: {spec.certificateNo}</span>
                      )}
                    </div>
                    {history.status === 1 && (
                      <button
                        onClick={() => handleDeleteSpecialization(spec.id)}
                        className="text-xs text-red-600 hover:text-red-800"
                      >
                        Xóa
                      </button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">Chưa có chuyên môn nào</p>
            )}
          </div>

          {/* Show actions - Editable Section */}
          <Separator />
          <div>
            <div className="mb-3 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">Khen thưởng / Kỷ luật</h4>
              {history.status === 1 && (
                <div className="flex gap-2">
                  <button
                    onClick={() => handleAddAction('HINH_THUC_KHEN_THUONG')}
                    className="rounded bg-green-50 px-2 py-1 text-xs text-green-600 hover:bg-green-100"
                  >
                    + Khen thưởng
                  </button>
                  <button
                    onClick={() => handleAddAction('HINH_THUC_KY_LUAT')}
                    className="rounded bg-orange-50 px-2 py-1 text-xs text-orange-600 hover:bg-orange-100"
                  >
                    + Kỷ luật
                  </button>
                </div>
              )}
            </div>

            {history.actions && history.actions.length > 0 ? (
              <div className="space-y-2">
                {history.actions.map((action: Any) => (
                  <div key={action.id} className="flex items-center justify-between rounded bg-gray-50 p-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{action.rewardDiscipline?.name}</Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(action.createdAt).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                    {history.status === 1 && (
                      <button
                        onClick={() => handleDeleteAction(action.id)}
                        className="text-xs text-red-600 hover:text-red-800"
                      >
                        Xóa
                      </button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">Chưa có hành động nào</p>
            )}
          </div>

          {/* Show events - Editable Section */}
          <Separator />
          <div>
            <div className="mb-3 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">Sự kiện tổ chức</h4>
              {history.status === 1 && (
                <button
                  onClick={() => handleAddEvent()}
                  className="rounded bg-purple-50 px-2 py-1 text-xs text-purple-600 hover:bg-purple-100"
                >
                  + Thêm sự kiện
                </button>
              )}
            </div>

            {history.events && history.events.length > 0 ? (
              <div className="space-y-2">
                {history.events.map((event: Any) => (
                  <div key={event.id} className="flex items-center justify-between rounded bg-gray-50 p-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{getEventTypeLabel(event.eventType)}</Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(event.eventDate).toLocaleDateString('vi-VN')}
                      </span>
                      {event.decisionNumber && (
                        <span className="text-xs text-gray-500">- QĐ: {event.decisionNumber}</span>
                      )}
                    </div>
                    {history.status === 1 && (
                      <button
                        onClick={() => handleDeleteEvent(event.id)}
                        className="text-xs text-red-600 hover:text-red-800"
                      >
                        Xóa
                      </button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">Chưa có sự kiện nào</p>
            )}
          </div>
        </CardContent>
      </Card>

      {editingField && (
        <FieldEditDialog
          isOpen={!!editingField}
          onClose={handleCloseDialog}
          field={editingField.field as Any}
          currentValue={editingField.currentValue}
          onSave={handleSaveField}
          isLoading={false}
          staff={staff}
        />
      )}

      {/* Add dialogs */}
      <AddSpecializationDialog
        isOpen={isAddSpecializationOpen}
        onClose={() => setIsAddSpecializationOpen(false)}
        staffId={staff.id}
        onSuccess={() => {
          onRefreshStaff?.() // Refresh staff data after successful addition
          console.log('Specialization added successfully')
        }}
      />

      <AddActionDialog
        isOpen={isAddActionOpen}
        onClose={() => {
          setIsAddActionOpen(false)
          setSelectedActionType(null)
        }}
        staffId={staff.id}
        initialActionType={selectedActionType || undefined}
        onSuccess={() => {
          onRefreshStaff?.() // Refresh staff data after successful addition
          console.log('Action added successfully')
        }}
      />

      <AddEventDialog
        isOpen={isAddEventOpen}
        onClose={() => setIsAddEventOpen(false)}
        staffId={staff.id}
        onSuccess={() => {
          onRefreshStaff?.() // Refresh staff data after successful addition
          console.log('Event added successfully')
        }}
      />
    </>
  )
}
