'use client'

import { Any } from '@/lib/types'
import { <PERSON><PERSON> } from '@workspace/ui/components/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { Edit3 } from 'lucide-react'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'

import { CategoryApiSelect } from '../form'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { EditableFieldConfig, StaffFieldUpdateDto } from '../types/staff-field-update.types'

interface MultiFieldEditDialogProps {
  isOpen: boolean
  onClose: () => void
  fields: EditableFieldConfig[]
  staff: StaffDetailResponse
  onSave: (data: StaffFieldUpdateDto) => Promise<void>
  isLoading: boolean
  title: string
}

export const MultiFieldEditDialog: React.FC<MultiFieldEditDialogProps> = ({
  isOpen,
  onClose,
  fields,
  staff,
  onSave,
  isLoading,
  title,
}) => {
  const form = useForm<StaffFieldUpdateDto>({
    defaultValues: {},
  })

  // Initialize form with current values
  useEffect(() => {
    if (isOpen && staff) {
      const initialValues: Partial<StaffFieldUpdateDto> = {}

      fields.forEach(field => {
        const currentValue = getFieldCurrentValue(field, staff)
        initialValues[field.key] = currentValue
      })

      form.reset(initialValues)
    }
  }, [isOpen, staff, fields, form])

  const handleSubmit = async (data: StaffFieldUpdateDto) => {
    try {
      // Only send fields that have actually changed
      const changedFields: StaffFieldUpdateDto = {}
      let hasChanges = false

      fields.forEach(field => {
        const currentValue = getFieldCurrentValue(field, staff)
        const newValue = data[field.key]

        // Check if value has actually changed
        if (currentValue !== newValue) {
          ;(changedFields as Any)[field.key] = newValue
          hasChanges = true
        }
      })

      if (!hasChanges) {
        onClose()

        return
      }

      await onSave(changedFields)
      onClose()
    } catch (error) {
      console.error('Error saving fields:', error)
    }
  }

  // Get current value for a field from staff data
  const getFieldCurrentValue = (field: EditableFieldConfig, staff: StaffDetailResponse) => {
    switch (field.key) {
      case 'genderId':
        return staff.genderId
      case 'ethnicId':
        return staff.ethnicId
      case 'provinceId':
        return staff.provinceId
      case 'districtId':
        return staff.districtId
      case 'wardId':
        return staff.wardId
      case 'professionalLevelId':
        return staff.professionalLevelId
      case 'activityStatusId':
        return staff.activityStatusId
      case 'organizationUnitId':
        return staff.organizationUnitId
      default:
        return (staff as Any)[field.key]
    }
  }

  // Check if field has unmet dependencies
  const hasUnmetDependencies = (field: EditableFieldConfig) => {
    if (!field.dependsOn) return false

    const dependentValue = form.watch(field.dependsOn)
    const currentDependentValue = getFieldCurrentValue(
      fields.find(f => f.key === field.dependsOn) || ({ key: field.dependsOn } as EditableFieldConfig),
      staff
    )

    return !dependentValue && !currentDependentValue
  }

  // Get dynamic filters for API fields
  const getDynamicFilters = (field: EditableFieldConfig) => {
    if (!field.getDynamicFilters) return {}

    // Create a combined object with current form values and staff data
    const combinedData = {
      ...staff,
      ...form.getValues(),
    }

    return field.getDynamicFilters(combinedData)
  }

  // Render individual field based on type
  const renderField = (field: EditableFieldConfig) => {
    const validation = field.validation || {}

    // Handle API select fields with dependencies
    if (field.type === 'api-select') {
      const hasUnmet = hasUnmetDependencies(field)

      if (hasUnmet) {
        return (
          <div className="space-y-2">
            <div className="rounded border border-amber-200 bg-amber-50 p-2 text-sm text-amber-700">
              Vui lòng chọn {getFieldLabel(field.dependsOn!)} trước
            </div>
            <Input disabled placeholder="Chọn trường phụ thuộc trước" className="bg-gray-50" />
          </div>
        )
      }

      return (
        <CategoryApiSelect
          control={form.control}
          name={field.key}
          label=""
          categoryType={field.categoryType!}
          fallbackOptions={Array.from(field.fallbackOptions || [])}
          placeholder={`Chọn ${field.label.toLowerCase()}`}
          additionalFilters={getDynamicFilters(field)}
          className="w-full"
        />
      )
    }

    // Handle other field types
    switch (field.type) {
      case 'select':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Select value={String(formField.value || '')} onValueChange={formField.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder={`Chọn ${field.label.toLowerCase()}`} />
                </SelectTrigger>
                <SelectContent>
                  {field.options?.map(option => (
                    <SelectItem key={option.value} value={`${option.value}`}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        )

      case 'textarea':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Textarea
                {...formField}
                value={String(formField.value || '')}
                placeholder={`Nhập ${field.label.toLowerCase()}`}
                maxLength={validation?.maxLength}
                rows={3}
              />
            )}
          />
        )

      case 'date':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => {
              const dateValue =
                formField.value instanceof Date ? formField.value.toISOString().split('T')[0] : formField.value || ''

              return (
                <Input
                  type="date"
                  value={dateValue}
                  onChange={e => formField.onChange(e.target.value ? new Date(e.target.value) : null)}
                />
              )
            }}
          />
        )

      case 'number':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Input
                type="number"
                {...formField}
                value={String(formField.value || '')}
                onChange={e => formField.onChange(e.target.value ? Number(e.target.value) : null)}
                placeholder={`Nhập ${field.label.toLowerCase()}`}
                min={validation?.min}
                max={validation?.max}
              />
            )}
          />
        )

      case 'email':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Input
                type="email"
                {...formField}
                value={String(formField.value || '')}
                placeholder={`Nhập ${field.label.toLowerCase()}`}
              />
            )}
          />
        )

      case 'phone':
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Input
                type="tel"
                {...formField}
                value={String(formField.value || '')}
                placeholder={`Nhập ${field.label.toLowerCase()}`}
                pattern={validation?.pattern?.source}
              />
            )}
          />
        )

      default: // text
        return (
          <FormField
            control={form.control}
            name={field.key}
            render={({ field: formField }) => (
              <Input
                type="text"
                {...formField}
                value={String(formField.value || '')}
                placeholder={`Nhập ${field.label.toLowerCase()}`}
                maxLength={validation?.maxLength}
                minLength={validation?.minLength}
              />
            )}
          />
        )
    }
  }

  // Helper function to get field label by key
  const getFieldLabel = (fieldKey: string): string => {
    const fieldLabels: Record<string, string> = {
      provinceId: 'Tỉnh/Thành phố',
      districtId: 'Quận/Huyện',
      wardId: 'Phường/Xã',
      genderId: 'Giới tính',
      ethnicId: 'Dân tộc',
      professionalLevelId: 'Trình độ chuyên môn',
      activityStatusId: 'Trạng thái hoạt động',
    }

    return fieldLabels[fieldKey] || fieldKey
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Chỉnh sửa {title}
          </DialogTitle>
          <DialogDescription>
            Cập nhật nhiều thông tin cùng lúc. Các trường có quan hệ phụ thuộc sẽ được xử lý tự động.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {fields.map(field => (
                <FormField
                  key={field.key}
                  control={form.control}
                  name={field.key}
                  rules={{
                    required: field.required ? `${field.label} là bắt buộc` : false,
                    pattern: field.validation?.pattern
                      ? {
                          value: field.validation.pattern,
                          message: `${field.label} không đúng định dạng`,
                        }
                      : undefined,
                    minLength: field.validation?.minLength
                      ? {
                          value: field.validation.minLength,
                          message: `${field.label} phải có ít nhất ${field.validation.minLength} ký tự`,
                        }
                      : undefined,
                    maxLength: field.validation?.maxLength
                      ? {
                          value: field.validation.maxLength,
                          message: `${field.label} không được quá ${field.validation.maxLength} ký tự`,
                        }
                      : undefined,
                    min: field.validation?.min
                      ? {
                          value: field.validation.min,
                          message: `${field.label} phải lớn hơn hoặc bằng ${field.validation.min}`,
                        }
                      : undefined,
                    max: field.validation?.max
                      ? {
                          value: field.validation.max,
                          message: `${field.label} phải nhỏ hơn hoặc bằng ${field.validation.max}`,
                        }
                      : undefined,
                  }}
                  render={() => (
                    <FormItem className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
                      <FormLabel>
                        {field.label}
                        {field.required && <span className="ml-1 text-red-500">*</span>}
                      </FormLabel>
                      <FormControl>{renderField(field)}</FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>

            <DialogFooter className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading} className="min-w-[120px]">
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Đang lưu...
                  </div>
                ) : (
                  'Lưu thay đổi'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
