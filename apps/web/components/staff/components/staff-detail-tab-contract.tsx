'use client'

import { Badge } from '@workspace/ui/components/badge'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/mi'
import { FileText, Plus } from 'lucide-react'
import { useMemo, useState } from 'react'

import { useStaffContracts } from '../hooks/use-staff-contracts'
import { CreateContractModal } from '../modals/create-contract-modal'
import { StaffDetailResponse } from '../types/staff-detail.types'
import { AcFilePreviewButton } from './ac-file-preview-button'
import { ContractEndDateEditor } from './index'

const contractTypeLabels: Record<string, string> = {
  LABOR: 'Hợp đồng lao động',
  SERVICE: 'Hợp đồng dịch vụ',
  SECONDMENT: 'Hợp đồng điều động',
  OTHER: 'Khác',
}

const statusLabels: Record<string, string> = {
  ACTIVE: 'Hoạt động',
  EXPIRED: 'Hết hạn',
  TERMINATED: 'Đã chấm dứt',
}

export const ContractsTab = ({ staff }: { staff: StaffDetailResponse }) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const { data, isLoading, refetch } = useStaffContracts(staff.id)

  const contracts = useMemo(() => {
    if (data?.contracts) {
      return data.contracts
    }

    return (
      staff.contracts?.map(contract => ({
        ...contract,
        startDate: contract.startDate?.toString?.() ?? contract.startDate,
        endDate: contract.endDate?.toString?.() ?? contract.endDate,
        createdAt: contract.createdAt?.toString?.() ?? contract.createdAt,
        updatedAt: contract.updatedAt?.toString?.() ?? contract.updatedAt,
      })) ?? []
    )
  }, [data?.contracts, staff.contracts])

  return (
    <div className="space-y-4">
      {/* Header with Add Contract button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Hợp đồng lao động</h3>
        <Button onClick={() => setIsCreateModalOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm hợp đồng
        </Button>
      </div>

      {/* Contracts list or empty state */}
      {isLoading ? (
        <Card>
          <CardContent className="text-muted-foreground py-8 text-center text-sm">
            Đang tải danh sách hợp đồng...
          </CardContent>
        </Card>
      ) : contracts.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <FileText className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <p className="mb-4 text-gray-500">Chưa có thông tin hợp đồng</p>
            <Button onClick={() => setIsCreateModalOpen(true)} variant="outline" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Tạo hợp đồng đầu tiên
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {contracts.map(contract => (
            <Card key={contract.id}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Hợp đồng {contract.contractNo}
                  </span>
                  <Badge variant={contract.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {statusLabels[contract.status] || contract.status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Loại hợp đồng</label>
                    <p className="text-sm">{contractTypeLabels[contract.contractType] || contract.contractType}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Đơn vị</label>
                    <p className="text-sm">{contract.organizationUnit?.name || 'Chưa có'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Ngày bắt đầu</label>
                    <p className="text-sm">{new Date(contract.startDate).toLocaleDateString('vi-VN')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Ngày kết thúc</label>
                    <ContractEndDateEditor
                      contractId={contract.id}
                      currentEndDate={contract.endDate}
                      staffId={staff.id}
                    />
                  </div>
                  {contract.note && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-600">Ghi chú</label>
                      <p className="text-sm">{contract.note}</p>
                    </div>
                  )}
                  {(contract.fileId || contract.file?.publicUrl || contract.fileRef) && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-600">Tài liệu hợp đồng</label>
                      <div className="flex items-center gap-2 text-sm">
                        {contract.file && contract?.file.id ? (
                          <AcFilePreviewButton fileId={contract?.file.id} className="text-primary" />
                        ) : contract.file?.publicUrl ? (
                          <a
                            href={contract.file.publicUrl}
                            target="_blank"
                            rel="noreferrer"
                            className="text-primary underline"
                          >
                            Xem tài liệu
                          </a>
                        ) : (
                          <span className="text-muted-foreground">{contract.fileRef}</span>
                        )}
                        {contract.fileId ? (
                          <Badge variant="outline" className="text-xs">
                            ID: {contract.fileId}
                          </Badge>
                        ) : null}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Contract Modal */}
      <CreateContractModal
        staffId={staff.id}
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSuccess={() => refetch()}
      />
    </div>
  )
}
