'use client'

import { useQuery } from '@tanstack/react-query'

interface StaffContractItem {
  id: string
  contractNo: string
  contractType: string
  startDate: string
  endDate?: string
  status: string
  organizationUnitId: string
  organizationUnit?: {
    id: string
    name: string
    code: string
    type: number
  }
  fileRef?: string
  fileId?: string
  file?: {
    id: string
    bucket: string
    objectKey: string
    publicUrl?: string
    contentType?: string
  }
  note?: string
  createdAt: string
  updatedAt: string
}

interface StaffContractListResponse {
  staffId: string
  contracts: StaffContractItem[]
}

const fetchStaffContracts = async (staffId: string): Promise<StaffContractListResponse> => {
  const response = await fetch(`/ac-apis/staffs/${staffId}/contracts`)

  if (!response.ok) {
    const payload = await response.json().catch(() => ({}))
    throw new Error(payload.message ?? 'Không thể lấy danh sách hợp đồng')
  }

  return response.json()
}

export const useStaffContracts = (staffId: string, enabled = true) => {
  return useQuery({
    queryKey: ['staff-contracts', staffId],
    queryFn: () => fetchStaffContracts(staffId),
    enabled: !!staffId && enabled,
    staleTime: 30 * 1000,
  })
}
