import { MODEL_STATUS_FILTER } from '@/constants'
import { Badge } from '@workspace/ui/components/badge'
import { RenderDateTime } from '@workspace/ui/mi/render-date-time'
import { TABLE_SIZE } from '@workspace/ui/mi/table/data-table-helper'
import { Any } from '@workspace/ui/types'
import { CircleCheck } from 'lucide-react'

import { RenderStatus } from './render-status'

export const addColumCreatedAt = (t: Any) => {
  return {
    accessorKey: 'createdAt',
    size: TABLE_SIZE.DATETIME,
    meta: {
      title: t('field.createdAt.label'),
      filter: {
        type: 'date-range',
        position: 'advanced',
      },
    },
    cell: ({ row, table }: Any) => (
      <RenderDateTime datetime={row.getValue('createdAt')} showTime={table.options.meta?.getShowTime()} />
    ),
  }
}

export const addColumnUpdatedAt = (t: Any) => {
  return {
    accessorKey: 'updatedAt',
    size: TABLE_SIZE.DATETIME,
    meta: {
      title: t('field.updatedAt.label'),
      filter: {
        type: 'date-range',
        position: 'advanced',
      },
    },
    cell: ({ row, table }: Any) => (
      <RenderDateTime datetime={row.getValue('updatedAt')} showTime={table.options.meta?.getShowTime()} />
    ),
  } as Any
}

export const addColumnDescription = (t: Any) => {
  return {
    accessorKey: 'description',
    meta: {
      title: t('field.description.label'),
    },
  } as Any
}

export const addColumnStatus = (t: Any) => {
  return {
    accessorKey: 'status',
    size: TABLE_SIZE.STATUS,
    meta: {
      title: t('field.status.label'),
      filter: {
        type: 'select',
        placeholder: t('field.status.placeholder'),
        multiple: true,
        dataType: 'number',
        options: MODEL_STATUS_FILTER.map(status => ({
          label: t(status.label),
          value: status.value.toString(),
          icon: status.icon,
        })),
      },
    },
    cell: ({ getValue }: Any) => <RenderStatus status={getValue()} />,
  }
}

export const addColumnYesNo = (name: string, title: string, t: Any) => {
  return {
    accessorKey: name,
    size: TABLE_SIZE.STATUS,
    meta: {
      title,
      filter: {
        type: 'select',
        dataType: 'number',
        options: [
          { label: t('common.yes'), value: '1' },
          { label: t('common.no'), value: '0' },
        ],
      },
    },
    cell: ({ getValue }: Any) => {
      return getValue() === 1 || getValue() === '1' ? (
        <Badge variant="outline" className="bg-primary text-white">
          <CircleCheck className="text-white" size={12} />
          Có
        </Badge>
      ) : (
        <Badge variant="outline">Không</Badge>
      )
    },
  }
}
