'use client'

import { useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'

export function useLocalSocket() {
  const socketRef = useRef<Socket | null>(null)

  useEffect(() => {
    const socket = io('http://localhost:3009', {
      transports: ['websocket'],
    })
    socketRef.current = socket

    socket.on('connect', () => {
      console.log('✅ Connected to local socket:', socket.id)
    })

    socket.on('disconnect', () => {
      console.log('❌ Disconnected from local socket')
    })

    return () => {
      socket.disconnect()
    }
  }, [])

  const sendFiles = (files: Record<string, string>) => {
    socketRef.current?.emit('updateFiles', files)
  }

  return { sendFiles }
}
