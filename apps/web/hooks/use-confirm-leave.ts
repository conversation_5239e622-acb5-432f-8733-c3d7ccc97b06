import { usePathname } from 'next/navigation'
import { useEffect } from 'react'

export function useConfirmLeave(shouldConfirm: boolean = true) {
  // const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (shouldConfirm) {
        e.preventDefault()
        e.returnValue = ''

        return ''
      }
    }

    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      const anchor = target.closest('a')

      if (anchor && anchor.href && !anchor.href.startsWith(window.location.origin + pathname)) {
        if (shouldConfirm) {
          if (!window.confirm('Leave site?\nChanges you made may not be saved')) {
            e.preventDefault()
          }
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('click', handleClick)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('click', handleClick)
    }
  }, [shouldConfirm, pathname])
}
