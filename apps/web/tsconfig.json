{"compilerOptions": {"baseUrl": ".", "emitDecoratorMetadata": true, "experimentalDecorators": true, "paths": {"@/*": ["./*"], "@ac/bpmn": ["../../packages/bpmn/src"], "@ac/data-types": ["../../packages/data-types/src"], "@workspace/ui/*": ["../../packages/ui/src/*"]}, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "extends": "@workspace/typescript-config/nextjs.json", "include": ["next-env.d.ts", "new-types.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}