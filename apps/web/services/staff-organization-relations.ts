// Organization relations management services for Staff Detail page
import { ApiClientInstance } from '@/lib/api-client'

// ========================================
// SPECIALIZATIONS MANAGEMENT
// ========================================

interface AddSpecializationRequest {
  staffId: string
  specializations: Array<{
    categoryId: string
    level?: string
    certificateNo?: string
    certIssuedAt?: string
    certExpiresAt?: string
    note?: string
  }>
}

export async function addStaffSpecializations(request: AddSpecializationRequest) {
  const { staffId, ...data } = request
  return ApiClientInstance.post(`/staffs/${staffId}/organization/specializations`, data)
}

export async function deleteStaffSpecialization(staffId: string, specializationId: string) {
  return ApiClientInstance.delete(`/staffs/${staffId}/organization/specializations/${specializationId}`)
}

// ========================================
// EVENTS MANAGEMENT
// ========================================

interface AddEventRequest {
  staffId: string
  eventType: 'APPOINTMENT' | 'REAPPOINTMENT' | 'DISMISSAL' | 'TRANSFER'
  eventDate: string
  termYears?: number
  decisionNumber?: string
  decisionDate?: string
  decisionAuthority?: string
  reason?: string
  note?: string
}

export async function addStaffEvent(request: AddEventRequest) {
  const { staffId, ...data } = request
  return ApiClientInstance.post(`/staffs/${staffId}/organization/events`, data)
}

export async function deleteStaffEvent(staffId: string, eventId: string) {
  return ApiClientInstance.delete(`/staffs/${staffId}/organization/events/${eventId}`)
}

// ========================================
// ACTIONS MANAGEMENT
// ========================================

interface AddActionRequest {
  staffId: string
  rewardDisciplineId: string
}

export async function addStaffAction(request: AddActionRequest) {
  const { staffId, ...data } = request
  return ApiClientInstance.post(`/staffs/${staffId}/organization/actions`, data)
}

export async function deleteStaffAction(staffId: string, actionId: string) {
  return ApiClientInstance.delete(`/staffs/${staffId}/organization/actions/${actionId}`)
}
