import { Organization } from '@/constants/so-tu-phap'
import { Staff } from '@/constants/staff'

import { apiService } from './common/api-service'

export const organizationService = {
  fetchOrganizationDetail: async (id: string) => {
    try {
      return await organizationService.getOrganizationUnitDetail(id)
    } catch (error) {
      throw error
    }
  },
  fetchOrganizationChildren: async (id: string) => {
    try {
      const result = (await organizationService.getOrganizationChildren(id)) as { items?: Organization[] }
      return result.items || []
    } catch (error) {
      throw error
    }
  },
  fetchOrganizationStaffs: async (id: string) => {
    try {
      const staffResult = (await organizationService.getOrganizationStaffs(id)) as { items?: Staff[] }
      return staffResult.items || []
    } catch (error) {
      throw error
    }
  },
  fetchOrganizationCollaboratorsCount: async (id: string) => {
    try {
      const collabResult = (await organizationService.getOrganizationCollaborators(id)) as { items?: Staff[] }
      return (collabResult.items || []).length
    } catch (error) {
      throw error
    }
  },
  getOrganizationChildren: async (id: string) => {
    return apiService.get(`/ac-apis/organization-units/${id}/children`)
  },
  getOrganizationStaffs: async (id: string) => {
    return apiService.get(`/ac-apis/staffs`, { params: { organizationUnitId: id } })
  },
  getOrganizationCollaborators: async (id: string) => {
    return apiService.get(`/ac-apis/staffs`, { params: { organizationUnitId: id, businessRoles: 'TGPL_COLLABORATOR' } })
  },
  getOrganizationUnit: async (params?: any) => {
    return apiService.get('/ac-apis/organization-units', { params })
  },
  getOrganizationType: async (type: number, params?: any) => {
    return apiService.get(`/ac-apis/organization-units/type/${type}`, { params })
  },
  getOrganizationUnitDetail: async (id: string) => {
    return apiService.get(`/ac-apis/organization-units/${id}`)
  },
  createOrganizationUnit: async (data: any) => {
    // Đảm bảo truyền contractFileId và contractFileRef nếu có
    const payload = { ...data }
    if (data.contractFileId) payload.contractFileId = data.contractFileId
    if (data.contractFileRef) payload.contractFileRef = data.contractFileRef
    return apiService.post('/ac-apis/organization-units', payload)
  },
  updateOrganizationUnit: async (id: string, data: any) => {
    const payload = { ...data }
    if (data.contractFileId) payload.contractFileId = data.contractFileId
    if (data.contractFileRef) payload.contractFileRef = data.contractFileRef
    return apiService.patch(`/ac-apis/organization-units/${id}`, payload)
  },
  deleteOrganizationUnit: async (id: string) => {
    return apiService.delete(`/ac-apis/organization-units/${id}`)
  },
  getAllOrganizationTypes: async (type: number) => {
    return apiService.get(`/ac-apis/organization-units/method/all-by-type/${type}`)
  },
}
