import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/components/toast'

import { CreateSubtaskPayload, workflowSubtaskApi } from '../services/workflow-subtask-api'
import { AnyError, WorkflowSubTaskType } from '../types'

// Query keys
export const workflowSubtaskKeys = {
  all: ['workflow-subtasks'] as const,
  lists: () => [...workflowSubtaskKeys.all, 'list'] as const,
  list: (params?: {
    filter?: {
      caseAdvancedId?: string
      assignedToId?: string
      type?: WorkflowSubTaskType
    }
    page?: number
    pageSize?: number
  }) => [...workflowSubtaskKeys.lists(), params] as const,
  details: () => [...workflowSubtaskKeys.all, 'detail'] as const,
  detail: (id: string) => [...workflowSubtaskKeys.details(), id] as const,
}

// Get subtasks list
export const useWorkflowSubtasksQuery = (params?: {
  filter?: {
    caseAdvancedId?: string
    assignedToId?: string
    type?: WorkflowSubTaskType
  }
  page?: number
  pageSize?: number
}) => {
  return useQuery({
    queryKey: workflowSubtaskKeys.list(params),
    queryFn: () => workflowSubtaskApi.getList(params),
    enabled: !!params?.filter?.caseAdvancedId, // Only run when we have a case ID
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get subtask detail
export const useWorkflowSubtaskDetailQuery = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: workflowSubtaskKeys.detail(id),
    queryFn: () => workflowSubtaskApi.getById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// Create subtask mutation
export const useCreateWorkflowSubtaskMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: CreateSubtaskPayload) => workflowSubtaskApi.create(payload),
    onSuccess: (data, variables) => {
      toast.success('Tạo subtask thành công')

      // Invalidate and refetch subtasks list
      queryClient.invalidateQueries({ queryKey: workflowSubtaskKeys.lists() })

      // Invalidate specific case subtasks
      queryClient.invalidateQueries({
        queryKey: workflowSubtaskKeys.list({ filter: { caseAdvancedId: variables.caseAdvancedId } }),
      })
    },
    onError: (error: AnyError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi tạo subtask')
    },
  })
}

// Update subtask mutation
export const useUpdateWorkflowSubtaskMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: Partial<CreateSubtaskPayload> }) =>
      workflowSubtaskApi.update(id, payload),
    onSuccess: (data, variables) => {
      toast.success('Cập nhật subtask thành công')

      // Update cache data
      queryClient.setQueryData(workflowSubtaskKeys.detail(variables.id), data)

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: workflowSubtaskKeys.lists() })
    },
    onError: (error: AnyError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật subtask')
    },
  })
}

// Delete subtask mutation
export const useDeleteWorkflowSubtaskMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => workflowSubtaskApi.delete(id),
    onSuccess: () => {
      toast.success('Xóa subtask thành công')

      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: workflowSubtaskKeys.all })
    },
    onError: (error: AnyError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi xóa subtask')
    },
  })
}
