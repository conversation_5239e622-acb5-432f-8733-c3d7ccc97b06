import { useQuery } from '@tanstack/react-query'

import { caseSimpleApi, CaseSimpleListParams } from '../services/case-simple-api'

export const caseSimpleKeys = {
  all: ['case-simples'] as const,
  lists: () => [...caseSimpleKeys.all, 'list'] as const,
  list: (params: CaseSimpleListParams) => [...caseSimpleKeys.lists(), params] as const,
  details: () => [...caseSimpleKeys.all, 'detail'] as const,
  detail: (id: string) => [...caseSimpleKeys.details(), id] as const,
  statistics: () => [...caseSimpleKeys.all, 'statistics'] as const,
}

export function userCaseSimpleDetail({ id, enable = true }: { id: string; enable: boolean }) {
  return useQuery({
    queryKey: caseSimpleKeys.detail(id),
    queryFn: () => caseSimpleApi.getById(id),
    enabled: enable && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}
