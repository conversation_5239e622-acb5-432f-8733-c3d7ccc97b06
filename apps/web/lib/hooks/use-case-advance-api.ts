import { caseAdvanceApi, CaseAdvanceListParams } from '@/lib/services/case-advance-api'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@workspace/ui/lib/toast'
import { useEffect, useState } from 'react'

import { Any, AnyError } from '../types'

export interface CaseAdvance {
  id: string
  oId?: number
  code?: string
  name?: string
  orgUnitId: string
  status?: number

  // Thông tin cuộc hẹn
  appointmentContent?: string
  appointmentDate?: string
  appointmentPlace?: string
  appointmentTime?: string

  // Thông tin pháp lý
  appraiser?: number
  assignmentStatus?: number
  attachType?: number
  commentLegal?: string
  content?: string
  contentRequest?: string
  coordinatorId?: string
  coordinatorName?: string
  createBy?: number
  createDate?: string
  datePrint?: string
  decentralizationId?: number
  documentAttachName?: string
  entryDate?: string
  executionTime?: number
  formsOfAssistance?: string
  formsOfAssistanceId?: string
  fromPhaseLegal?: number
  attachForRequestPerson?: boolean
  attachForLegalCase?: boolean

  // Thông tin người giám hộ
  guardianName?: string
  guardianAddress?: string
  guardianPhone?: string
  guardianCardNumber?: string
  guardianProvince?: string
  guardianDistrict?: string
  guardianWard?: string
  guardianPublishedDate?: string
  guardianPublishedPlace?: string

  indexLegalCode?: number
  indexLegalYear?: number
  isActive?: number
  isNotFullProfile?: boolean
  isOldLegalAidCase?: number
  isPrint?: number
  isSavedProfile?: number
  isTransferExpertise?: number
  legalField?: string
  legalFieldId?: string
  legalAidCaseName?: string
  legalOnlineCode?: string
  legalOnlineId?: number
  legalType?: number
  modifyBy?: number
  modifyDate?: string
  note?: string
  objectLegalField?: string
  objectLegalFieldId?: string
  objectLegalFieldIdReport?: number
  paymentSource?: string
  payComment?: string
  payDate?: string
  payMoney?: number
  payPeople?: number
  payStatus?: number
  performer?: string
  placeOfDisposal?: string
  rate?: number
  rateDate?: string
  receiver?: string

  // Thông tin người yêu cầu TGPL
  rpName?: string
  rpBirthDate?: string
  rpSex?: number
  rpCardNumber?: string
  rpPublishedDate?: string
  rpPublishedPlace?: string
  rpEthnic?: string
  rpAddress?: string
  rpProvince?: string
  rpDistrict?: string
  rpWard?: string
  rpPhone?: string
  rpJob?: string
  relationRp?: string
  rpRequestSource?: string
  rpEmail?: string

  // Phần xác minh
  toPhaseLegal?: number
  verifier?: string
  verifyDate?: string
  verifyStatus?: number

  createdAt?: string
  updatedAt?: string

  actions?: Any

  workflowId?: string
}

export type CreateCaseAdvanceDto = Omit<CaseAdvance, 'id'>

export interface UpdateCaseAdvanceDto extends Partial<CaseAdvance> {
  id: string
}

// Query keys
export const caseAdvanceKeys = {
  all: ['case-advances'] as const,
  lists: () => [...caseAdvanceKeys.all, 'list'] as const,
  list: (params: CaseAdvanceListParams) => [...caseAdvanceKeys.lists(), params] as const,
  details: () => [...caseAdvanceKeys.all, 'detail'] as const,
  detail: (id: string) => [...caseAdvanceKeys.details(), id] as const,
  statistics: () => [...caseAdvanceKeys.all, 'statistics'] as const,
}

// Hook để lấy danh sách vụ việc
export function useCaseAdvanceList(params: CaseAdvanceListParams = {}) {
  return useQuery({
    queryKey: caseAdvanceKeys.list(params),
    queryFn: () => caseAdvanceApi.getList(params),
    placeholderData: previousData => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Hook để lấy chi tiết vụ việc
export function useCaseAdvanceDetail(id: string, enabled = true) {
  return useQuery({
    queryKey: caseAdvanceKeys.detail(id),
    queryFn: () => caseAdvanceApi.getById(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Hook để tạo mới vụ việc
export function useCreateCaseAdvance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateCaseAdvanceDto) => caseAdvanceApi.create(data),
    onSuccess: response => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.lists() })
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.statistics() })

      toast.success(response.message || 'Tạo vụ việc thành công')
    },
    onError: (error: AnyError) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tạo vụ việc')
    },
  })
}

// Hook để cập nhật vụ việc
export function useUpdateCaseAdvance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<UpdateCaseAdvanceDto> }) => caseAdvanceApi.update(id, data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch queries liên quan
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.lists() })
      queryClient.invalidateQueries({
        queryKey: caseAdvanceKeys.detail(variables.id.toString()),
      })
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.statistics() })

      toast.success(response.message || 'Cập nhật vụ việc thành công')
    },
    onError: (error: AnyError) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật vụ việc')
    },
  })
}

// Hook để xóa vụ việc
export function useDeleteCaseAdvance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => caseAdvanceApi.delete(id),
    onSuccess: response => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.lists() })
      queryClient.invalidateQueries({ queryKey: caseAdvanceKeys.statistics() })

      toast.success(response.message || 'Xóa vụ việc thành công')
    },
    onError: (error: AnyError) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa vụ việc')
    },
  })
}

// export function useCaseAdvanceForm(initialData?: Partial<CreateCaseAdvanceDto>) {
export function useCaseAdvanceForm() {
  const createMutation = useCreateCaseAdvance()
  const updateMutation = useUpdateCaseAdvance()

  const handleSubmit = async (data: CreateCaseAdvanceDto, id?: string) => {
    try {
      if (id) {
        // Update existing case
        await updateMutation.mutateAsync({ id, data })
      } else {
        // Create new case
        await createMutation.mutateAsync(data)
      }

      return true
    } catch {
      return false
    }
  }

  return {
    handleSubmit,
    isLoading: createMutation.isPending || updateMutation.isPending,
    error: createMutation.error || updateMutation.error,
  }
}

// Hook để debounce search
export function useDebouncedCaseAdvanceSearch(params: CaseAdvanceListParams, delay = 500) {
  const [debouncedParams, setDebouncedParams] = useState(params)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedParams(params)
    }, delay)

    return () => clearTimeout(timer)
  }, [params, delay])

  return useCaseAdvanceList(debouncedParams)
}

// Hook để export danh sách
export function useExportCaseAdvances() {
  return useMutation({
    mutationFn: async (params: CaseAdvanceListParams) => {
      // Mock export function
      const response = await caseAdvanceApi.getListAll({ ...params })

      // Convert to CSV or Excel format
      const csvContent = convertToCSV(response.data)

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `vu-viec-${new Date().toISOString().split('T')[0]}.csv`
      link.click()

      return response
    },
    onSuccess: () => {
      toast.success('Xuất file thành công')
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi xuất file')
    },
  })
}

// Helper function để convert data sang CSV
function convertToCSV(data: Any[]): string {
  if (!data.length) return ''

  const headers = [
    'Mã vụ việc',
    'Tên vụ việc',
    'Người yêu cầu',
    'SĐT',
    'Email',
    'Lĩnh vực',
    'Hình thức TG',
    'Trạng thái PC',
    'Trạng thái XM',
    'Ngày tạo',
  ]

  const rows = data.map(item => [
    item.code || '',
    item.name || '',
    item.rpName || '',
    item.rpPhone || '',
    item.rpEmail || '',
    item.legalField || '',
    item.formsOfAssistance || '',
    getAssignmentStatusLabel(item.assignmentStatus),
    getVerifyStatusLabel(item.verifyStatus),
    item.createDate || '',
  ])

  const csvContent = [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n')

  return csvContent
}

function getAssignmentStatusLabel(status: number): string {
  switch (status) {
    case 0:
      return 'Chờ xử lý'
    case 1:
      return 'Đang xử lý'
    case 2:
      return 'Hoàn thành'
    case 3:
      return 'Hủy bỏ'
    default:
      return 'Không xác định'
  }
}

function getVerifyStatusLabel(status: number): string {
  switch (status) {
    case 0:
      return 'Chưa xác minh'
    case 1:
      return 'Đã xác minh'
    case 2:
      return 'Từ chối'
    default:
      return 'Không xác định'
  }
}
