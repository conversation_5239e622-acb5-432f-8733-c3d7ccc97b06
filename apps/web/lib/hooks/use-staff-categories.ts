// Hook for fetching staff-related categories
// This demonstrates the new database-driven approach for staff constants
import { CategoryResponseDto } from '@/constants/category'
import { CATEGORY_TYPES } from '@/constants/staff'
import { useQuery } from '@tanstack/react-query'

const fetchCategoriesByType = async (categoryType: string): Promise<CategoryResponseDto[]> => {
  try {
    const response = await fetch(`/ac-apis/categories?type=${categoryType}&status=PUBLISHED`)

    if (!response.ok) {
      throw new Error(`Failed to fetch categories for type: ${categoryType}`)
    }

    const data = await response.json()

    return data.items || []
  } catch (error) {
    console.error('Error fetching categories:', error)

    return []
  }
}

// Helper function to convert categories to select options
export const categoriesToOptions = (categories: CategoryResponseDto[]) => {
  return categories.map(category => ({
    label: category.name,
    value: category.categoryId,
  }))
}

// Convenience functions for common category fetches
const fetchGenderOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.GENDER)
const fetchEthnicOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.ETHNIC)
const fetchProvinceOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.PROVINCE)
const fetchDistrictOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.DISTRICT)
const fetchWardOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.WARD)
const fetchProfessionalLevelOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.PROFESSIONAL_LEVEL)
const fetchActivityStatusOptions = () => fetchCategoriesByType(CATEGORY_TYPES.STAFF.ACTIVITY_STATUS)
const fetchPositionOptions = () => fetchCategoriesByType(CATEGORY_TYPES.ORGANIZATION_HISTORY.POSITION)
const fetchLevelOptions = () => fetchCategoriesByType(CATEGORY_TYPES.ORGANIZATION_HISTORY.LEVEL)

// Hook for fetching gender options
export const useGenderOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.GENDER],
    queryFn: fetchGenderOptions,
    select: categoriesToOptions,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Hook for fetching professional level options
export const useProfessionalLevelOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.PROFESSIONAL_LEVEL],
    queryFn: fetchProfessionalLevelOptions,
    select: categoriesToOptions,
    staleTime: 5 * 60 * 1000,
  })
}

// Hook for fetching activity status options
export const useActivityStatusOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.ACTIVITY_STATUS],
    queryFn: fetchActivityStatusOptions,
    select: categoriesToOptions,
    staleTime: 5 * 60 * 1000,
  })
}

// Hook for fetching ethnic options
export const useEthnicOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.ETHNIC],
    queryFn: fetchEthnicOptions,
    select: categoriesToOptions,
    staleTime: 10 * 60 * 1000, // Longer cache for less frequently changed data
  })
}

// Hook for fetching location options
export const useProvinceOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.PROVINCE],
    queryFn: fetchProvinceOptions,
    select: categoriesToOptions,
    staleTime: 15 * 60 * 1000,
  })
}

export const useDistrictOptions = (provinceId?: string) => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.DISTRICT, provinceId],
    queryFn: () => fetchDistrictOptions(), // TODO: Add parent filtering
    select: categoriesToOptions,
    enabled: !!provinceId,
    staleTime: 10 * 60 * 1000,
  })
}

export const useWardOptions = (districtId?: string) => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.STAFF.WARD, districtId],
    queryFn: () => fetchWardOptions(), // TODO: Add parent filtering
    select: categoriesToOptions,
    enabled: !!districtId,
    staleTime: 10 * 60 * 1000,
  })
}

// Hook for fetching organization-related options
export const usePositionOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.ORGANIZATION_HISTORY.POSITION],
    queryFn: fetchPositionOptions,
    select: categoriesToOptions,
    staleTime: 5 * 60 * 1000,
  })
}

export const useLevelOptions = () => {
  return useQuery({
    queryKey: ['categories', CATEGORY_TYPES.ORGANIZATION_HISTORY.LEVEL],
    queryFn: fetchLevelOptions,
    select: categoriesToOptions,
    staleTime: 5 * 60 * 1000,
  })
}

// Composite hook for all common staff categories
export const useStaffCategories = () => {
  const genderOptions = useGenderOptions()
  const professionalLevelOptions = useProfessionalLevelOptions()
  const activityStatusOptions = useActivityStatusOptions()
  const ethnicOptions = useEthnicOptions()
  const provinceOptions = useProvinceOptions()
  const positionOptions = usePositionOptions()

  return {
    genderOptions,
    professionalLevelOptions,
    activityStatusOptions,
    ethnicOptions,
    provinceOptions,
    positionOptions,
    isLoading:
      genderOptions.isLoading ||
      professionalLevelOptions.isLoading ||
      activityStatusOptions.isLoading ||
      ethnicOptions.isLoading ||
      provinceOptions.isLoading ||
      positionOptions.isLoading,
    isError:
      genderOptions.isError ||
      professionalLevelOptions.isError ||
      activityStatusOptions.isError ||
      ethnicOptions.isError ||
      provinceOptions.isError ||
      positionOptions.isError,
  }
}
