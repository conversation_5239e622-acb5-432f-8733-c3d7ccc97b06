import { Any } from '@workspace/ui/types'
import { DBSchema, IDBPDatabase, openDB } from 'idb'

export type ApiType = 'swagger' | 'graphql'

export interface ApiSchema {
  id: string
  name: string
  description?: string
  type: ApiType
  schema: Any // Swagger/OpenAPI JSON hoặc GraphQL Schema
  endpoints: ApiEndpoint[]
  createdAt: Date
  updatedAt?: Date
  status: number // 1: active, 0: deleted
  url?: string // URL để fetch schema từ server
}

export interface ApiEndpoint {
  path: string
  method: string
  operationId?: string
  summary?: string
  description?: string
  parameters?: Any[]
  requestBody?: Any
  responses?: Any
  // GraphQL specific fields
  query?: string
  mutation?: string
  subscription?: string
  variables?: Any
}

interface ApiDB extends DBSchema {
  apis: {
    key: string
    value: ApiSchema
    indexes: {
      'by-name': string
      'by-date': Date
    }
  }
}

class ApiService {
  private db: IDBPDatabase<ApiDB> | null = null
  private readonly DB_NAME = 'form-builder-api-db'
  private readonly STORE_NAME = 'apis'
  private readonly VERSION = 1

  async init() {
    if (!this.db) {
      this.db = await openDB<ApiDB>(this.DB_NAME, this.VERSION, {
        upgrade(db) {
          const store = db.createObjectStore('apis', { keyPath: 'id' })
          store.createIndex('by-name', 'name')
          store.createIndex('by-date', 'createdAt')
        },
      })
    }

    return this.db
  }

  async saveApiSchema(apiSchema: ApiSchema): Promise<void> {
    console.log('save api schema', apiSchema)
    const db = await this.init()
    const apiWithDates = {
      ...apiSchema,
      createdAt: apiSchema.createdAt || new Date(),
    }
    await db.put(this.STORE_NAME, apiWithDates)
  }

  async getApiSchema(id: string): Promise<ApiSchema | undefined> {
    const db = await this.init()

    return db.get(this.STORE_NAME, id)
  }

  async getAllApiSchemas(): Promise<ApiSchema[]> {
    const db = await this.init()

    return db.getAll(this.STORE_NAME)
  }

  async searchApiSchemas(
    query: string,
    page: number = 1,
    pageSize: number = 5,
    status: number = 1
  ): Promise<{
    apis: ApiSchema[]
    total: number
    totalPages: number
  }> {
    const db = await this.init()
    const allApis = await db.getAll(this.STORE_NAME)

    // Lọc apis theo tên và status
    const filteredApis = allApis.filter(
      api =>
        api.status === status &&
        (api.name.toLowerCase().includes(query.toLowerCase()) ||
          api.description?.toLowerCase().includes(query.toLowerCase()))
    )

    // Tính toán phân trang
    const total = filteredApis.length
    const totalPages = Math.ceil(total / pageSize)
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      apis: filteredApis.slice(start, end),
      total,
      totalPages,
    }
  }

  async deleteApiSchema(id: string): Promise<void> {
    const db = await this.init()
    await db.delete(this.STORE_NAME, id)
  }

  async updateApiSchema(apiSchema: ApiSchema): Promise<void> {
    const db = await this.init()
    const apiWithDates = {
      ...apiSchema,
      updatedAt: new Date(),
    }
    await db.put(this.STORE_NAME, apiWithDates)
  }

  async updateApiSchemaStatus(apiId: string, status: number): Promise<void> {
    const db = await this.init()
    const api = await db.get(this.STORE_NAME, apiId)

    if (api) {
      await db.put(this.STORE_NAME, { ...api, status })
    }
  }

  async deleteApiSchemaPermanently(apiId: string): Promise<void> {
    const db = await this.init()
    await db.delete(this.STORE_NAME, apiId)
  }

  // Parse API schema và trích xuất endpoints
  parseApiToEndpoints(apiSchema: Any, type: ApiType): ApiEndpoint[] {
    if (type === 'swagger') {
      return this.parseSwaggerToEndpoints(apiSchema)
    } else if (type === 'graphql') {
      return this.parseGraphQLToEndpoints(apiSchema)
    }

    return []
  }

  // Parse Swagger JSON và trích xuất endpoints
  parseSwaggerToEndpoints(swagger: Any): ApiEndpoint[] {
    const endpoints: ApiEndpoint[] = []

    if (!swagger.paths) return endpoints

    Object.entries(swagger.paths).forEach(([path, pathItem]: [string, Any]) => {
      Object.entries(pathItem).forEach(([method, operation]: [string, Any]) => {
        if (['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(method)) {
          endpoints.push({
            path,
            method: method.toUpperCase(),
            operationId: operation.operationId,
            summary: operation.summary,
            description: operation.description,
            parameters: operation.parameters,
            requestBody: operation.requestBody,
            responses: operation.responses,
          })
        }
      })
    })

    return endpoints
  }

  // Parse GraphQL Schema và trích xuất operations
  parseGraphQLToEndpoints(graphqlSchema: Any): ApiEndpoint[] {
    const endpoints: ApiEndpoint[] = []

    try {
      // Parse GraphQL introspection query result hoặc SDL
      if (graphqlSchema.data?.__schema) {
        // Introspection query result
        const schema = graphqlSchema.data.__schema

        // Parse queries
        const queryType = schema.types.find((type: Any) => type.name === schema.queryType?.name)

        if (queryType?.fields) {
          queryType.fields.forEach((field: Any) => {
            endpoints.push({
              path: `/graphql`,
              method: 'POST',
              operationId: `query_${field.name}`,
              summary: field.description || field.name,
              description: field.description,
              query: field.name,
              parameters: field.args,
            })
          })
        }

        // Parse mutations
        const mutationType = schema.types.find((type: Any) => type.name === schema.mutationType?.name)

        if (mutationType?.fields) {
          mutationType.fields.forEach((field: Any) => {
            endpoints.push({
              path: `/graphql`,
              method: 'POST',
              operationId: `mutation_${field.name}`,
              summary: field.description || field.name,
              description: field.description,
              mutation: field.name,
              parameters: field.args,
            })
          })
        }

        // Parse subscriptions
        const subscriptionType = schema.types.find((type: Any) => type.name === schema.subscriptionType?.name)

        if (subscriptionType?.fields) {
          subscriptionType.fields.forEach((field: Any) => {
            endpoints.push({
              path: `/graphql`,
              method: 'WS',
              operationId: `subscription_${field.name}`,
              summary: field.description || field.name,
              description: field.description,
              subscription: field.name,
              parameters: field.args,
            })
          })
        }
      } else if (typeof graphqlSchema === 'string') {
        // SDL (Schema Definition Language) - Parse với support cho arguments
        const queries = graphqlSchema.match(/type Query\s*{([^}]*)}/)?.[1]
        const mutations = graphqlSchema.match(/type Mutation\s*{([^}]*)}/)?.[1]
        const subscriptions = graphqlSchema.match(/type Subscription\s*{([^}]*)}/)?.[1]

        // Helper function để parse field với arguments
        const parseSDLField = (fieldStr: string, operationType: 'query' | 'mutation' | 'subscription') => {
          // Match pattern: fieldName(arg1: Type!, arg2: Type): ReturnType
          const fieldMatch = fieldStr.match(/(\w+)\s*(?:\(([^)]*)\))?\s*:\s*([^,\n!]+!?)/)

          if (!fieldMatch) return null

          // const [, fieldName, argsStr, returnType] = fieldMatch
          const [, fieldName, argsStr] = fieldMatch

          // Parse arguments nếu có
          const parameters: Any[] = []

          if (argsStr && argsStr.trim()) {
            // Split arguments by comma, nhưng cẩn thận với nested types
            const argPairs = argsStr
              .split(',')
              .map(arg => arg.trim())
              .filter(arg => arg)

            argPairs.forEach(argPair => {
              // Match pattern: argName: Type! or argName: Type
              const argMatch = argPair.match(/(\w+)\s*:\s*(.+)/)

              if (argMatch) {
                const [, argName, argType] = argMatch
                const isRequired = argType?.includes('!')
                const cleanType = argType?.replace('!', '').trim()

                parameters.push({
                  name: argName,
                  type: {
                    name: cleanType,
                    kind: isRequired ? 'NON_NULL' : 'NULLABLE',
                    ofType: isRequired ? { name: cleanType } : null,
                  },
                  description: `${argName} parameter`,
                  defaultValue: null,
                })
              }
            })
          }

          const endpoint: ApiEndpoint = {
            path: `/graphql`,
            method: operationType === 'subscription' ? 'WS' : 'POST',
            operationId: `${operationType}_${fieldName}`,
            summary: `${fieldName}(${parameters.map(p => `${p.name}: ${p.type.name}${p.type.kind === 'NON_NULL' ? '!' : ''}`).join(', ')})`,
            description: `GraphQL ${operationType} operation: ${fieldName}`,
            parameters,
          }

          // Add operation-specific fields
          if (operationType === 'query') endpoint.query = fieldName
          else if (operationType === 'mutation') endpoint.mutation = fieldName
          else if (operationType === 'subscription') endpoint.subscription = fieldName

          return endpoint
        }

        if (queries) {
          const queryFields = queries.match(/(\w+)\s*(?:\([^)]*\))?\s*:\s*[^,\n]+/g) || []
          queryFields.forEach(field => {
            const endpoint = parseSDLField(field, 'query')

            if (endpoint) endpoints.push(endpoint)
          })
        }

        if (mutations) {
          const mutationFields = mutations.match(/(\w+)\s*(?:\([^)]*\))?\s*:\s*[^,\n]+/g) || []
          mutationFields.forEach(field => {
            const endpoint = parseSDLField(field, 'mutation')

            if (endpoint) endpoints.push(endpoint)
          })
        }

        if (subscriptions) {
          const subscriptionFields = subscriptions.match(/(\w+)\s*(?:\([^)]*\))?\s*:\s*[^,\n]+/g) || []
          subscriptionFields.forEach(field => {
            const endpoint = parseSDLField(field, 'subscription')

            if (endpoint) endpoints.push(endpoint)
          })
        }
      }
    } catch (error) {
      console.error('Error parsing GraphQL schema:', error)
    }

    return endpoints
  }

  // Fetch API schema từ URL
  async fetchApiSchemaFromUrl(url: string): Promise<{ schema: Any; type: ApiType }> {
    try {
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type') || ''
      const text = await response.text()

      // Tự động detect API type dựa trên content
      let schema: Any
      let type: ApiType

      if (contentType.includes('application/json') || text.trim().startsWith('{')) {
        // Có thể là JSON
        try {
          schema = JSON.parse(text)

          // Detect Swagger/OpenAPI
          if (schema.openapi || schema.swagger || schema.paths) {
            type = 'swagger'
          }
          // Detect GraphQL
          else if (schema.data?.__schema || schema.__schema) {
            type = 'graphql'
          } else {
            // Fallback: thử parse như GraphQL JSON
            type = 'graphql'
          }
        } catch (err) {
          throw new Error('Không thể parse JSON từ URL', { cause: err })
        }
      } else {
        // Có thể là GraphQL SDL (text)
        if (text.includes('type Query') || text.includes('type Mutation') || text.includes('type Subscription')) {
          schema = text.trim()
          type = 'graphql'
        } else {
          throw new Error('Định dạng schema không được hỗ trợ')
        }
      }

      return { schema, type }
    } catch (error) {
      if (error instanceof Error) {
        throw error
      }
      throw new Error('Không thể tải schema từ URL')
    }
  }

  // Sync API schema từ URL đã lưu
  async syncApiSchemaFromUrl(apiId: string): Promise<ApiSchema> {
    const existingApi = await this.getApiSchema(apiId)

    if (!existingApi) {
      throw new Error('Không tìm thấy API')
    }

    if (!existingApi.url) {
      throw new Error('API này không có URL để sync')
    }

    try {
      const { schema, type } = await this.fetchApiSchemaFromUrl(existingApi.url)
      const endpoints = this.parseApiToEndpoints(schema, type)

      const updatedApi: ApiSchema = {
        ...existingApi,
        schema,
        type,
        endpoints,
        updatedAt: new Date(),
      }

      await this.updateApiSchema(updatedApi)

      return updatedApi
    } catch (error) {
      throw new Error(`Không thể sync API: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

export const apiService = new ApiService()
