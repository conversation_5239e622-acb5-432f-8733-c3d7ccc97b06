import { type UsageType } from '@/app/(full-screen)/forms/e-form/endpoint-usage-modal'
import { type FormElement, FormElementJson } from '@/app/(full-screen)/forms/e-form/form-builder'

import { Any } from '../types'
import { type ApiEndpoint, type ApiSchema } from './json-form-api-service'

interface GeneratedForm {
  elements: FormElement[]
  metadata: {
    apiEndpoint: ApiEndpoint
    apiSchema: ApiSchema
    usageType: UsageType
    generatedAt: Date
  }
}

interface SelectOption {
  label: string
  value: string
}

interface FrameworkConfig {
  componentImports: { [component: string]: string }
  utilImports: string[]
  baseImports: string[]
}

const FRAMEWORK_CONFIGS: { [framework: string]: FrameworkConfig } = {
  shadcn: {
    componentImports: {
      Input: '@workspace/ui/components/input',
      Textarea: '@workspace/ui/components/textarea',
      Button: '@workspace/ui/components/button',
      Form: '@workspace/ui/components/form',
      FormControl: '@workspace/ui/components/form',
      FormField: '@workspace/ui/components/form',
      FormItem: '@workspace/ui/components/form',
      FormLabel: '@workspace/ui/components/form',
      FormMessage: '@workspace/ui/components/form',
      Checkbox: '@workspace/ui/components/checkbox',
      RadioGroup: '@workspace/ui/components/radio-group',
      RadioGroupItem: '@workspace/ui/components/radio-group',
      Slider: '@workspace/ui/components/slider',
      Combobox: '@workspace/ui/mi/combobox',
    },
    utilImports: ['import { toast } from "@workspace/ui/components/toast"'],
    baseImports: [
      `import { zodResolver } from '@hookform/resolvers/zod'`,
      `import { useForm } from 'react-hook-form'`,
      `import { useTranslations } from 'next-intl'`,
      `import { useState } from 'react'`,
      `import * as z from 'zod'`,
    ],
  },
  mui: {
    componentImports: {
      Input: '@mui/material/TextField',
      Textarea: '@mui/material/TextField',
      Button: '@mui/material/Button',
      Label: '@mui/material/FormLabel',
      Checkbox: '@mui/material/Checkbox',
      RadioGroup: '@mui/material/RadioGroup',
      RadioGroupItem: '@mui/material/FormControlLabel',
      Slider: '@mui/material/Slider',
    },
    utilImports: ['import { toast } from "react-toastify"'],
    baseImports: [
      `import { zodResolver } from '@hookform/resolvers/zod'`,
      `import { useForm } from 'react-hook-form'`,
      `import { useTranslations } from 'next-intl'`,
      `import { useState } from 'react'`,
      `import * as z from 'zod'`,
    ],
  },
  antd: {
    componentImports: {
      Input: 'antd/es/input',
      Textarea: 'antd/es/input',
      Button: 'antd/es/button',
      Label: 'antd/es/form',
      Checkbox: 'antd/es/checkbox',
      RadioGroup: 'antd/es/radio',
      Slider: 'antd/es/slider',
    },
    utilImports: ['import { message } from "antd"'],
    baseImports: [
      `import { zodResolver } from '@hookform/resolvers/zod'`,
      `import { useForm } from 'react-hook-form'`,
      `import { useTranslations } from 'next-intl'`,
      `import { useState } from 'react'`,
      `import * as z from 'zod'`,
    ],
  },
}

class FormGeneratorCodeService {
  generateReactCode(
    json: FormElementJson,
    moduleName: string,
    formName: string,
    framework: string = 'shadcn',
    options?: Any
  ): { [fileName: string]: string } {
    const modelName = options?.modelName || formName
    const pascalFormName = this.toPascalCase(formName)
    const camelFormName = this.toCamelCase(formName)
    const prefixPageFolder = options?.prefixPageFolder || '(admin)'

    // Collect all unique imports and dependencies
    const imports = new Set<string>()
    const zodImports = new Set<string>()
    const formComponents = new Set<string>()

    // Analyze elements to determine required imports
    this.analyzeElementsForImports(json.elements, imports, zodImports, formComponents)

    const frameworkConfig = FRAMEWORK_CONFIGS[framework] || FRAMEWORK_CONFIGS.shadcn

    return {
      [`app/${prefixPageFolder}/${moduleName}/forms/${camelFormName}-form.tsx`]: this.generateFormComponent(
        json,
        moduleName,
        modelName,
        pascalFormName,
        camelFormName,
        imports,
        zodImports,
        formComponents,
        frameworkConfig as FrameworkConfig
      ),
      [`messages/${moduleName}/vi.json`]: this.generateViLocale(json, moduleName, modelName),
      [`messages/${moduleName}/en.json`]: this.generateEnLocale(json, moduleName, modelName),
      [`app/${prefixPageFolder}/${moduleName}/types/${camelFormName}.ts`]: this.generateTypesFile(json, pascalFormName),
      [`app/${prefixPageFolder}/${moduleName}/validations/${camelFormName}-schema.ts`]: this.generateValidationSchema(
        json,
        pascalFormName,
        zodImports,
        moduleName,
        modelName
      ),
    }
  }

  private toPascalCase(str: string): string {
    return str.replace(/(?:^|[\s-_])(\w)/g, (_, char) => char.toUpperCase()).replace(/[\s-_]/g, '')
  }

  private toCamelCase(str: string): string {
    const pascal = this.toPascalCase(str)

    return pascal.charAt(0).toLowerCase() + pascal.slice(1)
  }

  private toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase()
  }

  private analyzeElementsForImports(
    elements: FormElement[],
    imports: Set<string>,
    zodImports: Set<string>,
    formComponents: Set<string>
  ): void {
    const analyzeElement = (element: FormElement) => {
      // Add UI component imports
      switch (element.type) {
        case 'input':
          imports.add('Input')
          formComponents.add('Input')
          zodImports.add('string')
          break
        case 'textarea':
          imports.add('Textarea')
          formComponents.add('Textarea')
          zodImports.add('string')
          break
        case 'select':
        case 'select-api':
          formComponents.add('Combobox')
          zodImports.add('string')
          break
        case 'checkbox':
          imports.add('Checkbox')
          formComponents.add('Checkbox')
          zodImports.add('boolean')
          break
        case 'radio':
          imports.add('RadioGroup')
          imports.add('RadioGroupItem')
          formComponents.add('RadioGroup')
          zodImports.add('string')
          break
        case 'button':
          imports.add('Button')
          formComponents.add('Button')
          break
        case 'date-picker':
          formComponents.add('DatePicker')
          zodImports.add('date')
          break
        case 'file-upload':
          formComponents.add('FileUpload')
          zodImports.add('any')
          break
        case 'rating':
          formComponents.add('Rating')
          zodImports.add('number')
          break
        case 'slider':
          imports.add('Slider')
          formComponents.add('Slider')
          zodImports.add('number')
          break
      }

      // Check validation rules for additional zod imports
      if (element.validationRules) {
        element.validationRules.forEach(rule => {
          switch (rule.type) {
            case 'email':
              zodImports.add('email')
              break
            case 'min':
            case 'max':
              zodImports.add('min')
              zodImports.add('max')
              break
            case 'regex':
              zodImports.add('regex')
              break
          }
        })
      }

      // Recursively check children
      if (element.children) {
        element.children.forEach(analyzeElement)
      }
    }

    elements.forEach(analyzeElement)

    // Always add Form components for shadcn
    imports.add('Form')
    imports.add('FormControl')
    imports.add('FormField')
    imports.add('FormItem')
    imports.add('FormLabel')
    imports.add('FormMessage')
    imports.add('Button')
  }

  private generateOptimizedImports(componentNames: string[], frameworkConfig: FrameworkConfig): string[] {
    // Group components by their import source
    const importGroups: { [source: string]: string[] } = {}

    componentNames.forEach(component => {
      const importPath = frameworkConfig.componentImports[component]

      if (importPath) {
        if (!importGroups[importPath]) {
          importGroups[importPath] = []
        }
        importGroups[importPath].push(component)
      }
    })

    // Generate grouped import statements
    return Object.entries(importGroups).map(([source, components]) => {
      // Remove duplicate components and sort them
      const uniqueComponents = [...new Set(components)].sort()

      return `import { ${uniqueComponents.join(', ')} } from '${source}'`
    })
  }

  private generateFormComponent(
    json: FormElementJson,
    moduleName: string,
    modelName: string,
    pascalFormName: string,
    camelFormName: string,
    imports: Set<string>,
    zodImports: Set<string>,
    formComponents: Set<string>,
    frameworkConfig: FrameworkConfig
  ): string {
    const importsArray = Array.from(imports)
    const hasCombobox = formComponents.has('Combobox')

    // Generate optimized imports
    const componentImports = this.generateOptimizedImports(importsArray, frameworkConfig)

    const importStatements = [
      `'use client'`,
      ``,
      ...frameworkConfig.baseImports,
      ``,
      ...componentImports,
      hasCombobox
        ? `import { Combobox } from '${frameworkConfig.componentImports['Combobox'] || '@workspace/ui/mi/combobox'}'`
        : '',
      ...frameworkConfig.utilImports,
      ``,
      `import { create${pascalFormName}Schema, type ${pascalFormName}FormValues } from '../validations/${camelFormName}-schema'`,
      ``,
      ``,
    ].filter(Boolean)

    const formFields = this.generateFormFields(json.elements, moduleName, modelName, 0)
    const submitHandler = this.generateSubmitHandler(moduleName, modelName, pascalFormName)

    return `${importStatements.join('\n')}

interface ${pascalFormName}FormProps {
  initialData?: Partial<${pascalFormName}FormValues>
  onSuccess: () => void
  onCancel: () => void
}

export function ${pascalFormName}Form({
  initialData,
  onSuccess,
  onCancel,
}: ${pascalFormName}FormProps) {
  const t = useTranslations()
  const [loading, setLoading] = useState(false)

  const form = useForm<${pascalFormName}FormValues>({
    resolver: zodResolver(create${pascalFormName}Schema(t)),
    defaultValues: {
      ${this.generateDefaultValues(json.elements)}
    },
  })

${submitHandler}

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
${formFields}
        
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} type="button">
            {t('action.cancel.label')}
          </Button>
          <Button type="submit" disabled={loading}>
            {loading
              ? t('${moduleName}.${modelName}.action.loading')
              : initialData
                ? t('${moduleName}.${modelName}.action.update')
                : t('${moduleName}.${modelName}.action.create')}
          </Button>
        </div>
      </form>
    </Form>
  )
}
`
  }

  private generateFormFields(
    elements: FormElement[],
    moduleName: string,
    modelName: string,
    indentLevel: number
  ): string {
    const indent = '  '.repeat(indentLevel + 3) // Base indent for form content

    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const allFormFields = collectFormFields(elements)

    return allFormFields
      .map(element => {
        const fieldKey = `${moduleName}.${modelName}.field.${element.name}`

        switch (element.type) {
          case 'input':
            return this.generateInputField(element, fieldKey, indent)
          case 'textarea':
            return this.generateTextareaField(element, fieldKey, indent)
          case 'select':
            return this.generateSelectField(element, fieldKey, indent)
          case 'select-api':
            return this.generateApiSelectField(element, fieldKey, indent)
          case 'checkbox':
            return this.generateCheckboxField(element, fieldKey, indent)
          case 'radio':
            return this.generateRadioField(element, fieldKey, indent)
          case 'date-picker':
            return this.generateDatePickerField(element, fieldKey, indent)
          case 'file-upload':
            return this.generateFileUploadField(element, fieldKey, indent)
          case 'rating':
            return this.generateRatingField(element, fieldKey, indent)
          case 'slider':
            return this.generateSliderField(element, fieldKey, indent)
          default:
            return ''
        }
      })
      .join('\n\n')
  }

  private generateInputField(element: FormElement, fieldKey: string, indent: string): string {
    const inputType = element.customAttributes?.type || 'text'

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Input
${indent}          type="${inputType}"
${indent}          placeholder={t('${fieldKey}.placeholder')}
${indent}          {...field}
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateTextareaField(element: FormElement, fieldKey: string, indent: string): string {
    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Textarea
${indent}          placeholder={t('${fieldKey}.placeholder')}
${indent}          {...field}
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateSelectField(element: FormElement, fieldKey: string, indent: string): string {
    const options = element.options || []
    const optionsCode = options.map(opt => `          { value: '${opt.value}', label: '${opt.label}' }`).join(',\n')

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Combobox
${indent}          options={[
${optionsCode}
${indent}          ]}
${indent}          placeholder={t('${fieldKey}.placeholder')}
${indent}          value={field.value || ''}
${indent}          onChange={field.onChange}
${indent}          labelInValue={false}
${indent}          className="w-full"
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateApiSelectField(element: FormElement, fieldKey: string, indent: string): string {
    const apiUrl = element.apiConfig?.url || ''

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Combobox
${indent}          placeholder={t('${fieldKey}.placeholder')}
${indent}          value={field.value || ''}
${indent}          onChange={field.onChange}
${indent}          labelInValue={false}
${indent}          apiRequest={async (keyword) => {
${indent}            try {
${indent}              const response = await fetch('${apiUrl}' + (keyword ? \`?search=\${keyword}\` : ''))
${indent}              const data = await response.json()
${indent}              return data.map((item: any) => ({
${indent}                value: item.id || item.value,
${indent}                label: item.name || item.label
${indent}              }))
${indent}            } catch (error) {
${indent}              console.error('API request failed:', error)
${indent}              return []
${indent}            }
${indent}          }}
${indent}          className="w-full"
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateCheckboxField(element: FormElement, fieldKey: string, indent: string): string {
    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
${indent}      <FormControl>
${indent}        <Checkbox
${indent}          checked={field.value}
${indent}          onCheckedChange={field.onChange}
${indent}        />
${indent}      </FormControl>
${indent}      <div className="space-y-1 leading-none">
${indent}        <FormLabel>
${indent}          {t('${fieldKey}.label')}
${indent}        </FormLabel>
${indent}      </div>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateRadioField(element: FormElement, fieldKey: string, indent: string): string {
    const options = element.options || []
    const optionsCode = options
      .map(
        opt =>
          `${indent}        <div className="flex items-center space-x-2">
${indent}          <RadioGroupItem value="${opt.value}" id="${element.name}_${opt.value}" />
${indent}          <label htmlFor="${element.name}_${opt.value}">${opt.label}</label>
${indent}        </div>`
      )
      .join('\n')

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <RadioGroup
${indent}          onValueChange={field.onChange}
${indent}          defaultValue={field.value}
${indent}        >
${optionsCode}
${indent}        </RadioGroup>
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateDatePickerField(element: FormElement, fieldKey: string, indent: string): string {
    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Input
${indent}          type="date"
${indent}          placeholder={t('${fieldKey}.placeholder')}
${indent}          {...field}
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateFileUploadField(element: FormElement, fieldKey: string, indent: string): string {
    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Input
${indent}          type="file"
${indent}          onChange={(e) => field.onChange(e.target.files)}
${indent}        />
${indent}      </FormControl>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateRatingField(element: FormElement, fieldKey: string, indent: string): string {
    const max = element.customAttributes?.max || 5

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Input
${indent}          type="range"
${indent}          min="0"
${indent}          max="${max}"
${indent}          step="1"
${indent}          {...field}
${indent}          onChange={(e) => field.onChange(Number(e.target.value))}
${indent}        />
${indent}      </FormControl>
${indent}      <div className="text-sm text-muted-foreground">
${indent}        {field.value || 0} / ${max}
${indent}      </div>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateSliderField(element: FormElement, fieldKey: string, indent: string): string {
    const min = element.customAttributes?.min || 0
    const max = element.customAttributes?.max || 100
    const step = element.customAttributes?.step || 1

    return `${indent}<FormField
${indent}  control={form.control}
${indent}  name="${element.name}"
${indent}  render={({ field }) => (
${indent}    <FormItem>
${indent}      <FormLabel>
${indent}        {t('${fieldKey}.label')}
${indent}      </FormLabel>
${indent}      <FormControl>
${indent}        <Slider
${indent}          min={${min}}
${indent}          max={${max}}
${indent}          step={${step}}
${indent}          value={[field.value || ${min}]}
${indent}          onValueChange={(values) => field.onChange(values[0])}
${indent}          className="w-full"
${indent}        />
${indent}      </FormControl>
${indent}      <div className="text-sm text-muted-foreground">
${indent}        {field.value || ${min}}
${indent}      </div>
${indent}      <FormMessage />
${indent}    </FormItem>
${indent}  )}
${indent}/>`
  }

  private generateDefaultValues(elements: FormElement[]): string {
    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const formElements = collectFormFields(elements)

    return formElements
      .map(element => {
        const defaultValue = this.getFieldDefaultValue(element)

        return `${element.name}: initialData?.${element.name} || ${defaultValue}`
      })
      .join(',\n      ')
  }

  private getFieldDefaultValue(element: FormElement): string {
    switch (element.type) {
      case 'checkbox':
        return 'false'
      case 'rating':
      case 'slider':
        return '0'
      case 'date-picker':
        return 'undefined'
      case 'file-upload':
        return 'undefined'
      default:
        return `''`
    }
  }

  private generateSubmitHandler(moduleName: string, modelName: string, pascalFormName: string): string {
    return `  const onSubmit = async (data: ${pascalFormName}FormValues) => {
    try {
      setLoading(true)
      
      // TODO: Replace with your actual API endpoint
      const response = await fetch('/api/${modelName}', {
        method: initialData ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to save data')
      }

      toast.success(
        initialData 
          ? t('${moduleName}.${modelName}.messages.updateSuccess')
          : t('${moduleName}.${modelName}.messages.createSuccess')
      )
      onSuccess()
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error(
        error instanceof Error 
          ? error.message 
          : t('${moduleName}.${modelName}.messages.error')
      )
    } finally {
      setLoading(false)
    }
  }`
  }

  private generateValidationSchema(
    json: FormElementJson,
    pascalFormName: string,
    zodImports: Set<string>,
    moduleName?: string,
    modelName?: string
  ): string {
    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const formElements = collectFormFields(json.elements)

    // Generate schema factory function with i18n support
    const zodFields = formElements
      .map(element => {
        return this.generateZodFieldWithI18n(element, moduleName, modelName)
      })
      .filter(Boolean)
      .join(',\n    ')

    return `import * as z from 'zod'

export const create${pascalFormName}Schema = (t: (key: string) => string) => z.object({
  ${zodFields}
})

export type ${pascalFormName}FormValues = z.infer<ReturnType<typeof create${pascalFormName}Schema>>
`
  }

  private generateZodFieldWithI18n(element: FormElement, moduleName?: string, modelName?: string): string {
    const fieldKey =
      moduleName && modelName ? `${moduleName}.${modelName}.field.${element.name}` : `fields.${element.name}`
    let zodType = ''

    switch (element.type) {
      case 'input':
        if (element.customAttributes?.type === 'email') {
          zodType = `z.string().email(t('${fieldKey}.validation.email'))`
        } else if (element.customAttributes?.type === 'number') {
          zodType = 'z.number()'
        } else {
          zodType = 'z.string()'
        }
        break
      case 'textarea':
        zodType = 'z.string()'
        break
      case 'select':
      case 'select-api':
        if (element.options && element.options.length > 0) {
          const enumValues = element.options.map(opt => `"${opt.value}"`).join(', ')
          zodType = `z.enum([${enumValues}])`
        } else {
          zodType = 'z.string()'
        }
        break
      case 'checkbox':
        zodType = 'z.boolean()'
        break
      case 'radio':
        if (element.options && element.options.length > 0) {
          const enumValues = element.options.map(opt => `"${opt.value}"`).join(', ')
          zodType = `z.enum([${enumValues}])`
        } else {
          zodType = 'z.string()'
        }
        break
      case 'date-picker':
        zodType = 'z.date()'
        break
      case 'file-upload':
        zodType = 'z.any()'
        break
      case 'rating':
      case 'slider':
        zodType = 'z.number()'
        break
      default:
        zodType = 'z.string()'
    }

    // Handle required validation - either from required flag or validation rules
    const hasRequiredRule = element.validationRules?.some(rule => rule.type === 'required')
    const isRequired = element.required || hasRequiredRule

    // Apply validation rules with i18n - only add validations that are actually defined
    const validationRules = element.validationRules || []

    if (validationRules.length > 0) {
      validationRules.forEach(rule => {
        switch (rule.type) {
          case 'required':
            zodType += `.min(1, t('${fieldKey}.validation.required'))`
            break
          case 'email':
            if (!zodType.includes('email(')) {
              zodType = zodType.replace('z.string()', `z.string().email(t('${fieldKey}.validation.email'))`)
            }
            break
          case 'min':
            if (element.type === 'input' && element.customAttributes?.type !== 'number') {
              zodType += `.min(${rule.value}, t('${fieldKey}.validation.minLength', { min: ${rule.value} }))`
            } else if (element.type === 'input' && element.customAttributes?.type === 'number') {
              zodType += `.min(${rule.value}, t('${fieldKey}.validation.minValue', { min: ${rule.value} }))`
            }
            break
          case 'max':
            if (element.type === 'input' && element.customAttributes?.type !== 'number') {
              zodType += `.max(${rule.value}, t('${fieldKey}.validation.maxLength', { max: ${rule.value} }))`
            } else if (element.type === 'input' && element.customAttributes?.type === 'number') {
              zodType += `.max(${rule.value}, t('${fieldKey}.validation.maxValue', { max: ${rule.value} }))`
            }
            break
          case 'regex':
            if (rule.value) {
              zodType += `.regex(new RegExp('${rule.value}'), t('${fieldKey}.validation.pattern'))`
            }
            break
        }
      })
    } else if (isRequired) {
      // Only add required validation if field is marked as required and no other validation rules exist
      zodType += `.min(1, t('${fieldKey}.validation.required'))`
    }

    // Add optional if not required
    if (!isRequired) {
      zodType += '.optional()'
    }

    return `${element.name}: ${zodType}`
  }

  private generateZodField(element: FormElement): string {
    let zodType = 'z.string()'

    switch (element.type) {
      case 'input':
        if (element.customAttributes?.type === 'email') {
          zodType = 'z.string().email()'
        } else if (element.customAttributes?.type === 'number') {
          zodType = 'z.number()'
        } else {
          zodType = 'z.string()'
        }
        break
      case 'textarea':
        zodType = 'z.string()'
        break
      case 'select':
      case 'select-api':
        if (element.options && element.options.length > 0) {
          const enumValues = element.options.map(opt => `"${opt.value}"`).join(', ')
          zodType = `z.enum([${enumValues}])`
        } else {
          zodType = 'z.string()'
        }
        break
      case 'checkbox':
        zodType = 'z.boolean()'
        break
      case 'radio':
        if (element.options && element.options.length > 0) {
          const enumValues = element.options.map(opt => `"${opt.value}"`).join(', ')
          zodType = `z.enum([${enumValues}])`
        } else {
          zodType = 'z.string()'
        }
        break
      case 'date-picker':
        zodType = 'z.date().optional()'
        break
      case 'file-upload':
        zodType = 'z.any().optional()'
        break
      case 'rating':
      case 'slider':
        zodType = 'z.number()'
        break
      default:
        zodType = 'z.string()'
    }

    // Apply validation rules
    if (element.validationRules) {
      element.validationRules.forEach(rule => {
        switch (rule.type) {
          case 'required':
            if (!zodType.includes('optional()')) {
              // Already required by default
            }
            break
          case 'email':
            if (!zodType.includes('email()')) {
              zodType = zodType.replace('z.string()', 'z.string().email()')
            }
            break
          case 'min':
            if (element.type === 'input' && element.customAttributes?.type !== 'number') {
              zodType = zodType.replace(')', `.min(${rule.value})`)
            } else if (['rating', 'slider'].includes(element.type)) {
              zodType = zodType.replace(')', `.min(${rule.value})`)
            }
            break
          case 'max':
            if (element.type === 'input' && element.customAttributes?.type !== 'number') {
              zodType = zodType.replace(')', `.max(${rule.value})`)
            } else if (['rating', 'slider'].includes(element.type)) {
              zodType = zodType.replace(')', `.max(${rule.value})`)
            }
            break
          case 'regex':
            zodType = zodType.replace(')', `.regex(/${rule.value}/)`)
            break
        }
      })
    }

    // Make optional if not required
    if (!element.required && !zodType.includes('optional()') && !zodType.includes('boolean()')) {
      zodType = zodType.replace(')', '.optional()')
    }

    return `${element.name}: ${zodType}`
  }

  private generateTypesFile(json: FormElementJson, pascalFormName: string): string {
    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const formElements = collectFormFields(json.elements)

    const typeFields = formElements
      .map(element => {
        const typeStr = this.getTypeScriptType(element)
        const optional = !element.required ? '?' : ''

        return `  ${element.name}${optional}: ${typeStr}`
      })
      .join('\n')

    return `export interface ${pascalFormName}Data {
${typeFields}
}

export interface ${pascalFormName}FormProps {
  initialData?: Partial<${pascalFormName}Data>
  onSuccess: () => void
  onCancel: () => void
}
`
  }

  private getTypeScriptType(element: FormElement): string {
    switch (element.type) {
      case 'input':
        if (element.customAttributes?.type === 'number') {
          return 'number'
        }

        return 'string'
      case 'textarea':
        return 'string'
      case 'select':
      case 'select-api':
        if (element.options && element.options.length > 0) {
          const unionTypes = element.options.map(opt => `'${opt.value}'`).join(' | ')

          return unionTypes
        }

        return 'string'
      case 'checkbox':
        return 'boolean'
      case 'radio':
        if (element.options && element.options.length > 0) {
          const unionTypes = element.options.map(opt => `'${opt.value}'`).join(' | ')

          return unionTypes
        }

        return 'string'
      case 'date-picker':
        return 'Date'
      case 'file-upload':
        return 'File | FileList'
      case 'rating':
      case 'slider':
        return 'number'
      default:
        return 'string'
    }
  }

  private generateViLocale(json: FormElementJson, moduleName: string, modelName: string): string {
    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const formElements = collectFormFields(json.elements)

    const localeData: { [key: string]: string } = {}

    // Add form fields
    formElements.forEach(element => {
      const fieldName = element.name || 'field'
      const humanizedName = this.humanize(fieldName)

      localeData[`${moduleName}.${modelName}.field.${fieldName}.label`] = element.label || humanizedName
      localeData[`${moduleName}.${modelName}.field.${fieldName}.placeholder`] =
        element.placeholder || `Nhập ${humanizedName.toLowerCase()}`

      // Only add validation messages that are actually used
      const validationRules = element.validationRules || []
      const hasValidationRules = validationRules.length > 0

      // Add required validation if field is required
      if (element.required) {
        localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.required`] = `${humanizedName} là bắt buộc`
      }

      // Add validation messages only for defined rules
      if (hasValidationRules) {
        validationRules.forEach(rule => {
          switch (rule.type) {
            case 'required':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.required`] =
                `${humanizedName} là bắt buộc`
              break
            case 'email':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.email`] =
                `${humanizedName} phải là email hợp lệ`
              break
            case 'min':
              if (element.type === 'input' && element.customAttributes?.type !== 'number') {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.minLength`] =
                  `${humanizedName} phải có ít nhất {min} ký tự`
              } else {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.minValue`] =
                  `${humanizedName} phải ít nhất {min}`
              }
              break
            case 'max':
              if (element.type === 'input' && element.customAttributes?.type !== 'number') {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.maxLength`] =
                  `${humanizedName} không được vượt quá {max} ký tự`
              } else {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.maxValue`] =
                  `${humanizedName} không được vượt quá {max}`
              }
              break
            case 'regex':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.pattern`] =
                `${humanizedName} không đúng định dạng`
              break
          }
        })
      }
    })

    // Add action buttons
    localeData[`${moduleName}.${modelName}.action.create`] = 'Tạo mới'
    localeData[`${moduleName}.${modelName}.action.update`] = 'Cập nhật'
    // localeData[`${moduleName}.${modelName}.action.cancel`] = "Hủy bỏ"
    localeData[`${moduleName}.${modelName}.action.loading`] = 'Đang xử lý...'

    // Add messages
    localeData[`${moduleName}.${modelName}.messages.createSuccess`] = 'Tạo mới thành công'
    localeData[`${moduleName}.${modelName}.messages.updateSuccess`] = 'Cập nhật thành công'
    localeData[`${moduleName}.${modelName}.messages.error`] = 'Có lỗi xảy ra'

    return JSON.stringify(localeData, null, 2)
  }

  private generateEnLocale(json: FormElementJson, moduleName: string, modelName: string): string {
    // Recursively collect all form fields from nested elements
    const collectFormFields = (elements: FormElement[]): FormElement[] => {
      const formFields: FormElement[] = []

      for (const element of elements) {
        // If it's a form field with name, add it
        if (
          element.name &&
          [
            'input',
            'textarea',
            'select',
            'select-api',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          formFields.push(element)
        }

        // If it has children, recursively collect from children
        if (element.children && element.children.length > 0) {
          formFields.push(...collectFormFields(element.children))
        }
      }

      return formFields
    }

    const formElements = collectFormFields(json.elements)

    const localeData: { [key: string]: string } = {}

    // Add form fields
    formElements.forEach(element => {
      const fieldName = element.name || 'field'
      const humanizedName = this.humanize(fieldName)

      localeData[`${moduleName}.${modelName}.field.${fieldName}.label`] = element.label || humanizedName
      localeData[`${moduleName}.${modelName}.field.${fieldName}.placeholder`] =
        element.placeholder || `Enter ${humanizedName.toLowerCase()}`

      // Only add validation messages that are actually used
      const validationRules = element.validationRules || []
      const hasValidationRules = validationRules.length > 0

      // Add required validation if field is required
      if (element.required) {
        localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.required`] = `${humanizedName} is required`
      }

      // Add validation messages only for defined rules
      if (hasValidationRules) {
        validationRules.forEach(rule => {
          switch (rule.type) {
            case 'required':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.required`] =
                `${humanizedName} is required`
              break
            case 'email':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.email`] =
                `${humanizedName} must be a valid email`
              break
            case 'min':
              if (element.type === 'input' && element.customAttributes?.type !== 'number') {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.minLength`] =
                  `${humanizedName} must be at least {min} characters`
              } else {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.minValue`] =
                  `${humanizedName} must be at least {min}`
              }
              break
            case 'max':
              if (element.type === 'input' && element.customAttributes?.type !== 'number') {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.maxLength`] =
                  `${humanizedName} must not exceed {max} characters`
              } else {
                localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.maxValue`] =
                  `${humanizedName} must not exceed {max}`
              }
              break
            case 'regex':
              localeData[`${moduleName}.${modelName}.field.${fieldName}.validation.pattern`] =
                `${humanizedName} format is invalid`
              break
          }
        })
      }
    })

    // Add action buttons
    localeData[`${moduleName}.${modelName}.action.create`] = 'Create'
    localeData[`${moduleName}.${modelName}.action.update`] = 'Update'
    // localeData[`${moduleName}.${modelName}.action.cancel`] = "Cancel"
    localeData[`${moduleName}.${modelName}.action.loading`] = 'Processing...'

    // Add messages
    localeData[`${moduleName}.${modelName}.messages.createSuccess`] = 'Created successfully'
    localeData[`${moduleName}.${modelName}.messages.updateSuccess`] = 'Updated successfully'
    localeData[`${moduleName}.${modelName}.messages.error`] = 'An error occurred while saving data'

    return JSON.stringify(localeData, null, 2)
  }

  /**
   * Convert camelCase/snake_case to human readable
   */
  private humanize(str: string): string {
    return str
      .replace(/([A-Z])/g, ' $1')
      .replace(/[_-]/g, ' ')
      .toLowerCase()
      .replace(/^\w/, c => c.toUpperCase())
      .trim()
  }

  generateDetailComponent(
    schema: FormElementJson,
    moduleName: string,
    componentName: string,
    framework: string,
    options?: Any
  ): { [fileName: string]: string } {
    console.log('generateDetailComponent', options)
    // For now, return the same as form generation
    // TODO: Implement specific detail component generation

    return this.generateReactCode(schema, moduleName, componentName, framework)
  }

  generateTableComponent(
    schema: FormElementJson,
    moduleName: string,
    componentName: string,
    framework: string,
    options?: Any
  ): { [fileName: string]: string } {
    // Find the first table element in the schema
    const findTableElement = (elements: Any[]): Any => {
      for (const element of elements) {
        if (element.type === 'table') {
          return element
        }

        // Recursively search in children (for containers, grids, etc.)
        if (element.children && Array.isArray(element.children)) {
          const foundTable = findTableElement(element.children)

          if (foundTable) return foundTable
        }
      }

      return null
    }

    const tableElement = findTableElement(schema.elements)

    if (!tableElement) {
      throw new Error('No table element found in the form schema')
    }

    const modelName = options?.modelName || this.toKebabCase(componentName.replace(/Table$|Page$/, ''))
    const prefixPageFolder = options?.prefixPageFolder || '(admin)'
    const columns = tableElement.metadata?.columns || []
    const kebabComponentName = this.toKebabCase(componentName)
    const pascalComponentName = this.toPascalCase(componentName)

    // Generate files
    const files: { [fileName: string]: string } = {}

    // Main table component
    files[`app/${prefixPageFolder}/${moduleName}/${kebabComponentName}.page.tsx`] = this.generateTablePageComponent(
      tableElement,
      columns,
      pascalComponentName,
      framework,
      options,
      moduleName,
      modelName
    )

    // Types file
    // files[`types.d.ts`] = this.generateTableTypes(columns, pascalComponentName)

    // Locale files for table
    files[`messages/${moduleName}/vi.json`] = this.generateTableViLocale(columns, moduleName, modelName)
    files[`messages/${moduleName}/en.json`] = this.generateTableEnLocale(columns, moduleName, modelName)

    // Render components
    // files[`components/render-text.tsx`] = this.generateRenderTextComponent()
    // files[`components/render-datetime.tsx`] = this.generateRenderDateTimeComponent()
    // files[`components/render-status.tsx`] = this.generateRenderStatusComponent()
    // files[`components/render-interactive-link.tsx`] = this.generateRenderInteractiveLinkComponent()

    return files
  }

  private generateTablePageComponent(
    tableElement: Any,
    columns: Any[],
    componentName: string,
    framework: string,
    options?: Any,
    moduleName?: string,
    modelName?: string
  ): string {
    console.log('generateTablePageComponent tableElement', tableElement)
    const entityName = componentName.replace(/Table$|Page$/, '')
    const kebabEntityName = this.toKebabCase(entityName)

    // Generate columns definition
    const columnsCode = this.generateColumnsDefinition(columns, options, moduleName, modelName)

    // Generate action handlers based on options
    const actionHandlers = this.generateActionHandlers(options)

    return `'use client'

import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderStatus } from '@/components/render-status'
import { RenderText } from '@/components/render-text'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@workspace/ui/components/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import {
  generateQueryParams,
  useEnhancedTable,
} from '@workspace/ui/hooks/use-table'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import {
  DataTable,
  selectColumn,
  TABLE_ALIGN,
  TABLE_SIZE,
} from '@workspace/ui/mi/table'
import { Edit, PlusIcon, Trash2, Users, Eye } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

${this.generateEntityType(columns)}

export function ${componentName}() {
  const t = useTranslations()
  const queryClient = useQueryClient()

  // State for dialog management
  ${this.generateDialogStates(options)}

  // Action handlers
  ${actionHandlers}

  ${columnsCode}

  const { table } = useEnhancedTable<${entityName}>({
    columns,
    pageName: '${kebabEntityName}s',
    keyObject: {},
    queryFn: async state => {
      const params = generateQueryParams(state, {
        baseParams: {},
      })
      const res = await fetch(\`/api/${kebabEntityName}s?\${params}\`, {
        cache: 'no-store',
      })

      return res.json()
    },
    initialState: {
      ${options?.filtering ? `filter: [{ id: 'status', value: '1' }],` : ''}
      ${options?.sorting ? `sorting: [{ id: 'createdAt', desc: true }],` : ''}
    },
    enabled: true,
    queryKey: ['${kebabEntityName}s'],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('${kebabEntityName}.title'),
            href: '/${kebabEntityName}s',
          },
        ]}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('${kebabEntityName}.title')}
              </CardTitle>
              ${
                options?.createAction
                  ? `
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setIsCreateDialogOpen(true)}>
                  <PlusIcon className="h-4 w-4" />
                  {t('${kebabEntityName}.action.create')}
                </Button>
              </div>
              `
                  : ''
              }
            </div>
          </CardHeader>
          <CardContent>
            <DataTable table={table} />
          </CardContent>
        </Card>
      </AdminPageLayout>

      ${this.generateDialogComponents(options, entityName, kebabEntityName)}
    </>
  )
}`
  }

  private generateColumnsDefinition(columns: Any[], options?: Any, moduleName?: string, modelName?: string): string {
    const columnDefinitions = columns
      .map(column => {
        return this.generateSingleColumnDefinition(column, options, moduleName, modelName)
      })
      .join(',\n    ')

    const actionsColumn =
      options?.editAction || options?.deleteAction || options?.viewAction
        ? `
    {
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.action.label'),
      },
      cell: ({ row }) => {
        const item = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            ${
              options?.viewAction
                ? `
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedItem(item)
                setIsViewDialogOpen(true)
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            `
                : ''
            }
            ${
              options?.editAction
                ? `
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedItem(item)
                setIsEditDialogOpen(true)
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            `
                : ''
            }
            ${
              options?.deleteAction
                ? `
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedItem(item)
                setIsDeleteDialogOpen(true)
              }}
            >
              <Trash2 className="text-destructive h-4 w-4" />
            </Button>
            `
                : ''
            }
          </div>
        )
      },
    },`
        : ''

    return `const columns: ColumnDef<${this.getEntityTypeName(columns)}>[] = [
    ${options?.selectColumn !== false ? 'selectColumn(t),' : ''}
    ${columnDefinitions}${actionsColumn}
  ]`
  }

  private generateSingleColumnDefinition(column: Any, options?: Any, moduleName?: string, modelName?: string): string {
    const { key, label, dataType, sortable, filterable } = column
    console.log('generateSingleColumnDefinition column', sortable, label)

    let cellRenderer = ''
    const fieldKey = moduleName && modelName ? `${moduleName}.${modelName}.field.${key}` : `fields.${key}`
    let metaConfig = `
        title: t('${fieldKey}.label'),`

    // Add filter configuration
    if (filterable && options?.filtering) {
      switch (dataType) {
        case 'datetime':
        case 'date':
          metaConfig += `
        filter: {
          type: 'date-range',
        },`
          break
        case 'text':
          metaConfig += `
        filter: {
          type: 'text',
          placeholder: t('${fieldKey}.placeholder'),
        },`
          break
        case 'boolean':
          metaConfig += `
        filter: {
          type: 'select',
          placeholder: 'Chọn trạng thái',
          options: [
            { label: 'Có', value: 'true' },
            { label: 'Không', value: 'false' }
          ],
        },`
          break
      }
    }

    // Generate cell renderer based on data type
    switch (dataType) {
      case 'datetime':
        cellRenderer = `
      cell: ({ row }) => {
        return (
          <RenderDateTime
            datetime={row.getValue('${key}')}
            showTime={table.options.meta?.uiState?.showTime ?? false}
          />
        )
      },`
        break
      case 'date':
        cellRenderer = `
      cell: ({ row }) => {
        return (
          <RenderDateTime
            datetime={row.getValue('${key}')}
            showTime={false}
          />
        )
      },`
        break
      case 'boolean':
        cellRenderer = `
      cell: ({ row }) => {
        const value = row.getValue('${key}')
        return (
          <Badge variant={value ? 'default' : 'secondary'}>
            {value ? 'Có' : 'Không'}
          </Badge>
        )
      },`
        break
      case 'actions':
        // Actions column will be handled separately
        return ''

      default:
        if (key === 'name' || key === 'title') {
          cellRenderer = `
      cell: ({ row }) => {
        return (
          <RenderInteractiveLink
            onClick={() => {
              setSelectedItem(row.original)
              setIsEditDialogOpen(true)
            }}
          >
            {row.original.${key}}
          </RenderInteractiveLink>
        )
      },`
        } else {
          cellRenderer = `
      cell: ({ row }) => {
        return <RenderText text={row.original.${key}} />
      },`
        }
    }

    return `{
      accessorKey: '${key}',${
        dataType === 'datetime' || dataType === 'date'
          ? `
      size: TABLE_SIZE.DATETIME,`
          : ''
      }
      meta: {${metaConfig}
      },${cellRenderer}
    }`
  }

  private generateEntityType(columns: Any[]): string {
    const properties = columns
      .filter(col => col.dataType !== 'actions')
      .map(col => {
        let type = 'string'

        switch (col.dataType) {
          case 'number':
            type = 'number'
            break
          case 'boolean':
            type = 'boolean'
            break
          case 'datetime':
          case 'date':
            type = 'string'
            break
          case 'array':
            type = 'any[]'
            break
          case 'object':
            type = 'any'
            break
          default:
            type = 'string'
        }

        return `  ${col.key}: ${type}`
      })
      .join('\n')

    const entityName = this.getEntityTypeName(columns)

    return `type ${entityName} = {
${properties}
}`
  }

  private getEntityTypeName(columns: Any[]): string {
    // Try to infer entity name from first text column or use default
    const nameColumn = columns.find(col => col.key === 'name' || col.key === 'title')

    return nameColumn ? this.toPascalCase(nameColumn.key) + 'Entity' : 'DataEntity'
  }

  private generateActionHandlers(options?: Any): string {
    let handlers = ''

    if (options?.deleteAction) {
      handlers += `
  const handleDelete = async () => {
    if (!selectedItem) return

    try {
      const response = await fetch(\`/api/items/\${selectedItem.id}\`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete item')
      }

      toast.success(t('msg.deleted.success', { name: selectedItem.name || '' }))
      queryClient.invalidateQueries({ queryKey: ['items'] })
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.error(error instanceof Error ? error.message : t('common.error'))
    }
  }`
    }

    return handlers
  }

  private generateDialogStates(options?: Any): string {
    let states = `const [selectedItem, setSelectedItem] = useState<any>(null)`

    if (options?.createAction) {
      states += `\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)`
    }

    if (options?.editAction) {
      states += `\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)`
    }

    if (options?.viewAction) {
      states += `\n  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)`
    }

    if (options?.deleteAction) {
      states += `\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)`
    }

    return states
  }

  private generateDialogComponents(options: Any, entityName: string, kebabEntityName: string): string {
    console.log('generateDialogComponents entityName', entityName)
    let dialogs = ''

    if (options?.createAction) {
      dialogs += `
      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {t('${kebabEntityName}.action.createTitle')}
            </DialogTitle>
          </DialogHeader>
          {/* TODO: Add your form component here */}
          <div className="p-4">
            <p>Form component will be implemented here</p>
          </div>
        </DialogContent>
      </Dialog>`
    }

    if (options?.editAction) {
      dialogs += `
      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {t('${kebabEntityName}.action.edit')}
            </DialogTitle>
          </DialogHeader>
          {/* TODO: Add your form component here */}
          <div className="p-4">
            <p>Edit form component will be implemented here</p>
          </div>
        </DialogContent>
      </Dialog>`
    }

    if (options?.viewAction) {
      dialogs += `
      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {t('${kebabEntityName}.action.view')}
            </DialogTitle>
          </DialogHeader>
          {/* TODO: Add your detail view component here */}
          <div className="p-4">
            <p>Detail view component will be implemented here</p>
          </div>
        </DialogContent>
      </Dialog>`
    }

    if (options?.deleteAction) {
      dialogs += `
      {/* Delete Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t('${kebabEntityName}.action.delete')}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t('${kebabEntityName}.action.deleteConfirm', {
                name: selectedItem?.name || '',
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('action.cancel.label')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              {t('action.delete.label')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>`
    }

    return dialogs
  }

  //   private generateTableTypes(columns: any[], componentName: string): string {
  //     return `/* eslint-disable @typescript-eslint/triple-slash-reference */
  // /* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
  // /* eslint-disable @typescript-eslint/no-unused-vars */
  // /// <reference types="@tanstack/react-table" />
  // import { UiState } from '@/lib/json-form/tableStateUtils'
  // import '@tanstack/react-table'
  // import { LucideProps } from 'lucide-react'

  // type FilterDateRange = {
  //   type: 'date-range'
  // }

  // type FilterText = {
  //   type: 'text'
  //   placeholder?: string
  // }

  // type FilterSelect = {
  //   type: 'select'
  //   placeholder?: string
  //   options?: {
  //     label: string
  //     value: string
  //     icon?:
  //       | React.ReactNode
  //       | ForwardRefExoticComponent<
  //           Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>
  //         >
  //   }[]
  // }

  // type FilterCombobox = {
  //   type: 'combobox'
  //   placeholder?: string
  //   multiple?: boolean
  //   apiRequest?: (keyword?: string) => Promise<
  //     {
  //       label: string
  //       value: string
  //     }[]
  //   >
  //   options?: {
  //     label: string
  //     value: string
  //     icon?:
  //       | React.ReactNode
  //       | ForwardRefExoticComponent<
  //           Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>
  //         >
  //   }[]
  // }

  // declare module '@tanstack/react-table' {
  //   interface ColumnMeta<TData extends RowData, TValue> {
  //     title?: string
  //     customHeader?: boolean
  //     filter?: (FilterDateRange | FilterCombobox | FilterText | FilterSelect) & {
  //       position?: 'default' | 'advanced'
  //       label?: string // If not provided, use the column title
  //     }
  //   }

  //   interface TableMeta<TData extends unknown> {
  //     isLoading?: boolean
  //     isFetching?: boolean
  //     error?: unknown
  //     uiState?: UiState
  //     setUiState?: (
  //       updater: Partial<UiState> | ((prev: UiState) => UiState)
  //     ) => void
  //     setKeyword?: (keyword: string) => void
  //     getKeyword?: () => string
  //     initialized?: boolean
  //     reset: () => void
  //     reload: () => void
  //   }
  // }`
  //   }

  //   private generateRenderTextComponent(): string {
  //     return `export function RenderText({ text }: { text: string }) {
  //   return <div className="flex flex-col gap-2">{text}</div>
  // }`
  //   }

  //   private generateRenderDateTimeComponent(): string {
  //     return `import { Calendar, Clock } from 'lucide-react'

  // export function RenderDateTime({
  //   datetime,
  //   showTime = false,
  // }: {
  //   datetime: string
  //   showTime?: boolean
  // }) {
  //   return (
  //     <div className="flex flex-col gap-1">
  //       <span className="flex items-center gap-1">
  //         <Calendar size={16} />
  //         {new Date(datetime).toLocaleDateString()}
  //       </span>
  //       {showTime && (
  //         <span className="flex items-center gap-1">
  //           <Clock size={16} />
  //           {new Date(datetime).toLocaleTimeString()}
  //         </span>
  //       )}
  //     </div>
  //   )
  // }`
  //   }

  //   private generateRenderStatusComponent(): string {
  //     return `import { Badge } from '@workspace/ui/components/badge'
  // import { Check, SquareDashedKanban, Trash, Delete } from 'lucide-react'

  // export type ModelStatus = 'ACTIVE' | 'DRAFT' | 'SOFT_DELETE' | 'DELETED'

  // export const RenderStatus = ({ status }: { status: ModelStatus }) => {
  //   switch (status) {
  //     case 'ACTIVE':
  //       return (
  //         <Badge variant="outline" className="gap-1 bg-emerald-500 text-white">
  //           <Check className="" size={12} aria-hidden="true" />
  //           Active
  //         </Badge>
  //       )
  //     case 'DRAFT':
  //       return (
  //         <Badge variant="outline" className="gap-1">
  //           <SquareDashedKanban className="" size={12} aria-hidden="true" />
  //           Draft
  //         </Badge>
  //       )
  //     case 'SOFT_DELETE':
  //       return (
  //         <Badge variant="outline" className="gap-1 bg-red-500 text-white">
  //           <Trash className="" size={12} aria-hidden="true" />
  //           Deleted
  //         </Badge>
  //       )
  //     case 'DELETED':
  //       return (
  //         <Badge variant="outline" className="gap-1 bg-gray-500 text-white">
  //           <Delete className="" size={12} aria-hidden="true" />
  //           Permanently Deleted
  //         </Badge>
  //       )
  //     default:
  //       return (
  //         <Badge variant="outline" className="gap-1 bg-black text-white">
  //           Unknown
  //         </Badge>
  //       )
  //   }
  // }`
  //   }

  //   private generateRenderInteractiveLinkComponent(): string {
  //     return `import { cn } from '@workspace/ui/lib/utils'
  // import { CSSProperties, ReactNode } from 'react'

  // type TextBehavior = 'wrap' | 'truncate' | 'nowrap'

  // interface RenderInteractiveLinkProps {
  //   children: ReactNode
  //   onClick: () => void
  //   className?: string
  //   textBehavior?: TextBehavior
  //   maxLines?: number
  //   title?: string
  // }

  // export function RenderInteractiveLink({
  //   children,
  //   onClick,
  //   className,
  //   textBehavior = 'wrap',
  //   maxLines,
  //   title,
  // }: RenderInteractiveLinkProps) {
  //   const autoTitle =
  //     textBehavior === 'truncate' && !title
  //       ? typeof children === 'string'
  //         ? children
  //         : undefined
  //       : title

  //   const getTextClassesAndStyles = (): {
  //     classes: string
  //     styles?: CSSProperties
  //   } => {
  //     const baseClasses = 'text-left'

  //     switch (textBehavior) {
  //       case 'truncate':
  //         return {
  //           classes: \`\${baseClasses} truncate\`,
  //           styles: undefined,
  //         }
  //       case 'nowrap':
  //         return {
  //           classes: \`\${baseClasses} whitespace-nowrap\`,
  //           styles: undefined,
  //         }
  //       case 'wrap':
  //       default:
  //         if (maxLines && maxLines > 0) {
  //           return {
  //             classes: \`\${baseClasses} overflow-hidden\`,
  //             styles: {
  //               display: '-webkit-box',
  //               WebkitLineClamp: maxLines,
  //               WebkitBoxOrient: 'vertical' as const,
  //               wordBreak: 'break-word',
  //             },
  //           }
  //         }

  //         return {
  //           classes: \`\${baseClasses} break-words\`,
  //           styles: undefined,
  //         }
  //     }
  //   }

  //   const { classes: textClasses, styles: textStyles } = getTextClassesAndStyles()

  //   return (
  //     <button
  //       type="button"
  //       onClick={onClick}
  //       title={autoTitle}
  //       style={textStyles}
  //       className={cn(
  //         'text-primary cursor-pointer font-medium transition-colors hover:underline',
  //         'focus:ring-primary rounded-sm',
  //         'inline-block w-full text-start',
  //         textClasses,
  //         className
  //       )}
  //     >
  //       {children}
  //     </button>
  //   )
  // }`
  //   }

  private generateTableViLocale(columns: Any[], moduleName: string, modelName: string): string {
    const localeData: { [key: string]: string } = {}

    // Add table fields
    columns.forEach(column => {
      const fieldName = column.key || 'field'
      const humanizedName = this.humanize(fieldName)

      localeData[`${moduleName}.${modelName}.field.${fieldName}.label`] = column.label || humanizedName
      localeData[`${moduleName}.${modelName}.field.${fieldName}.placeholder`] =
        `Tìm kiếm ${humanizedName.toLowerCase()}`
    })

    // Add table management actions
    localeData[`${moduleName}.title`] = `Quản lý ${this.humanize(modelName)}`
    localeData[`${moduleName}.action.create`] = 'Tạo mới'
    localeData[`${moduleName}.action.createTitle`] = `Tạo ${this.humanize(modelName)} mới`
    localeData[`${moduleName}.action.edit`] = 'Chỉnh sửa'
    localeData[`${moduleName}.action.view`] = 'Xem chi tiết'
    localeData[`${moduleName}.action.delete`] = 'Xóa'
    localeData[`${moduleName}.action.deleteConfirm`] = 'Bạn có chắc chắn muốn xóa {name}?'

    // Add common table labels
    // localeData["table.action.label"] = "Thao tác"
    // localeData["action.cancel.label"] = "Hủy"
    // localeData["action.delete.label"] = "Xóa"
    // localeData["msg.deleted.success"] = "Đã xóa {name} thành công"
    // localeData["common.error"] = "Có lỗi xảy ra"

    return JSON.stringify(localeData, null, 2)
  }

  private generateTableEnLocale(columns: Any[], moduleName: string, modelName: string): string {
    const localeData: { [key: string]: string } = {}

    // Add table fields
    columns.forEach(column => {
      const fieldName = column.key || 'field'
      const humanizedName = this.humanize(fieldName)

      localeData[`${moduleName}.${modelName}.field.${fieldName}.label`] = column.label || humanizedName
      localeData[`${moduleName}.${modelName}.field.${fieldName}.placeholder`] = `Search ${humanizedName.toLowerCase()}`
    })

    // Add table management actions
    localeData[`${moduleName}.title`] = `${this.humanize(modelName)} Management`
    localeData[`${moduleName}.action.create`] = 'Create'
    localeData[`${moduleName}.action.createTitle`] = `Create New ${this.humanize(modelName)}`
    localeData[`${moduleName}.action.edit`] = 'Edit'
    localeData[`${moduleName}.action.view`] = 'View Details'
    localeData[`${moduleName}.action.delete`] = 'Delete'
    localeData[`${moduleName}.action.deleteConfirm`] = 'Are you sure you want to delete {name}?'

    // Add common table labels
    // localeData["table.action.label"] = "Actions"
    // localeData["action.cancel.label"] = "Cancel"
    // localeData["action.delete.label"] = "Delete"
    // localeData["msg.deleted.success"] = "Successfully deleted {name}"
    // localeData["common.error"] = "An error occurred"

    return JSON.stringify(localeData, null, 2)
  }
}

export const formGeneratorCodeService = new FormGeneratorCodeService()
