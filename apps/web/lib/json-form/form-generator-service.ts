/* eslint-disable @typescript-eslint/no-unused-vars */
import { type UsageType } from '@/app/(full-screen)/forms/e-form/endpoint-usage-modal'
import { type ElementType, type FormElement } from '@/app/(full-screen)/forms/e-form/form-builder'

import { Any } from '../types'
import { type ApiEndpoint, type ApiSchema } from './json-form-api-service'

interface GeneratedForm {
  elements: FormElement[]
  metadata: {
    apiEndpoint: ApiEndpoint
    apiSchema: ApiSchema
    usageType: UsageType
    dataPath?: string
    generatedAt: Date
  }
}

interface SelectOption {
  label: string
  value: string
}

class FormGeneratorService {
  /**
   * Generate form JSON từ API endpoint
   */
  generateForm(endpoint: ApiEndpoint, schema: ApiSchema, usageType: UsageType, dataPath?: string): GeneratedForm {
    console.log('Generating form for:', { endpoint, schema, usageType, dataPath })

    let elements: FormElement[] = []

    if (schema.type === 'swagger') {
      elements = this.generateFromSwagger(endpoint, schema, usageType, dataPath)
    } else if (schema.type === 'graphql') {
      elements = this.generateFromGraphQL(endpoint, schema, usageType, dataPath)
    }

    return {
      elements,
      metadata: {
        apiEndpoint: endpoint,
        apiSchema: schema,
        usageType,
        dataPath,
        generatedAt: new Date(),
      },
    }
  }

  /**
   * Generate form từ Swagger/OpenAPI endpoint
   */
  private generateFromSwagger(
    endpoint: ApiEndpoint,
    schema: ApiSchema,
    usageType: UsageType,
    dataPath?: string
  ): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = 1

    const titleElement: FormElement = {
      id: `title_${idCounter++}`,
      type: 'h1',
      customAttributes: {
        text: this.generateTitle(endpoint, usageType),
      },
      styles: {
        desktop: { fontSize: '24px', fontWeight: 'bold', margin: '0 0 20px 0' },
        tablet: { fontSize: '22px', fontWeight: 'bold', margin: '0 0 20px 0' },
        mobile: { fontSize: '20px', fontWeight: 'bold', margin: '0 0 16px 0' },
      },
    }

    if (usageType === 'detail') {
      // Tạo layout hiển thị chi tiết
      elements.push(...this.generateDetailLayout(endpoint, schema, idCounter))
    } else if (usageType === 'form') {
      // Tạo form input
      elements.push(titleElement)
      elements.push(...this.generateFormLayout(endpoint, schema, idCounter))
    } else if (usageType === 'table') {
      // Add title
      elements.push(titleElement)
      // Tạo table hiển thị danh sách
      elements.push(...this.generateTableLayout(endpoint, schema, idCounter, dataPath))
    }

    return elements
  }

  /**
   * Generate form từ GraphQL endpoint
   */
  private generateFromGraphQL(
    endpoint: ApiEndpoint,
    schema: ApiSchema,
    usageType: UsageType,
    dataPath?: string
  ): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = 1

    const titleElement: FormElement = {
      id: `title_${idCounter++}`,
      type: 'h1',
      customAttributes: {
        text: this.generateGraphQLTitle(endpoint, usageType),
      },
      styles: {
        desktop: { fontSize: '24px', fontWeight: 'bold', margin: '0 0 20px 0' },
        tablet: { fontSize: '22px', fontWeight: 'bold', margin: '0 0 20px 0' },
        mobile: { fontSize: '20px', fontWeight: 'bold', margin: '0 0 16px 0' },
      },
    }

    if (usageType === 'detail') {
      // Detail đã có title được tạo trong generateGraphQLDetailLayout
      elements.push(...this.generateGraphQLDetailLayout(endpoint, schema, idCounter))
    } else if (usageType === 'table') {
      elements.push(titleElement)
      elements.push(...this.generateGraphQLTableLayout(endpoint, schema, idCounter, dataPath))
    } else if (usageType === 'form') {
      elements.push(titleElement)
      elements.push(...this.generateGraphQLFormLayout(endpoint, schema, idCounter))
    }

    return elements
  }

  /**
   * Resolve schema reference ($ref) từ Swagger schema
   */
  private resolveSchemaRef(ref: string, swaggerSchema: Any): Any {
    try {
      console.log('Resolving schema ref:', ref)

      // Parse reference path (e.g., "#/components/schemas/BSignCertificationDto")
      const refPath = ref.replace('#/', '').split('/')

      let resolvedSchema = swaggerSchema

      for (const pathPart of refPath) {
        if (resolvedSchema && resolvedSchema[pathPart]) {
          resolvedSchema = resolvedSchema[pathPart]
        } else {
          console.warn(`Schema reference path not found: ${pathPart} in ${ref}`)

          return null
        }
      }

      console.log('Resolved schema:', resolvedSchema)

      return resolvedSchema
    } catch (error) {
      console.error('Error resolving schema reference:', error)

      return null
    }
  }

  /**
   * Extract và resolve request schema từ Swagger endpoint
   */
  private extractRequestSchema(endpoint: ApiEndpoint, swaggerSchema?: Any): Any {
    try {
      if (endpoint.requestBody?.content) {
        const jsonContent = endpoint.requestBody.content['application/json']
        let schema = jsonContent?.schema

        // Check if schema has $ref
        if (schema && schema.$ref) {
          console.log('Found schema reference:', schema.$ref)

          if (swaggerSchema) {
            const resolvedSchema = this.resolveSchemaRef(schema.$ref, swaggerSchema)

            if (resolvedSchema) {
              schema = resolvedSchema
            }
          }
        }

        return schema
      }
    } catch (error) {
      console.warn('Error extracting request schema:', error)
    }

    return null
  }

  /**
   * Extract và resolve response schema từ Swagger endpoint
   */
  private extractResponseSchema(endpoint: ApiEndpoint, swaggerSchema?: Any): Any {
    try {
      if (endpoint.responses) {
        const successResponse = endpoint.responses['200'] || endpoint.responses['201']

        if (successResponse?.content) {
          const jsonContent = successResponse.content['application/json']
          let schema = jsonContent?.schema

          // Check if schema has $ref
          if (schema && schema.$ref) {
            console.log('Found response schema reference:', schema.$ref)

            if (swaggerSchema) {
              const resolvedSchema = this.resolveSchemaRef(schema.$ref, swaggerSchema)

              if (resolvedSchema) {
                schema = resolvedSchema
              }
            }
          }

          return schema
        }
      }
    } catch (error) {
      console.warn('Error extracting response schema:', error)
    }

    return null
  }

  /**
   * Smart field type detection dựa trên tên field
   */
  private detectFieldTypeByName(fieldName: string): {
    type: ElementType
    customAttributes?: Any
    options?: SelectOption[]
  } {
    const lowerName = fieldName.toLowerCase()

    // Check for email fields -> email input
    if (lowerName.includes('email') || lowerName.includes('mail')) {
      return { type: 'input', customAttributes: { type: 'email' } }
    }

    // Check for phone fields -> tel input
    if (lowerName.includes('phone') || lowerName.includes('tel') || lowerName.includes('mobile')) {
      return { type: 'input', customAttributes: { type: 'tel' } }
    }

    // Check for password fields -> password input
    if (lowerName.includes('password') || lowerName.includes('pass')) {
      return { type: 'input', customAttributes: { type: 'password' } }
    }

    // Check for URL fields -> url input
    if (lowerName.includes('url') || lowerName.includes('link') || lowerName.includes('website')) {
      return { type: 'input', customAttributes: { type: 'url' } }
    }

    // Check for content/data fields -> textarea
    if (
      lowerName.includes('data') ||
      lowerName.includes('metadata') ||
      lowerName.includes('content') ||
      lowerName.includes('description') ||
      lowerName.includes('note') ||
      lowerName.includes('comment')
    ) {
      return { type: 'textarea' }
    }

    // Check for ID fields -> select
    if (lowerName.includes('ids')) {
      // Multiple IDs -> multi-select
      return {
        type: 'select',
        customAttributes: { multiple: true },
        options: this.generateFakeSelectOptions(fieldName, true),
      }
    } else if (lowerName.includes('id')) {
      // Single ID -> select
      return {
        type: 'select',
        options: this.generateFakeSelectOptions(fieldName, false),
      }
    }

    // Check for status/state fields -> select
    if (lowerName.includes('status') || lowerName.includes('state')) {
      return {
        type: 'select',
        options: [
          { label: 'Hoạt động', value: 'active' },
          { label: 'Không hoạt động', value: 'inactive' },
          { label: 'Chờ xử lý', value: 'pending' },
        ],
      }
    }

    // Check for priority fields -> select
    if (lowerName.includes('priority')) {
      return {
        type: 'select',
        options: [
          { label: 'Thấp', value: 'low' },
          { label: 'Trung bình', value: 'medium' },
          { label: 'Cao', value: 'high' },
          { label: 'Khẩn cấp', value: 'urgent' },
        ],
      }
    }

    // Check for type/category fields -> select
    if (lowerName.includes('type') || lowerName.includes('category')) {
      return {
        type: 'select',
        options: this.generateFakeSelectOptions(fieldName, false),
      }
    }

    // Default to text input
    return { type: 'input', customAttributes: { type: 'text' } }
  }

  /**
   * Generate fake options cho select fields
   */
  private generateFakeSelectOptions(fieldName: string, isMultiple: boolean = false): SelectOption[] {
    const lowerName = fieldName.toLowerCase()

    // Generate context-aware options
    if (lowerName.includes('user')) {
      return [
        { label: 'Nguyễn Văn A', value: 'user_1' },
        { label: 'Trần Thị B', value: 'user_2' },
        { label: 'Lê Văn C', value: 'user_3' },
      ]
    } else if (lowerName.includes('category')) {
      return [
        { label: 'Danh mục 1', value: 'category_1' },
        { label: 'Danh mục 2', value: 'category_2' },
        { label: 'Danh mục 3', value: 'category_3' },
      ]
    } else if (lowerName.includes('product')) {
      return [
        { label: 'Sản phẩm A', value: 'product_1' },
        { label: 'Sản phẩm B', value: 'product_2' },
        { label: 'Sản phẩm C', value: 'product_3' },
      ]
    } else if (lowerName.includes('department')) {
      return [
        { label: 'Phòng IT', value: 'dept_it' },
        { label: 'Phòng HR', value: 'dept_hr' },
        { label: 'Phòng Marketing', value: 'dept_marketing' },
      ]
    } else {
      // Generic options
      return [
        { label: `${this.humanize(fieldName)} 1`, value: `${fieldName}_1` },
        { label: `${this.humanize(fieldName)} 2`, value: `${fieldName}_2` },
        { label: `${this.humanize(fieldName)} 3`, value: `${fieldName}_3` },
      ]
    }
  }

  /**
   * Convert string[] options to SelectOption[]
   */
  private convertToSelectOptions(stringOptions: string[]): SelectOption[] {
    try {
      return stringOptions.map(option => ({
        label: this.humanize(option),
        value: option,
      }))
    } catch (error) {
      console.error('[Error] convertToSelectOptions: converting to select options:', error)

      return []
    }
  }

  /**
   * Generate title cho form
   */
  private generateTitle(endpoint: ApiEndpoint, usageType: UsageType): string {
    const operation = endpoint.operationId || endpoint.path.split('/').pop() || 'Item'

    switch (usageType) {
      case 'detail':
        return `Chi tiết ${this.humanize(operation)}`
      case 'form':
        if (endpoint.method === 'POST') {
          return `Tạo ${this.humanize(operation)}`
        } else if (['PUT', 'PATCH'].includes(endpoint.method)) {
          return `Cập nhật ${this.humanize(operation)}`
        }

        return `Form ${this.humanize(operation)}`
      case 'table':
        return `Danh sách ${this.humanize(operation)}`
      default:
        return this.humanize(operation)
    }
  }

  private generateGraphQLTitle(endpoint: ApiEndpoint, usageType: UsageType): string {
    let operation = ''

    if (endpoint.query) operation = endpoint.query
    else if (endpoint.mutation) operation = endpoint.mutation
    else if (endpoint.subscription) operation = endpoint.subscription
    else operation = 'Data'

    switch (usageType) {
      case 'detail':
        return `Chi tiết ${this.humanize(operation)}`
      case 'form':
        if (endpoint.mutation) {
          return operation.startsWith('create')
            ? `Tạo ${this.humanize(operation)}`
            : `Cập nhật ${this.humanize(operation)}`
        }

        return `Form ${this.humanize(operation)}`
      case 'table':
        return `Danh sách ${this.humanize(operation)}`
      default:
        return this.humanize(operation)
    }
  }

  /**
   * Generate detail layout
   */
  private generateDetailLayout(endpoint: ApiEndpoint, schema: ApiSchema, startId: number): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Tạo detail element với metadata rows
    const detailElement: FormElement = {
      id: `detail_${idCounter++}`,
      type: 'detail',
      label: this.generateTitle(endpoint, 'detail'),
      name: `detail_${endpoint.operationId || endpoint.path.replace(/[^a-zA-Z0-9]/g, '_')}`,
      category: 'data',
      styles: {
        desktop: { width: '100%', margin: '0 0 20px 0' },
        tablet: { width: '100%', margin: '0 0 20px 0' },
        mobile: { width: '100%', margin: '0 0 16px 0' },
      },
      metadata: {
        endpoint: {
          url: endpoint.path,
          method: endpoint.method,
          operationId: endpoint.operationId,
          summary: endpoint.summary,
          description: endpoint.description,
        },
        apiSchema: {
          name: schema.name,
          type: schema.type,
          id: schema.id,
        },
        rows: this.generateDetailRows(endpoint, schema),
        columns: [
          {
            id: 'col_name',
            label: 'Tên trường',
            key: 'name',
            dataType: 'text',
            sortable: false,
            filterable: false,
            width: '33%',
          },
          {
            id: 'col_value',
            label: 'Thông tin',
            key: 'value',
            dataType: 'text',
            sortable: false,
            filterable: false,
            width: '67%',
          },
        ],
        showHeader: true,
        showPagination: false,
        striped: true,
        bordered: true,
        hover: true,
      },
      apiConfig: {
        url: endpoint.path,
        method: endpoint.method,
        headers: {},
      },
    }

    elements.push(detailElement)

    return elements
  }

  /**
   * Generate detail layout (old method - keeping for compatibility)
   */
  private generateDetailLayoutOld(endpoint: ApiEndpoint, schema: ApiSchema, startId: number): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Container cho detail view
    const detailContainer: FormElement = {
      id: `detail_container_${idCounter++}`,
      type: 'container',
      styles: {
        desktop: { width: '100%', padding: '20px' },
        tablet: { width: '100%', padding: '20px' },
        mobile: { width: '100%', padding: '16px' },
      },
      children: [],
      containerConfig: {
        columns: {
          desktop: 2,
          tablet: 2,
          mobile: 1,
        },
        verticalAlign: 'top',
        gap: '16px',
        children: {},
      },
    }

    // Parse response schema để tạo detail fields
    const responseSchema = this.extractResponseSchema(endpoint, schema.schema)

    if (responseSchema && responseSchema.properties) {
      console.log('Using resolved response schema properties:', Object.keys(responseSchema.properties))

      for (const [fieldName, fieldSchema] of Object.entries(responseSchema.properties)) {
        detailContainer.children!.push({
          id: `detail_field_${idCounter++}`,
          type: 'html',
          customAttributes: {
            content: `<div style="margin-bottom: 16px;">
              <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 4px;">${this.humanize(fieldName)}</label>
              <div style="padding: 12px; background-color: #f9fafb; border: 1px solid #d1d5db; border-radius: 6px;">
                <span style="color: #111827;">{{${fieldName}}}</span>
              </div>
            </div>`,
          },
          styles: {
            desktop: { margin: '0 0 16px 0' },
            tablet: { margin: '0 0 16px 0' },
            mobile: { margin: '0 0 12px 0' },
          },
        })
      }
    } else {
      console.log('No response schema found, using default fields')
      // Default fields nếu không có schema
      const defaultFields = ['id', 'name', 'title', 'description', 'createdAt', 'updatedAt']
      defaultFields.forEach(fieldName => {
        detailContainer.children!.push({
          id: `detail_field_${idCounter++}`,
          type: 'html',
          customAttributes: {
            content: `<div style="margin-bottom: 16px;">
              <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 4px;">${this.humanize(fieldName)}</label>
              <div style="padding: 12px; background-color: #f9fafb; border: 1px solid #d1d5db; border-radius: 6px;">
                <span style="color: #111827;">{{${fieldName}}}</span>
              </div>
            </div>`,
          },
          styles: {
            desktop: { margin: '0 0 16px 0' },
            tablet: { margin: '0 0 16px 0' },
            mobile: { margin: '0 0 12px 0' },
          },
        })
      })
    }

    elements.push(detailContainer)

    return elements
  }

  /**
   * Generate table layout
   */
  private generateTableLayout(
    endpoint: ApiEndpoint,
    schema: ApiSchema,
    startId: number,
    dataPath: string = 'data.items'
  ): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Tạo table element với metadata endpoint
    const tableElement: FormElement = {
      id: `table_${idCounter++}`,
      type: 'table',
      label: this.generateTitle(endpoint, 'table'),
      name: `table_${endpoint.operationId || endpoint.path.replace(/[^a-zA-Z0-9]/g, '_')}`,
      category: 'data',
      styles: {
        desktop: { width: '100%', margin: '0 0 20px 0' },
        tablet: { width: '100%', margin: '0 0 20px 0' },
        mobile: { width: '100%', margin: '0 0 16px 0' },
      },
      metadata: {
        endpoint: {
          url: endpoint.path,
          method: endpoint.method,
          operationId: endpoint.operationId,
          summary: endpoint.summary,
          description: endpoint.description,
        },
        apiSchema: {
          name: schema.name,
          type: schema.type,
          id: schema.id,
        },
        dataPath: dataPath,
        columns: this.generateTableColumns(endpoint, schema, dataPath),
        showHeader: true,
        showPagination: true,
        pageSize: 10,
        striped: true,
        bordered: true,
        hover: true,
        responsive: true,
      },
      apiConfig: {
        url: endpoint.path,
        method: endpoint.method,
        headers: {},
      },
    }

    elements.push(tableElement)

    return elements
  }

  /**
   * Generate form layout
   */
  private generateFormLayout(endpoint: ApiEndpoint, schema: ApiSchema, startId: number): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Container cho form
    const formContainer: FormElement = {
      id: `form_container_${idCounter++}`,
      type: 'container',
      styles: {
        desktop: { width: '100%', padding: '20px' },
        tablet: { width: '100%', padding: '20px' },
        mobile: { width: '100%', padding: '16px' },
      },
      children: [],
      containerConfig: {
        columns: {
          desktop: 2,
          tablet: 2,
          mobile: 1,
        },
        verticalAlign: 'top',
        gap: '16px',
        children: {},
      },
    }

    // Parse request body schema để tạo form fields với reference resolution
    const requestSchema = this.extractRequestSchema(endpoint, schema.schema)

    if (requestSchema && requestSchema.properties) {
      console.log('Using resolved request schema properties:', Object.keys(requestSchema.properties))
      console.log('Required fields:', requestSchema.required || [])

      for (const [fieldName, fieldSchema] of Object.entries(requestSchema.properties)) {
        const field = this.createFormField(
          fieldName,
          fieldSchema as Any,
          idCounter++,
          schema.schema,
          requestSchema.required || []
        )

        if (field) {
          formContainer.children!.push(field)
        }
      }
    } else {
      console.log('No request schema found, using default fields')
      // Default form fields
      const defaultFields = [
        { name: 'name', type: 'text', required: true },
        { name: 'description', type: 'textarea', required: false },
        { name: 'email', type: 'email', required: false },
        { name: 'phone', type: 'tel', required: false },
      ]

      defaultFields.forEach(field => {
        const validationRules: Any[] = []

        // Add required validation
        if (field.required) {
          validationRules.push({
            type: 'required',
            message: 'Trường này là bắt buộc',
          })
        }

        // Add email validation
        if (field.type === 'email') {
          validationRules.push({
            type: 'email',
            message: 'Vui lòng nhập địa chỉ email hợp lệ',
          })
        }

        formContainer.children!.push({
          id: `form_field_${idCounter++}`,
          type: field.type === 'textarea' ? 'textarea' : 'input',
          name: field.name,
          label: this.humanize(field.name),
          placeholder: `Nhập ${this.humanize(field.name).toLowerCase()}...`,
          required: field.required,
          customAttributes: field.type !== 'textarea' ? { type: field.type } : undefined,
          styles: {
            desktop: { margin: '0 0 16px 0' },
            tablet: { margin: '0 0 16px 0' },
            mobile: { margin: '0 0 12px 0' },
          },
          validationRules: validationRules,
        })
      })
    }

    // Add submit button
    formContainer.children!.push({
      id: `submit_btn_${idCounter++}`,
      type: 'button',
      label: endpoint.method === 'POST' ? 'Tạo mới' : 'Cập nhật',
      customAttributes: {
        type: 'submit',
        variant: 'primary',
      },
      styles: {
        desktop: { margin: '20px 0 0 0', width: '100%' },
        tablet: { margin: '20px 0 0 0', width: '100%' },
        mobile: { margin: '16px 0 0 0', width: '100%' },
      },
    })

    elements.push(formContainer)

    return elements
  }

  /**
   * Generate GraphQL detail layout
   */
  private generateGraphQLDetailLayout(endpoint: ApiEndpoint, schema: ApiSchema, startId: number): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Tạo detail element với metadata rows cho GraphQL
    const detailElement: FormElement = {
      id: `detail_${idCounter++}`,
      type: 'detail',
      label: this.generateGraphQLTitle(endpoint, 'detail'),
      name: `detail_${endpoint.query || endpoint.mutation || endpoint.subscription || 'data'}`.replace(
        /[^a-zA-Z0-9]/g,
        '_'
      ),
      category: 'data',
      styles: {
        desktop: { width: '100%', margin: '0 0 20px 0' },
        tablet: { width: '100%', margin: '0 0 20px 0' },
        mobile: { width: '100%', margin: '0 0 16px 0' },
      },
      metadata: {
        endpoint: {
          query: endpoint.query,
          mutation: endpoint.mutation,
          subscription: endpoint.subscription,
          variables: endpoint.variables,
          description: endpoint.description,
        },
        apiSchema: {
          name: schema.name,
          type: schema.type,
          id: schema.id,
        },
        rows: this.generateGraphQLDetailRows(endpoint, schema),
        columns: [
          {
            id: 'col_name',
            label: 'Tên trường',
            key: 'name',
            dataType: 'text',
            sortable: false,
            filterable: false,
            width: '33%',
          },
          {
            id: 'col_value',
            label: 'Thông tin',
            key: 'value',
            dataType: 'text',
            sortable: false,
            filterable: false,
            width: '67%',
          },
        ],
        showHeader: true,
        showPagination: false,
        striped: true,
        bordered: true,
        hover: true,
      },
      apiConfig: {
        url: '/graphql',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    }

    elements.push(detailElement)

    return elements
  }

  /**
   * Generate GraphQL table layout
   */
  private generateGraphQLTableLayout(
    endpoint: ApiEndpoint,
    schema: ApiSchema,
    startId: number,
    dataPath?: string
  ): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Tạo table element với metadata endpoint cho GraphQL
    const tableElement: FormElement = {
      id: `table_${idCounter++}`,
      type: 'table',
      label: this.generateGraphQLTitle(endpoint, 'table'),
      name: `table_${endpoint.query || endpoint.mutation || endpoint.subscription || 'data'}`.replace(
        /[^a-zA-Z0-9]/g,
        '_'
      ),
      category: 'data',
      styles: {
        desktop: { width: '100%', margin: '0 0 20px 0' },
        tablet: { width: '100%', margin: '0 0 20px 0' },
        mobile: { width: '100%', margin: '0 0 16px 0' },
      },
      metadata: {
        endpoint: {
          query: endpoint.query,
          mutation: endpoint.mutation,
          subscription: endpoint.subscription,
          variables: endpoint.variables,
          description: endpoint.description,
        },
        apiSchema: {
          name: schema.name,
          type: schema.type,
          id: schema.id,
        },
        dataPath: dataPath,
        columns: this.generateGraphQLTableColumns(endpoint, schema, dataPath),
        showHeader: true,
        showPagination: true,
        pageSize: 10,
        striped: true,
        bordered: true,
        hover: true,
        responsive: true,
      },
      apiConfig: {
        url: '/graphql', // GraphQL endpoint thường là /graphql
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    }

    elements.push(tableElement)

    return elements
  }

  /**
   * Generate GraphQL form layout
   */
  private generateGraphQLFormLayout(endpoint: ApiEndpoint, schema: ApiSchema, startId: number): FormElement[] {
    const elements: FormElement[] = []
    let idCounter = startId

    // Container cho form
    const formContainer: FormElement = {
      id: `form_container_${idCounter++}`,
      type: 'container',
      styles: {
        desktop: { width: '100%', padding: '20px' },
        tablet: { width: '100%', padding: '20px' },
        mobile: { width: '100%', padding: '16px' },
      },
      children: [],
      containerConfig: {
        columns: {
          desktop: 2,
          tablet: 2,
          mobile: 1,
        },
        verticalAlign: 'top',
        gap: '16px',
        children: {},
      },
    }

    // Parse GraphQL parameters/arguments
    if (endpoint.parameters && endpoint.parameters.length > 0) {
      endpoint.parameters.forEach(param => {
        const field = this.createGraphQLFormField(param, idCounter++)

        if (field) {
          formContainer.children!.push(field)
        }
      })
    } else {
      // Default GraphQL form fields
      const defaultFields = [
        { name: 'name', type: 'String', required: true },
        { name: 'description', type: 'String', required: false },
      ]

      defaultFields.forEach(field => {
        const validationRules: Any[] = []

        // Add required validation
        if (field.required) {
          validationRules.push({
            type: 'required',
            message: 'Trường này là bắt buộc',
          })
        }

        formContainer.children!.push({
          id: `gql_field_${idCounter++}`,
          type: 'input',
          name: field.name,
          label: this.humanize(field.name),
          placeholder: `Nhập ${this.humanize(field.name).toLowerCase()}...`,
          required: field.required,
          customAttributes: { type: 'text' },
          styles: {
            desktop: { margin: '0 0 16px 0' },
            tablet: { margin: '0 0 16px 0' },
            mobile: { margin: '0 0 12px 0' },
          },
          validationRules: validationRules,
        })
      })
    }

    // Add submit button
    formContainer.children!.push({
      id: `submit_btn_${idCounter++}`,
      type: 'button',
      label: endpoint.mutation ? 'Thực hiện' : 'Gửi',
      customAttributes: {
        type: 'submit',
        variant: 'primary',
      },
      styles: {
        desktop: { margin: '20px 0 0 0', width: '100%' },
        tablet: { margin: '20px 0 0 0', width: '100%' },
        mobile: { margin: '16px 0 0 0', width: '100%' },
      },
    })

    elements.push(formContainer)

    return elements
  }

  /**
   * Create form field từ schema với support cho nested references
   */
  private createFormField(
    fieldName: string,
    fieldSchema: Any,
    id: number,
    swaggerSchema?: Any,
    requiredFields?: string[]
  ): FormElement | null {
    // Resolve reference nếu có
    let resolvedSchema = fieldSchema

    if (fieldSchema.$ref && swaggerSchema) {
      console.log('Resolving field reference:', fieldSchema.$ref)
      resolvedSchema = this.resolveSchemaRef(fieldSchema.$ref, swaggerSchema)

      if (!resolvedSchema) {
        console.warn('Could not resolve field reference:', fieldSchema.$ref)
        resolvedSchema = fieldSchema
      }
    }

    // Fix: Check required fields from parent schema, not individual field schema
    const isRequired = requiredFields?.includes(fieldName) || false

    // Use description as label if available, otherwise use humanized field name
    const fieldLabel = resolvedSchema.description || this.humanize(fieldName)

    const baseField: FormElement = {
      id: `field_${id}`,
      name: fieldName,
      label: fieldLabel,
      placeholder: `Nhập ${fieldLabel.toLowerCase()}...`,
      required: isRequired,
      type: 'input',
      styles: {
        desktop: { margin: '0 0 16px 0' },
        tablet: { margin: '0 0 16px 0' },
        mobile: { margin: '0 0 12px 0' },
      },
      validationRules: [],
    }

    // Map schema type to form field type
    switch (resolvedSchema.type) {
      case 'string':
        if (resolvedSchema.format === 'email') {
          baseField.customAttributes = { type: 'email' }
          // Add email validation rule
          baseField.validationRules!.push({
            type: 'email',
            message: 'Vui lòng nhập địa chỉ email hợp lệ',
          })
        } else if (resolvedSchema.format === 'date') {
          baseField.type = 'date-picker'
        } else if (resolvedSchema.format === 'date-time') {
          baseField.type = 'date-picker'
          baseField.customAttributes = { showTime: true }
        } else if (resolvedSchema.maxLength && resolvedSchema.maxLength > 128) {
          baseField.type = 'textarea'
        } else {
          // Use smart field detection cho string fields
          const smartField = this.detectFieldTypeByName(fieldName)
          baseField.type = smartField.type
          baseField.customAttributes = smartField.customAttributes

          if (smartField.options) {
            baseField.options = smartField.options // Keep string[] format for compatibility
          }
        }
        break
      case 'number':
      case 'integer':
        baseField.customAttributes = { type: 'number' }

        if (resolvedSchema.minimum !== undefined) {
          baseField.customAttributes.min = resolvedSchema.minimum
          baseField.validationRules!.push({
            type: 'min',
            value: resolvedSchema.minimum,
            message: `Giá trị phải lớn hơn hoặc bằng ${resolvedSchema.minimum}`,
          })
        }

        if (resolvedSchema.maximum !== undefined) {
          baseField.customAttributes.max = resolvedSchema.maximum
          baseField.validationRules!.push({
            type: 'max',
            value: resolvedSchema.maximum,
            message: `Giá trị phải nhỏ hơn hoặc bằng ${resolvedSchema.maximum}`,
          })
        }
        break
      case 'boolean':
        baseField.type = 'checkbox'
        break
      case 'array':
        if (resolvedSchema.items?.enum) {
          baseField.type = 'select'
          const selectOptions = this.convertToSelectOptions(resolvedSchema.items.enum)
          baseField.options = selectOptions.map(opt => {
            return {
              label: opt.label,
              value: opt.value,
              data: opt,
            }
          })
          baseField.customAttributes = { multiple: true }
        } else if (resolvedSchema.enum) {
          baseField.type = 'select'
          const selectOptions = this.convertToSelectOptions(resolvedSchema.enum)
          baseField.options = selectOptions.map(opt => {
            return {
              label: opt.label,
              value: opt.value,
              data: opt,
            }
          })
          baseField.customAttributes = { multiple: true }
        }
        break
      case 'object':
        // For nested objects, create a container with nested fields
        if (resolvedSchema.properties) {
          baseField.type = 'container'
          baseField.children = []
          let nestedIdCounter = id + 1000 // Offset để tránh conflict

          for (const [nestedFieldName, nestedFieldSchema] of Object.entries(resolvedSchema.properties)) {
            const nestedField = this.createFormField(
              `${fieldName}.${nestedFieldName}`,
              nestedFieldSchema as Any,
              nestedIdCounter++,
              swaggerSchema,
              resolvedSchema.required || []
            )

            if (nestedField) {
              baseField.children.push(nestedField)
            }
          }
        }
        break

      default: {
        // Use smart field detection cho unknown types
        const smartField = this.detectFieldTypeByName(fieldName)

        baseField.type = smartField.type
        baseField.customAttributes = smartField.customAttributes

        if (smartField.options) {
          baseField.options = smartField.options
          baseField.customAttributes = { ...baseField.customAttributes }
        }
      }
    }

    // Handle enum values
    if (resolvedSchema.enum && resolvedSchema.enum.length > 0) {
      baseField.type = 'select'
      const selectOptions = this.convertToSelectOptions(resolvedSchema.enum)
      baseField.options = selectOptions
      baseField.customAttributes = { ...baseField.customAttributes }
    }

    // Add validation rules based on schema constraints
    if (resolvedSchema.minLength !== undefined) {
      baseField.customAttributes = { ...baseField.customAttributes, minLength: resolvedSchema.minLength }
      baseField.validationRules!.push({
        type: 'custom',
        value: `minLength:${resolvedSchema.minLength}`,
        message: `Độ dài tối thiểu là ${resolvedSchema.minLength} ký tự`,
      })
    }

    if (resolvedSchema.maxLength !== undefined) {
      baseField.customAttributes = { ...baseField.customAttributes, maxLength: resolvedSchema.maxLength }
      baseField.validationRules!.push({
        type: 'custom',
        value: `maxLength:${resolvedSchema.maxLength}`,
        message: `Độ dài tối đa là ${resolvedSchema.maxLength} ký tự`,
      })
    }

    if (resolvedSchema.pattern) {
      baseField.customAttributes = { ...baseField.customAttributes, pattern: resolvedSchema.pattern }
      baseField.validationRules!.push({
        type: 'regex',
        value: resolvedSchema.pattern,
        message: 'Định dạng không hợp lệ',
      })
    }

    // Add required validation if field is required
    if (isRequired) {
      baseField.validationRules!.push({
        type: 'required',
        message: 'Trường này là bắt buộc',
      })
    }

    // Add email validation if field is email type (from format or field name detection)
    if (baseField.customAttributes?.type === 'email') {
      baseField.validationRules!.push({
        type: 'email',
        message: 'Vui lòng nhập địa chỉ email hợp lệ',
      })
    }

    return baseField
  }

  /**
   * Create GraphQL form field
   */
  private createGraphQLFormField(param: Any, id: number): FormElement | null {
    const fieldName = param.name || `field_${id}`
    const isRequired = param.type?.kind === 'NON_NULL'

    // Use description as label if available, otherwise use humanized field name
    const fieldLabel = param.description || this.humanize(fieldName)

    const baseField: FormElement = {
      id: `gql_field_${id}`,
      name: fieldName,
      label: fieldLabel,
      placeholder: `Nhập ${fieldLabel.toLowerCase()}...`,
      required: isRequired,
      type: 'input',
      styles: {
        desktop: { margin: '0 0 16px 0' },
        tablet: { margin: '0 0 16px 0' },
        mobile: { margin: '0 0 12px 0' },
      },
      validationRules: [],
    }

    // Map GraphQL type to form field type
    const gqlType = param.type?.ofType?.name || param.type?.name

    switch (gqlType) {
      case 'String': {
        // Use smart field detection for string types
        const smartField = this.detectFieldTypeByName(fieldName)
        baseField.type = smartField.type
        baseField.customAttributes = smartField.customAttributes

        if (smartField.options) {
          baseField.options = smartField.options
          baseField.customAttributes = { ...baseField.customAttributes }
        }

        break
      }
      case 'Int':
      case 'Float':
        baseField.customAttributes = { type: 'number' }
        break
      case 'Boolean':
        baseField.type = 'checkbox'
        break

      case 'ID': {
        const idField = this.detectFieldTypeByName(fieldName)
        baseField.type = idField.type
        baseField.customAttributes = idField.customAttributes

        if (idField.options) {
          baseField.options = idField.options
          baseField.customAttributes = { ...baseField.customAttributes }
        }

        break
      }
      default:
        baseField.customAttributes = { type: 'text' }
    }

    // Add required validation if field is required
    if (isRequired) {
      baseField.validationRules!.push({
        type: 'required',
        message: 'Trường này là bắt buộc',
      })
    }

    // Add email validation if field is email type (from field name detection)
    if (baseField.customAttributes?.type === 'email') {
      baseField.validationRules!.push({
        type: 'email',
        message: 'Vui lòng nhập địa chỉ email hợp lệ',
      })
    }

    return baseField
  }

  /**
   * Convert camelCase/snake_case to human readable
   */
  private humanize(str: string): string {
    return str
      .replace(/([A-Z])/g, ' $1')
      .replace(/[_-]/g, ' ')
      .toLowerCase()
      .replace(/^\w/, c => c.toUpperCase())
      .trim()
  }

  /**
   * Generate table columns từ Swagger endpoint
   */
  private generateTableColumns(endpoint: ApiEndpoint, schema: ApiSchema, dataPath: string = 'data.items'): Any[] {
    const columns: Any[] = []
    let columnCounter = 1

    // Thử parse response schema để tạo columns
    try {
      if (endpoint.responses && endpoint.responses['200']) {
        const responseSchema = endpoint.responses['200'].content?.['application/json']?.schema

        if (responseSchema) {
          // Parse dataPath để tìm schema đúng
          let targetSchema = responseSchema

          if (dataPath && dataPath !== 'data') {
            // Xử lý dataPath như "data.items", "items", etc.
            const pathParts = dataPath.split('.')

            for (const part of pathParts) {
              if (targetSchema.properties && targetSchema.properties[part]) {
                targetSchema = targetSchema.properties[part]
              } else if (targetSchema.type === 'array' && targetSchema.items) {
                targetSchema = targetSchema.items
              } else {
                break
              }
            }
          }

          // Nếu targetSchema là array, lấy properties của item
          let itemSchema = targetSchema

          if (targetSchema.type === 'array' && targetSchema.items) {
            itemSchema = targetSchema.items
          }

          console.log('itemSchema', itemSchema)

          // Kiểm tra và resolve reference nếu cần
          if (itemSchema.$ref) {
            console.log('Found itemSchema reference:', itemSchema.$ref)
            const resolvedSchema = this.resolveSchemaRef(itemSchema.$ref, schema.schema)

            if (resolvedSchema) {
              itemSchema = resolvedSchema
            }
          }

          // Nếu có properties, tạo columns từ properties
          if (itemSchema.properties) {
            Object.entries(itemSchema.properties).forEach(([key, property]: [string, Any]) => {
              // Kiểm tra và resolve reference của property nếu cần
              let propertySchema = property

              if (property.$ref) {
                console.log('Found property reference:', property.$ref)
                const resolvedPropertySchema = this.resolveSchemaRef(property.$ref, schema.schema)

                if (resolvedPropertySchema) {
                  propertySchema = resolvedPropertySchema
                }
              }

              const dataType = this.mapSwaggerTypeToTableType(propertySchema.type, propertySchema.format)
              const columnLabel = propertySchema.description || this.humanize(key)

              columns.push({
                id: `col_${columnCounter++}`,
                label: columnLabel,
                key: key,
                dataType: dataType,
                sortable: dataType !== 'object' && dataType !== 'array',
                filterable: dataType === 'text' || dataType === 'number',
                width: this.getColumnWidth(dataType),
              })
            })
          }
        }
      }

      // Nếu không có columns từ schema, tạo columns mặc định
      if (columns.length === 0) {
        columns.push(
          {
            id: `col_${columnCounter++}`,
            label: 'ID',
            key: 'id',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: '80px',
          },
          {
            id: `col_${columnCounter++}`,
            label: 'Tên',
            key: 'name',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: 'auto',
          },
          {
            id: `col_${columnCounter++}`,
            label: 'Ngày tạo',
            key: 'createdAt',
            dataType: 'datetime',
            sortable: true,
            filterable: false,
            width: '150px',
          }
        )
      }

      // Thêm cột actions ở cuối
      columns.push({
        id: `col_actions_${columnCounter++}`,
        label: 'Actions',
        key: 'actions',
        dataType: 'actions',
        sortable: false,
        filterable: false,
        width: '120px',
      })
    } catch (error) {
      console.error('Error generating table columns:', error)

      // Fallback columns
      return [
        {
          id: 'col_1',
          label: 'ID',
          key: 'id',
          dataType: 'text',
          sortable: true,
          filterable: true,
          width: '80px',
        },
        {
          id: 'col_2',
          label: 'Data',
          key: 'data',
          dataType: 'text',
          sortable: true,
          filterable: true,
          width: 'auto',
        },
        {
          id: 'col_actions',
          label: 'Actions',
          key: 'actions',
          dataType: 'actions',
          sortable: false,
          filterable: false,
          width: '120px',
        },
      ]
    }

    return columns
  }

  /**
   * Generate table columns từ GraphQL endpoint
   */
  private generateGraphQLTableColumns(endpoint: ApiEndpoint, schema: ApiSchema, dataPath?: string): Any[] {
    const columns: Any[] = []
    let columnCounter = 1

    // Thử parse GraphQL schema để tạo columns
    try {
      // For GraphQL, ta sẽ tạo columns dựa trên query/mutation structure
      // Đây là implementation cơ bản, có thể phải phức tạp hơn tùy theo GraphQL schema

      const operationName = endpoint.query || endpoint.mutation || endpoint.subscription

      if (operationName) {
        // Tạo columns dựa trên tên operation
        columns.push(
          {
            id: `col_${columnCounter++}`,
            label: 'ID',
            key: 'id',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: '80px',
          },
          {
            id: `col_${columnCounter++}`,
            label: this.humanize(operationName),
            key: operationName.toLowerCase(),
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: 'auto',
          },
          {
            id: `col_${columnCounter++}`,
            label: 'Trạng thái',
            key: 'status',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: '100px',
          }
        )
      }

      // Nếu không có columns, tạo mặc định
      if (columns.length === 0) {
        columns.push(
          {
            id: `col_${columnCounter++}`,
            label: 'ID',
            key: 'id',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: '80px',
          },
          {
            id: `col_${columnCounter++}`,
            label: 'Data',
            key: 'data',
            dataType: 'text',
            sortable: true,
            filterable: true,
            width: 'auto',
          }
        )
      }

      // Thêm cột actions
      columns.push({
        id: `col_actions_${columnCounter++}`,
        label: 'Actions',
        key: 'actions',
        dataType: 'actions',
        sortable: false,
        filterable: false,
        width: '120px',
      })
    } catch (error) {
      console.error('Error generating GraphQL table columns:', error)

      // Fallback columns
      return [
        {
          id: 'col_1',
          label: 'ID',
          key: 'id',
          dataType: 'text',
          sortable: true,
          filterable: true,
          width: '80px',
        },
        {
          id: 'col_2',
          label: 'Data',
          key: 'data',
          dataType: 'text',
          sortable: true,
          filterable: true,
          width: 'auto',
        },
        {
          id: 'col_actions',
          label: 'Actions',
          key: 'actions',
          dataType: 'actions',
          sortable: false,
          filterable: false,
          width: '120px',
        },
      ]
    }

    return columns
  }

  /**
   * Map Swagger types to table column types
   */
  private mapSwaggerTypeToTableType(type: string, format?: string): string {
    switch (type) {
      case 'string':
        if (format === 'date') return 'date'

        if (format === 'date-time') return 'datetime'

        return 'text'
      case 'integer':
      case 'number':
        return 'number'
      case 'boolean':
        return 'boolean'
      case 'array':
        return 'array'
      case 'object':
        return 'object'
      default:
        return 'text'
    }
  }

  /**
   * Get column width based on data type
   */
  private getColumnWidth(dataType: string): string {
    switch (dataType) {
      case 'date':
      case 'datetime':
        return '150px'
      case 'number':
        return '100px'
      case 'boolean':
        return '80px'
      case 'actions':
        return '120px'
      case 'object':
      case 'array':
        return '120px'
      default:
        return 'auto'
    }
  }

  /**
   * Generate detail rows từ Swagger endpoint
   */
  private generateDetailRows(endpoint: ApiEndpoint, schema: ApiSchema): Any[] {
    const rows: Any[] = []
    let rowCounter = 1

    try {
      if (endpoint.responses && endpoint.responses['200']) {
        const responseSchema = endpoint.responses['200'].content?.['application/json']?.schema

        if (responseSchema) {
          // Nếu response có data wrapper, lấy data.properties
          let itemSchema = responseSchema

          if (responseSchema.properties && responseSchema.properties.data) {
            itemSchema = responseSchema.properties.data
          }

          // Kiểm tra và resolve reference nếu cần
          if (itemSchema.$ref) {
            console.log('Found itemSchema reference in generateDetailRows:', itemSchema.$ref)
            const resolvedSchema = this.resolveSchemaRef(itemSchema.$ref, schema.schema)

            if (resolvedSchema) {
              itemSchema = resolvedSchema
            }
          }

          // Nếu có properties, tạo rows từ properties
          if (itemSchema.properties) {
            Object.entries(itemSchema.properties).forEach(([key, property]: [string, Any]) => {
              // Kiểm tra và resolve reference của property nếu cần
              let propertySchema = property

              if (property.$ref) {
                console.log('Found property reference in generateDetailRows:', property.$ref)
                const resolvedPropertySchema = this.resolveSchemaRef(property.$ref, schema.schema)

                if (resolvedPropertySchema) {
                  propertySchema = resolvedPropertySchema
                }
              }

              const dataType = this.mapSwaggerTypeToDetailType(propertySchema.type, propertySchema.format)
              const rowLabel = propertySchema.description || this.humanize(key)

              rows.push({
                id: `row_${rowCounter++}`,
                name: rowLabel,
                value: this.getDefaultValue(propertySchema.type, propertySchema.format),
                type: dataType,
              })
            })
          }
        }
      }

      // Nếu không có rows từ schema, tạo rows mặc định
      if (rows.length === 0) {
        rows.push(
          {
            id: `row_${rowCounter++}`,
            name: 'ID',
            value: '123456',
            type: 'text',
          },
          {
            id: `row_${rowCounter++}`,
            name: 'Tên',
            value: 'Dữ liệu mẫu',
            type: 'text',
          },
          {
            id: `row_${rowCounter++}`,
            name: 'Ngày tạo',
            value: '2024-01-15T10:30:00Z',
            type: 'datetime',
          },
          {
            id: `row_${rowCounter++}`,
            name: 'Trạng thái',
            value: true,
            type: 'boolean',
          }
        )
      }
    } catch (error) {
      console.error('Error generating detail rows:', error)

      // Fallback rows
      return [
        {
          id: 'row_1',
          name: 'ID',
          value: '123456',
          type: 'text',
        },
        {
          id: 'row_2',
          name: 'Data',
          value: 'Sample data',
          type: 'text',
        },
      ]
    }

    return rows
  }

  /**
   * Generate detail rows từ GraphQL endpoint
   */
  private generateGraphQLDetailRows(endpoint: ApiEndpoint, schema: ApiSchema): Any[] {
    const rows: Any[] = []
    let rowCounter = 1

    try {
      const operationName = endpoint.query || endpoint.mutation || endpoint.subscription

      if (operationName && schema.schema) {
        // Thử parse GraphQL schema để tạo rows thông minh hơn
        const gqlSchema = schema.schema

        // Tìm type definition trong schema
        if (typeof gqlSchema === 'string') {
          // Parse GraphQL schema string để tìm return type
          const returnTypeRows = this.parseGraphQLSchemaForDetailRows(gqlSchema, operationName, rowCounter)

          if (returnTypeRows.length > 0) {
            return returnTypeRows
          }
        } else if (typeof gqlSchema === 'object' && gqlSchema.types) {
          // Handle GraphQL introspection schema
          const introspectionRows = this.parseGraphQLIntrospectionForDetailRows(gqlSchema, operationName, rowCounter)

          if (introspectionRows.length > 0) {
            return introspectionRows
          }
        }
      }

      // Fallback: Tạo rows dựa trên operation name
      if (operationName) {
        // Dựa trên tên operation để đoán fields phù hợp
        if (operationName.toLowerCase().includes('user')) {
          rows.push(
            {
              id: `row_${rowCounter++}`,
              name: 'ID',
              value: 'user_123456',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Tên',
              value: 'Người dùng mẫu',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Email',
              value: '<EMAIL>',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Ngày tạo',
              value: '2024-01-15T10:30:00Z',
              type: 'datetime',
            }
          )
        } else if (operationName.toLowerCase().includes('post')) {
          rows.push(
            {
              id: `row_${rowCounter++}`,
              name: 'ID',
              value: 'post_123456',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Tiêu đề',
              value: 'Bài viết mẫu',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Nội dung',
              value: 'Nội dung bài viết...',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Ngày đăng',
              value: '2024-01-15T10:30:00Z',
              type: 'datetime',
            }
          )
        } else {
          // Generic fallback
          rows.push(
            {
              id: `row_${rowCounter++}`,
              name: 'ID',
              value: 'item_123456',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: this.humanize(operationName),
              value: 'Dữ liệu mẫu',
              type: 'text',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Ngày tạo',
              value: '2024-01-15T10:30:00Z',
              type: 'datetime',
            },
            {
              id: `row_${rowCounter++}`,
              name: 'Trạng thái',
              value: true,
              type: 'boolean',
            }
          )
        }
      }

      // Nếu không có rows, tạo mặc định
      if (rows.length === 0) {
        rows.push(
          {
            id: `row_${rowCounter++}`,
            name: 'ID',
            value: 'gql_123',
            type: 'text',
          },
          {
            id: `row_${rowCounter++}`,
            name: 'Data',
            value: 'GraphQL data',
            type: 'text',
          }
        )
      }
    } catch (error) {
      console.error('Error generating GraphQL detail rows:', error)

      // Fallback rows
      return [
        {
          id: 'row_1',
          name: 'ID',
          value: 'gql_123',
          type: 'text',
        },
        {
          id: 'row_2',
          name: 'Data',
          value: 'GraphQL data',
          type: 'text',
        },
      ]
    }

    return rows
  }

  /**
   * Map Swagger types to detail row types
   */
  private mapSwaggerTypeToDetailType(type: string, format?: string): string {
    switch (type) {
      case 'string':
        if (format === 'date') return 'date'

        if (format === 'date-time') return 'datetime'

        return 'text'
      case 'integer':
      case 'number':
        return 'number'
      case 'boolean':
        return 'boolean'
      case 'array':
        return 'array'
      case 'object':
        return 'object'
      default:
        return 'text'
    }
  }

  /**
   * Get default value for data type
   */
  private getDefaultValue(type: string, format?: string): Any {
    switch (type) {
      case 'string':
        if (format === 'date') return '2024-01-15'

        if (format === 'date-time') return '2024-01-15T10:30:00Z'

        return 'Sample text'
      case 'integer':
      case 'number':
        return 123
      case 'boolean':
        return true
      case 'array':
        return []
      case 'object':
        return {}
      default:
        return 'Sample value'
    }
  }

  /**
   * Parse GraphQL schema string để tạo detail rows
   */
  private parseGraphQLSchemaForDetailRows(schemaString: string, operationName: string, startCounter: number): Any[] {
    const rows: Any[] = []
    const rowCounter = startCounter

    try {
      // Tìm return type của operation trong schema
      const lines = schemaString.split('\n')
      let inQuery = false
      let returnType = ''

      for (const line of lines) {
        const trimmedLine = line.trim()

        // Tìm type Query/Mutation/Subscription
        if (
          trimmedLine.includes('type Query') ||
          trimmedLine.includes('type Mutation') ||
          trimmedLine.includes('type Subscription')
        ) {
          inQuery = true
          continue
        }

        if (inQuery && trimmedLine === '}') {
          inQuery = false
          continue
        }

        if (inQuery && trimmedLine.includes(operationName)) {
          // Parse return type: getUser(id: ID!): User
          const match = trimmedLine.match(/:\s*(\w+)/)

          if (match) {
            returnType = match[1] as string
            break
          }
        }
      }

      // Nếu tìm được return type, tìm definition của type đó
      if (returnType) {
        const typeRows = this.findGraphQLTypeFields(schemaString, returnType, rowCounter)

        if (typeRows.length > 0) {
          return typeRows
        }
      }
    } catch (error) {
      console.error('Error parsing GraphQL schema:', error)
    }

    return rows
  }

  /**
   * Parse GraphQL introspection để tạo detail rows
   */
  private parseGraphQLIntrospectionForDetailRows(
    introspectionSchema: Any,
    operationName: string,
    startCounter: number
  ): Any[] {
    const rows: Any[] = []
    const rowCounter = startCounter

    try {
      // TODO: Implement GraphQL introspection parsing
      // Đây là placeholder cho việc parse introspection schema
      console.log('GraphQL introspection parsing not implemented yet', rowCounter, introspectionSchema, operationName)
    } catch (error) {
      console.error('Error parsing GraphQL introspection:', error)
    }

    return rows
  }

  /**
   * Tìm fields của một GraphQL type
   */
  private findGraphQLTypeFields(schemaString: string, typeName: string, startCounter: number): Any[] {
    const rows: Any[] = []
    let rowCounter = startCounter

    try {
      const lines = schemaString.split('\n')
      let inType = false
      const typeStartPattern = new RegExp(`type\\s+${typeName}\\s*{`)

      for (const line of lines) {
        const trimmedLine = line.trim()

        if (typeStartPattern.test(trimmedLine)) {
          inType = true
          continue
        }

        if (inType && trimmedLine === '}') {
          break
        }

        if (inType && trimmedLine && !trimmedLine.startsWith('#')) {
          // Parse field: id: ID! hoặc name: String
          const fieldMatch = trimmedLine.match(/(\w+):\s*(\w+)/)

          if (fieldMatch) {
            const [, fieldName, fieldType] = fieldMatch
            const dataType = this.mapGraphQLTypeToDetailType(fieldType as string)
            const value = this.getGraphQLDefaultValue(fieldType as string, fieldName as string)

            rows.push({
              id: `row_${rowCounter++}`,
              name: this.humanize(fieldName as string),
              value: value,
              type: dataType,
            })
          }
        }
      }
    } catch (error) {
      console.error('Error finding GraphQL type fields:', error)
    }

    return rows
  }

  /**
   * Map GraphQL types to detail row types
   */
  private mapGraphQLTypeToDetailType(gqlType: string): string {
    // Remove ! and [] from type
    const cleanType = gqlType.replace(/[![\]]/g, '')

    switch (cleanType) {
      case 'String':
        return 'text'
      case 'Int':
      case 'Float':
        return 'number'
      case 'Boolean':
        return 'boolean'
      case 'ID':
        return 'text'
      case 'Date':
        return 'date'
      case 'DateTime':
        return 'datetime'
      default:
        // Custom types
        return 'object'
    }
  }

  /**
   * Get GraphQL default value based on type and field name
   */
  private getGraphQLDefaultValue(gqlType: string, fieldName: string): Any {
    const cleanType = gqlType.replace(/[![\]]/g, '')
    const lowerFieldName = fieldName.toLowerCase()

    // Smart defaults based on field names
    if (lowerFieldName.includes('id')) {
      return `${fieldName}_123456`
    }

    if (lowerFieldName.includes('email')) {
      return '<EMAIL>'
    }

    if (lowerFieldName.includes('name') || lowerFieldName.includes('title')) {
      return `Sample ${fieldName}`
    }

    if (lowerFieldName.includes('date') || lowerFieldName.includes('time')) {
      return '2024-01-15T10:30:00Z'
    }

    if (lowerFieldName.includes('url') || lowerFieldName.includes('link')) {
      return 'https://example.com'
    }

    if (lowerFieldName.includes('count') || lowerFieldName.includes('number')) {
      return 42
    }

    // Type-based defaults
    switch (cleanType) {
      case 'String':
        return `Sample ${fieldName}`
      case 'Int':
      case 'Float':
        return 123
      case 'Boolean':
        return true
      case 'ID':
        return `${cleanType}_123456`
      case 'Date':
        return '2024-01-15'
      case 'DateTime':
        return '2024-01-15T10:30:00Z'
      default:
        return `Sample ${fieldName}`
    }
  }
}

export const formGeneratorService = new FormGeneratorService()
