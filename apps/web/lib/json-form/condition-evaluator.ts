import { Any } from '../types'

// Simple JSONata-like expression evaluator
export function evaluateCondition(condition: string, data: Record<string, Any>): boolean {
  if (!condition.trim()) return true

  try {
    // Replace field names with data values
    const expression = condition

    // Handle simple equality checks
    const equalityMatch = expression.match(/(\w+)\s*=\s*"([^"]*)"/)

    if (equalityMatch) {
      const [, field, value] = equalityMatch

      return String(data[field as string] || '') === value
    }

    // Handle boolean checks
    const booleanMatch = expression.match(/(\w+)\s*=\s*(true|false)/)

    if (booleanMatch) {
      const [, field, value] = booleanMatch

      return Boolean(data[field as string]) === (value === 'true')
    }

    // Handle numeric comparisons
    const numericMatch = expression.match(/(\w+)\s*([><=!]+)\s*(\d+)/)

    if (numericMatch) {
      const [, field, operator, value] = numericMatch
      const fieldValue = Number(data[field as string] || 0)
      const compareValue = Number(value)

      switch (operator) {
        case '>':
          return fieldValue > compareValue
        case '<':
          return fieldValue < compareValue
        case '>=':
          return fieldValue >= compareValue
        case '<=':
          return fieldValue <= compareValue
        case '==':
          return fieldValue === compareValue
        case '!=':
          return fieldValue !== compareValue
        default:
          return false
      }
    }

    // Handle contains function
    const containsMatch = expression.match(/\$contains$$(\w+),\s*"([^"]*)"$$/)

    if (containsMatch) {
      const [, field, value] = containsMatch
      const fieldValue = String(data[field as string] || '')

      return fieldValue.includes(value as string)
    }

    // Handle exists function
    const existsMatch = expression.match(/\$exists$$(\w+)$$/)

    if (existsMatch) {
      const [, field] = existsMatch

      return data[field as string] !== undefined && data[field as string] !== null && data[field as string] !== ''
    }

    // Handle not exists
    const notExistsMatch = expression.match(/\$not$$\$exists\((\w+)$$\)/)

    if (notExistsMatch) {
      const [, field] = notExistsMatch

      return data[field as string] === undefined || data[field as string] === null || data[field as string] === ''
    }

    return true
  } catch (error) {
    console.warn('Error evaluating condition:', condition, error)

    return true
  }
}
