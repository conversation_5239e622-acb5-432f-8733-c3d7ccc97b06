import { Template } from '@/app/(full-screen)/forms/e-form/form-builder'
import { DBSchema, IDBPDatabase, openDB } from 'idb'

interface TemplateDB extends DBSchema {
  templates: {
    key: string
    value: Template
    indexes: {
      'by-name': string
      'by-date': Date
    }
  }
}

class TemplateService {
  private db: IDBPDatabase<TemplateDB> | null = null
  private readonly DB_NAME = 'form-builder-db'
  private readonly STORE_NAME = 'templates'
  private readonly VERSION = 1

  async init() {
    if (!this.db) {
      this.db = await openDB<TemplateDB>(this.DB_NAME, this.VERSION, {
        upgrade(db) {
          const store = db.createObjectStore('templates', { keyPath: 'id' })
          store.createIndex('by-name', 'name')
          store.createIndex('by-date', 'createdAt')
        },
      })
    }

    return this.db
  }

  async saveTemplate(template: Template): Promise<void> {
    console.log('save template', template)
    const db = await this.init()
    const templateWithDates = {
      ...template,
      createdAt: template.createdAt || new Date(),
    }
    await db.put(this.STORE_NAME, templateWithDates)
  }

  async getTemplate(id: string): Promise<Template | undefined> {
    const db = await this.init()

    return db.get(this.STORE_NAME, id)
  }

  async getAllTemplates(): Promise<Template[]> {
    const db = await this.init()

    return db.getAll(this.STORE_NAME)
  }

  async searchTemplates(
    query: string,
    page: number = 1,
    pageSize: number = 5,
    status: number = 1
  ): Promise<{
    templates: Template[]
    total: number
    totalPages: number
  }> {
    const db = await this.init()
    const allTemplates = await db.getAll(this.STORE_NAME)

    // Lọc templates theo tên và status
    const filteredTemplates = allTemplates.filter(
      template =>
        template.status === status &&
        (template.name.toLowerCase().includes(query.toLowerCase()) ||
          template.description?.toLowerCase().includes(query.toLowerCase()))
    )

    // Tính toán phân trang
    const total = filteredTemplates.length
    const totalPages = Math.ceil(total / pageSize)
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      templates: filteredTemplates.slice(start, end),
      total,
      totalPages,
    }
  }

  async deleteTemplate(id: string): Promise<void> {
    const db = await this.init()
    await db.delete(this.STORE_NAME, id)
  }

  async updateTemplate(template: Template): Promise<void> {
    const db = await this.init()
    const templateWithDates = {
      ...template,
      updatedAt: new Date(),
    }
    await db.put(this.STORE_NAME, templateWithDates)
  }

  async updateTemplateStatus(templateId: string, status: number): Promise<void> {
    const db = await this.init()
    const template = await db.get(this.STORE_NAME, templateId)

    if (template) {
      await db.put(this.STORE_NAME, { ...template, status })
    }
  }

  async deleteTemplatePermanently(templateId: string): Promise<void> {
    const db = await this.init()
    await db.delete(this.STORE_NAME, templateId)
  }
}

export const templateService = new TemplateService()
