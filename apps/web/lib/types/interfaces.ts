/* eslint-disable @typescript-eslint/no-explicit-any */
// Auto-generated interfaces from TypeORM entities
// DO NOT EDIT MANUALLY
import {
  AssignWorkTargetGroupProcessStatus,
  CaseSimpleStatus,
  CategoryStatus,
  CollabCardStatus,
  EnumCaseAdvancedStatus,
  EnumGender,
  ModelStatus,
  ProbationAssessmentStatus,
  ProbationResult,
  ProcessStatus,
  StaffBusinessRole,
  StaffContractStatus,
  StaffContractType,
  StaffOrgEventType,
  StaffType,
  UploadSessionStatus,
  UserStatus,
  WorkTargetStatus,
  WorkTargetType,
  WorkflowHistoryStatus,
  WorkflowStatus,
  WorkflowSubTaskStatus,
  WorkflowSubTaskType,
} from '@ac/data-types'

export interface Account {
  userId: string
  provider: string
  providerAccountId: string
  type: string
  refresh_token?: string
  access_token?: string
  expires_at?: number
  token_type?: string
  scope?: string
  id_token?: string
  session_state?: string
  oauth_token_secret?: string
  oauth_token?: string
  createdAt: string
  updatedAt: string
  user: User
}

export interface AssignPermission {
  id: string
  subjectId: string
  subjectType: string
  refId?: string
  permission: string
  resource?: string
  createdAt: string
  updatedAt: string
}

export interface AssignRole {
  id: string
  roleId: string
  subjectId: string
  subjectType: string
  refId?: string
  resource?: string
  createdAt: string
  updatedAt: string
  role: Role
  user: User
}

export interface Authenticator {
  userId: string
  credentialId: string
  providerAccountId: string
  credentialPublicKey: string
  counter: number
  credentialDeviceType: string
  credentialBackedUp: boolean
  transports?: string
  user: User
}

export interface CaseAdvanced {
  id: string
  oId?: number
  code?: string
  name?: string
  orgUnit: OrganizationUnit
  orgUnitId: string
  status?: EnumCaseAdvancedStatus
  appointmentContent?: string
  appointmentDate?: string
  appointmentPlace?: string
  appointmentTime?: string
  appraiser?: number
  assignmentStatus?: number
  attachType?: number
  commentLegal?: string
  content?: string
  contentRequest?: string
  coordinatorId?: string
  coordinatorName?: string
  createdById?: string
  createDate?: string
  datePrint?: string
  decentralizationId?: number
  documentAttachName?: string
  entryDate?: string
  executionTime?: number
  formsOfAssistance?: string
  formsOfAssistanceId?: string
  fromPhaseLegal?: number
  guardianName?: string
  guardianAddress?: string
  guardianPhone?: string
  guardianCardNumber?: string
  guardianProvince?: string
  guardianDistrict?: string
  guardianWard?: string
  guardianPublishedDate?: string
  guardianPublishedPlace?: string
  indexLegalCode?: number
  indexLegalYear?: number
  isActive?: number
  attachForRequestPerson?: number
  attachForLegalCase?: number
  isNotFullProfile?: number
  isOldLegalAidCase?: number
  isPrint?: number
  isSavedProfile?: number
  isTransferExpertise?: number
  legalField?: string
  legalFieldId?: string
  legalOnlineCode?: string
  legalOnlineId?: number
  legalType?: number
  note?: string
  objectLegalField?: string
  objectLegalFieldId?: string
  objectLegalFieldIdReport?: number
  legalAidCaseName?: string
  paymentSource?: string
  payComment?: string
  payDate?: string
  payMoney?: number
  payPeople?: number
  payStatus?: number
  performer?: string
  placeOfDisposal?: string
  rate?: number
  rateDate?: string
  receiver?: string
  rpName?: string
  rpBirthDate?: string
  rpSex?: EnumGender
  rpCardNumber?: string
  rpPublishedDate?: string
  rpPublishedPlace?: string
  rpEthnic?: string
  rpAddress?: string
  rpProvince?: string
  rpDistrict?: string
  rpWard?: string
  rpPhone?: string
  rpJob?: string
  relationRp?: string
  rpRequestSource?: string
  rpEmail?: string
  toPhaseLegal?: number
  verifier?: string
  verifyDate?: string
  verifyStatus?: number
  currentWorkflowId?: string
  currentWorkflow?: Workflow
  currentProcessId?: string
  currentProcess?: Process
  keyword?: string
  createdAt: string
  updatedAt: string
  updatedById?: string
  updatedBy?: User
  createdBy?: User
}

export interface CaseAnswer {
  answerId: string
  answer?: string
  question?: string
  keyword?: string
  categoryId?: string
  category?: Category
  orgUnitId: string
  orgUnit: OrganizationUnit
  createdById?: string
  createdBy: User
  createdAt: string
  updatedById?: string
  updatedBy?: User
  updatedAt: string
  status: ProcessStatus
}

export interface CaseSimple {
  caseSimpleId: string
  caseSimpleCode: string
  caseSimpleName: string
  entryDate?: string
  placeOfDisposal: string
  receiver: string
  rpName: string
  rpBirthDate?: string
  rpCardNumber?: string
  rpSex?: string
  rpEthnic?: string
  rpJob?: string
  rpEmail?: string
  rpPhone?: string
  rpAddress?: string
  rpProvince?: string
  rpProvinceNew?: string
  rpDistrict?: string
  rpWard?: string
  rpWardNew?: string
  rpPublishedDate?: string
  rpPublishedPlace?: string
  legalField: string
  legalFieldId: string
  objectLegalField: string
  objectLegalFieldId: string
  content: string
  contentRequest: string
  caseSimpleStatus?: CaseSimpleStatus
  orgUnitId: string
  orgUnit: OrganizationUnit
  decentralizationId: string
  createdById?: string
  createdBy: User
  createdAt: string
  updatedById?: string
  updatedBy?: User
  updatedAt: string
  status: ProcessStatus
  note: string
  documentAttachName: string
  guardianName: string
  guardianCardNumber: string
  guardianAddress: string
  guardianProvince: string
  guardianProvinceNew: string
  guardianDistrict: string
  guardianWard: string
  guardianWardNew: string
  keyword?: string
}

export interface Category {
  categoryId: string
  parentId?: string
  parent?: Category
  children?: Category[]
  name: string
  code?: string
  type: string
  description?: string
  keyword?: string
  status: CategoryStatus
  updatedById?: string
  updatedBy?: User
  legacyId?: number
  legacyParentId?: number
  createdAt: string
  updatedAt: string
}

export interface EForm {
  id: string
  name?: string
  code: string
  status: ModelStatus
  jsonData: string
  orgUnitId: string
  orgUnit?: OrganizationUnit
  keyword?: string
  createdAt: string
  updatedAt: string
  updatedById?: string
  createdById?: string
  updatedBy?: User
  createdBy?: User
}

export interface OrganizationUnit {
  id: string // ID đơn vị tổ chức
  oldId?: number
  code: string
  name: string
  fullName?: string
  address?: string
  representative?: string
  phone?: string
  fax?: string
  email?: string
  website?: string
  oldProvinceId?: string
  cityId?: number
  oldDistrictId?: string
  oldWardId?: string
  parentId?: string
  parent?: OrganizationUnit
  children?: OrganizationUnit[]
  staff?: Array<any> // Staff[] at runtime - using any to avoid circular dependency[]
  status: number
  type?: number
  organizationGroupId?: string
  establishmentDate?: string
  decisionDate?: string
  decisionNumber?: string
  legalFieldId?: any
  legalFormId?: string[]
  rangeActivitiesId?: string
  payrollNumber?: number
  roomNumber?: number
  clubNumber?: number
  adminGroupId?: string
  adminStatusId?: number
  experience?: string
  deadlineContractDate?: string
  note?: string
  createdAt: string
  updatedAt: string
  oldProvince?: Category
  oldDistrict?: Category
  oldWard?: Category
  organizationGroup?: Category
  legalField?: Category
  rangeActivities?: Category
  adminGroup?: Category
  createById?: string
  updateById?: string
  createdBy?: User
  updatedBy?: User
  issueDate?: string
  issuingAgency?: string
  participationReason?: string
  registrationTerminationReason?: string
  contractTerminationReason?: string
  expiryDate?: string
  renewalMonths?: number
  renewedExpiryDate?: string
  contractNumber?: string
  registrationNumber?: string
  keyword?: string
  contractFileId?: string
  contractFileRef?: string
  attachments?: any
}

export interface StaffCardHistory {
  id: string
  staffId: string
  staff: Staff
  cardNumber: string // Oracle: CARD_NUMBER - Mã thẻ CTV
  issuedAt: string
  status: CollabCardStatus // Oracle: STATUS
  isCurrent: number // Oracle: IS_CURRENT - 1
  fileRefFront?: string // Oracle: FILE_REF_FRONT
  fileRefBack?: string // Oracle: FILE_REF_BACK
  note?: string // Oracle: NOTE
  createdAt: string
  updatedAt: string
}

export interface StaffContract {
  id: string // Oracle: ID
  staffId: string // Oracle: STAFF_ID
  staff: Staff
  organizationUnitId: string // Oracle: ORGANIZATION_UNIT_ID
  organizationUnit: OrganizationUnit
  contractNo: string // Oracle: CONTRACT_NO
  contractType: StaffContractType // Oracle: CONTRACT_TYPE
  startDate: string
  endDate?: string
  status: StaffContractStatus // Oracle: STATUS
  fileRef?: string // Oracle: FILE_REF
  fileId?: string // Oracle: FILE_ID
  file?: Media
  note?: string // Oracle: NOTE
  createdAt: string
  updatedAt: string
}

export interface StaffOrgEvent {
  id: string
  historyId: string
  history: StaffOrganizationHistory
  eventType: StaffOrgEventType // APPOINTMENT
  eventDate: string
  termYears?: number
  decisionNumber?: string
  decisionDate?: string
  decisionAuthority?: string
  reason?: string
  note?: string
  fileRef?: string // link/ID tới DMS/S3
  createdAt: string
  updatedAt: string
}

export interface StaffOrgHistoryAction {
  id: string
  historyId: string
  history: StaffOrganizationHistory
  rewardDisciplineId: string
  rewardDiscipline: Category
  createdAt: string
  updatedAt: string
}

export interface StaffOrgRole {
  id: string // Oracle: ID
  staffOrgId: string // Oracle: STAFF_ORG_ID
  role: StaffBusinessRole // Oracle: ROLE - Vai trò trong đơn vị cụ thể
  createdAt: string
}

export interface StaffOrganizationHistory {
  id: string
  staffId: string
  staff: Staff
  organizationUnitId: string
  organizationUnit: OrganizationUnit
  positionId?: string
  position?: Category
  levelId?: string
  level?: Category
  legalAidFormId?: string // Oracle: LEGAL_AID_FORM_ID
  legalAidForm?: Category
  actions?: StaffOrgHistoryAction[]
  events?: StaffOrgEvent[]
  staffOrgRoles?: StaffOrgRole[]
  specializations?: StaffSpecialization[]
  isHead: number // 1
  startDate: string
  endDate?: string
  status: number
  decisionNumber?: string
  decisionDate?: string
  decisionAuthority?: string
  reason?: string
  note?: string
  isProbation: number // 1
  probationStartDate?: string
  probationDurationMonths?: number // Thời gian tập sự (tháng)
  probationResult?: ProbationResult // Đạt / Không đạt
  probationAssessmentStatus?: ProbationAssessmentStatus // Chưa kiểm tra / Đạt / Không đạt
  probationExemptionReason?: string // Lý do miễn giảm tập sự
  probationAttachments?: string // Đính kèm quyết định tập sự
  createdAt: string
  updatedAt: string
}

export interface StaffPreviousPosition {
  id: string
  staff: Staff
  category: Category
  organizationUnit?: OrganizationUnit
}

export interface StaffSpecialization {
  id: string
  staffOrganization: StaffOrganizationHistory
  specialization: Category
}

export interface StaffTrainingHistory {
  id: string
  staffId?: string
  staff: Staff
  trainingClassId?: string
  trainingClass: TrainingClass
  registrationDate?: string
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export interface Staff {
  id: string
  organizationUnitId?: string
  fullName: string
  dateOfBirth?: string
  genderId?: string
  ethnicId?: string
  email?: string
  phone?: string
  address?: string
  permanentAddress?: string
  provinceId?: string
  districtId?: string
  wardId?: string
  cccd?: string
  cccdIssuanceDate?: string
  cccdPlaceOfIssuance?: string
  professionalLevelId?: string
  staffType?: StaffType
  maritalStatus?: string
  avatarRef?: string // link ảnh hồ sơ (DMS/S3)
  activityStatusId?: string
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  yearsOfExperience?: number
  contracts?: StaffContract[]
  organizationHistory?: StaffOrganizationHistory[]
  cardHistory?: StaffCardHistory[]
  trainingHistory?: StaffTrainingHistory[]
  previousPositions?: StaffPreviousPosition[]
}

export interface TrainingClass {
  id: string
  trainingClassName: string // Tên lớp đào tạo
  description?: string // Mô tả lớp đào tạo
  startDate: string
  endDate?: string
  trainingOrganizationId?: string
  trainingOrganization?: OrganizationUnit
  trainingTypeId?: string // Loại hình đào tạo - reference đến category
  trainingType?: Category
  trainingFormatId?: string // Hình thức đào tạo - reference đến category
  trainingFormat?: Category
  trainingHours?: number // Số giờ đào tạo
  maxParticipants?: number // Số lượng tối đa
  currentParticipants: number // Số lượng hiện tại
  status: string // PLANNING, OPEN_REGISTRATION, IN_PROGRESS, COMPLETED, CANCELLED
  certificate?: string // Văn bằng chứng chỉ
  keyword?: string
  note?: string
  decisionNumber?: string
  decisionDate?: string
  fileRef?: string
  participantHistory?: StaffTrainingHistory[]
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export interface Process {
  id: string
  name?: string
  code: string
  status: ProcessStatus
  isDefault: number
  jsonData: string
  orgUnitId: string
  orgUnit?: OrganizationUnit
  keyword?: string
  createdAt: string
  updatedAt: string
  updatedById?: string
  createdById?: string
  updatedBy?: User
  createdBy?: User
}

export interface Role {
  id: string
  name: string
  code: string
  description: string
  permissions: string[]
  keyword: string
  createdAt: string
  updatedAt: string
  type: number
  isSystem: number
  status: number
  assignRoles: AssignRole[]
  createBy?: User
  updateBy?: User
}

export interface Session {
  id: string
  sessionToken: string
  userId: string
  expires: string
  createdAt: string
  updatedAt: string
  user: User
}

export interface User {
  id: string
  name?: string
  email: string
  password?: string
  status: UserStatus
  emailVerified?: string
  image?: string
  phone?: string
  keyword?: string
  staffId?: string
  orgUnit?: OrganizationUnit
  orgUnitId?: string
  createdAt: string
  updatedAt: string
  accounts: Account[]
  sessions: Session[]
  authenticators: Authenticator[]
  createdRoles: Role[]
  updatedRoles: Role[]
  assignRoles: AssignRole[]
}

export interface VerificationToken {
  id: string
  identifier: string
  token: string
  expires: string
}

export interface WorkTargetGroup {
  id: string
  name: string
  note: string
  orgUnitId: string
  processStatus: WorkTargetStatus
  requiredRate: number
  onTimeRate: number
  status: ProcessStatus
  endDate: string
  keyword?: string
  categoryId?: string
  category?: Category
  workTargets?: any
  createdAt: string
  updatedAt: string
  updatedById?: string
  createdById?: string
  updatedBy?: User
  createdBy?: User
}

export interface WorkflowHistory {
  id: string
  actions: string[]
  selectedAction?: string
  fixedForm?: string
  stepId: string
  stepName: string
  status: WorkflowHistoryStatus
  data: any
  logData: any
  content: any
  parentHistoryId?: string
  backToHistoryId?: string
  parentHistory?: WorkflowHistory
  workflowId: string
  workflow?: Workflow
  processId: string
  process?: Process
  caseAdvancedId: string
  caseAdvanced?: CaseAdvanced
  orgUnitId: string
  orgUnit?: OrganizationUnit
  assignedToId?: string
  assignedTo?: User
  assignedAt?: string
  startedAt?: string
  completedAt?: string
  cancelledAt?: string
  assignedById?: string
  assignedBy?: User
  createdAt: string
  updatedAt: string
}

export interface WorkflowSubTask {
  id: string
  status: WorkflowSubTaskStatus
  type: WorkflowSubTaskType
  data: string
  workflowId: string
  workflow?: Workflow
  processId: string
  process?: Process
  caseAdvancedId: string
  caseAdvanced?: CaseAdvanced
  fromOrgUnitId: string
  fromOrgUnit?: OrganizationUnit
  toOrgUnitId: string
  toOrgUnit?: OrganizationUnit
  assignedById: string
  assignedBy?: User
  assignedToId: string
  assignedTo?: User
  createdAt: string
  updatedAt: string
}

export interface Workflow {
  id: string
  data: string
  processData: any
  status: WorkflowStatus
  currentStepName: string
  currentStepStatus: string
  workflowHistoriesData: string
  processId: string
  caseAdvancedId: string
  orgUnitId: string
  temporalId?: string
  startedById: string
  process?: Process
  caseAdvanced?: CaseAdvanced
  orgUnit?: OrganizationUnit
  startedBy?: User
  createdAt: string
  updatedAt: string
}

export interface AssignWorkTargetGroup {
  id: string
  workTargetGroupId: string
  workTargetGroup: WorkTargetGroup
  orgUnitId: string
  orgUnit: OrganizationUnit
  processStatus: AssignWorkTargetGroupProcessStatus
  status: ProcessStatus
  endDate: string
  keyword?: string
  response?: any
  evaluatorId?: string
  evaluator: User
  evaluatorNote?: string
  approvalId?: string
  approval: User
  approvalNote?: string
  createdAt: string
  updatedAt: string
  updatedById?: string
  createdById?: string
  updatedBy?: User
  createdBy?: User
}

export interface UploadSession {
  id: string
  bucket: string
  objectKey: string
  uploadId: string
  status: UploadSessionStatus
  contentType?: string
  contentLength?: number
  checksum?: string
  ownerId?: string
  owner?: User
  metadata?: Record<string, unknown>
  expiresAt?: string
  parts?: UploadPart[]
  createdAt: string
  updatedAt: string
}

export interface UploadPart {
  id: string
  sessionId: string
  partNumber: number
  eTag: string
  size?: number
  createdAt: string
  updatedAt: string
}

export interface Media {
  id: string
  bucket: string
  objectKey: string
  fileName?: string
  contentType?: string
  contentLength?: number
  checksum?: string
  type?: string
  eTag?: string
  publicUrl?: string
  ownerId?: string
  owner?: User
  sessionId?: string
  session?: UploadSession
  orgUnitId?: string
  orgUnit?: OrganizationUnit
  metadata?: Record<string, unknown>
  contracts?: StaffContract[]
  createdAt: string
  updatedAt: string
}
