/**
 * Utility functions for generating case codes
 * Format: [type hình thức][DD][MM][YYYY][XXXX]
 * Example: TGTT05122024ABCD (Tr<PERSON> giúp tư vấn + 05/12/2024 + random 4 letters)
 */
import { Category } from '../services/types'

/**
 * Generate random 4 uppercase letters
 * @returns Random 4 character string
 */
function generateRandomLetters(): string {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  let result = ''

  for (let i = 0; i < 4; i++) {
    result += letters.charAt(Math.floor(Math.random() * letters.length))
  }

  return result
}

/**
 * Generate case code based on form type and current date
 * @param formsOfAssistance - The selected form of assistance category
 * @param customDate - Optional custom date, defaults to current date
 * @returns Generated case code
 */
export function generateCaseCode(formsOfAssistance: Category | null, customDate?: Date): string {
  const date = customDate || new Date()

  // Format date as DDMMYYYY
  const day = date.getDate().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const year = date.getFullYear().toString()

  // Get type code from category
  let typeCode = ''

  if (formsOfAssistance) {
    // Use the category code or generate from name
    typeCode = formsOfAssistance.code || generateTypeCodeFromName(formsOfAssistance.name)
  }

  // Generate random 4 uppercase letters
  const randomLetters = generateRandomLetters()

  return `${typeCode}${day}${month}${year}${randomLetters}`
}

/**
 * Generate type code from category name
 * @param name - Category name
 * @returns Generated type code
 */
function generateTypeCodeFromName(name: string): string {
  // Common mappings for Vietnamese legal aid forms
  const typeMapping: Record<string, string> = {
    'Tư vấn pháp luật': 'TVPL',
    'Tư vấn trực tiếp': 'TVTT',
    'Tư vấn qua điện thoại': 'TVDT',
    'Tư vấn trực tuyến': 'TVTO',
    'Soạn thảo văn bản': 'STVB',
    'Đại diện ngoài tố tụng': 'DDNT',
    'Bào chữa': 'BC',
    'Bảo vệ quyền và lợi ích hợp pháp': 'BVQL',
    'Tham gia tố tụng': 'TGTT',
    'Đại diện tại tòa': 'DDTT',
    'Hòa giải': 'HG',
    'Trợ giúp khác': 'TGK',
  }

  // Check if we have a direct mapping
  if (typeMapping[name]) {
    return typeMapping[name]
  }

  // Generate code from name if no direct mapping
  return name
    .toUpperCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^A-Z0-9\s]/g, '') // Keep only letters, numbers, spaces
    .split(' ')
    .map(word => word.substring(0, 2)) // Take first 2 letters of each word
    .join('')
    .substring(0, 4) // Limit to 4 characters
}

/**
 * Parse case code to extract information
 * @param caseCode - The case code to parse
 * @returns Parsed information
 */
export function parseCaseCode(caseCode: string): {
  typeCode: string
  day: string
  month: string
  year: string
  randomLetters: string
  date: Date | null
} {
  // Assuming format: [TYPE][DD][MM][YYYY][XXXX]
  // Minimum length should be 14 (2 chars type + 8 chars date + 4 chars random)
  if (caseCode.length < 14) {
    return {
      typeCode: caseCode,
      day: '',
      month: '',
      year: '',
      randomLetters: '',
      date: null,
    }
  }

  // Extract random letters (last 4 characters)
  const randomLetters = caseCode.slice(-4)

  // Extract date part (8 characters before random letters)
  const datePart = caseCode.slice(-12, -4)
  const day = datePart.substring(0, 2)
  const month = datePart.substring(2, 4)
  const year = datePart.substring(4, 8)

  // Extract type code (everything before date and random letters)
  const typeCode = caseCode.slice(0, -12)

  // Try to create date
  let date: Date | null = null

  try {
    const parsedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))

    if (!isNaN(parsedDate.getTime())) {
      date = parsedDate
    }
  } catch (error) {
    console.log('error parseCaseCode', error)
    // Invalid date
  }

  return {
    typeCode,
    day,
    month,
    year,
    randomLetters,
    date,
  }
}

/**
 * Validate case code format
 * @param caseCode - The case code to validate
 * @returns True if valid format
 */
export function validateCaseCode(caseCode: string): boolean {
  if (!caseCode || caseCode.length < 14) {
    return false
  }

  const parsed = parseCaseCode(caseCode)

  return parsed.date !== null
}

/**
 * Format case code for display
 * @param caseCode - The case code to format
 * @returns Formatted case code with dots
 */
export function formatCaseCodeDisplay(caseCode: string): string {
  const parsed = parseCaseCode(caseCode)

  if (!parsed.date) {
    return caseCode
  }

  return `${parsed.typeCode}.${parsed.day}.${parsed.month}.${parsed.year}.${parsed.randomLetters}`
}
