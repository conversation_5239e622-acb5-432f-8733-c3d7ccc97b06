import { CategoryStatus } from '@/app/(pm2)/categories/category-status'
import { ApiClientInstance, AxiosRequestConfig } from '@/lib/api-client'

import { Any } from '../types'
import { Category, CategoryListResponse } from './types'

export interface CategoryListParams {
  page?: number
  pageSize?: number
  keyword?: string
  orderBy?: string
  orderDir?: 'ASC' | 'DESC'
  filter?: {
    type?: string
    status?: CategoryStatus
    parentId?: string
    [key: string]: Any
  }
}

export interface CategoryCreatePayload {
  name: string
  code: string
  description?: string
  parentId?: string
  type: string
  status?: CategoryStatus
}

export type CategoryUpdatePayload = Partial<CategoryCreatePayload>

// Helper function to get ID from category object
// TODO: Tại sao có hàm này
export const getCategoryId = (category: Any): string => {
  const result = category?.id || category?._id || category?.categoryId || ''

  return result
}

// Category API functions
// Note: These functions use ApiClientInstance which works on both client and server side
// For server-side with custom URL, use: createServerApiClient(customUrl).get(...)

const getCategories = (params: CategoryListParams, config: AxiosRequestConfig = {}) => {
  // Prepare query params - let the HTTP client handle encoding normally
  // Server should be able to decode URL-encoded JSON
  const queryParams: Any = {}

  // Add basic params
  if (params.page) queryParams.page = params.page

  if (params.pageSize) queryParams.pageSize = params.pageSize

  if (params.keyword) queryParams.keyword = params.keyword

  if (params.orderBy) queryParams.orderBy = params.orderBy

  if (params.orderDir) queryParams.orderDir = params.orderDir

  // Add filter as JSON string - this will be URL encoded automatically
  if (params.filter && Object.keys(params.filter).length > 0) {
    queryParams.filter = JSON.stringify(params.filter)
  }

  return ApiClientInstance.get<CategoryListResponse>('categories', {
    ...config,
    params: queryParams,
  })
}

const getAllCategories = (type?: string, config: AxiosRequestConfig = {}) => {
  const queryParams: Any = {}

  // Add filter as JSON string if type is provided
  if (type) {
    queryParams.filter = JSON.stringify({ type })
  }

  return ApiClientInstance.get<Category[]>('categories/method/all', {
    ...config,
    params: queryParams,
  })
}

const getCategoryDetail = (id: string, config: AxiosRequestConfig = {}) => {
  return ApiClientInstance.get<Category>(`categories/${id}`, config)
}

const createCategory = (payload: CategoryCreatePayload, config: AxiosRequestConfig = {}) => {
  return ApiClientInstance.post<Category>('categories', payload, config)
}

const updateCategory = (id: string, payload: CategoryUpdatePayload, config: AxiosRequestConfig = {}) => {
  return ApiClientInstance.patch<Category>(`categories/${id}`, payload, config)
}

const deleteCategory = (id: string, config: AxiosRequestConfig = {}) => {
  return ApiClientInstance.delete(`categories/${id}`, config)
}

export const categoryApi = {
  getCategories,
  getAllCategories,
  getCategoryDetail,
  createCategory,
  updateCategory,
  deleteCategory,
}
