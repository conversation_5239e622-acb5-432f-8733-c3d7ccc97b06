import { CategoryStatus, WorkflowSubTaskType } from '@ac/data-types'

export interface CategoryListResponse {
  items: Category[]
  total: number
  totalPages: number
  // Legacy support
  data?: Category[]
  result?: Category[]
  page?: number
  pageSize?: number
}

export interface Category {
  id: string
  name: string
  code: string
  description?: string
  parentId?: string
  parent?: {
    id: string
    name: string
    code?: string
  }
  type: string
  status?: CategoryStatus
  createdAt: string
  updatedAt: string
  // Alternative ID fields for different APIs
  _id?: string
  categoryId: string
}

export interface WorkflowSubtask {
  id: string
  assignedToId: string
  caseAdvancedId: string
  data: string
  fromOrgUnitId: string
  processId: string
  toOrgUnitId: string
  type: WorkflowSubTaskType
  workflowId: string
  createdAt: string
  updatedAt: string
}
