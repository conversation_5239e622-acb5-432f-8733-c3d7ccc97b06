import { ApiClientInstance } from '../api-client'
import { Any } from '../types'

// History interfaces
interface CreateHistoryPayload {
  assignedToId?: string
  caseAdvancedId: string
  data: string // JSON stringified data
  orgUnitId: string
  processId: string
  workflowId: string
  action: string
  actions?: string[]
  selectedAction?: string
  stepId: string
}

interface WorkflowHistory {
  id: string
  assignedToId: string
  caseAdvancedId: string
  data: string
  orgUnitId: string
  processId: string
  workflowId: string
  action: string
  actions?: string[]
  selectedAction?: string
  stepId: string
  createdAt: string
  updatedAt: string
}

// History API service
export const workflowHistoryApi = {
  create: async (payload: CreateHistoryPayload): Promise<WorkflowHistory> => {
    return ApiClientInstance.post<WorkflowHistory>('workflows-Histories', payload)
  },

  getById: async (id: string): Promise<WorkflowHistory> => {
    return ApiClientInstance.get<WorkflowHistory>(`workflows-Histories/${id}`)
  },

  getList: async (params?: {
    filter?: {
      caseAdvancedId?: string
      assignedToId?: string
      // type?: WorkflowHistoryType
    }
    page?: number
    pageSize?: number
  }): Promise<WorkflowHistory[]> => {
    // Prepare query params - follow category API pattern
    const queryParams: Any = {}

    // Add basic params
    if (params?.page) queryParams.page = params.page

    if (params?.pageSize) queryParams.pageSize = params.pageSize

    // Add filter as JSON string - this will be URL encoded automatically
    if (params?.filter && Object.keys(params.filter).length > 0) {
      queryParams.filter = JSON.stringify(params.filter)
    }

    return ApiClientInstance.get<WorkflowHistory[]>('workflows-Histories', {
      params: queryParams,
    })
  },

  update: async (id: string, payload: Partial<CreateHistoryPayload>): Promise<WorkflowHistory> => {
    return ApiClientInstance.put<WorkflowHistory>(`workflows-Histories/${id}`, payload)
  },

  delete: async (id: string): Promise<void> => {
    return ApiClientInstance.delete<void>(`workflows-Histories/${id}`)
  },
}
