import { ApiClientInstance } from '../api-client'
import { Any, WorkflowSubTaskType } from '../types'
import { WorkflowSubtask } from './types'

// Subtask interfaces
export interface CreateSubtaskPayload {
  assignedToId?: string // Optional for some subtask types like KET_THUC
  caseAdvancedId: string
  data: string // JSON stringified data
  fromOrgUnitId: string
  processId: string
  toOrgUnitId: string
  type: WorkflowSubTaskType
  workflowId: string
}

// Subtask API service
export const workflowSubtaskApi = {
  create: async (payload: CreateSubtaskPayload): Promise<WorkflowSubtask> => {
    return ApiClientInstance.post<WorkflowSubtask>('workflows-subtasks', payload)
  },

  getById: async (id: string): Promise<WorkflowSubtask> => {
    return ApiClientInstance.get<WorkflowSubtask>(`workflows-subtasks/${id}`)
  },

  getList: async (params?: {
    filter?: {
      caseAdvancedId?: string
      assignedToId?: string
      type?: WorkflowSubTaskType
    }
    page?: number
    pageSize?: number
  }): Promise<WorkflowSubtask[]> => {
    // Prepare query params - follow category API pattern
    const queryParams: Any = {}

    // Add basic params
    if (params?.page) queryParams.page = params.page

    if (params?.pageSize) queryParams.pageSize = params.pageSize

    // Add filter as JSON string - this will be URL encoded automatically
    if (params?.filter && Object.keys(params.filter).length > 0) {
      queryParams.filter = JSON.stringify(params.filter)
    }

    return ApiClientInstance.get<WorkflowSubtask[]>('workflows-subtasks', {
      params: queryParams,
    })
  },

  update: async (id: string, payload: Partial<CreateSubtaskPayload>): Promise<WorkflowSubtask> => {
    return ApiClientInstance.put<WorkflowSubtask>(`workflows-subtasks/${id}`, payload)
  },

  delete: async (id: string): Promise<void> => {
    return ApiClientInstance.delete<void>(`workflows-subtasks/${id}`)
  },
}
