import { ApiClientInstance } from '../api-client'
import { CreateCaseAdvanceDto, UpdateCaseAdvanceDto } from '../hooks'
import { Any } from '../types'

// import { ApiResponse, PaginatedResponse } from '@/lib/api-client'

// const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

export interface CaseAdvanceListParams {
  page?: number
  pageSize?: number
  orderBy?: string
  orderDir?: 'ASC' | 'DESC'
  filter?: Record<string, Any>
  keyword?: string
  legalField?: string
  assignmentStatus?: number
  verifyStatus?: number
  payStatus?: number
  createDateFrom?: string
  createDateTo?: string
}

// API functions với real endpoints
export const caseAdvanceApi = {
  // Lấy danh sách vụ việc
  getList: async (params: CaseAdvanceListParams = {}) => {
    try {
      // Build query parameters theo format API
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        orderBy: params.orderBy || 'createdAt',
        orderDir: params.orderDir || 'DESC',
        filter: JSON.stringify(params.filter || {}),
        ...(params.keyword && { keyword: params.keyword }),
      }

      const result = await ApiClientInstance.get('case-advanced', {
        params: queryParams,
      })

      // Transform API response to match our expected format
      return {
        data: result.items || [],
        pagination: {
          total: result.total || 0,
          page: result.page || 1,
          limit: result.pageSize || 10,
          totalPages: Math.ceil((result.total || 0) / (result.pageSize || 10)),
        },
      }
    } catch (error) {
      console.error('Error fetching legal aid cases:', error)
      throw error
    }
  },

  getListAll: async (params: CaseAdvanceListParams = {}) => {
    try {
      // Build query parameters theo format API
      const queryParams = {
        orderBy: params.orderBy || 'createdAt',
        orderDir: params.orderDir || 'DESC',
        filter: JSON.stringify(params.filter || {}),
        ...(params.keyword && { keyword: params.keyword }),
      }

      const result = await ApiClientInstance.get('case-advanced/method/all', {
        params: queryParams,
      })

      // Transform API response to match our expected format
      return {
        data: result.items || [],
      }
    } catch (error) {
      console.error('Error fetching legal aid cases:', error)
      throw error
    }
  },

  // Lấy chi tiết vụ việc
  getById: async (id: string) => {
    try {
      const url = `case-advanced/${id}`

      const result = await ApiClientInstance.get(url)

      return result
    } catch (error) {
      console.error('Error fetching legal aid case detail:', error)
      throw error
    }
  },

  // Tạo mới vụ việc
  create: async (data: CreateCaseAdvanceDto) => {
    try {
      const url = `case-advanced`

      const result = await ApiClientInstance.post(url, data)

      return {
        data: result.data || result,
        message: result.message || 'Tạo vụ việc thành công',
      }
    } catch (error) {
      console.error('Error creating legal aid case:', error)
      throw error
    }
  },

  // Cập nhật vụ việc
  update: async (id: string, data: Partial<UpdateCaseAdvanceDto>) => {
    try {
      const url = `case-advanced/${id}`

      const result = await ApiClientInstance.patch(url, data)

      return {
        affected: result.affected || 0,
        message: result.message || 'Cập nhật vụ việc thành công',
      }
    } catch (error) {
      console.error('Error updating legal aid case:', error)
      throw error
    }
  },

  // Xóa vụ việc
  delete: async (id: string) => {
    try {
      const url = `case-advanced/${id}`

      const result = await ApiClientInstance.delete(url)

      return {
        affected: result.affected || 0,
        message: result.message || 'Xóa vụ việc thành công',
      }
    } catch (error) {
      console.error('Error deleting legal aid case:', error)
      throw error
    }
  },
}
