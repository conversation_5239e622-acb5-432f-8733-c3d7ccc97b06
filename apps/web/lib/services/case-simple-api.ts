import { ApiClientInstance } from '../api-client'

export interface CaseSimpleListParams {
  page?: number
  pageSize?: number
  orderBy?: string
  orderDir?: 'ASC' | 'DESC'
  filter?: Record<string, any>
  caseSimpleName?: string
  keyword?: string
  sex?: string
  payStatus?: number
  createDateFrom?: string
  createDateTo?: string
}

export const caseSimpleApi = {
  getList: async (params: CaseSimpleListParams = {}) => {
    try {
      // Build query parameters theo format API
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        orderBy: params.orderBy || 'createdAt',
        orderDir: params.orderDir || 'DESC',
        filter: JSON.stringify(params.filter || {}),
        ...(params.keyword && { keyword: params.keyword }),
      }

      const result = await ApiClientInstance.get('case-simples', {
        params: queryParams,
      })

      // Transform API response to match our expected format
      return {
        data: result.items || [],
        pagination: {
          total: result.total || 0,
          page: result.page || 1,
          limit: result.pageSize || 10,
          totalPages: Math.ceil((result.total || 0) / (result.pageSize || 10)),
        },
      }
    } catch (error) {
      console.error('Error fetching case simples:', error)
      throw error
    }
  },

  getById: async (id: string) => {
    try {
      const url = `case-simples/${id}`

      const result = await ApiClientInstance.get(url)

      return result
    } catch (error) {
      console.error('Error fetching legal aid case detail:', error)
      throw error
    }
  },
}
