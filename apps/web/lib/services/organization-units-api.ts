import { ApiClientInstance } from '../api-client'
import { Any } from '../types'

interface OrganizationUnitListParams {
  page?: number
  pageSize?: number
  orderBy?: string
  orderDir?: 'ASC' | 'DESC'
  filter?: Record<string, Any>
  keyword?: string
}

// API functions
export const organizationUnitsApi = {
  // L<PERSON>y danh sách đơn vị
  getList: async (params: OrganizationUnitListParams = {}) => {
    try {
      // Build query parameters theo format API
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        orderBy: params.orderBy || 'createdAt',
        orderDir: params.orderDir || 'DESC',
        filter: JSON.stringify(params.filter || {}),
        ...(params.keyword && { keyword: params.keyword }),
      }

      const result = await ApiClientInstance.get('organization-units', {
        params: queryParams,
      })

      // Transform API response to match our expected format
      return {
        data: result.items || [],
        items: result.items || [], // For compatibility
        pagination: {
          total: result.total || 0,
          page: result.page || 1,
          limit: result.pageSize || 10,
          totalPages: Math.ceil((result.total || 0) / (result.pageSize || 10)),
        },
      }
    } catch (error) {
      console.error('Error fetching organization units:', error)
      throw error
    }
  },

  // Lấy chi tiết đơn vị
  getById: async (id: string) => {
    try {
      const result = await ApiClientInstance.get(`organization-units/${id}`)

      return result
    } catch (error) {
      console.error('Error fetching organization unit:', error)
      throw error
    }
  },
}
