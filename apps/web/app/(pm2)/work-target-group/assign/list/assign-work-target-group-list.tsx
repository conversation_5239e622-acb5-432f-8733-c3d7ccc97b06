'use client'

import { AssignWorkTargetGroup, AssignWorkTargetGroupProcessStatus, Role } from '@/lib/types'
import { assignWorkTargetGroupService } from '@/services/assignWorkTargetGroup'
import { useUserStore } from '@/stores'
import { useGlobalStore } from '@/stores/global.store'
import { PERMISSIONS } from '@ac/permissions'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { AdminPageContent, AdminPageLayout, TABLE_SIZE } from '@workspace/ui/mi'
import { DataTable, selectColumn, TABLE_ALIGN } from '@workspace/ui/mi'
import { AddButton } from '@workspace/ui/mi/add-button'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { PlusIcon } from 'lucide-react'
import React, { useEffect, useId, useState } from 'react'

import { getWorkTargetGroupStatusText } from '../../types'
import { AssignWorkTargetGroupForm } from '../assign-work-target-group-form'

type AssignWorkTargetGroupListResponse = {
  items: AssignWorkTargetGroup[]
  totalPages: number
  totalItems: number
}

export function AssignWorkTargetGroupList() {
  const t = useTranslations()
  const { hasPermissions } = useUserStore()

  const formId = useId()
  const [selectedItem, setSelectedItem] = useState<AssignWorkTargetGroup | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const { roles, getRoles } = useGlobalStore()

  useEffect(() => {
    getRoles()
  }, [])

  const columns: ColumnDef<AssignWorkTargetGroup>[] = [
    selectColumn(t),
    {
      accessorKey: 'workTargetGroup.name',
      meta: {
        title: 'Tên bộ chỉ tiêu',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.workTargetGroup?.name || ''}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'evaluator.name',
      meta: {
        title: 'Cán bộ đánh giá',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.evaluator?.name || ''}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'approval.name',
      meta: {
        title: 'Cán bộ phê duyệt',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.approval?.name || ''}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'processStatus',
      meta: {
        title: 'Trạng thái',
      },

      cell: ({ row }) => {
        const variantValue =
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.OPEN && 'outline') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.CLOSED && 'outline') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.REJECTED && 'warning') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.SUCCEEDED && 'success') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.SUBMITTED && 'default') ||
          'default'

        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground text-sm leading-normal font-semibold">
              <Badge variant={variantValue} className="text-xs">
                {getWorkTargetGroupStatusText(row?.original?.processStatus)}
              </Badge>
            </p>
          </div>
        )
      },
    },
  ]

  if (
    hasPermissions([PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_UPDATE, PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_DELETE])
  ) {
    columns.push({
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },

      cell: ({ row }) => {
        const member = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_UPDATE}
              disabled={
                row?.original?.processStatus === 'SUBMITTED' || row?.original?.processStatus === 'SUCCEEDED'
                  ? true
                  : false
              }
              onClick={() => {
                setSelectedItem(member)
                setIsEditDialogOpen(true)
              }}
            />
            <DeleteButtonIcon
              permission={PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_DELETE}
              onClick={() => {
                setSelectedItem(member)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    })
  }

  const { table } = useEnhancedTable<AssignWorkTargetGroup>({
    columns,
    pageName: 'assign-work-target-groups',
    keyObject: {},
    queryFn: async state => {
      const params = new URLSearchParams()
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))

      const response = (await assignWorkTargetGroupService.getAssignWorkTargetGroup(
        params
      )) as AssignWorkTargetGroupListResponse

      if (!response) {
        throw new Error('Failed to fetch work target organization unit')
      }

      return {
        items: Array.isArray(response.items) ? response.items : [],
        totalItems: Number(response.totalItems) || 0,
        totalPages: Number(response.totalPages) || 0,
      }
    },
    initialState: {},
    enabled: true,
    queryKey: ['assign-work-target-groups'],
  })

  const handleDeleteAssignWorkTargetGroup = async () => {
    try {
      if (!selectedItem) {
        return
      }
      const response = await assignWorkTargetGroupService.deleteAssignWorkTargetGroup(selectedItem.id)

      if (!response) {
        throw new Error('Xóa bộ chỉ tiêu thất bại')
      }
      setTimeout(() => {
        table.options.meta?.reload()
        toast.success('Bộ chỉ tiêu thành công')
      }, 100)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  const cleanSelectedItem = () => {
    setSelectedItem(null)
    setIsDeleteDialogOpen(false)
    setIsEditDialogOpen(false)
  }

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('assign-work-target-group.title'),
            href: '/work-target-group/assign/list',
          },
        ]}
      >
        <AdminPageContent
          title={
            <div className="flex flex-col gap-1.5">
              <p className="text-card-foreground text-2xl leading-none font-semibold">
                {t('assign-work-target-group.title')}
              </p>
            </div>
          }
          actions={[
            <AddButton
              tooltip={t('assign-work-target-group.form.create.title')}
              key="create"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={() => setIsCreateDialogOpen(true)}
            />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create case answer Dialog */}
      {isCreateDialogOpen && (
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent onInteractOutside={e => e.preventDefault()}>
            <DialogHeader>
              <DialogTitle>Gán bộ chỉ tiêu</DialogTitle>
            </DialogHeader>
            <AssignWorkTargetGroupForm
              onSuccess={() => {
                setIsCreateDialogOpen(false)
                setIsEditDialogOpen(false)
                cleanSelectedItem()
                table.options.meta?.reload()
                setTimeout(() => {
                  toast.success(`Gán bộ chỉ tiêu thành công`)
                }, 100)
              }}
              onCancel={() => {
                setIsCreateDialogOpen(false)
                cleanSelectedItem()
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {isEditDialogOpen && selectedItem && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent onInteractOutside={e => e.preventDefault()}>
            <DialogHeader>
              <DialogTitle>Cập nhật bộ chỉ tiêu</DialogTitle>
            </DialogHeader>
            <AssignWorkTargetGroupForm
              initialData={selectedItem}
              onSuccess={() => {
                setIsEditDialogOpen(false)
                cleanSelectedItem()
                table.options.meta?.reload()
                setTimeout(() => {
                  toast.success(`Cập nhật bộ chỉ tiêu thành công`)
                }, 100)
              }}
              onCancel={() => {
                setIsEditDialogOpen(false)
                cleanSelectedItem()
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {isDeleteDialogOpen && selectedItem && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa bộ chỉ tiêu &quot;{selectedItem?.workTargetGroup?.name}&quot; được gán cho cán
                bộ &quot;{selectedItem?.evaluator?.name}&quot;? Hành động này không thể hoàn tác.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteAssignWorkTargetGroup}
                className="bg-destructive hover:bg-destructive/90 text-white"
              >
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  )
}
