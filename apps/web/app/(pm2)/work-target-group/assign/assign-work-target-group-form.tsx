'use client'

import { AssignWorkTargetGroup, WorkTargetGroup } from '@/lib/types'
import { assignWorkTargetGroupService } from '@/services/assignWorkTargetGroup'
import { workTargetService } from '@/services/workTargetService'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { toastError } from '@workspace/ui/components/toast'
import { Button, Combobox, Required } from '@workspace/ui/mi'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

interface UserOption {
  value: string
  label: string
}

interface WorkTargetOption {
  value: string
  label: string
}

type IWorkTargetGroupOption = {
  id: string
  name: string
}

type props = {
  initialData?: AssignWorkTargetGroup
  onSuccess: () => void
  onCancel: () => void
}

const categoryFormSchema = z.object({
  workTargetGroupId: z.string().min(1, 'Bộ chỉ tiêu không được để trống'),
  approvalId: z.string().min(1, 'Cán bộ đánh giá bộ chỉ tiêu không được để trống'),
  evaluatorId: z.string().min(1, 'Cán bộ phê duyệt bộ chỉ tiêu không được để trống'),
})

type CategoryFormValues = z.infer<typeof categoryFormSchema>

export function AssignWorkTargetGroupForm({ initialData, onSuccess, onCancel }: props) {
  const t = useTranslations()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userOptions, setUserOptions] = useState<UserOption[]>([])
  const [workTargetGroupOptions, setWorkTargetOptions] = useState<WorkTargetOption[]>([])
  const [loadingOptions, setLoadingOptions] = useState(false)

  useEffect(() => {
    setLoadingOptions(true)
    fetchApi()
  }, [])

  const fetchApi = async () => {
    try {
      const userFake = [
        { value: '6ebc396a-6be6-4421-97c0-69d4dbdd777c', label: 'HNO Lãnh Đạo 01' },
        { value: 'c2434d4c-d528-4f1c-8045-7aea9763f2b9', label: 'HNO Lãnh Đạo 02' },
        { value: '3976b408-a888-4b56-b19b-72ab93573a76', label: 'HNO Tiếp Nhận 01' },
        { value: 'eb59b9d8-40b6-4706-97b7-c6966a9338e2', label: 'HNO Tiếp Nhận 02' },
        { value: '1260acb8-22be-4b70-8be5-e02a6ff8ff36', label: 'HNO Phân Công 01' },
        { value: 'abbbeedd-7ae4-48e2-8eb2-69567a166198', label: 'HNO Phân Công 02' },
        { value: '3c60173e-6319-3029-e063-4832800a3c4c', label: 'TEST' },
      ]
      const [workTargetGroupResponse] = await Promise.all([workTargetService.getAllWorkTargetGroups()])

      const workTargetGroupOptions = workTargetGroupResponse.map((item: WorkTargetGroup) => ({
        value: item.id,
        label: item.name,
      }))

      setWorkTargetOptions(workTargetGroupOptions)
      setUserOptions(userFake)
    } catch (error) {
      console.log('fetch api error', error)
    } finally {
      setLoadingOptions(false)
    }
  }

  const defaultValues = useMemo(
    () => ({
      workTargetGroupId: initialData?.workTargetGroup?.id || '',
      approvalId: initialData?.approval?.id || '',
      evaluatorId: initialData?.evaluator?.id || '',
    }),
    [initialData]
  )

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: defaultValues,
  })

  useEffect(() => {
    form.reset(defaultValues)
  }, [defaultValues, form])

  const onSubmit = useCallback(
    async (values: CategoryFormValues) => {
      setIsSubmitting(true)

      try {
        if (initialData) {
          const payload = {
            evaluatorId: values.evaluatorId,
            approvalId: values.approvalId,
          }
          const updatedAssignWorkTargetGroup = await assignWorkTargetGroupService.updateAssignWorkTargetGroup(
            initialData.id,
            payload
          )

          if (updatedAssignWorkTargetGroup) {
            onSuccess()

            return
          }

          return
        }
        const newAssignWorkTargetGroup = await assignWorkTargetGroupService.createAssignWorkTargetGroup(values)

        if (newAssignWorkTargetGroup) {
          onSuccess()
        }
      } catch (error) {
        toastError(error)
      } finally {
        setIsSubmitting(false)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [initialData, onSuccess, t]
  )

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 gap-4">
          <FormField
            control={form.control}
            name="workTargetGroupId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Bộ chỉ tiêu <Required />
                </FormLabel>
                <FormControl>
                  <Combobox
                    options={workTargetGroupOptions}
                    value={field.value}
                    onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                    placeholder={loadingOptions ? 'Đang tải...' : 'Chọn bộ chỉ tiêu'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="evaluatorId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Cán bộ đánh giá bộ chỉ tiêu <Required />
                </FormLabel>
                <FormControl>
                  <Combobox
                    options={userOptions}
                    value={field.value}
                    onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                    placeholder={loadingOptions ? 'Đang tải...' : 'Chọn cán bộ đánh giá'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="approvalId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Cán bộ phê duyệt bộ chỉ tiêu <Required />
                </FormLabel>
                <FormControl>
                  <Combobox
                    options={userOptions}
                    value={field.value}
                    onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                    placeholder={loadingOptions ? 'Đang tải...' : 'Chọn cán bộ phê duyệt'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {initialData ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
