'use client'

import { AssignWorkTargetGroup, AssignWorkTargetGroupProcessStatus } from '@/lib/types'
import { assignWorkTargetGroupService } from '@/services/assignWorkTargetGroup'
import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@workspace/ui/components/badge'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { Button, DataTable, TABLE_ALIGN } from '@workspace/ui/mi'
import { useState } from 'react'

import { getWorkTargetGroupStatusText } from '../types'

type props = {
  useId: string
}

type WorkTargetListResponse = {
  items: AssignWorkTargetGroup[]
  totalPages?: number
  totalItems?: number
}

export function EvaluatorWorkTargetGroup({ useId }: props) {
  // Remove unused variables
  const t = useTranslations()
  const [selectedItem, setSelectedItem] = useState<AssignWorkTargetGroup | undefined>(undefined)
  const [openDialog, setOpenDialog] = useState<boolean>(false)

  const columns: ColumnDef<AssignWorkTargetGroup>[] = [
    {
      accessorKey: 'name',
      meta: {
        title: 'Tên nhóm chỉ tiêu',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground text-sm leading-normal font-semibold">
              {row?.original?.workTargetGroup?.name || ''}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'approval.name',
      meta: {
        title: 'Cán bộ phê duyệt',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground text-sm leading-normal font-semibold">
              {row?.original?.approval?.name || ''}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'processStatus',
      meta: {
        title: 'Trạng thái',
      },
      cell: ({ row }) => {
        const variantValue =
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.OPEN && 'outline') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.CLOSED && 'outline') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.REJECTED && 'warning') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.SUCCEEDED && 'success') ||
          (row?.original?.processStatus === AssignWorkTargetGroupProcessStatus.SUBMITTED && 'default') ||
          'default'

        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground text-sm leading-normal font-semibold">
              <Badge variant={variantValue} className="text-xs">
                {getWorkTargetGroupStatusText(row?.original?.processStatus)}
              </Badge>
            </p>
          </div>
        )
      },
    },
    {
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const organization = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <Button
              disabled={
                row?.original?.processStatus === 'CLOSED' || row?.original?.processStatus === 'SUCCEEDED' ? true : false
              }
              variant="default"
              size="sm"
              onClick={() => {
                setOpenDialog(true)
                setSelectedItem(row.original)
              }}
            >
              Đánh giá
            </Button>
          </div>
        )
      },
    },
  ]

  const { table } = useEnhancedTable<AssignWorkTargetGroup>({
    columns,
    pageName: 'work-target-list',
    keyObject: {},
    queryFn: async state => {
      const params = new URLSearchParams()
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))

      const response = (await assignWorkTargetGroupService.getEvaluatorWorkTargetGroup(
        params
      )) as WorkTargetListResponse

      if (!response) {
        throw new Error('Failed to fetch work target organization unit')
      }

      return {
        items: response.items || [],
        totalPages: response.totalPages ?? 1,
        totalItems: response.totalItems ?? response.items?.length ?? 0,
      }
    },
    initialState: {},
    enabled: true,
    useQueryFn: true,
    queryKey: ['work-target-list'],
  })

  return (
    <>
      <DataTable table={table} />
    </>
  )
}
