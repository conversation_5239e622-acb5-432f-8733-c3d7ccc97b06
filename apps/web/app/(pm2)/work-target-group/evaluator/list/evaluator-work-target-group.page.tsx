'use client'

import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi'
import { useTranslations } from 'next-intl'
import React from 'react'

import { EvaluatorWorkTargetGroup } from '../evaluator-work-target-group-list'

export function EvaluatorWorkTargetGroupList() {
  const t = useTranslations()

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('case-answer.title'),
            href: '/work-target-group/evaluator/list',
          },
        ]}
      >
        <AdminPageContent
          title={
            <div className="flex flex-col gap-1.5">
              <p className="text-card-foreground text-2xl leading-none font-semibold">{t('work-target.owner.title')}</p>
            </div>
          }
        >
          <EvaluatorWorkTargetGroup useId="" />
        </AdminPageContent>
      </AdminPageLayout>
    </>
  )
}
