'use client'

import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { WorkTargetGroup, WorkTargetStatus } from '@/lib/types'
import { workTargetService } from '@/services/workTargetService'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { toast } from '@workspace/ui/lib/toast'
import {
  AdminPageContent,
  AdminPageLayout,
  Button,
  DataTable,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  select<PERSON><PERSON>umn,
  TABLE_ALIGN,
} from '@workspace/ui/mi'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { PlusIcon } from 'lucide-react'
import { redirect } from 'next/navigation'
import React, { useId, useState } from 'react'

import { WorkTargetFormValues, WorkTargetGroupForm } from '../work-target-group-form'

type WorkTargetOrganizationUnitListResponse = {
  items: WorkTargetGroup[]
  totalPages: number
  totalItems: number
}

export function WorkTargetOrganizationUnitList() {
  const t = useTranslations()
  const formId = useId()

  const [selectedItem, setSelectedItem] = useState<WorkTargetGroup | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  const columns: ColumnDef<WorkTargetGroup>[] = [
    selectColumn(t),
    {
      accessorKey: 'name',
      meta: {
        title: 'Tên bộ chỉ tiêu',
      },

      cell: ({ row }) => {
        return (
          <RenderInteractiveLink
            onClick={() => {
              setSelectedItem(row.original)
              setIsEditDialogOpen(false)
              setIsViewDialogOpen(true)
            }}
          >
            {row?.original?.name || ''}
          </RenderInteractiveLink>
        )
      },
    },
    {
      accessorKey: 'category.id',
      meta: {
        title: 'Hình thức thực hiện',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.category?.name}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'requiredRate',
      meta: {
        title: 'Tỷ lệ đạt yêu cầu',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-center text-sm leading-normal font-semibold">
              {row?.original?.requiredRate}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'onTimeRate',
      meta: {
        title: 'Tỷ lệ đạt đúng hạn',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-center text-sm leading-normal font-semibold">
              {row?.original?.onTimeRate}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'processStatus',
      meta: {
        title: 'Trạng thái',
      },

      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.processStatus === WorkTargetStatus.OPEN ? (
                <Badge variant="success" className="text-xs">
                  Đang hoạt động
                </Badge>
              ) : (
                <Badge variant="default" className="text-xs">
                  Dừng hoạt động
                </Badge>
              )}
            </p>
          </div>
        )
      },
    },
    {
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const member = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              onClick={() => {
                setSelectedItem(member)
                setIsViewDialogOpen(false)
                setIsEditDialogOpen(true)
              }}
            />
            <DeleteButtonIcon
              onClick={() => {
                setSelectedItem(member)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    },
  ]

  const { table } = useEnhancedTable<WorkTargetGroup>({
    columns,
    pageName: 'work-target-organization-unit',
    keyObject: {},
    queryFn: async state => {
      const params = new URLSearchParams()
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))

      const response = (await workTargetService.getWorkTargetGroups(params)) as WorkTargetOrganizationUnitListResponse

      if (!response) {
        throw new Error('Failed to fetch work target organization unit')
      }

      return {
        items: response.items || [],
        totalPages: response.totalPages || 0,
        totalItems: response.totalItems || 0,
      }
    },
    initialState: {},
    enabled: true,
    useQueryFn: true,
    queryKey: ['work-target-organization-unit'],
  })

  const handleDeleteWorkTargetGroup = async () => {
    if (!selectedItem) return

    try {
      const response = await workTargetService.deleteWorkTarget(selectedItem?.id)

      if (!response) {
        throw new Error('Xóa bộ chỉ tiêu thất bại')
      }
      setSelectedItem(null)
      setIsDeleteDialogOpen(false)
      setTimeout(() => {
        table.options.meta?.reload()
        toast.success(`Đã xóa "${selectedItem?.name}" thành công`)
      }, 100)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('work-target-group.title'),
            href: '/work-target-group/organization-unit/list',
          },
        ]}
      >
        <AdminPageContent
          title={
            <div className="flex flex-col gap-1.5">
              <p className="text-card-foreground text-2xl leading-none font-semibold">{t('work-target-group.title')}</p>
            </div>
          }
          actions={[
            <Button key="create" onClick={() => redirect('/work-target-group/organization-unit/create')}>
              <PlusIcon className="h-4 w-4" />
              {t('work-target.organization-unit.actions.create')}
            </Button>,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {(isEditDialogOpen || isViewDialogOpen) && selectedItem && (
        <Dialog
          open={isEditDialogOpen || isViewDialogOpen}
          onOpenChange={() => {
            setIsEditDialogOpen(false)
            setIsViewDialogOpen(false)
            setSelectedItem(null)
          }}
        >
          <DialogContent
            onInteractOutside={e => e.preventDefault()}
            className="flex flex-col p-0"
            style={{
              maxWidth: '80rem',
              width: '90vw',
              maxHeight: '90vh',
            }}
          >
            <DialogHeader className="border-b px-6 py-4">
              <DialogTitle>{isViewDialogOpen ? 'Chi tiết' : 'Chỉnh sửa'} bộ chỉ tiêu</DialogTitle>
            </DialogHeader>
            <div className="flex-1 overflow-auto px-6 py-4">
              <WorkTargetGroupForm
                isView={isViewDialogOpen ? true : false}
                initialData={selectedItem as WorkTargetFormValues & { id?: string }}
                formId={formId}
                onHandleUpdateSuccess={() => {
                  setIsEditDialogOpen(false)
                  setIsViewDialogOpen(false)
                  setSelectedItem(null)
                  setTimeout(() => {
                    table.options.meta?.reload()
                  }, 100)
                }}
                onHandleCancel={() => {
                  setIsEditDialogOpen(false)
                  setIsViewDialogOpen(false)
                  setSelectedItem(null)
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}

      {isDeleteDialogOpen && selectedItem && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa bộ chỉ tiêu này &ldquo;{selectedItem?.name}&rdquo;? Hành động này không thể
                hoàn tác.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteWorkTargetGroup}
                className="bg-destructive hover:bg-destructive/90 text-white"
              >
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  )
}
