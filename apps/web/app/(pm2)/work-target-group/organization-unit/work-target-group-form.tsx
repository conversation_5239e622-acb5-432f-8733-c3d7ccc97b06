'use client'

import { WorkTargetType } from '@/lib/types'
import { workTargetService } from '@/services/workTargetService'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/lib/toast'
import { Required } from '@workspace/ui/mi/required'
import { Plus, Trash } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { getWorkTargetGroupTypeText } from '../types'

const workTargetFormSchema = z.object({
  name: z.string().min(1, 'Tên nhóm chỉ tiêu không được để trống'),
  categoryId: z.string().min(1, 'Loại chỉ tiêu không được để trống'),
  onTimeRate: z.number().min(0, 'Tỷ lệ đạt đúng hạn không được để trống'),
  requiredRate: z.number().min(0, 'Tỷ lệ đạt yêu cầu không được để trống'),
  workTargets: z.array(
    z.object({
      code: z.string().optional().nullable(),
      name: z.string().min(1, 'Tên chỉ tiêu không được để trống'),
      type: z.string().min(1, 'Loại chỉ tiêu không được để trống'),
      targetNumber: z.number().optional(),
      note: z.string().optional(),
    })
  ),
})

export type WorkTargetFormValues = z.infer<typeof workTargetFormSchema>

export type FormImperativeHandle = {
  resetForm: () => void
  isDirty: () => boolean
}

type props = {
  isView?: boolean
  initialData?: WorkTargetFormValues & { id?: string }
  formId: string
  onHandleUpdateSuccess?: () => void
  onHandleCancel?: () => void
}

const workTargetEmpty = {
  code: null,
  name: '',
  type: '',
  targetNumber: 0,
  note: '',
}

// eslint-disable-next-line react/display-name
export const WorkTargetGroupForm = forwardRef<FormImperativeHandle, props>(
  ({ isView = false, initialData, onHandleUpdateSuccess, onHandleCancel, formId }: props, ref) => {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [typeOptions, setTypeOptions] = useState<Array<{ id: string; name: string }>>([])

    const [tgOptions, setTgOptions] = useState<Array<{ id: string; name: string }>>([])

    const isDisabled = isView || isSubmitting

    const initialValuesRef = useRef<WorkTargetFormValues | null>(null)
    const listContainerRef = useRef<HTMLDivElement | null>(null)
    const lastItemRef = useRef<HTMLDivElement | null>(null)
    const router = useRouter()

    useEffect(() => {
      fetchUserOption()

      if (!initialValuesRef.current) {
        initialValuesRef.current = form.getValues() as WorkTargetFormValues
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    useEffect(() => {
      if (!initialData) {
        if (
          defaultValues &&
          defaultValues.workTargets?.length === 1 &&
          defaultValues.workTargets[0]?.name === '' &&
          defaultValues.workTargets[0]?.type === '' &&
          typeOptions.length === 1
        ) {
          form.setValue('workTargets.0', {
            ...workTargetEmpty,
            type: typeOptions[0]?.id || '',
          })
        }

        return
      }

      if (tgOptions.length === 0 && typeOptions.length === 0) {
        return
      }

      if (form.formState.isDirty) return

      const nextValues: Partial<WorkTargetFormValues> = {
        ...form.getValues(),
        name: initialData?.name || '',
        categoryId: initialData?.categoryId || '',
        onTimeRate: initialData?.onTimeRate || 0,
        requiredRate: initialData?.requiredRate || 0,
        workTargets: initialData?.workTargets || [workTargetEmpty],
      }

      form.reset(nextValues as WorkTargetFormValues)
      initialValuesRef.current = nextValues as WorkTargetFormValues
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialData, tgOptions, typeOptions])

    // Comment: Function

    const fetchUserOption = async () => {
      try {
        const typeOptionValues = Object.keys(WorkTargetType).map(item => ({
          id: item,
          name: getWorkTargetGroupTypeText(item as WorkTargetType),
        }))

        setTypeOptions(typeOptionValues)
        const filter = { type: 'TG' }
        const res = await fetch(`/ac-apis/categories/method/all?filter=${JSON.stringify(filter)}`, {
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!res.ok) {
          throw new Error('Failed to fetch categories')
        }
        const response = await res.json()
        const tgOptions = response.map((item: { categoryId: string; name: string }) => ({
          id: item.categoryId,
          name: item.name,
        }))
        setTgOptions(tgOptions)
      } catch (error) {
        console.error('Error fetching user option:', error)
      }
    }

    // Comment: Form
    const defaultValues: Partial<WorkTargetFormValues> = useMemo(
      () => ({
        name: initialData?.name || '',
        categoryId: initialData?.categoryId || '',
        onTimeRate: initialData?.onTimeRate || 0,
        requiredRate: initialData?.requiredRate || 0,
        workTargets: initialData?.workTargets || [workTargetEmpty],
      }),
      [initialData]
    )

    const form = useForm<WorkTargetFormValues>({
      resolver: zodResolver(workTargetFormSchema),
      defaultValues: defaultValues,
    })

    const workTargets = form.watch('workTargets') ?? []

    const onSubmit = useCallback(
      async (values: WorkTargetFormValues) => {
        console.log({ values, isView })

        if (isView) {
          return
        }
        setIsSubmitting(true)

        try {
          const action = initialData
            ? await workTargetService.updateWorkTarget(initialData.id as string, values)
            : await workTargetService.createWorkTarget(values)
          const response = action

          if (!response) {
            throw new Error(`Lỗi khi tạo chỉ tiêu`)
          }

          toast.success(`${initialData ? 'Cập nhật' : 'Tạo'} chỉ tiêu thành công`)
          form.reset()

          if (!initialData) {
            return router.push('/work-target-group/organization-unit/list')
          }
          onHandleUpdateSuccess?.()
        } catch {
          toast.error(`Lỗi khi tạo chỉ tiêu`)
        } finally {
          setIsSubmitting(false)
        }
      },
      [form, initialData, isView, onHandleUpdateSuccess, router]
    )

    useImperativeHandle(ref, () => ({
      resetForm: () => {
        form.reset()
        form.clearErrors()
      },
      isDirty: () =>
        form.formState.isDirty ||
        ((): boolean => {
          try {
            const current = form.getValues() as WorkTargetFormValues
            const initial = initialValuesRef.current

            return JSON.stringify(current) !== JSON.stringify(initial)
          } catch {
            return form.formState.isDirty
          }
        })(),
    }))

    useEffect(() => {
      // Scroll the last item into view after DOM updates
      requestAnimationFrame(() => {
        lastItemRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
      })
    }, [workTargets.length])

    return (
      <>
        <Form {...form}>
          <form id={formId} onSubmit={form.handleSubmit(onSubmit)} className="flex h-full flex-col">
            {/* Thông tin người được TGPL */}
            <div className="flex h-full min-h-0 flex-col space-y-6">
              {/* Thông tin cơ bản */}
              <div className="flex min-h-0 flex-1 flex-col gap-4">
                <div className="flex flex-col gap-1">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Tên nhóm chỉ tiêu
                              <Required />
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="Nhập tên nhóm chỉ tiêu" {...field} disabled={isDisabled} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div>
                      <FormField
                        control={form.control}
                        name="categoryId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Hình thức trợ giúp
                              <Required />
                            </FormLabel>
                            <FormControl>
                              <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                                <SelectTrigger className="w-full" disabled={isDisabled}>
                                  <SelectValue placeholder="Chọn hình thức trợ giúp" />
                                </SelectTrigger>
                                <SelectContent>
                                  {tgOptions.length > 0 &&
                                    tgOptions.map(item => (
                                      <SelectItem key={item.id} value={String(item.id)}>
                                        {item.name}
                                      </SelectItem>
                                    ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <FormLabel>
                        Tỉ lệ đạt yêu cầu
                        <Required />
                      </FormLabel>
                      <Input
                        type="number"
                        min={0}
                        placeholder="Nhập tỉ lệ đạt yêu cầu (tính theo %)"
                        {...form.register(`requiredRate`, { valueAsNumber: true })}
                        disabled={isDisabled}
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <FormLabel>
                        Tỉ lệ đúng hạn
                        <Required />
                      </FormLabel>
                      <Input
                        type="number"
                        min={0}
                        placeholder="Nhập tỉ lệ đạt đúng hạn (tính theo %)"
                        {...form.register(`onTimeRate`, { valueAsNumber: true })}
                        disabled={isDisabled}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex min-h-0 flex-1 flex-col gap-2">
                  <h4 className="text-lg font-medium">Nội dung chỉ tiêu</h4>
                  <div
                    ref={listContainerRef}
                    className="grid min-h-0 w-full flex-1 grid-cols-1 gap-4 overflow-y-auto py-4 md:grid-cols-1"
                  >
                    {workTargets.map((_item, index, arr) => (
                      <div
                        key={index}
                        ref={index === arr.length - 1 ? lastItemRef : null}
                        className="flex flex-col gap-1"
                      >
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                              Chỉ tiêu {index + 1}
                              {arr.length > 1 && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    form.setValue(
                                      'workTargets',
                                      form.getValues('workTargets').filter((_item, i) => i !== index)
                                    )
                                  }}
                                >
                                  <Trash className="h-4 w-4 text-red-500" />
                                </Button>
                              )}
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 gap-4">
                              <div className="flex flex-col gap-1">
                                <FormLabel>
                                  Nội dung chỉ tiêu
                                  <Required />
                                </FormLabel>
                                <Input
                                  placeholder="Nhập nội dung chỉ tiêu"
                                  {...form.register(`workTargets.${index}.name`)}
                                  disabled={isDisabled}
                                />
                              </div>
                              <div className="flex flex-col gap-1">
                                <FormLabel>Ghi chú</FormLabel>
                                <Textarea
                                  rows={3}
                                  placeholder="Nhập ghi chú"
                                  {...form.register(`workTargets.${index}.note`)}
                                  disabled={isDisabled}
                                />
                              </div>
                              <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
                                <div className="flex flex-col gap-1">
                                  <FormLabel>
                                    Loại chỉ tiêu
                                    <Required />
                                  </FormLabel>
                                  <Select
                                    value={form.watch(`workTargets.${index}.type`)}
                                    onValueChange={value => {
                                      form.setValue(`workTargets.${index}.type`, value)
                                    }}
                                  >
                                    <SelectTrigger className="w-full" disabled={isDisabled}>
                                      <SelectValue placeholder="Chọn loại chỉ tiêu" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {typeOptions.length > 0 &&
                                        typeOptions.map((item, index: number) => (
                                          <SelectItem key={index} value={String(item.id)}>
                                            {item.name}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>

                              <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
                                <FormLabel>
                                  Nhập giá trị chỉ tiêu
                                  <Required />
                                </FormLabel>
                                <Input
                                  type="number"
                                  min={0}
                                  placeholder="Nhập giá trị yêu cầu"
                                  {...form.register(`workTargets.${index}.targetNumber`, {
                                    valueAsNumber: true,
                                  })}
                                  disabled={isDisabled}
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    ))}
                  </div>
                  <div className="w-full">
                    {!isDisabled && (
                      <Button
                        className="w-full"
                        size="sm"
                        onClick={e => {
                          e.preventDefault()
                          let workTargetValue = workTargetEmpty

                          if (typeOptions.length === 1) {
                            workTargetValue = {
                              ...workTargetEmpty,
                              type: typeOptions[0]?.id || '',
                            }
                          }
                          form.setValue('workTargets', [...form.getValues('workTargets'), workTargetValue])
                        }}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Thêm chỉ tiêu
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {isView ? (
              <></>
            ) : initialData && onHandleCancel ? (
              <div className="flex justify-end">
                <div className="flex flex-row gap-2 pt-4">
                  <Button variant="outline" size="sm" onClick={onHandleCancel} disabled={isDisabled}>
                    Huỷ
                  </Button>
                  <Button type="submit" disabled={isDisabled}>
                    Lưu thay đổi
                  </Button>
                </div>
              </div>
            ) : null}
          </form>
        </Form>
      </>
    )
  }
)
