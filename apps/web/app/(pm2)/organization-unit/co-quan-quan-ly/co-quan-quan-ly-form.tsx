'use client'

import { Organization } from '@/constants/so-tu-phap'
import { Any } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { toast } from '@workspace/ui/components/toast'
import { Required } from '@workspace/ui/mi/required'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  code: z.string().min(1, 'Mã cơ quan là bắt buộc'),
  name: z.string().min(1, 'Tên cơ quan là bắt buộc'),
  fullName: z.string().optional(),
  address: z.string().optional(),

  // Cấu trúc cây
  parentId: z.string().optional().nullable(),

  // Trạng thái và phân loại
  type: z.number().optional(),
  status: z.number(),
})

type FormData = z.infer<typeof formSchema>

interface SoTuPhapFormProps {
  initialData?: Organization | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
}

export function CoQuanQuanLyForm({ initialData, onSuccess, onCancel, organizationType }: SoTuPhapFormProps) {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: initialData?.code || undefined,
      name: initialData?.name || undefined,
      fullName: initialData?.fullName || undefined,
      address: initialData?.address || undefined,

      parentId: initialData?.parentId || undefined,

      // Trạng thái và phân loại
      type: initialData?.type || organizationType || undefined,
      status: initialData?.status ?? 1,
    },
  })

  const onSubmit = async (data: FormData) => {
    try {
      // Try different URL patterns
      const url = initialData ? `/ac-apis/organization-unit/${initialData.id}` : '/ac-apis/organization-unit'

      const method = initialData ? 'PATCH' : 'POST'

      const submitData = {
        ...data,
        parentId: data.parentId || null,
        // Đảm bảo type luôn được set đúng theo organizationType
        type: organizationType || data.type,
        // Include id in body for update
        ...(initialData && { id: initialData.id }),
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorText = await response.text()

        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || errorData.message || response.statusText)
        } catch {
          throw new Error(response.statusText || `${initialData ? 'Cập nhật' : 'Tạo'} sở tư pháp thất bại`)
        }
      }

      toast.success(`${initialData ? 'Cập nhật' : 'Tạo'} sở tư pháp thành công`)
      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col">
        {/* Content area */}
        <div className="space-y-6 px-6 py-4">
          {/* Thông tin cơ bản */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Thông tin cơ bản</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Mã cơ quan <Required />
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập mã cơ quan" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Tên cơ quan <Required />
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên cơ quan" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên đầy đủ</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Nhập tên đầy đủ" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Địa chỉ <Required />
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Nhập địa chỉ" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Footer with buttons */}
        <div className="bg-muted/50 flex justify-end gap-2 border-t px-6 py-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button type="submit">{initialData ? 'Cập nhật' : 'Tạo mới'}</Button>
        </div>
      </form>
    </Form>
  )
}
