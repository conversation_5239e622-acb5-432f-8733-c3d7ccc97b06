'use client'

import { ORGANIZATION_TYPE, ORGANIZATION_TYPE_OPTIONS } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Separator } from '@workspace/ui/components/separator'
import { toast } from '@workspace/ui/components/toast'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Loading } from '@workspace/ui/mi/loading'
import { ArrowLeft, Building2, Contact, Edit, FileText, Globe, Mail, MoreHorizontal, Phone, Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { CoQuanQuanLyForm } from '../co-quan-quan-ly-form'

interface CoQuanQuanLyDetailPageProps {
  id: string
}

export function CoQuanQuanLyDetailPage({ id }: CoQuanQuanLyDetailPageProps) {
  const router = useRouter()
  const [data, setData] = useState<Organization | null>(null)
  const [children, setChildren] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [childrenLoading, setChildrenLoading] = useState(true)
  const [isEditMode, setIsEditMode] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch data')
        }
        const result = await response.json()
        setData(result)
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Không thể tải thông tin Cơ quan quản lý')
      } finally {
        setLoading(false)
      }
    }

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
        toast.error('Không thể tải thông tin đơn vị trực thuộc')
      } finally {
        setChildrenLoading(false)
      }
    }

    if (id) {
      fetchData()
      fetchChildren()
    }
  }, [id])

  const handleEditSuccess = () => {
    setIsEditMode(false)
    // Refetch data
    setLoading(true)
    setChildrenLoading(true)

    const fetchData = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch data')
        }
        const result = await response.json()
        setData(result)
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Không thể tải thông tin Cơ quan quản lý')
      } finally {
        setLoading(false)
      }
    }

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
        toast.error('Không thể tải thông tin đơn vị trực thuộc')
      } finally {
        setChildrenLoading(false)
      }
    }

    fetchData()
    fetchChildren()
  }

  if (loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Cơ quan quản lý',
            href: '/organization-unit/co-quan-quan-ly',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <Loading />
      </AdminPageLayout>
    )
  }

  if (!data) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Cơ quan quản lý',
            href: '/organization-unit/co-quan-quan-ly',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">Không tìm thấy thông tin Cơ quan quản lý</p>
        </div>
      </AdminPageLayout>
    )
  }

  const statusOption = STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === data.status)
  const typeOption = ORGANIZATION_TYPE_OPTIONS.find(t => t.value === data.type)

  // Phân loại các đơn vị trực thuộc
  const soTuPhapList = children.filter(child => child.type === ORGANIZATION_TYPE.SO_TU_PHAP)
  const trungTamList = children.filter(child => child.type === ORGANIZATION_TYPE.TRUNG_TAM_TGPL)

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Cơ quan quản lý',
            href: '/organization-unit/co-quan-quan-ly',
          },
          { label: data.name, href: '#' },
        ]}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.back()} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="flex items-center gap-2 text-2xl font-semibold">Thông tin chi tiết</h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setIsEditMode(true)} className="gap-2">
                <Edit className="h-4 w-4" />
                Cập nhật
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Thông tin chính */}
            <Card className="h-fit py-0">
              <CardContent className="p-0">
                <div className="rounded-t-lg p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <h3 className="mb-1 text-base font-semibold">{data.name}</h3>
                  <p className="text-muted-foreground text-sm">
                    {data.fullName && data.fullName !== data.name && (
                      <>
                        Tên đầy đủ: {data.fullName}
                        <br />
                      </>
                    )}
                    Địa chỉ: {data.address || 'Chưa cập nhật'}
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge variant={data.status === 1 ? 'default' : data.status === 2 ? 'secondary' : 'destructive'}>
                      {statusOption?.label || 'Không xác định'}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-4 p-6">
                  {/* Thông tin cơ bản */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <FileText className="h-4 w-4" />
                      Thông tin cơ bản
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Mã cơ quan</span>
                        <span className="font-medium">{data.code}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Tên cơ quan</span>
                        <span className="font-medium">{data.name}</span>
                      </div>
                      {data.fullName && data.fullName !== data.name && (
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground text-sm">Tên đầy đủ</span>
                          <span className="font-medium">{data.fullName}</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Loại cơ quan</span>
                        <span className="font-medium">{typeOption?.label || 'Không xác định'}</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Thông tin liên hệ */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Contact className="h-4 w-4" />
                      Thông tin liên hệ
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Phone className="h-3 w-3" />
                          Điện thoại
                        </span>
                        <span className="font-medium">{data.phone || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Mail className="h-3 w-3" />
                          Email
                        </span>
                        <span className="font-medium">{data.email || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Globe className="h-3 w-3" />
                          Website
                        </span>
                        <span className="font-medium">{data.website || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Fax</span>
                        <span className="font-medium">{data.fax || 'Chưa cập nhật'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Ghi chú */}
                  {data.note && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                          <FileText className="h-4 w-4" />
                          Ghi chú
                        </h4>
                        <p className="text-sm">{data.note}</p>
                      </div>
                    </>
                  )}
                </div>

                <div className="rounded-b-lg border-t p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <div className="text-muted-foreground flex justify-between text-xs">
                    <span>Tạo: {data.createdAt ? new Date(data.createdAt).toLocaleDateString('vi-VN') : 'N/A'}</span>
                    <span>
                      Cập nhật: {data.updatedAt ? new Date(data.updatedAt).toLocaleDateString('vi-VN') : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Đơn vị trực thuộc */}
            <div className="space-y-6">
              {/* Sở Tư pháp */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Sở Tư pháp
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        router.push(`/organization-unit/so-tu-phap`)
                      }}
                    >
                      <Plus className="h-4 w-4" />
                      Xem tất cả
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {soTuPhapList.length} sở tư pháp</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : soTuPhapList.length > 0 ? (
                    <div className="space-y-4">
                      {soTuPhapList.slice(0, 5).map((soTuPhap, index) => (
                        <div key={soTuPhap.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">{soTuPhap.name}</p>
                              <p className="text-muted-foreground text-xs">
                                Mã: {soTuPhap.code} | Địa chỉ: {soTuPhap.address || 'Chưa cập nhật'}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  soTuPhap.status === 1
                                    ? 'default'
                                    : soTuPhap.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === soTuPhap.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  router.push(`/organization-unit/so-tu-phap/${soTuPhap.id}`)
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < Math.min(soTuPhapList.length, 5) - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có sở tư pháp nào</p>
                    </div>
                  )}

                  {soTuPhapList.length > 5 && (
                    <>
                      <hr className="border-border my-4" />
                      <div className="flex justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600"
                          onClick={() => {
                            router.push(`/organization-unit/so-tu-phap`)
                          }}
                        >
                          Xem thêm ({soTuPhapList.length - 5} sở tư pháp)
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Trung tâm trợ giúp pháp lý */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Trung tâm TGPL
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        router.push(`/organization-unit/trung-tam-tro-giup-phap-ly`)
                      }}
                    >
                      <Plus className="h-4 w-4" />
                      Xem tất cả
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {trungTamList.length} trung tâm</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : trungTamList.length > 0 ? (
                    <div className="space-y-4">
                      {trungTamList.slice(0, 5).map((trungTam, index) => (
                        <div key={trungTam.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">{trungTam.name}</p>
                              <p className="text-muted-foreground text-xs">
                                Mã: {trungTam.code} | Địa chỉ: {trungTam.address || 'Chưa cập nhật'}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  trungTam.status === 1
                                    ? 'default'
                                    : trungTam.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === trungTam.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  router.push(`/organization-unit/trung-tam-tro-giup-phap-ly/${trungTam.id}`)
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < Math.min(trungTamList.length, 5) - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có trung tâm nào</p>
                    </div>
                  )}

                  {trungTamList.length > 5 && (
                    <>
                      <hr className="border-border my-4" />
                      <div className="flex justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600"
                          onClick={() => {
                            router.push(`/organization-unit/trung-tam-tro-giup-phap-ly`)
                          }}
                        >
                          Xem thêm ({trungTamList.length - 5} trung tâm)
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </AdminPageLayout>

      {/* Edit Dialog */}
      <Dialog open={isEditMode} onOpenChange={setIsEditMode}>
        <DialogContent className="flex max-h-[85vh] flex-col gap-0 p-0 sm:max-w-[700px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Chỉnh sửa Cơ quan quản lý</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <div className="overflow-y-auto">
            <CoQuanQuanLyForm
              initialData={data}
              organizationType={data.type}
              onSuccess={handleEditSuccess}
              onCancel={() => setIsEditMode(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
