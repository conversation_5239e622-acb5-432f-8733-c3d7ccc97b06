'use client'

import { PermissionWrapper } from '@/components/permission-wrapper'
import { PERMISSIONS } from '@workspace/ui/constants/base'

import { CoQuanQuanLyPage } from './co-quan-quan-ly-page'

export default function CoQuanQuanLyPageRoute() {
  return (
    <PermissionWrapper permissions={[PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL]}>
      <CoQuanQuanLyPage />
    </PermissionWrapper>
  )
}
