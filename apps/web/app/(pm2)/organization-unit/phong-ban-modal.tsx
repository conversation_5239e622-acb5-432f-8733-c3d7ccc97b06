'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { Category, OrganizationUnit } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { Combobox } from '@workspace/ui/mi/combobox'
import { CalendarIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

interface PhongBanModalProps {
  isOpen: boolean
  onClose: () => void
  parentId: string
  onSuccess: () => void
  fixedParent?: { id: string; name: string; code: string } | null
}

const formSchema = z.object({
  name: z.string().min(1, 'Tên phòng ban là bắt buộc'),
  code: z.string().min(1, 'Mã phòng ban là bắt buộc'),
  fullName: z.string().optional(),
  representative: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  website: z.string().url('Website không hợp lệ').optional().or(z.literal('')),
  establishmentDate: z.string().optional(),
  decisionDate: z.string().optional(),
  decisionNumber: z.string().optional(),
  legalField: z.string().optional(),
  legalForm: z.string().optional(),
  rangeActivities: z.string().optional(),
  payrollNumber: z.number().nullable().optional(),
  roomNumber: z.number().nullable().optional(),
  clubNumber: z.number().nullable().optional(),
  experience: z.string().optional(),
  deadlineContractDate: z.string().optional(),
  note: z.string().optional(),
  status: z.number().min(1, 'Trạng thái là bắt buộc'),
})

type FormValues = z.infer<typeof formSchema>

interface CategoryOption {
  value: string
  label: string
}

export function PhongBanModal({ isOpen, onClose, parentId, onSuccess, fixedParent = null }: PhongBanModalProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      code: '',
      fullName: '',
      representative: '',
      address: '',
      phone: '',
      fax: '',
      email: '',
      website: '',
      establishmentDate: '',
      decisionDate: '',
      decisionNumber: '',
      legalField: '',
      legalForm: '',
      rangeActivities: '',
      payrollNumber: null,
      roomNumber: null,
      clubNumber: null,
      experience: '',
      deadlineContractDate: '',
      note: '',
      status: 1,
    },
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [, setParentOptions] = useState<CategoryOption[]>([])
  const [, setLoadingParents] = useState(false)
  const [legalFieldOptions, setLegalFieldOptions] = useState<CategoryOption[]>([])
  const [legalFormOptions, setLegalFormOptions] = useState<CategoryOption[]>([])
  const [loadingLegalField, setLoadingLegalField] = useState(false)
  const [loadingLegalForm, setLoadingLegalForm] = useState(false)
  // Fetch lĩnh vực pháp lý
  useEffect(() => {
    const fetchLegalField = async () => {
      setLoadingLegalField(true)

      try {
        const result = await import('@/services/categoryService').then(m => m.categoryService.getCategories('LV'))
        setLegalFieldOptions(
          (result as Category[])
            .map(item => ({ value: item.categoryId || '', label: item.name || '' }))
            .filter(option => option.value && option.label)
        )
      } catch {
        setLegalFieldOptions([])
      } finally {
        setLoadingLegalField(false)
      }
    }
    fetchLegalField()
  }, [])

  // Fetch hình thức pháp lý
  useEffect(() => {
    const fetchLegalForm = async () => {
      setLoadingLegalForm(true)

      try {
        const result = await import('@/services/categoryService').then(m => m.categoryService.getCategories('TG'))
        setLegalFormOptions(
          (result as Category[])
            .map(item => ({ value: item.categoryId || '', label: item.name || '' }))
            .filter(option => option.value && option.label)
        )
      } catch {
        setLegalFormOptions([])
      } finally {
        setLoadingLegalForm(false)
      }
    }
    fetchLegalForm()
  }, [])

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      form.reset()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen])

  // Fetch parent options when modal opens
  useEffect(() => {
    if (isOpen && !fixedParent) {
      fetchParentOptions()
    } else if (fixedParent) {
      setParentOptions([
        {
          value: fixedParent.id,
          label: `${fixedParent.code} - ${fixedParent.name}`,
        },
      ])
    }
  }, [isOpen, fixedParent])

  const fetchParentOptions = async () => {
    setLoadingParents(true)

    try {
      // Fetch Sở Tư pháp (type 2) as potential parents for Phòng ban
      const response = await fetch('/ac-apis/organization-units/type/2')

      if (response.ok) {
        const result = (await response.json()) as { items: (OrganizationUnit & { categoryId: string })[] }
        const options =
          result.items
            ?.map(item => ({
              value: item.id || item.categoryId || '',
              label: `${item.code || ''} - ${item.name || ''}`,
            }))
            .filter(option => option.value && option.label) || []
        setParentOptions(options)
      }
    } catch (error) {
      console.error('Error fetching parent options:', error)
      toast.error('Không thể tải danh sách đơn vị quản lý')
    } finally {
      setLoadingParents(false)
    }
  }

  const handleClose = () => {
    form.reset()

    onClose()
  }

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      // Remove all keys with empty string, null, or undefined
      const filtered = Object.fromEntries(
        Object.entries(values).filter(([, value]) => !(value === '' || value === null || value === undefined))
      )
      const submitData = {
        ...filtered,
        type: ORGANIZATION_TYPE.PHONG_BAN,
        parentId: fixedParent ? fixedParent.id : parentId,
      }
      const response = await fetch('/ac-apis/organization-units', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Tạo phòng ban thất bại')
      }
      toast.success('Tạo phòng ban thành công')
      handleClose()

      onSuccess()
    } catch (error) {
      console.error('Error creating phong ban:', error)

      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Không cần updateFormData nữa

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-[90vw] max-w-5xl min-w-[850px]">
        <DialogHeader>
          <DialogTitle>Thêm mới Phòng ban</DialogTitle>
          <DialogDescription />
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="max-h-[70vh] space-y-6 overflow-y-auto py-4">
            {/* Thông tin cơ bản */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin cơ bản</h3>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Tên phòng ban </FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên phòng ban" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {fixedParent && (
                <div className="space-y-2">
                  <Label>Đơn vị quản lý</Label>
                  <Input value={`${fixedParent.code} - ${fixedParent.name}`} disabled className="bg-muted" />
                </div>
              )}
              <div className="grid grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Mã phòng ban </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập mã phòng ban" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="representative"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Người đại diện</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên người đại diện" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Trạng thái</FormLabel>
                      <FormControl>
                        <Select value={field.value?.toString()} onValueChange={val => field.onChange(Number(val))}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn trạng thái" />
                          </SelectTrigger>
                          <SelectContent>
                            {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                              <SelectItem key={option.value} value={option.value.toString()}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đầy đủ</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên đầy đủ" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập địa chỉ" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* Thông tin liên hệ */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin liên hệ</h3>
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số điện thoại</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số điện thoại" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số fax</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số fax" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Nhập email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập website" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            {/* Thông tin thành lập */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin thành lập</h3>
              <div className="grid grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="establishmentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ngày thành lập</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? field.value : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={date => field.onChange(date ? date.toISOString().slice(0, 10) : '')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="decisionDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ngày quyết định</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? field.value : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={date => field.onChange(date ? date.toISOString().slice(0, 10) : '')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="decisionNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số quyết định</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số quyết định" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            {/* Thông tin tổ chức */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin tổ chức</h3>
              <div className="grid grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="payrollNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số nhân sự</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Nhập số nhân sự"
                          value={field.value ?? ''}
                          onChange={e => {
                            const val = e.target.value
                            field.onChange(val === '' ? null : Number(val))
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="roomNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số phòng</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Nhập số phòng"
                          value={field.value ?? ''}
                          onChange={e => {
                            const val = e.target.value
                            field.onChange(val === '' ? null : Number(val))
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="clubNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số câu lạc bộ</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="Nhập số câu lạc bộ"
                          value={field.value ?? ''}
                          onChange={e => {
                            const val = e.target.value
                            field.onChange(val === '' ? null : Number(val))
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            {/* Lĩnh vực pháp lý */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Lĩnh vực pháp lý</h3>
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="legalField"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lĩnh vực pháp lý</FormLabel>
                      <FormControl>
                        <Combobox
                          options={legalFieldOptions}
                          value={field.value || ''}
                          onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                          placeholder={loadingLegalField ? 'Đang tải...' : 'Chọn lĩnh vực'}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="legalForm"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hình thức pháp lý</FormLabel>
                      <FormControl>
                        <Combobox
                          options={legalFormOptions}
                          value={field.value || ''}
                          onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                          placeholder={loadingLegalForm ? 'Đang tải...' : 'Chọn hình thức'}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="rangeActivities"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phạm vi hoạt động</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Nhập phạm vi hoạt động" rows={3} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* Thông tin bổ sung */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin bổ sung</h3>
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kinh nghiệm</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Nhập thông tin kinh nghiệm" rows={3} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ghi chú</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Nhập ghi chú" rows={3} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="deadlineContractDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày hết hạn hợp đồng</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full justify-start text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? field.value : <span>Chọn ngày</span>}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={date => field.onChange(date ? date.toISOString().slice(0, 10) : '')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
          <div className="flex flex-shrink-0 justify-end gap-2 border-t pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button type="button" disabled={isSubmitting} onClick={form.handleSubmit(handleSubmit)}>
              {isSubmitting ? 'Đang tạo...' : 'Tạo mới'}
            </Button>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
