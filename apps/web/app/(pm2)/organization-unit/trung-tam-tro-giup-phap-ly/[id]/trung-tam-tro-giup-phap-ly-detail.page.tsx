'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { Staff } from '@/constants/staff'
import { organizationService } from '@/services/organizationService'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Loading } from '@workspace/ui/mi/loading'
import { ArrowLeft, Building2, Contact, Edit, FileText, MoreHorizontal, Plus, Users } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { ChiNhanhModal } from '../../chi-nhanh-modal'
import { PhongBanModal } from '../../phong-ban-modal'
import { TrungTamTroGiupPhapLyForm } from '../trung-tam-tro-giup-phap-ly-form'

interface TrungTamTroGiupPhapLyDetailPageProps {
  id: string
}

export function TrungTamTroGiupPhapLyDetailPage({ id }: TrungTamTroGiupPhapLyDetailPageProps) {
  const router = useRouter()
  const [data, setData] = useState<Organization | null>(null)
  const [children, setChildren] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [childrenLoading, setChildrenLoading] = useState(true)
  const [isEditMode, setIsEditMode] = useState(false)
  const [isAddChiNhanhModalOpen, setIsAddChiNhanhModalOpen] = useState(false)
  const [isAddPhongBanModalOpen, setIsAddPhongBanModalOpen] = useState(false)
  const [staff, setStaff] = useState<Staff[]>([])
  const [collaboratorCount, setCollaboratorCount] = useState<number>(0)

  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true)
      setChildrenLoading(true)

      try {
        const [org, children, staff, collaboratorCount] = await Promise.all([
          organizationService.fetchOrganizationDetail(id),
          organizationService.fetchOrganizationChildren(id),
          organizationService.fetchOrganizationStaffs(id),
          organizationService.fetchOrganizationCollaboratorsCount(id),
        ])

        setData(org as Organization)

        setChildren(children as Organization[])

        setStaff(staff as Staff[])

        setCollaboratorCount(collaboratorCount as number)
      } catch {
        toast.error('Không thể tải thông tin tổ chức hoặc nhân sự')
      } finally {
        setLoading(false)
        setChildrenLoading(false)
      }
    }

    if (id) fetchAll()
  }, [id])

  const handleEditSuccess = async () => {
    setIsEditMode(false)

    setLoading(true)
    setChildrenLoading(true)

    try {
      const [org, children] = await Promise.all([
        organizationService.fetchOrganizationDetail(id),
        organizationService.fetchOrganizationChildren(id),
      ])

      setData(org as Organization)

      setChildren(children as Organization[])
    } catch {
      toast.error('Không thể tải thông tin tổ chức hoặc phòng ban')
    } finally {
      setLoading(false)
      setChildrenLoading(false)
    }
  }

  const handleChiNhanhSuccess = async () => {
    setChildrenLoading(true)

    try {
      const children = await organizationService.fetchOrganizationChildren(id)

      setChildren(children as Organization[])
    } catch {
      toast.error('Không thể tải thông tin phòng ban và chi nhánh')
    } finally {
      setChildrenLoading(false)
    }
  }

  const handlePhongBanSuccess = async () => {
    setChildrenLoading(true)

    try {
      const children = await organizationService.fetchOrganizationChildren(id)

      setChildren(children as Organization[])
    } catch {
      toast.error('Không thể tải thông tin phòng ban và chi nhánh')
    } finally {
      setChildrenLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Trung tâm trợ giúp pháp lý',
            href: '/organization-unit/trung-tam-tro-giup-phap-ly',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <Loading />
      </AdminPageLayout>
    )
  }

  if (!data) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Trung tâm trợ giúp pháp lý',
            href: '/organization-unit/trung-tam-tro-giup-phap-ly',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">Không tìm thấy thông tin Trung tâm trợ giúp pháp lý</p>
        </div>
      </AdminPageLayout>
    )
  }

  // const statusOption = STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === data.status)
  // const typeOption = ORGANIZATION_TYPE_OPTIONS.find(t => t.value === data.type)

  // Phân loại phòng ban và chi nhánh từ children data
  const phongBanList = children?.filter(child => child.type === ORGANIZATION_TYPE.PHONG_BAN)
  const chiNhanhList = children?.filter(child => child.type === ORGANIZATION_TYPE.CHI_NHANH)

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Trung tâm trợ giúp pháp lý',
            href: '/organization-unit/trung-tam-tro-giup-phap-ly',
          },
          { label: data.name, href: '#' },
        ]}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.back()} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="flex items-center gap-2 text-2xl font-semibold">Thông tin chi tiết</h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setIsEditMode(true)} className="gap-2">
                <Edit className="h-4 w-4" />
                Cập nhật
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card className="h-fit py-0">
              <CardContent className="p-0">
                <div className="rounded-t-lg p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <h3 className="mb-1 text-base font-semibold">
                    Tên tổ chức: {data.name || 'Trung tâm TGPL NN thành phố Hà Nội'}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Địa chỉ: {data.address || 'Số 2 đường Quang Trung, Phường Yết Kiêu, Quận Hà Đông, Thành phố Hà Nội'}
                  </p>
                </div>

                <div className="space-y-4 p-6">
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Users className="h-4 w-4" />
                      Thông tin nhân sự
                    </h4>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Người đứng đầu</span>
                        <span className="font-medium">{data.representative}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số nhân sự hiện có</span>
                        <span className="font-medium">{staff?.length} người</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số trợ giúp viên</span>
                        <span className="font-medium">{collaboratorCount} người</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số nhân sự khác</span>
                        <span className="font-medium">03 người</span>
                      </div>
                    </div>
                  </div>

                  {/* Thông tin liên hệ */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Contact className="h-4 w-4" />
                      Thông tin liên hệ
                    </h4>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số điện thoại</span>
                        <span className="font-medium">{data.phone || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Email</span>
                        <span className="font-medium">{data.email || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Website</span>
                        <span className="text-muted-foreground italic">{data.website || 'Chưa cập nhật'}</span>
                      </div>
                    </div>
                  </div>

                  {data.note && (
                    <div>
                      <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                        <FileText className="h-4 w-4" />
                        Ghi chú
                      </h4>
                      <p className="text-muted-foreground text-sm">{data.note}</p>
                    </div>
                  )}

                  {!data.note && (
                    <div>
                      <h4 className="mb-2 text-base font-medium">Ghi chú</h4>
                      <p className="text-muted-foreground text-sm italic">Chưa cập nhật</p>
                    </div>
                  )}
                </div>

                <div className="rounded-b-lg border-t p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <p className="text-muted-foreground text-xs">
                    Ngày cập nhật:{' '}
                    {data.updatedAt ? new Date(data.updatedAt).toLocaleDateString('vi-VN') : '12/08/2025'}
                  </p>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Phòng ban
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsAddPhongBanModalOpen(true)}>
                      <Plus className="h-4 w-4" />
                      Thêm phòng ban
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {phongBanList.length} phòng ban</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : phongBanList.length > 0 ? (
                    <div className="space-y-4">
                      {phongBanList.map((phongBan, index) => (
                        <div key={phongBan.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">Tên phòng ban</p>
                              <p className="text-muted-foreground text-xs">{phongBan.name}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <p className="text-muted-foreground text-xs">Trạng thái</p>
                              <Badge
                                variant={
                                  phongBan.status === 1
                                    ? 'default'
                                    : phongBan.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                                style={{
                                  backgroundColor:
                                    phongBan.status === 1 ? '#22C55E' : phongBan.status === 2 ? '#F59E0B' : '#EF4444',
                                  color: 'white',
                                  borderColor:
                                    phongBan.status === 1 ? '#22C55E' : phongBan.status === 2 ? '#F59E0B' : '#EF4444',
                                }}
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === phongBan.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < phongBanList.length - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có phòng ban nào</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Chi nhánh */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Chi nhánh
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsAddChiNhanhModalOpen(true)}>
                      <Plus className="h-4 w-4" />
                      Thêm chi nhánh
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {chiNhanhList.length} chi nhánh</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : chiNhanhList.length > 0 ? (
                    <div className="space-y-4">
                      {chiNhanhList.slice(0, 5).map((chiNhanh, index) => (
                        <div key={chiNhanh.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">Tên chi nhánh</p>
                              <p className="text-muted-foreground text-xs">{chiNhanh.name}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <p className="text-muted-foreground text-xs">Trạng thái</p>
                              <Badge
                                variant={
                                  chiNhanh.status === 1
                                    ? 'default'
                                    : chiNhanh.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                                style={{
                                  backgroundColor:
                                    chiNhanh.status === 1 ? '#22C55E' : chiNhanh.status === 2 ? '#F59E0B' : '#EF4444',
                                  color: 'white',
                                  borderColor:
                                    chiNhanh.status === 1 ? '#22C55E' : chiNhanh.status === 2 ? '#F59E0B' : '#EF4444',
                                }}
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === chiNhanh.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < Math.min(chiNhanhList.length, 5) - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có chi nhánh nào</p>
                    </div>
                  )}

                  {chiNhanhList.length > 5 && (
                    <>
                      <hr className="border-border my-4" />
                      <div className="flex justify-center">
                        <Button variant="ghost" size="sm" className="text-blue-600">
                          Xem thêm ({chiNhanhList.length - 5} chi nhánh)
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </AdminPageLayout>

      {/* Edit Dialog */}
      <Dialog open={isEditMode} onOpenChange={setIsEditMode}>
        <DialogContent className="flex max-h-[85vh] flex-col gap-0 p-0 sm:max-w-[700px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Chỉnh sửa Trung tâm trợ giúp pháp lý</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <div className="overflow-y-auto">
            <TrungTamTroGiupPhapLyForm
              initialData={data}
              organizationType={data.type}
              onSuccess={handleEditSuccess}
              onCancel={() => setIsEditMode(false)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Chi Nhanh Modal */}
      <ChiNhanhModal
        isOpen={isAddChiNhanhModalOpen}
        onClose={() => setIsAddChiNhanhModalOpen(false)}
        parentId={id}
        onSuccess={handleChiNhanhSuccess}
        fixedParent={data ? { id: data.id, name: data.name, code: data.code } : null}
      />

      {/* Add Phong Ban Modal */}
      <PhongBanModal
        isOpen={isAddPhongBanModalOpen}
        onClose={() => setIsAddPhongBanModalOpen(false)}
        parentId={id}
        onSuccess={handlePhongBanSuccess}
        fixedParent={data ? { id: data.id, name: data.name, code: data.code } : null}
      />
    </>
  )
}
