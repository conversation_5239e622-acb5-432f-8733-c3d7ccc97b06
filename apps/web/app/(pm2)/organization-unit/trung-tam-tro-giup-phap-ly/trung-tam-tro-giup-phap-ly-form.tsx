'use client'

import { ORGANIZATION_TYPE_OPTIONS } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { Required } from '@workspace/ui/mi/required'
import { Loader2 } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  code: z.string().min(1, '<PERSON><PERSON> cơ quan là bắt buộc'),
  name: z.string().min(1, '<PERSON>ên cơ quan là bắt buộc'),
  fullName: z.string().optional(),
  representative: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  website: z.string().url('Website không hợp lệ').optional().or(z.literal('')),

  // Địa phương
  oldProvinceId: z.string().optional(),
  cityId: z.number().optional(),
  oldDistrictId: z.string().optional(),
  communeId: z.number().optional(),

  // Cấu trúc cây
  parentId: z.string().min(1, 'Đơn vị quản lý là bắt buộc'),

  // Trạng thái và phân loại
  type: z.number().optional(),
  status: z.number(),

  // Thành lập
  establishmentDate: z.string().optional(),
  decisionDate: z.string().optional(),
  decisionNumber: z.string().optional(),

  // Lĩnh vực pháp lý
  legalFieldId: z.string().optional(),
  legalFormId: z.string().optional(),

  // Phạm vi hoạt động
  rangeActivities: z.string().optional(),
  rangeActivitiesId: z.string().optional(),

  // Tổ chức
  payrollNumber: z.number().optional(),
  roomNumber: z.number().optional(),
  clubNumber: z.number().optional(),

  // Quản trị
  adminGroup: z.number().optional(),
  adminStatusId: z.number().optional(),
  experience: z.string().optional(),
  deadlineContractDate: z.string().optional(),
  note: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

interface TrungTamTroGiupPhapLyFormProps {
  initialData?: Organization | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
}

export function TrungTamTroGiupPhapLyForm({
  initialData,
  onSuccess,
  organizationType,
}: TrungTamTroGiupPhapLyFormProps) {
  const [parentOptions, setParentOptions] = useState<Array<{ id: string; code: string; name: string }>>([])
  const [isLoadingParents, setIsLoadingParents] = useState(false)
  const [hasMoreParents, setHasMoreParents] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState('')
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastLoadTriggerRef = useRef(0)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: initialData?.code || '',
      name: initialData?.name || '',
      fullName: initialData?.fullName || '',
      representative: initialData?.representative || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      fax: initialData?.fax || '',
      email: initialData?.email || '',
      website: initialData?.website || '',

      // Địa phương
      oldProvinceId: initialData?.oldProvinceId || '',
      oldDistrictId: initialData?.oldDistrictId || '',
      communeId: initialData?.communeId || undefined,

      // Cấu trúc cây
      parentId: initialData?.parentId || '',

      // Trạng thái và phân loại
      type: initialData?.type || organizationType || undefined,
      status: initialData?.status ?? 1,

      // Thành lập
      establishmentDate: initialData?.establishmentDate || '',
      decisionDate: initialData?.decisionDate || '',
      decisionNumber: initialData?.decisionNumber || '',

      // Lĩnh vực pháp lý
      legalFieldId: initialData?.legalFieldId || '',

      // Phạm vi hoạt động
      rangeActivitiesId: initialData?.rangeActivitiesId || '',

      // Tổ chức
      payrollNumber: initialData?.payrollNumber || undefined,
      roomNumber: initialData?.roomNumber || undefined,
      clubNumber: initialData?.clubNumber || undefined,

      // Quản trị
      adminGroup: Number(initialData?.adminGroup?.categoryId) || undefined,
      adminStatusId: initialData?.adminStatusId || undefined,
      experience: initialData?.experience || '',
      deadlineContractDate: initialData?.deadlineContractDate || '',
      note: initialData?.note || '',
    },
  })

  const loadParentOptions = async (page = 1, search = '', append = false) => {
    try {
      setIsLoadingParents(true)

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: '20',
      })

      if (search.trim()) {
        params.append('keyword', search.trim())
      }

      const response = await fetch(`/ac-apis/organization-units/type/2?${params.toString()}`)

      if (response.ok) {
        const data = (await response.json()) as {
          data: Organization[]
          items: Organization[]
          totalItems: number
          total: number
        }
        const items = data.data || data.items || []

        if (append) {
          setParentOptions(prev => {
            const existing = new Set(prev.map(item => item.id))
            const newItems = items.filter(item => !existing.has(item.id))

            return [...prev, ...newItems]
          })
        } else {
          setParentOptions(items)
        }

        // Check if there are more items
        const totalItems = data.totalItems || data.total || 0
        const loadedItems = append ? parentOptions.length + items.length : items.length
        setHasMoreParents(loadedItems < totalItems)

        setCurrentPage(page)
      }
    } catch (error) {
      console.error('Error loading parent options:', error)
    } finally {
      setIsLoadingParents(false)
    }
  }

  const loadMoreParents = () => {
    if (!isLoadingParents && hasMoreParents) {
      loadParentOptions(currentPage + 1, searchValue, true)
    }
  }

  useEffect(() => {
    loadParentOptions(1, searchValue, false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue])

  useEffect(() => {
    loadParentOptions()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const onSubmit = async (data: FormData) => {
    try {
      const url = initialData ? `/ac-apis/organization-units/${initialData.id}` : '/ac-apis/organization-units'

      const method = initialData ? 'PATCH' : 'POST'

      const submitData = {
        ...data,
        parentId: data.parentId || null,
        type: organizationType || data.type,
        ...(initialData && { id: initialData.id }),
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Có lỗi xảy ra')
      }

      const result = await response.json()
      console.log(result)
      toast.success(initialData ? `Đã cập nhật ${data.name} thành công` : `Đã tạo ${data.name} thành công`)

      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Form {...form}>
        <form id="organization-form" onSubmit={form.handleSubmit(onSubmit)} className="flex h-full flex-col">
          <div className="flex-1 space-y-6 overflow-y-auto px-6 py-4">
            {/* Thông tin cơ bản */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Thông tin cơ bản</h3>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Mã cơ quan <Required />
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập mã cơ quan" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tên cơ quan <Required />
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập tên cơ quan" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đầy đủ</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên đầy đủ" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="representative"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Người đại diện</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên người đại diện" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Loại cơ quan và trạng thái */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Phân loại</h3>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="type"
                  render={() => {
                    const organizationTypeLabel = organizationType
                      ? ORGANIZATION_TYPE_OPTIONS.find(opt => opt.value === organizationType)?.label
                      : 'Không xác định'

                    return (
                      <FormItem>
                        <FormLabel>Loại cơ quan </FormLabel>
                        <FormControl>
                          <Input value={organizationTypeLabel} disabled className="bg-muted" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trạng thái</FormLabel>
                      <Select onValueChange={value => field.onChange(Number(value))} value={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn trạng thái" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Đơn vị quản lý */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Đơn vị quản lý</h3>

              <FormField
                control={form.control}
                name="parentId"
                render={({ field }) => (
                  <FormItem className="overflow-hidden">
                    <FormLabel>
                      Đơn vị quản lý <Required />
                    </FormLabel>
                    <FormControl className="overflow-hidden">
                      <Select
                        value={field.value || 'none'}
                        onValueChange={value => {
                          field.onChange(value === 'none' ? undefined : value)
                          setSearchValue('')
                        }}
                        onOpenChange={open => {
                          if (!open) {
                            setSearchValue('')
                            lastLoadTriggerRef.current = 0
                          } else {
                            lastLoadTriggerRef.current = 0
                          }
                        }}
                      >
                        <SelectTrigger className="w-full overflow-hidden [&>span]:!block [&>span]:!max-w-[calc(100%-2rem)] [&>span]:!truncate [&>span]:!pr-8">
                          <SelectValue placeholder="Chọn đơn vị quản lý" />
                        </SelectTrigger>
                        <SelectContent className="max-w-[90vw] min-w-[var(--radix-select-trigger-width)] sm:max-w-[425px]">
                          <div className="p-2">
                            <Input
                              placeholder="Tìm kiếm đơn vị..."
                              value={searchValue}
                              onChange={e => setSearchValue(e.target.value)}
                              onKeyDown={e => {
                                // Prevent Select from closing when typing
                                e.stopPropagation()

                                // Prevent default behavior for special keys
                                if (e.key === 'Enter' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                                  e.preventDefault()
                                }
                              }}
                              onClick={e => {
                                // Prevent Select from closing when clicking input
                                e.stopPropagation()
                              }}
                              onFocus={e => {
                                // Ensure input stays focused
                                e.stopPropagation()
                              }}
                              autoFocus
                              className="mb-2"
                            />
                          </div>
                          <div
                            className="max-h-[200px] overflow-y-auto"
                            onScroll={e => {
                              const { scrollTop, scrollHeight, clientHeight } = e.currentTarget

                              // Debounce scroll events
                              if (scrollTimeoutRef.current) {
                                clearTimeout(scrollTimeoutRef.current)
                              }

                              scrollTimeoutRef.current = setTimeout(() => {
                                const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10 // 10px buffer
                                const now = Date.now()

                                // Load more when near bottom (10px buffer is more reliable)
                                if (
                                  isNearBottom &&
                                  hasMoreParents &&
                                  !isLoadingParents &&
                                  now - lastLoadTriggerRef.current > 500
                                ) {
                                  console.log('🚀 TRIGGERING loadMoreParents', {
                                    reason: 'Near bottom detected',
                                    currentOptions: parentOptions.length,
                                    hasMore: hasMoreParents,
                                    isLoading: isLoadingParents,
                                  })
                                  lastLoadTriggerRef.current = now
                                  loadMoreParents()
                                } else if (isNearBottom) {
                                  console.log('❌ NOT triggering loadMore because:', {
                                    hasMoreParents,
                                    isLoadingParents,
                                    timeSinceLastLoad: now - lastLoadTriggerRef.current,
                                    needsWait: now - lastLoadTriggerRef.current <= 500,
                                  })
                                }
                              }, 50)
                            }}
                          >
                            <SelectItem value="none">Không có</SelectItem>
                            {parentOptions
                              .filter((option, index, arr) => {
                                // Remove duplicates by ID
                                const firstIndex = arr.findIndex(c => c.id === option.id)

                                return firstIndex === index
                              })
                              .map((option, index) => {
                                const uniqueKey = `parent-${option.id}-${index}`

                                return (
                                  <SelectItem
                                    key={uniqueKey}
                                    value={option.id}
                                    title={`${option.code} - ${option.name}`}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex w-full items-center overflow-hidden">
                                      <span className="flex-1 truncate">
                                        {option.code} - {option.name}
                                      </span>
                                    </div>
                                  </SelectItem>
                                )
                              })}
                            {isLoadingParents && (
                              <div className="flex items-center justify-center p-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="text-muted-foreground ml-2 text-sm">Đang tải...</span>
                              </div>
                            )}
                            {!hasMoreParents && parentOptions.length > 0 && (
                              <div className="text-muted-foreground p-2 text-center text-sm">Đã tải hết đơn vị</div>
                            )}
                          </div>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Thông tin liên hệ */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Thông tin liên hệ</h3>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Nhập địa chỉ" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Điện thoại</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số điện thoại" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="fax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fax</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số fax" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" placeholder="Nhập email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập website" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Ghi chú */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Ghi chú</h3>

              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ghi chú</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Nhập ghi chú" rows={3} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
