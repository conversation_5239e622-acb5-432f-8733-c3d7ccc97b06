'use client'

import { ORGANIZATION_TYPE_OPTIONS } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { Badge } from '@workspace/ui/components/badge'
import { Card, CardHeader } from '@workspace/ui/components/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip'
import { cn } from '@workspace/ui/lib/utils'
import { Handle, NodeProps, Position } from '@xyflow/react'
import { Building2 } from 'lucide-react'

export interface OrganizationNodeData extends Record<string, unknown> {
  organization: Organization
  variant?: 'default' | 'current' | 'parent' | 'child'
  level: number
}

function OrganizationNode({ data, selected }: NodeProps) {
  const { organization, variant = 'default' } = data as OrganizationNodeData

  const getStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'bg-green-100 text-green-800 border-green-200'
      case 0:
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getNodeBorderColor = () => {
    if (selected) return 'border-blue-500'

    switch (variant) {
      case 'current':
        return 'border-green-500'
      case 'parent':
        return 'border-blue-400'
      case 'child':
        return 'border-orange-400'
      default:
        return 'border-gray-200'
    }
  }

  const getNodeBgColor = () => {
    switch (variant) {
      case 'current':
        return 'bg-green-50/50'
      case 'parent':
        return 'bg-blue-50/50'
      case 'child':
        return 'bg-orange-50/50'
      default:
        return 'bg-white'
    }
  }

  return (
    <div className="relative">
      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        className="h-3 w-3 !border-2 !border-white !bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className="h-3 w-3 !border-2 !border-white !bg-blue-500"
      />

      <Card
        className={cn(
          'w-72 shadow-lg transition-all duration-200 hover:shadow-xl',
          getNodeBorderColor(),
          getNodeBgColor(),
          selected && 'scale-105 shadow-xl'
        )}
      >
        <CardHeader className="pt-3 pb-2">
          <div className="flex items-start gap-2">
            <div
              className={cn(
                'rounded-lg p-1.5',
                variant === 'current'
                  ? 'bg-green-100 text-green-600'
                  : variant === 'parent'
                    ? 'bg-blue-100 text-blue-600'
                    : variant === 'child'
                      ? 'bg-orange-100 text-orange-600'
                      : 'bg-gray-100 text-gray-600'
              )}
            >
              <Building2 className="h-4 w-4" />
            </div>

            <div className="min-w-0 flex-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <h3 className="line-clamp-2 cursor-help text-sm leading-tight font-semibold">
                      {organization.name}
                    </h3>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <p>{organization.name}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {organization.fullName && organization.fullName !== organization.name && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <p className="text-muted-foreground mt-1 line-clamp-1 cursor-help text-xs">
                        {organization.fullName}
                      </p>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="max-w-xs">
                      <p>{organization.fullName}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              <div className="mt-1.5 flex flex-wrap items-center gap-1">
                <Badge variant="outline" className={cn('px-2 py-0.5 text-xs', getStatusColor(organization.status))}>
                  {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === organization.status)?.label || 'Không xác định'}
                </Badge>

                {organization.type && (
                  <Badge variant="secondary" className="px-2 py-0.5 text-xs">
                    {ORGANIZATION_TYPE_OPTIONS.find(t => t.value === organization.type)?.label || 'Khác'}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>
    </div>
  )
}

// Define node types for React Flow
export const nodeTypes = {
  organizationNode: OrganizationNode,
}
