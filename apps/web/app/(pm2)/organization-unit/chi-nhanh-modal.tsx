'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Category } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { Combobox } from '@workspace/ui/mi/combobox'
import { CalendarIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useWatch } from 'react-hook-form'
import z from 'zod'

interface ChiNhanhModalProps {
  isOpen: boolean
  onClose: () => void
  parentId: string
  onSuccess: () => void
  fixedParent?: { id: string; name: string; code: string } | null
}

const formSchema = z.object({
  name: z.string().min(1, 'Tên chi nhánh là bắt buộc'),
  code: z.string().min(1, 'Mã chi nhánh là bắt buộc'),
  decisionNumber: z.string().min(1, 'Số quyết định thành lập là bắt buộc'),
  decisionDate: z.string().min(1, 'Ngày quyết định là bắt buộc'),
  representative: z.string().optional(),
  payrollNumber: z.number().nullable().optional(),
  experience: z.string().optional(),
  rangeActivities: z.string().optional(),
  oldProvinceId: z.string().min(1, 'Tỉnh / Thành phố là bắt buộc'),
  oldDistrictId: z.string().min(1, 'Phường / Xã là bắt buộc'),
  address: z.string().min(1, 'Địa chỉ là bắt buộc'),
  phone: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional(),
  website: z.string().url('Website không hợp lệ').optional(),
  note: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

const initialFormData: FormValues = {
  name: '',
  code: '',
  decisionNumber: '',
  decisionDate: '',
  representative: '',
  payrollNumber: null,
  experience: '',
  rangeActivities: '',
  oldProvinceId: '',
  oldDistrictId: '',
  address: '',
  phone: '',
  email: '',
  website: '',
  note: '',
}

interface CategoryOption {
  value: string
  label: string
}

export function ChiNhanhModal({ isOpen, onClose, parentId, onSuccess, fixedParent = null }: ChiNhanhModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [provinces, setProvinces] = useState<CategoryOption[]>([])
  const [districts, setDistricts] = useState<CategoryOption[]>([])
  const [loadingProvinces, setLoadingProvinces] = useState(false)
  const [loadingDistricts, setLoadingDistricts] = useState(false)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialFormData,
  })

  // Fetch provinces when modal opens
  useEffect(() => {
    if (isOpen && provinces.length === 0) {
      fetchProvinces()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen])

  // Fetch districts when province changes
  const provinceId = useWatch({ control: form.control, name: 'oldProvinceId' })

  useEffect(() => {
    if (provinceId) {
      fetchDistricts(provinceId)
    } else {
      setDistricts([])
      form.setValue('oldDistrictId', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [provinceId])

  const fetchProvinces = async () => {
    setLoadingProvinces(true)

    try {
      const response = await fetch('/ac-apis/categories?type=TP')

      if (response.ok) {
        const result = (await response.json()) as { items: (Category & { id: string })[] }
        const provinceOptions =
          result.items
            ?.map(item => ({
              value: item.categoryId || item.id || item.code || '',
              label: item.name || '',
            }))
            .filter(option => option.value && option.label) || []
        setProvinces(provinceOptions)
      }
    } catch (error) {
      console.error('Error fetching provinces:', error)
      toast.error('Không thể tải danh sách tỉnh/thành phố')
    } finally {
      setLoadingProvinces(false)
    }
  }

  const fetchDistricts = async (oldProvinceId: string) => {
    setLoadingDistricts(true)

    try {
      const response = await fetch(`/ac-apis/categories?type=QH&parentId=${oldProvinceId}`)

      if (response.ok) {
        const result = (await response.json()) as { items: (Category & { id: string })[] }
        const districtOptions =
          result.items
            ?.map(item => ({
              value: item.categoryId || item.id || item.code || '',
              label: item.name || '',
            }))
            .filter(option => option.value && option.label) || []
        setDistricts(districtOptions)
      }
    } catch (error) {
      console.error('Error fetching districts:', error)
      toast.error('Không thể tải danh sách quận/huyện')
    } finally {
      setLoadingDistricts(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      const raw = form.getValues()
      const filtered = Object.fromEntries(
        Object.entries(raw).filter(([, v]) => v !== '' && v !== null && v !== undefined)
      )
      const payload = {
        ...filtered,
        type: ORGANIZATION_TYPE.CHI_NHANH,
        parentId: parentId,
        status: 1,
      }
      const response = await fetch('/ac-apis/organization-units', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Có lỗi xảy ra khi tạo chi nhánh')
      }
      toast.success('Đã tạo chi nhánh thành công')
      handleClose()
      onSuccess()
    } catch (error) {
      console.error('Error creating chi nhanh:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra khi tạo chi nhánh')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-[90vw] max-w-5xl min-w-[850px]">
        <DialogHeader>
          <DialogTitle>Thêm mới Chi nhánh</DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="max-h-[70vh] space-y-6 overflow-y-auto py-4">
            {/* Thông tin cơ bản */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin cơ bản</h3>

              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Tên chi nhánh </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên chi nhánh" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {fixedParent && (
              <div className="space-y-2">
                <Label>Đơn vị quản lý</Label>
                <Input value={`${fixedParent.code} - ${fixedParent.name}`} disabled className="bg-muted" />
              </div>
            )}

            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Mã chi nhánh </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập mã chi nhánh" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="decisionNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Số quyết định thành lập</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số quyết định" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="decisionDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ngày quyết định</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? field.value : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={date => field.onChange(date ? date.toISOString().slice(0, 10) : '')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="representative"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Người đứng đầu</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên người đứng đầu" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="payrollNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số nhân sự</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Nhập số nhân sự"
                        value={field.value ?? ''}
                        onChange={e => {
                          const val = e.target.value
                          field.onChange(val === '' ? null : Number(val))
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="experience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kinh nghiệm năng lực</FormLabel>
                    <FormControl>
                      <Input id="experience" placeholder="Nhập số" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <FormField
                control={form.control}
                name="rangeActivities"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phạm vi hoạt động</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="toan-quoc">Toàn quốc</SelectItem>
                          <SelectItem value="khu-vuc">Khu vực</SelectItem>
                          <SelectItem value="dia-phuong">Địa phương</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Thông tin liên hệ */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Thông tin liên hệ</h3>

              <div className="grid grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="oldProvinceId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Tỉnh / Thành phố</FormLabel>
                      <FormControl>
                        <Combobox
                          options={provinces}
                          value={field.value}
                          onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                          placeholder={loadingProvinces ? 'Đang tải...' : 'Chọn tỉnh/thành phố'}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="oldDistrictId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Phường / Xã</FormLabel>
                      <FormControl>
                        <Combobox
                          options={districts}
                          value={field.value}
                          onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                          placeholder={
                            loadingDistricts
                              ? 'Đang tải...'
                              : !form.watch('oldProvinceId')
                                ? 'Chọn tỉnh/thành phố trước'
                                : 'Chọn phường/xã'
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Địa chỉ</FormLabel>
                      <FormControl>
                        <Input id="address" placeholder="Nhập địa chỉ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số điện thoại</FormLabel>
                      <FormControl>
                        <Input id="phone" placeholder="Nhập số" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input id="email" type="email" placeholder="Nhập email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input id="website" placeholder="Nhập địa chỉ website" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Ghi chú */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Ghi chú</h3>

              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ghi chú</FormLabel>
                      <FormControl>
                        <Textarea id="note" placeholder="Nhập ghi chú" rows={4} className="resize-none" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Đính kèm và Tải lên tệp */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button variant="outline" className="border-blue-600 text-blue-600">
                  Đính kèm
                </Button>
                <Button variant="outline" className="border-blue-600 text-blue-600">
                  Tải lên tệp
                </Button>
              </div>
            </div>
          </form>
          <div className="flex flex-shrink-0 justify-end gap-2 border-t pt-4">
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" onClick={form.handleSubmit(handleSubmit)} disabled={isSubmitting}>
              {isSubmitting ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
