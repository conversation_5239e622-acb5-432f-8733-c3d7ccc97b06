'use client'

import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { organizationService } from '@/services/organizationService'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { PERMISSIONS } from '@workspace/ui/constants/base'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { TableState } from '@workspace/ui/lib/tableStateUtils'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { ViewButtonIcon } from '@workspace/ui/mi/view-button-icon'
import { useUserStore } from '@workspace/ui/stores/user.store'
import { Building2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { OrganizationHierarchyModal } from './organization-hierarchy-modal'

interface OrganizationListProps {
  type: number
  title: string
  breadcrumbLabel: string
  breadcrumbHref: string
  showAddButton?: boolean
  FormComponent: React.ComponentType<{
    initialData?: Organization | null
    onSuccess: () => void
    onCancel: () => void
    organizationType?: number
    onSubmitStart?: () => void
    onSubmitEnd?: () => void
  }>
  columns?: ColumnDef<Organization>[]
  customColumns?: {
    before?: ColumnDef<Organization>[]
    after?: ColumnDef<Organization>[]
    replace?: ColumnDef<Organization>[]
  }
  actions?: {
    showHierarchy?: boolean
    showDetail?: boolean
    showEdit?: boolean
    showDelete?: boolean
  }
}

export function OrganizationList({
  type,
  title,
  breadcrumbLabel,
  breadcrumbHref,
  showAddButton = true,
  FormComponent,
  columns: customColumns,
  customColumns: columnConfig,
  actions = {
    showHierarchy: true,
    showDetail: false,
    showEdit: true,
    showDelete: true,
  },
}: OrganizationListProps) {
  const t = useTranslations()
  const router = useRouter()
  const { hasPermissions } = useUserStore()

  // State for dialog management
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isHierarchyModalOpen, setIsHierarchyModalOpen] = useState(false)
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null)
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)

  // Function to delete an organization
  const handleDeleteOrganization = async () => {
    if (!selectedOrganization) return

    try {
      const organizationId = selectedOrganization.categoryId || selectedOrganization.id
      await organizationService.deleteOrganizationUnit(organizationId)

      toast.success(`Đã xóa ${selectedOrganization.name} thành công`)

      // Reload table data
      if (table.options.meta?.reload) {
        table.options.meta.reload()
      }
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error('Error deleting organization:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  // Default columns
  const defaultColumns: ColumnDef<Organization>[] = [
    selectColumn(t),
    {
      accessorKey: 'name',
      meta: {
        title: 'Tên cơ quan',
      },
      cell: ({ row }) => {
        const organization = row.original
        const organizationId = organization.categoryId || organization.id

        return (
          <RenderInteractiveLink
            onClick={() => {
              router.push(`${breadcrumbHref}/${organizationId}`)
            }}
          >
            <div className="flex flex-col">
              <span>{row.original.name}</span>
              <span className="text-muted-foreground mt-1 text-xs">Tên đầy đủ: {row.original.fullName}</span>
            </div>
          </RenderInteractiveLink>
        )
      },
    },
    {
      id: 'contactInfo',
      meta: {
        title: 'Thông tin liên hệ',
      },
      cell: ({ row }) => {
        const { address, phone, email, representative } = row.original

        return (
          <div className="space-y-1 text-sm">
            <div>
              <span className="text-muted-foreground text-xs font-medium">Người đại diện: </span>
              <span>{representative || 'Chưa có thông tin'}</span>
            </div>
            <div>
              <span className="text-muted-foreground text-xs font-medium">Địa chỉ: </span>
              <span>{address || 'Chưa có thông tin'}</span>
            </div>
            <div>
              <span className="text-muted-foreground text-xs font-medium">Điện thoại: </span>
              <span>{phone || 'Chưa có thông tin'}</span>
            </div>
            <div>
              <span className="text-muted-foreground text-xs font-medium">Email: </span>
              <span>{email || 'Chưa có thông tin'}</span>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'status',
      size: TABLE_SIZE.STATUS,
      meta: {
        title: 'Trạng thái',
        filter: {
          type: 'select',
          placeholder: 'Chọn trạng thái',
          options: STATUS_SO_TU_PHAP_OPTIONS.map(status => ({
            label: status.label,
            value: status.value.toString(),
          })),
        },
      },
      cell: ({ row }) => {
        const status = row.original.status
        const statusOption = STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === status)

        return (
          <Badge variant={status === 1 ? 'default' : status === 2 ? 'secondary' : 'destructive'}>
            {statusOption?.label || 'Không xác định'}
          </Badge>
        )
      },
    },
    {
      size: 120,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const organization = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            {actions.showHierarchy && (
              <ViewButtonIcon
                onClick={() => {
                  setSelectedOrganization(organization)
                  setIsHierarchyModalOpen(true)
                }}
                tooltip="Xem cấu trúc tổ chức"
              />
            )}
            {actions.showEdit && hasPermissions([PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE]) && (
              <EditButtonIcon
                permission={PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE}
                onClick={() => {
                  setSelectedOrganization(organization)
                  setIsEditDialogOpen(true)
                }}
              />
            )}
            {actions.showDelete && hasPermissions([PERMISSIONS.PM02_ORGANIZATION_UNIT_DELETE]) && (
              <DeleteButtonIcon
                permission={PERMISSIONS.PM02_ORGANIZATION_UNIT_DELETE}
                onClick={() => {
                  setSelectedOrganization(organization)
                  setIsDeleteDialogOpen(true)
                }}
              />
            )}
          </div>
        )
      },
    },
  ]

  const columns = (() => {
    if (customColumns) {
      return customColumns
    }

    if (columnConfig) {
      const selectCol = defaultColumns[0]
      const actionCol = defaultColumns[defaultColumns.length - 1]

      if (columnConfig.replace) {
        return [selectCol, ...columnConfig.replace, actionCol]
      }

      const beforeColumns = columnConfig.before || []
      const afterColumns = columnConfig.after || []
      const middleColumns = defaultColumns.slice(1, -1)

      return [selectCol, ...beforeColumns, ...middleColumns, ...afterColumns, actionCol]
    }

    return defaultColumns
  })()

  const { table } = useEnhancedTable<Organization>({
    columns: columns as ColumnDef<Organization>[],
    pageName: `organization-type-${type}`,
    keyObject: { type },
    queryFn: async (state: TableState) => {
      const safeColumns = columns.filter(Boolean) as ColumnDef<Organization>[]
      const params = generateQueryParams(state, {
        columns: safeColumns,
        baseParams: { type },
      })

      return (await organizationService.getOrganizationType(type, params)) as {
        items: Organization[]
        totalPages: number
        totalItems: number
      }
    },
    initialState: {
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['organizations', type.toString()],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: 'Quản lý tổ chức',
            href: '/organization-units',
          },
          {
            label: breadcrumbLabel,
            href: breadcrumbHref,
          },
        ]}
      >
        <AdminPageContent
          title={title}
          icon={<Building2 className="h-5 w-5" />}
          actions={[
            <AddButton key="create" onClick={() => setIsCreateDialogOpen(true)} tooltip={t('action.add.label')} />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create Dialog */}
      {showAddButton && (
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="flex h-auto max-h-[90vh] flex-col gap-0 p-0 sm:max-w-[800px]">
            <div className="flex-shrink-0 border-b border-gray-200 px-6 py-4">
              <DialogHeader>
                <DialogTitle>Thêm mới {breadcrumbLabel}</DialogTitle>
                <DialogDescription />
              </DialogHeader>
            </div>

            <div className="min-h-0 flex-1 overflow-y-auto">
              <FormComponent
                organizationType={type}
                onSuccess={() => {
                  setIsCreateDialogOpen(false)
                  setIsFormSubmitting(false)

                  // Reload table data
                  if (table.options.meta?.reload) {
                    table.options.meta.reload()
                  }
                }}
                onCancel={() => setIsCreateDialogOpen(false)}
                onSubmitStart={() => setIsFormSubmitting(true)}
                onSubmitEnd={() => setIsFormSubmitting(false)}
              />
            </div>

            <DialogFooter className="flex-shrink-0 border-t border-gray-200 px-6 py-4">
              <div className="flex items-center justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  disabled={isFormSubmitting}
                >
                  Hủy
                </Button>
                <Button type="submit" form="organization-form" disabled={isFormSubmitting}>
                  {isFormSubmitting ? 'Đang lưu...' : 'Lưu'}
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="flex h-auto max-h-[90vh] flex-col gap-0 p-0 sm:max-w-[800px]">
          <div className="flex-shrink-0 border-b border-gray-200 px-6 py-4">
            <DialogHeader>
              <DialogTitle>Chỉnh sửa {breadcrumbLabel}</DialogTitle>
              <DialogDescription />
            </DialogHeader>
          </div>

          <div className="min-h-0 flex-1 overflow-y-auto">
            <FormComponent
              initialData={selectedOrganization}
              organizationType={type}
              onSuccess={() => {
                setIsEditDialogOpen(false)
                setIsFormSubmitting(false)

                // Reload table data
                if (table.options.meta?.reload) {
                  table.options.meta.reload()
                }
              }}
              onCancel={() => setIsEditDialogOpen(false)}
              onSubmitStart={() => setIsFormSubmitting(true)}
              onSubmitEnd={() => setIsFormSubmitting(false)}
            />
          </div>

          <DialogFooter className="flex-shrink-0 border-t border-gray-200 px-6 py-4">
            <div className="flex items-center justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isFormSubmitting}
              >
                Hủy
              </Button>
              <Button type="submit" form="organization-form" disabled={isFormSubmitting}>
                {isFormSubmitting ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Hierarchy Modal */}
      <OrganizationHierarchyModal
        open={isHierarchyModalOpen}
        onOpenChange={setIsHierarchyModalOpen}
        organizationId={selectedOrganization?.id || null}
        organizationName={selectedOrganization?.name}
      />

      {/* Delete Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa &quot;{selectedOrganization?.name}&quot;? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOrganization}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
