'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Organization } from '@/constants/so-tu-phap'
import { STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { AnyRecord } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@workspace/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { Combobox } from '@workspace/ui/mi/combobox'
import { Required } from '@workspace/ui/mi/required'
import { Save, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  code: z.string().min(1, 'Mã phòng ban là bắt buộc'),
  name: z.string().min(1, 'Tên phòng ban là bắt buộc'),
  fullName: z.string().optional(),
  representative: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  website: z.string().url('Website không hợp lệ').optional().or(z.literal('')),
  oldProvinceId: z.string().optional(),
  cityId: z.number().optional(),
  oldDistrictId: z.string().optional(),
  communeId: z.number().optional(),
  parentId: z.string().optional(),
  status: z.number(),
  establishmentDate: z.string().optional(),
  decisionDate: z.string().optional(),
  decisionNumber: z.string().optional(),
  legalField: z.string().optional(),
  legalFieldId: z.string().optional(),
  legalForm: z.string().optional(),
  legalFormId: z.string().optional(),
  rangeActivities: z.string().optional(),
  rangeActivitiesId: z.string().optional(),
  payrollNumber: z.number().optional(),
  roomNumber: z.number().optional(),
  clubNumber: z.number().optional(),
  adminGroup: z.number().optional(),
  adminStatusId: z.number().optional(),
  experience: z.string().optional(),
  deadlineContractDate: z.string().optional(),
  note: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface PhongBanFormProps {
  initialData?: Organization | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
  fixedParent?: { id: string; name: string; code: string } | null
}

export function PhongBanForm({
  initialData,
  onSuccess,
  onCancel,
  organizationType = ORGANIZATION_TYPE.PHONG_BAN,
  fixedParent = null,
}: PhongBanFormProps) {
  const queryClient = useQueryClient()
  const [parentOptions, setParentOptions] = useState<Array<{ id: string; code: string; name: string }>>([])
  const [isLoadingParents, setIsLoadingParents] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: initialData?.code || '',
      name: initialData?.name || '',
      fullName: initialData?.fullName || '',
      representative: initialData?.representative || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      fax: initialData?.fax || '',
      email: initialData?.email || '',
      website: initialData?.website || '',
      oldProvinceId: initialData?.oldProvinceId || '',
      oldDistrictId: initialData?.oldDistrictId || '',
      communeId: initialData?.communeId || undefined,
      parentId: initialData?.parentId || fixedParent?.id || '',
      status: initialData?.status ?? 1,
      establishmentDate: initialData?.establishmentDate || '',
      decisionDate: initialData?.decisionDate || '',
      decisionNumber: initialData?.decisionNumber || '',
      legalFieldId: initialData?.legalFieldId || '',
      legalFormId: initialData?.legalFormId || '',
      rangeActivitiesId: initialData?.rangeActivitiesId || '',
      payrollNumber: initialData?.payrollNumber || undefined,
      roomNumber: initialData?.roomNumber || undefined,
      clubNumber: initialData?.clubNumber || undefined,
      adminGroup: Number(initialData?.adminGroup?.categoryId) || undefined,
      adminStatusId: initialData?.adminStatusId || undefined,
      experience: initialData?.experience || '',
      deadlineContractDate: initialData?.deadlineContractDate || '',
      note: initialData?.note || '',
    },
  })

  useEffect(() => {
    const loadParentOptions = async () => {
      // Nếu có fixedParent, không cần fetch options
      if (fixedParent) {
        setParentOptions([fixedParent])

        return
      }

      try {
        setIsLoadingParents(true)
        const response = await fetch('/ac-apis/organization-units/type/3')

        if (response.ok) {
          const data = await response.json()
          setParentOptions(data.data || data.items || [])
        }
      } catch (error) {
        console.error('Error loading parent options:', error)
      } finally {
        setIsLoadingParents(false)
      }
    }

    loadParentOptions()
  }, [fixedParent])

  const onSubmit = async (values: FormValues) => {
    try {
      const cleanData = (obj: AnyRecord) => {
        const cleaned: AnyRecord = {}

        Object.entries(obj).forEach(([key, value]) => {
          if (value !== '' && value !== null && value !== undefined) {
            cleaned[key] = value
          }
        })

        return cleaned
      }

      const submitData = cleanData({
        ...values,
        type: organizationType,
      })

      const url = initialData ? `/ac-apis/organization-units/${initialData.id}` : '/ac-apis/organization-units'

      const method = initialData ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      toast.success(initialData ? 'Cập nhật phòng ban thành công' : 'Tạo phòng ban thành công')

      // Invalidate queries
      queryClient.invalidateQueries({
        queryKey: ['organizations', organizationType.toString()],
      })

      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(initialData ? 'Có lỗi xảy ra khi cập nhật phòng ban' : 'Có lỗi xảy ra khi tạo phòng ban')
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex h-full flex-col">
          <div className="flex-1 overflow-y-auto px-6 py-4">
            <div className="space-y-6">
              {/* Thông tin cơ bản */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Thông tin cơ bản</h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mã phòng ban <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập mã phòng ban" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tên phòng ban <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên phòng ban" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tên đầy đủ</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên đầy đủ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="representative"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Người đại diện</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên người đại diện" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Trạng thái <Required />
                        </FormLabel>
                        <Select onValueChange={value => field.onChange(Number(value))} value={field.value?.toString()}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Chọn trạng thái" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                              <SelectItem key={option.value} value={option.value.toString()}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="parentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Đơn vị quản lý {!fixedParent && <Required />}</FormLabel>
                      <FormControl>
                        {fixedParent ? (
                          <Input value={`${fixedParent.code} - ${fixedParent.name}`} disabled className="bg-muted" />
                        ) : (
                          <Combobox
                            value={field.value}
                            onChange={field.onChange}
                            placeholder={isLoadingParents ? 'Đang tải...' : 'Tìm kiếm và chọn đơn vị quản lý...'}
                            options={parentOptions.map(option => ({
                              value: option.id,
                              label: `${option.code} - ${option.name}`,
                            }))}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Thông tin liên hệ */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Thông tin liên hệ</h3>

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Địa chỉ</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Nhập địa chỉ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số điện thoại" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="fax"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fax</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số fax" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Nhập email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập website" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Thông tin tổ chức */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Thông tin tổ chức</h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="payrollNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số biên chế</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Nhập số biên chế"
                            {...field}
                            onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="roomNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số phòng</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Nhập số phòng"
                            {...field}
                            onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="clubNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số câu lạc bộ</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Nhập số câu lạc bộ"
                            {...field}
                            onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Ghi chú */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Ghi chú</h3>

                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ghi chú</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Nhập ghi chú" rows={4} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-background shrink-0 border-t px-6 py-4">
            <div className="flex items-center justify-end gap-2">
              <Button type="button" variant="outline" onClick={onCancel} className="min-w-[100px]">
                <X className="mr-2 h-4 w-4" />
                Hủy
              </Button>
              <Button type="submit" className="min-w-[100px]" disabled={form.formState.isSubmitting}>
                <Save className="mr-2 h-4 w-4" />
                {form.formState.isSubmitting ? 'Đang lưu...' : initialData ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
