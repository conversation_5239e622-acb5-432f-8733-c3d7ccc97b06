'use client'

import { FilePreview } from '@/components/common/file-preview'
import { UploadDocument } from '@/components/common/multi-file-upload-dialog'
import { UploadedFileSummary } from '@/components/staff/upload/use-staff-upload'
import { SearchComboBox } from '@/components/ui/search-combobox'
import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { flattenAndCleanArray, normalizeToArray } from '@/lib/array-utils'
import { Any, Category } from '@/lib/types'
import { formatDate } from '@/lib/utils'
import { categoryService } from '@/services/categoryService'
import { apiService } from '@/services/common/api-service'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { RadioGroup, RadioGroupItem } from '@workspace/ui/components/radio-group'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { Combobox } from '@workspace/ui/mi/combobox'
import { Required } from '@workspace/ui/mi/required'
import { CalendarIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

// Extend Organization type to include attachments (for type safety)
interface OrganizationWithAttachments extends Organization {
  attachments?: Array<{
    url?: string
    fileKey?: string
    fileId?: string
    bucket?: string
    type?: string
    [key: string]: any
  }>
}

const formSchema = z.object({
  // Thông tin tổ chức - Section 1
  name: z.string().min(1),
  fullName: z.string().min(1),
  code: z.string().min(1),
  establishmentDate: z.date(),
  organizationGroupId: z.string().min(1),
  legalFieldId: z.string().min(1),
  legalFormId: z.array(z.string()).min(1),
  status: z.string().min(1),
  rangeActivitiesId: z.string().optional(),
  experience: z.string().optional(),
  registrationNumber: z.string().min(1),
  issueDate: z.date(),
  issuingAgency: z.string().min(1),
  participationReason: z.string().min(1),
  registrationTerminationReason: z.string().min(1),

  // Thông tin liên hệ - Section 2
  oldProvinceId: z.string().min(1),
  oldDistrictId: z.string().min(1),
  oldWardId: z.string().min(1),
  address: z.string().min(1),
  phone: z.string().optional(),
  email: z.email().optional().or(z.literal('')),
  website: z.url().optional().or(z.literal('')),
  representative: z.string().optional(),
  // Ghi chú
  note: z.string().optional(),

  // Đính kèm
  attachments: z
    .array(
      z.object({
        name: z.string(),
        url: z.string(),
      })
    )
    .optional(),

  // Thông tin đăng ký tham gia - Section 3
  registrationFile: z.any().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ToChucDangKyTGPLFormProps {
  initialData?: OrganizationWithAttachments | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
  onSubmitStart?: () => void
  onSubmitEnd?: () => void
}

interface CategoryOption {
  value: string
  label: string
}

export function ToChucDangKyTGPLForm({
  initialData,
  onSuccess,
  organizationType = ORGANIZATION_TYPE.TO_CHUC_DANG_KY_THAM_GIA_TGPL,
  onSubmitStart,
  onSubmitEnd,
}: ToChucDangKyTGPLFormProps) {
  const queryClient = useQueryClient()
  const [provinces, setProvinces] = useState<CategoryOption[]>([])
  const [districts, setDistricts] = useState<CategoryOption[]>([])
  const [wards, setWards] = useState<CategoryOption[]>([])
  const [loadingProvinces, setLoadingProvinces] = useState(false)
  const [loadingDistricts, setLoadingDistricts] = useState(false)
  const [loadingWards, setLoadingWards] = useState(false)
  const [organizationGroups, setOrganizationGroups] = useState<CategoryOption[]>([])
  const [legalFields, setLegalFields] = useState<CategoryOption[]>([])
  const [legalForms, setLegalForms] = useState<CategoryOption[]>([])
  const [rangeActivitiesOptions, setRangeActivitiesOptions] = useState<CategoryOption[]>([])
  const [loadingOrganizationGroups, setLoadingOrganizationGroups] = useState(false)
  const [loadingLegalFields, setLoadingLegalFields] = useState(false)
  const [loadingLegalForms, setLoadingLegalForms] = useState(false)

  const [contractFile, setContractFile] = useState<UploadedFileSummary | null>(null)
  const [attachedFiles, setAttachedFiles] = useState<UploadedFileSummary[]>([])

  useEffect(() => {
    if (initialData?.contractFileId || initialData?.contractFileRef) {
      setContractFile({
        fileId: initialData.contractFileId || '',
        url: initialData.contractFileRef || '',
        fileKey: initialData.contractFileRef || '',
      })
    }

    if (Array.isArray(initialData?.attachments)) {
      setAttachedFiles(
        (initialData.attachments as any[]).map(f => ({
          url: f.url || f.fileKey || '',
          fileKey: f.fileKey || '',
          fileId: f.fileId || '',
          bucket: f.bucket,
          type: f.type,
        }))
      )
    }
  }, [initialData])

  useEffect(() => {
    const files = attachedFiles.map(f => ({
      name: f.fileKey || f.url || f.fileId || '',
      url: f.url || f.fileKey || '',
    }))
    form.setValue('attachments', files)
  }, [attachedFiles])

  useEffect(() => {
    if (contractFile) {
      form.setValue('registrationFile', contractFile)
    } else {
      form.setValue('registrationFile', null)
    }
  }, [contractFile])

  const uploadOverrides = { pathPrefix: `organization-units/registration` }

  const contractFileId = contractFile?.fileId || contractFile?.fileKey || contractFile?.url || ''
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || '',
      fullName: initialData?.fullName || '',
      code: initialData?.code || '',
      establishmentDate: initialData?.establishmentDate ? new Date(initialData.establishmentDate) : undefined,
      organizationGroupId: initialData?.organizationGroupId || '',
      legalFieldId: initialData?.legalFieldId || '',
      legalFormId: normalizeToArray(initialData?.legalFormId),
      status: initialData?.status?.toString() || '',
      rangeActivitiesId: initialData?.rangeActivitiesId || '',
      experience: initialData?.experience || '',
      oldProvinceId: initialData?.oldProvinceId || '',
      oldDistrictId: initialData?.oldDistrictId || '',
      oldWardId: initialData?.oldWardId || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      website: initialData?.website || '',
      note: initialData?.note || '',
      attachments: [],
      registrationFile: null,
      registrationNumber: initialData?.registrationNumber || '',
      issueDate: initialData?.issueDate ? new Date(initialData.issueDate) : undefined,
      issuingAgency: initialData?.issuingAgency || '',
      participationReason: initialData?.participationReason || '',
      registrationTerminationReason: initialData?.registrationTerminationReason || '',
      representative: initialData?.representative || '',
    },
  })

  // Fetch provinces when form loads
  useEffect(() => {
    fetchProvinces()
    fetchOrganizationGroups()
    fetchLegalFields()
    fetchLegalForms()
    // Set static options
    setRangeActivitiesOptions([
      { value: 'toan-quoc', label: 'Toàn quốc' },
      { value: 'khu-vuc', label: 'Khu vực' },
      { value: 'dia-phuong', label: 'Địa phương' },
    ])
  }, [])

  // Fetch districts and wards when form loads with initialData
  useEffect(() => {
    const loadInitialData = async () => {
      if (initialData?.oldProvinceId) {
        await fetchDistricts(initialData.oldProvinceId)
      }

      if (initialData?.oldDistrictId) {
        await fetchWards(initialData.oldDistrictId)
      }
    }
    loadInitialData()
  }, [initialData?.oldProvinceId, initialData?.oldDistrictId])

  // Watch oldProvinceId changes để load districts tương ứng
  const selectedProvinceId = form.watch('oldProvinceId')
  useEffect(() => {
    if (selectedProvinceId) {
      fetchDistricts(selectedProvinceId)
      // Chỉ reset nếu không phải initialData
      const isInitialLoad = initialData?.oldProvinceId === selectedProvinceId

      if (!isInitialLoad) {
        form.setValue('oldDistrictId', '')
        form.setValue('oldWardId', '')
        setWards([])
      }
    } else {
      setDistricts([])
      setWards([])
      form.setValue('oldDistrictId', '')
      form.setValue('oldWardId', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProvinceId])

  // Watch oldDistrictId changes để load wards tương ứng
  const selectedDistrictId = form.watch('oldDistrictId')
  useEffect(() => {
    if (selectedDistrictId) {
      fetchWards(selectedDistrictId)
      // Chỉ reset oldWardId nếu không phải initialData
      const isInitialLoad = initialData?.oldDistrictId === selectedDistrictId

      if (!isInitialLoad) {
        form.setValue('oldWardId', '')
      }
    } else {
      setWards([])
      form.setValue('oldWardId', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDistrictId])

  const fetchProvinces = async () => {
    setLoadingProvinces(true)

    try {
      const result = (await categoryService.getCategories('TP')) as Category[]
      const provinceOptions =
        result
          ?.map(item => {
            return {
              value: String(item.categoryId || ''),
              label: item.name || '',
            }
          })
          .filter(option => option.value && option.label) || []
      setProvinces(provinceOptions)
    } catch (error) {
      console.error('Error fetching provinces:', error)
      toast.error('Không thể tải danh sách tỉnh/thành phố')
    } finally {
      setLoadingProvinces(false)
    }
  }

  const fetchDistricts = async (oldProvinceId?: string) => {
    setLoadingDistricts(true)

    try {
      const filter = oldProvinceId ? { type: 'QH', parentId: oldProvinceId } : { type: 'QH' }
      const result = (await apiService.get('/ac-apis/categories/method/all', {
        params: { filter: JSON.stringify(filter) },
      })) as (Category & { id: string })[]
      const districtOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || item.id || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setDistricts(districtOptions)
    } catch (error) {
      console.error('Error fetching districts:', error)
      toast.error('Không thể tải danh sách quận/huyện')
    } finally {
      setLoadingDistricts(false)
    }
  }

  const fetchWards = async (oldDistrictId?: string) => {
    setLoadingWards(true)

    try {
      const filter = oldDistrictId ? { type: 'PX', parentId: oldDistrictId } : { type: 'PX' }
      const result = (await apiService.get('/ac-apis/categories/method/all', {
        params: { filter: JSON.stringify(filter) },
      })) as (Category & { id: string })[]
      const wardOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || item.id || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setWards(wardOptions)
    } catch (error) {
      console.error('Error fetching wards:', error)
      toast.error('Không thể tải danh sách phường/xã')
    } finally {
      setLoadingWards(false)
    }
  }

  const fetchOrganizationGroups = async () => {
    setLoadingOrganizationGroups(true)

    try {
      const result = (await categoryService.getCategories('NTC')) as (Category & { id: string })[]
      const options =
        result
          ?.map(item => ({
            value: item.categoryId || item.id || item.code || '',
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setOrganizationGroups(options)
    } catch (error) {
      console.error('Error fetching organization groups:', error)
      setOrganizationGroups([])
    } finally {
      setLoadingOrganizationGroups(false)
    }
  }

  const fetchLegalFields = async () => {
    setLoadingLegalFields(true)

    try {
      const result = (await categoryService.getCategories('LV')) as (Category & { id: string })[]
      const options =
        result
          ?.map(item => ({
            value: item.categoryId || item.id || item.code || '',
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setLegalFields(options)
    } catch (error) {
      console.error('Error fetching legal fields:', error)
      setLegalFields([])
    } finally {
      setLoadingLegalFields(false)
    }
  }

  const fetchLegalForms = async () => {
    setLoadingLegalForms(true)

    try {
      const result = (await categoryService.getCategories('TG')) as (Category & { id: string })[]
      const options =
        result
          ?.map(item => ({
            value: item.categoryId || item.id || item.code || '',
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setLegalForms(options)
    } catch (error) {
      console.error('Error fetching legal forms:', error)
      setLegalForms([])
    } finally {
      setLoadingLegalForms(false)
    }
  }

  const onSubmit = async (values: FormValues) => {
    onSubmitStart?.()

    try {
      const cleanedValues = Object.entries(values).reduce((acc, [key, value]) => {
        if (value !== '') {
          if (Array.isArray(value) && value.length === 0) {
            return acc
          }
          acc[key] = value
        }

        return acc
      }, {} as Any)

      const submitData = {
        ...cleanedValues,
        legalFormId: Array.isArray(values.legalFormId) ? flattenAndCleanArray(values.legalFormId) : [],
        type: organizationType,
        status: Number(values.status),
        oldProvinceId: values.oldProvinceId || undefined,
        oldDistrictId: values.oldDistrictId || undefined,
        oldWardId: values.oldWardId || undefined,
        establishmentDate: values.establishmentDate ? values.establishmentDate.toISOString() : undefined,
        issueDate: values.issueDate ? values.issueDate.toISOString() : undefined,
        // attachments: lấy đủ thông tin file
        attachments: attachedFiles.map(f => ({
          fileId: f.fileId || '',
          fileKey: f.fileKey || '',
          url: f.url || '',
          name: f.fileKey || f.url || f.fileId || '',
        })),
      }

      // Đồng bộ file hợp đồng TGPL từ state contractFile nếu có
      if (contractFile?.fileId) {
        submitData.contractFileId = contractFile.fileId
      }

      if (contractFile?.url) {
        submitData.contractFileRef = contractFile.url
      }

      Object.keys(submitData).forEach(key => {
        if (submitData[key] === undefined) {
          delete submitData[key]
        }
      })

      const url = initialData ? `/ac-apis/organization-units/${initialData.id}` : '/ac-apis/organization-units'
      const method = initialData ? 'PATCH' : 'POST'
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      toast.success(initialData ? 'Cập nhật tổ chức đăng ký TGPL thành công' : 'Tạo tổ chức đăng ký TGPL thành công')
      queryClient.invalidateQueries({
        queryKey: ['organizations', organizationType.toString()],
      })
      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(
        initialData ? 'Có lỗi xảy ra khi cập nhật tổ chức đăng ký TGPL' : 'Có lỗi xảy ra khi tạo tổ chức đăng ký TGPL'
      )
    } finally {
      onSubmitEnd?.()
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Form {...form}>
        <div className="flex-1 overflow-y-auto">
          <form id="organization-form" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-0">
              {/* Thông tin tổ chức */}
              <div className="border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium text-blue-600">Thông tin tổ chức</h3>
              </div>
              <div className="space-y-4 px-6 py-4">
                <h4 className="text-sm font-medium">Thông tin cơ bản</h4>

                {/* Row 1 */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tên tổ chức <Required />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên tổ chức" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tên đầy đủ <Required />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập tên đầy đủ của tổ chức" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Row 2 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mã tổ chức <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập mã" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="establishmentDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày thành lập <Required />
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full justify-start text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="representative"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Người đứng đầu</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập họ tên" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 3 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="organizationGroupId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nhóm tổ chức <Required />
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            options={organizationGroups}
                            value={field.value}
                            onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder={loadingOrganizationGroups ? 'Đang tải...' : 'Chọn'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="legalFieldId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lĩnh vực trợ giúp <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={legalFields}
                            value={field.value}
                            onValueChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder={loadingLegalFields ? 'Đang tải...' : 'Chọn'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="legalFormId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Hình thức trợ giúp <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            multiple={true}
                            options={legalForms}
                            value={field.value}
                            onValueChange={field.onChange}
                            searchPlaceholder="Tìm kiếm hình thức..."
                            emptyMessage="Không tìm thấy hình thức."
                            disabled={loadingLegalForms}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 4 */}
                <FormField
                  control={form.control}
                  name="rangeActivitiesId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phạm vi hoạt động</FormLabel>
                      <FormControl>
                        <Combobox
                          options={rangeActivitiesOptions}
                          value={field.value || ''}
                          onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                          placeholder="Chọn"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Row 5 */}
                <FormField
                  control={form.control}
                  name="experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kinh nghiệm năng lực</FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Thông tin liên hệ */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Thông tin liên hệ</h3>
              </div>
              <div className="space-y-4 px-6 py-4">
                {/* Row 1 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="oldProvinceId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tỉnh / Thành phố <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={provinces}
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder={loadingProvinces ? 'Đang tải...' : 'Chọn tỉnh/thành phố'}
                            searchPlaceholder="Tìm kiếm tỉnh/thành phố..."
                            emptyMessage="Không tìm thấy tỉnh/thành phố."
                            disabled={loadingProvinces}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="oldDistrictId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Quận / Huyện <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={selectedProvinceId ? districts : []}
                            value={field.value}
                            onValueChange={value => {
                              if (selectedProvinceId) {
                                field.onChange(value)
                              }
                            }}
                            placeholder={
                              loadingDistricts
                                ? 'Đang tải...'
                                : !selectedProvinceId
                                  ? 'Vui lòng chọn tỉnh/thành phố trước'
                                  : districts.length === 0
                                    ? 'Không có dữ liệu'
                                    : 'Chọn quận/huyện'
                            }
                            searchPlaceholder="Tìm kiếm quận/huyện..."
                            emptyMessage="Không tìm thấy quận/huyện."
                            disabled={loadingDistricts || !selectedProvinceId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="oldWardId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Phường / Xã <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={selectedDistrictId ? wards : []}
                            value={field.value}
                            onValueChange={value => {
                              if (selectedDistrictId) {
                                field.onChange(value)
                              }
                            }}
                            placeholder={
                              loadingWards
                                ? 'Đang tải...'
                                : !selectedDistrictId
                                  ? 'Vui lòng chọn quận/huyện trước'
                                  : wards.length === 0
                                    ? 'Không có dữ liệu'
                                    : 'Chọn phường/xã'
                            }
                            searchPlaceholder="Tìm kiếm phường/xã..."
                            emptyMessage="Không tìm thấy phường/xã."
                            disabled={loadingWards || !selectedDistrictId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Địa chỉ <Required />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập địa chỉ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Row 2 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Nhập email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập địa chỉ website" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Trạng thái hoạt động <Required />
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onValueChange={field.onChange}
                            className="mt-2 flex flex-row gap-6"
                          >
                            {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                              <div key={option.value} className="flex items-center gap-3">
                                <RadioGroupItem value={option.value.toString()} id={option.value.toString()} />
                                <Label htmlFor={option.value.toString()}>{option.label}</Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Ghi chú */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Ghi chú</h3>
              </div>
              <div className="px-6 py-4">
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea placeholder="Nhập ghi chú" rows={4} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Đính kèm */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Đính kèm</h3>
              </div>
              <div className="px-6 py-4">
                <UploadDocument
                  config={{ defaultPathPrefix: 'organization-units/attachments' }}
                  title="Tải lên tệp đính kèm"
                  description="Chọn các tệp cần đính kèm cho tổ chức."
                  multiple={true}
                  onUploadedFiles={(files: UploadedFileSummary[]) => {
                    // Lọc trùng theo url hoặc fileKey
                    const uniqueFiles = files.filter(
                      (f: UploadedFileSummary, i: number, arr: UploadedFileSummary[]) =>
                        arr.findIndex((x: UploadedFileSummary) => (x.url || x.fileKey) === (f.url || f.fileKey)) === i
                    )
                    setAttachedFiles(uniqueFiles)
                  }}
                />
                <div className="mt-2 flex flex-col gap-2">
                  {attachedFiles.map((file, idx) => (
                    <div
                      key={file.fileId || file.url || idx}
                      className="flex items-center gap-2 rounded border px-2 py-1"
                    >
                      <div className="flex-1 truncate">
                        {file.fileId ? (
                          <FilePreview fileId={file.fileId} fileName={file.fileKey} />
                        ) : file.url ? (
                          <a href={file.url} target="_blank" rel="noreferrer" className="text-primary underline">
                            Xem file
                          </a>
                        ) : (
                          <span className="text-muted-foreground">{file.fileKey || file.url || file.fileId}</span>
                        )}
                      </div>
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        onClick={() => setAttachedFiles(files => files.filter((_, i) => i !== idx))}
                      >
                        <span className="sr-only">Xóa</span>X
                      </Button>
                    </div>
                  ))}
                </div>
                <p className="mt-2 text-sm text-gray-500 italic">(Có thể tải lên nhiều file đính kèm)</p>
              </div>

              {/* Thông tin đăng ký tham gia */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Thông tin đăng ký tham gia</h3>
              </div>
              <div className="space-y-4 px-6 py-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="registrationNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Số đăng ký <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số đăng ký" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="issueDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày hiệu lực <Required />
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full justify-start text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="issuingAgency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Đơn vị cấp <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập đơn vị cấp" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="participationReason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lý do cấp giấy đăng ký tham gia <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập lý do cấp giấy đăng ký tham gia" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="registrationTerminationReason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lý do chấm dứt đăng ký <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập lý do chấm dứt đăng ký" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="px-6 py-4">
                <label className="mb-2 block font-medium">Tệp hợp đồng ĐKTG</label>
                {/* Chỉ hiển thị nút upload nếu chưa có file */}
                {!contractFileId ? (
                  <UploadDocument
                    config={{ defaultPathPrefix: 'organization-units/registration' }}
                    title="Tải lên hợp đồng ĐKTG"
                    description="Chọn tệp hợp đồng cho tổ chức."
                    multiple={false}
                    onUploadedFiles={(files: UploadedFileSummary[]) => {
                      if (files.length > 0 && files[0]) {
                        setContractFile(files[0])
                      }
                    }}
                  />
                ) : null}
                {contractFileId ? (
                  <div className="border-muted flex flex-col gap-2 rounded-md border px-3 py-2 text-sm">
                    <div className="flex items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="font-semibold">
                          {contractFile?.fileKey || contractFile?.url || contractFile?.fileId}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          <FilePreview fileId={contractFileId} fileName={contractFile?.fileKey} />
                        </div>
                        {contractFile?.fileId ? (
                          <div className="text-muted-foreground text-xs">File ID: {contractFile?.fileId}</div>
                        ) : null}
                      </div>
                      <Button type="button" size="icon" variant="ghost" onClick={() => setContractFile(null)}>
                        <span className="sr-only">Xóa</span>X
                      </Button>
                    </div>
                  </div>
                ) : null}
                <p className="mt-2 text-sm text-gray-500 italic">(Loại file: .pdf, .docx, .jpg, .png)</p>
              </div>
            </div>
          </form>
        </div>
      </Form>
    </div>
  )
}
