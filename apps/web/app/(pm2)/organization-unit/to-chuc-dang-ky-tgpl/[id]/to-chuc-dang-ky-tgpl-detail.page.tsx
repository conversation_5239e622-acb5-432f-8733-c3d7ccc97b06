'use client'

import { FilePreview } from '@/components/common/file-preview'
import { Organization } from '@/constants/so-tu-phap'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { ArrowLeft, Download, Edit } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { ToChucDangKyTGPLForm } from '../to-chuc-dang-ky-tgpl-form'

interface ToChucDangKyTGPLDetailPageProps {
  id: string
}

export function ToChucDangKyTGPLDetailPage({ id }: ToChucDangKyTGPLDetailPageProps) {
  const router = useRouter()
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  useEffect(() => {
    fetchOrganizationDetail()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id])

  const fetchOrganizationDetail = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/ac-apis/organization-units/${id}`)

      if (!response.ok) {
        throw new Error('Không thể tải thông tin chi tiết')
      }
      const data = await response.json()
      setOrganization(data)
    } catch (error) {
      console.error('Error fetching organization detail:', error)
      toast.error('Không thể tải thông tin chi tiết')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    setIsEditDialogOpen(true)
  }

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false)
    fetchOrganizationDetail() // Reload data after successful edit
    toast.success('Cập nhật tổ chức thành công')
  }

  const handleBack = () => {
    router.push('/organization-unit/to-chuc-dang-ky-tgpl')
  }

  // const handleDownloadFile = (fileUrl: string, fileName: string) => {
  //   // Implement file download logic
  //   window.open(fileUrl, '_blank')
  // }

  // const getStatusBadge = (status: number) => {
  //   const statusOption = STATUS_SO_TU_PHAP_OPTIONS.find(opt => opt.value === status)

  //   return statusOption ? (
  //     <Badge variant={status === 1 ? 'default' : status === 2 ? 'secondary' : 'destructive'}>
  //       {statusOption.label}
  //     </Badge>
  //   ) : null
  // }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Chưa cập nhật'

    return new Date(dateString).toLocaleDateString('vi-VN')
  }

  if (loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức TGPL', href: '/organization-units' },
          { label: 'Tổ chức đăng ký tham gia', href: '/organization-unit/to-chuc-dang-ky-tgpl' },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
            <p className="text-muted-foreground mt-4">Đang tải thông tin...</p>
          </div>
        </div>
      </AdminPageLayout>
    )
  }

  if (!organization) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức TGPL', href: '/organization-units' },
          { label: 'Tổ chức đăng ký tham gia', href: '/organization-unit/to-chuc-dang-ky-tgpl' },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <p className="text-muted-foreground">Không tìm thấy thông tin tổ chức</p>
            <Button variant="outline" onClick={handleBack} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </div>
        </div>
      </AdminPageLayout>
    )
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Quản lý tổ chức TGPL', href: '/organization-units' },
        { label: 'Tổ chức đăng ký tham gia', href: '/organization-unit/to-chuc-dang-ky-tgpl' },
        { label: 'Chi tiết', href: '#' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with title and actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleBack} size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-semibold">Thông tin chi tiết</h1>
          </div>
          <Button onClick={handleEdit} variant="outline" size="sm">
            <Edit className="mr-2 h-4 w-4" />
            Cập nhật
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Left block - Organization info */}
          <div className="overflow-hidden rounded-lg border border-[#E5E5E5]">
            {/* Header with gradient background */}
            <div
              className="px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <h2 className="text-base font-medium">
                Tên tổ chức:{' '}
                <span className="font-semibold">
                  {organization.name || organization.fullName || 'Hành chính Tổng hợp (CN-Trà Vinh)'}
                </span>
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Địa chỉ:{' '}
                {organization.address || 'Số 2 đường Quang Trung, Phường Yết Kiêu, Quận Hà Đông, Thành phố Hà Nội'}
              </p>
            </div>

            {/* Content */}
            <div className="space-y-8 p-6">
              {/* Thông tin cơ bản */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Thông tin cơ bản</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Mã tổ chức</span>
                    <span className="font-medium">{organization.code || 'TCHASD'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Ngày thành lập</span>
                    <span className="font-medium">{formatDate(organization.establishmentDate) || '10/08/2020'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Nhóm tổ chức</span>
                    <span className="font-medium">
                      {organization?.organizationGroup?.name || 'Tổ chức hành nghề luật sư'}
                    </span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Lĩnh vực trợ giúp</span>
                    <span className="font-medium">{organization.legalField?.name || 'Trợ giúp pháp lý'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Hình thức trợ giúp</span>
                    <span className="font-medium">{organization.legalForm || 'Bào chữa'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Phạm vi hoạt động</span>
                    <span className="font-medium text-gray-400 italic">
                      {organization.rangeActivitiesId || 'Chưa cập nhật'}
                    </span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Kinh nghiệm năng lực</span>
                    <span className="font-medium">{organization.experience || '5 năm'}</span>
                  </div>
                </div>
              </div>

              {/* Thông tin liên hệ */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Thông tin liên hệ</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Số điện thoại</span>
                    <span className="font-medium">{organization.phone || 'Chưa cập nhật'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Email</span>
                    <span className="font-medium">{organization.email || 'Chưa cập nhật'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Website</span>
                    <span className="font-medium text-gray-400 italic">{organization.website || 'Chưa cập nhật'}</span>
                  </div>
                </div>
              </div>

              {/* Ghi chú */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Ghi chú</h3>
                <p className="text-gray-400 italic">{organization.note || 'Chưa cập nhật'}</p>
              </div>

              {/* Đính kèm */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Đính kèm</h3>
                <p className="text-gray-400 italic">
                  {organization.attachedFiles && organization.attachedFiles.length > 0
                    ? `${organization.attachedFiles.length} tệp đính kèm`
                    : 'Chưa cập nhật'}
                </p>
              </div>
            </div>

            {/* Footer with update date */}
            <div
              className="px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <p className="text-sm text-gray-600">
                Ngày cập nhật: {formatDate(organization.updatedAt) || '12/08/2025'}
              </p>
            </div>
          </div>

          {/* Right block - TGPL Registration info */}
          <div className="flex flex-col overflow-hidden rounded-lg border border-[#E5E5E5]">
            {/* Header with gradient background */}
            <div
              className="px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <h2 className="text-base font-medium">Thông tin đăng ký tham gia TGPL</h2>
            </div>

            {/* Content */}
            <div className="flex-1 p-6">
              {organization.contractFileId ? (
                <FilePreview fileId={organization.contractFileId} fileName="Hợp đồng ĐKTG" />
              ) : (
                <div className="flex h-full min-h-[400px] flex-col items-center justify-center">
                  <div className="flex h-full w-full flex-col items-center justify-center rounded-lg bg-gray-50">
                    <p className="mb-6 text-gray-400 italic">Chưa cập nhật</p>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Tải lên thông tin ĐKTG
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Footer border */}
            <div
              className="h-[52px]"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            />
          </div>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="flex max-h-[85vh] min-w-[520px] flex-col gap-0 p-0 sm:max-w-[700px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Chỉnh sửa tổ chức đăng ký tham gia TGPL</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <div className="overflow-y-auto">
            <ToChucDangKyTGPLForm
              initialData={organization}
              onSuccess={handleEditSuccess}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </AdminPageLayout>
  )
}
