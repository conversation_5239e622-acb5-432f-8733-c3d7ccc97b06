'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Any, Category } from '@/lib/types'
import { categoryService } from '@/services/categoryService'
import { useEffect, useState } from 'react'

import { getOrganizationGroupColumn } from '../organization-columns'
import { OrganizationList } from '../organization-list'
import { ToChucDangKyTGPLForm } from './to-chuc-dang-ky-tgpl-form'

function ToChucDangKyTGPLPage() {
  const [organizationGroups, setOrganizationGroups] = useState<{ label: string; value: string | number }[]>([])

  // Fetch organization groups từ API
  useEffect(() => {
    const fetchOrganizationGroups = async () => {
      try {
        const result = (await categoryService.getCategories('NTC')) as (Category & { id: string })[]
        const options =
          result
            ?.map(item => ({
              value: item.categoryId || item.id || item.code || '',
              label: item.name || '',
            }))
            .filter(option => option.value && option.label) || []

        setOrganizationGroups(options)
      } catch (error) {
        console.error('Error fetching organization groups:', error)
        setOrganizationGroups([])
      }
    }

    fetchOrganizationGroups()
  }, [])

  // Tạo columns với filter options từ API
  const customColumns = organizationGroups.length > 0 ? [getOrganizationGroupColumn(organizationGroups)] : []

  return (
    <OrganizationList
      type={ORGANIZATION_TYPE.TO_CHUC_DANG_KY_THAM_GIA_TGPL}
      title="Quản lý Tổ chức đăng ký tham gia TGPL"
      breadcrumbLabel="Tổ chức đăng ký tham gia TGPL"
      breadcrumbHref="/organization-unit/to-chuc-dang-ky-tgpl"
      FormComponent={ToChucDangKyTGPLForm as Any}
      customColumns={{
        after: customColumns as Any, // Thêm sau columns mặc định
      }}
    />
  )
}
export { ToChucDangKyTGPLPage }
