'use client'

import { Organization } from '@/constants/so-tu-phap'
import { Alert, AlertDescription } from '@workspace/ui/components/alert'
import { Button } from '@workspace/ui/components/button'
import { Card } from '@workspace/ui/components/card'
import { Skeleton } from '@workspace/ui/components/skeleton'
import { cn } from '@workspace/ui/lib/utils'
import {
  Background,
  Controls,
  Edge,
  Node,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
  useReactFlow,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { AlertCircle, Building2, Maximize2, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'

import { nodeTypes, OrganizationNodeData } from './organization-node'

interface OrganizationHierarchyViewProps {
  organizationId: string
  className?: string
  compact?: boolean
}

interface OrganizationHierarchyData extends Organization {
  parent?: Organization
  children?: Organization[]
}

function createTreeLayout(data: OrganizationHierarchyData): {
  nodes: Node<OrganizationNodeData>[]
  edges: Edge[]
} {
  const nodes: Node<OrganizationNodeData>[] = []
  const edges: Edge[] = []

  // const nodeWidth = 288
  const nodeHeight = 200
  const levelWidth = 450 // Khoảng cách giữa các level theo chiều ngang (tăng lên)

  // Function để tạo nodes và edges đệ quy
  function createNodes(
    org: Organization,
    level: number,
    variant: 'parent' | 'current' | 'child' | 'default',
    parentY?: number,
    siblingIndex: number = 0,
    totalSiblings: number = 1
  ): void {
    // Tính vị trí x và y với multi-column layout
    let x = level * levelWidth
    let y = 0

    if (level === 0) {
      // Root node ở giữa
      y = 0
    } else if (parentY !== undefined) {
      // Multi-column layout cho nhiều siblings
      const maxNodesPerColumn = 5 // Tối đa 5 nodes mỗi cột (giảm để thoáng hơn)
      const columnSpacing = 380 // Khoảng cách giữa các cột (tăng lên)
      const nodeSpacing = 220 // Khoảng cách giữa nodes trong cùng cột (tăng lên)

      if (totalSiblings > maxNodesPerColumn) {
        // Chia thành nhiều cột
        // const columnsNeeded = Math.ceil(totalSiblings / maxNodesPerColumn)
        const currentColumn = Math.floor(siblingIndex / maxNodesPerColumn)
        const indexInColumn = siblingIndex % maxNodesPerColumn
        const nodesInCurrentColumn = Math.min(maxNodesPerColumn, totalSiblings - currentColumn * maxNodesPerColumn)

        // Điều chỉnh x position cho multi-column
        x = level * levelWidth + currentColumn * columnSpacing

        // Tính y position trong cột
        const columnHeight = (nodesInCurrentColumn - 1) * nodeSpacing
        const startY = parentY - columnHeight / 2
        y = startY + indexInColumn * nodeSpacing
      } else {
        // Single column layout cho ít siblings
        const spacing = Math.max(200, nodeHeight * 1.3) // Tăng spacing cho single column
        const totalHeight = (totalSiblings - 1) * spacing
        const startY = parentY - totalHeight / 2
        y = startY + siblingIndex * spacing
      }
    }

    // Tạo node
    const nodeId = org.id
    nodes.push({
      id: nodeId,
      type: 'organizationNode',
      position: { x, y },
      data: {
        organization: org,
        variant,
        level,
      },
    })

    // Tạo edges và children nodes
    if (org.children && org.children.length > 0) {
      org.children.forEach((child, index) => {
        // Tạo edge với handles phù hợp cho multi-column
        edges.push({
          id: `${org.id}-${child.id}`,
          source: org.id,
          target: child.id,
          sourceHandle: 'right',
          targetHandle: 'left',
          type: 'smoothstep',
          style: {
            stroke: variant === 'current' ? '#10b981' : '#64748b',
            strokeWidth: variant === 'current' ? 3 : 2,
          },
          animated: variant === 'current',
        })

        // Tạo child node
        createNodes(child, level + 1, variant === 'current' ? 'child' : 'default', y, index, org.children!.length)
      })
    }
  }

  // Bắt đầu tạo từ root
  if (data.parent) {
    // Nếu có parent, tạo parent trước
    createNodes(data.parent, 0, 'parent')

    // Tìm vị trí của current trong children của parent
    const currentIndex = data.parent.children?.findIndex(child => child.id === data.id) || 0
    const totalSiblings = data.parent.children?.length || 1

    createNodes(data, 1, 'current', 0, currentIndex, totalSiblings)

    // Tạo edge từ parent đến current
    edges.push({
      id: `${data.parent.id}-${data.id}`,
      source: data.parent.id,
      target: data.id,
      sourceHandle: 'right',
      targetHandle: 'left',
      type: 'smoothstep',
      style: { stroke: '#3b82f6', strokeWidth: 3 },
      animated: true,
    })
  } else {
    // Không có parent, current là root
    createNodes(data, 0, 'current')
  }

  return { nodes, edges }
}

// Flow component chính
function OrganizationFlow({ data }: { data: OrganizationHierarchyData }) {
  const { nodes: initialNodes, edges: initialEdges } = createTreeLayout(data)
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const { fitView, zoomIn, zoomOut, setCenter } = useReactFlow()

  // Update nodes when data changes
  useEffect(() => {
    const { nodes: newNodes, edges: newEdges } = createTreeLayout(data)
    setNodes(newNodes)
    setEdges(newEdges)

    // Auto fit view after layout với padding lớn hơn để tránh bị cắt
    setTimeout(() => {
      fitView({ duration: 800, padding: 0.2, maxZoom: 1 })
    }, 100)
  }, [data, setNodes, setEdges, fitView])

  const handleFitView = useCallback(() => {
    fitView({ duration: 800, padding: 0.2, maxZoom: 1 })
  }, [fitView])

  const handleZoomIn = useCallback(() => {
    zoomIn({ duration: 200 })
  }, [zoomIn])

  const handleZoomOut = useCallback(() => {
    zoomOut({ duration: 200 })
  }, [zoomOut])

  const handleReset = useCallback(() => {
    setCenter(0, 0, { zoom: 1, duration: 800 })
  }, [setCenter])

  return (
    <div className="relative h-full w-full overflow-hidden">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 0.6 }}
        nodesDraggable={true}
        nodesConnectable={false}
        elementsSelectable={true}
        className="bg-gray-50"
        panOnDrag={true}
        panOnScroll={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={false}
      >
        <Background />
        <Controls position="top-right" className="rounded-lg border bg-white shadow-lg" />
      </ReactFlow>

      {/* Custom Controls */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <Button variant="outline" size="sm" onClick={handleFitView} className="bg-white shadow-sm">
          <Maximize2 className="mr-2 h-4 w-4" />
          Phóng to
        </Button>

        <Button variant="outline" size="sm" onClick={handleZoomIn} className="bg-white shadow-sm">
          <ZoomIn className="h-4 w-4" />
        </Button>

        <Button variant="outline" size="sm" onClick={handleZoomOut} className="bg-white shadow-sm">
          <ZoomOut className="h-4 w-4" />
        </Button>

        <Button variant="outline" size="sm" onClick={handleReset} className="bg-white shadow-sm">
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>

      {/* Stats */}
      <div className="absolute bottom-4 left-4 z-10">
        <Card className="bg-white/90 p-3 backdrop-blur-sm">
          <div className="text-muted-foreground flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span>{nodes.length} tổ chức</span>
            </div>
            {data.children && data.children.length > 0 && <div>{data.children.length} tổ chức con</div>}
          </div>
        </Card>
      </div>
    </div>
  )
}

// Component chính để xem hierarchy của tổ chức
function OrganizationHierarchyViewContent({ organizationId, className }: OrganizationHierarchyViewProps) {
  const [data, setData] = useState<OrganizationHierarchyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchOrganizationHierarchy = async () => {
      if (!organizationId) return

      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/ac-apis/organization-units/${organizationId}/hierarchy`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        setData(result)
      } catch (err) {
        console.error('Error fetching organization hierarchy:', err)
        setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải dữ liệu')
      } finally {
        setLoading(false)
      }
    }

    fetchOrganizationHierarchy()
  }, [organizationId])

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Card className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-6 w-3/4" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('space-y-4', className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Không thể tải thông tin tổ chức: {error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!data) {
    return (
      <div className={cn('space-y-4', className)}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Không tìm thấy thông tin tổ chức với ID: {organizationId}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className={cn('h-[calc(95vh-8rem)] w-full', className)}>
      <OrganizationFlow data={data} />
    </div>
  )
}

export function OrganizationHierarchyView(props: OrganizationHierarchyViewProps) {
  return (
    <ReactFlowProvider>
      <OrganizationHierarchyViewContent {...props} />
    </ReactFlowProvider>
  )
}
