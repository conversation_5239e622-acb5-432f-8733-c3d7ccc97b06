'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { Badge } from '@workspace/ui/components/badge'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Separator } from '@workspace/ui/components/separator'
import { toast } from '@workspace/ui/components/toast'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Loading } from '@workspace/ui/mi/loading'
import {
  ArrowLeft,
  Building2,
  Calendar,
  Contact,
  Edit,
  FileText,
  Globe,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Scale,
  Settings,
  Users,
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { ChiNhanhModal } from '../../chi-nhanh-modal'
import { PhongBanModal } from '../../phong-ban-modal'
import { SoTuPhapForm } from '../so-tu-phap-form'

interface SoTuPhapDetailPageProps {
  id: string
}

export function SoTuPhapDetailPage({ id }: SoTuPhapDetailPageProps) {
  const router = useRouter()
  const [data, setData] = useState<Organization | null>(null)
  const [children, setChildren] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [childrenLoading, setChildrenLoading] = useState(true)
  const [isEditMode, setIsEditMode] = useState(false)
  const [isAddChiNhanhModalOpen, setIsAddChiNhanhModalOpen] = useState(false)
  const [isAddPhongBanModalOpen, setIsAddPhongBanModalOpen] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch data')
        }
        const result = await response.json()
        setData(result)
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Không thể tải thông tin Sở Tư pháp')
      } finally {
        setLoading(false)
      }
    }

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
        toast.error('Không thể tải thông tin phòng ban và chi nhánh')
      } finally {
        setChildrenLoading(false)
      }
    }

    if (id) {
      fetchData()
      fetchChildren()
    }
  }, [id])

  const handleEditSuccess = () => {
    setIsEditMode(false)
    // Refetch data
    setLoading(true)
    setChildrenLoading(true)

    const fetchData = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch data')
        }
        const result = await response.json()
        setData(result)
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Không thể tải thông tin Sở Tư pháp')
      } finally {
        setLoading(false)
      }
    }

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
        toast.error('Không thể tải thông tin phòng ban và chi nhánh')
      } finally {
        setChildrenLoading(false)
      }
    }

    fetchData()
    fetchChildren()
  }

  const handleChiNhanhSuccess = () => {
    // Refetch children data after successful creation
    setChildrenLoading(true)

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
      } finally {
        setChildrenLoading(false)
      }
    }
    fetchChildren()
  }

  const handlePhongBanSuccess = () => {
    // Refetch children data after successful creation
    setChildrenLoading(true)

    const fetchChildren = async () => {
      try {
        const response = await fetch(`/ac-apis/organization-units/${id}/children`, {
          cache: 'no-store',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch children data')
        }
        const result = await response.json()
        setChildren(result.items || [])
      } catch (error) {
        console.error('Error fetching children data:', error)
      } finally {
        setChildrenLoading(false)
      }
    }
    fetchChildren()
  }

  if (loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Sở Tư pháp',
            href: '/organization-unit/so-tu-phap',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <Loading />
      </AdminPageLayout>
    )
  }

  if (!data) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Sở Tư pháp',
            href: '/organization-unit/so-tu-phap',
          },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">Không tìm thấy thông tin Sở Tư pháp</p>
        </div>
      </AdminPageLayout>
    )
  }

  const statusOption = STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === data.status)

  // Phân loại phòng ban và chi nhánh từ children data
  const phongBanList = children.filter(child => child.type === ORGANIZATION_TYPE.PHONG_BAN)
  const chiNhanhList = children.filter(child => child.type === ORGANIZATION_TYPE.CHI_NHANH)

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tổ chức', href: '/organization-units' },
          {
            label: 'Sở Tư pháp',
            href: '/organization-unit/so-tu-phap',
          },
          { label: data.name, href: '#' },
        ]}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.back()} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="flex items-center gap-2 text-2xl font-semibold">Thông tin chi tiết</h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setIsEditMode(true)} className="gap-2">
                <Edit className="h-4 w-4" />
                Cập nhật
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Thông tin chính */}
            <Card className="h-fit py-0">
              <CardContent className="p-0">
                <div className="rounded-t-lg p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <h3 className="mb-1 text-base font-semibold">{data.name}</h3>
                  <p className="text-muted-foreground text-sm">
                    {data.fullName && data.fullName !== data.name && (
                      <>
                        Tên đầy đủ: {data.fullName}
                        <br />
                      </>
                    )}
                    Địa chỉ: {data.address || 'Chưa cập nhật'}
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge variant={data.status === 1 ? 'default' : data.status === 2 ? 'secondary' : 'destructive'}>
                      {statusOption?.label || 'Không xác định'}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-4 p-6">
                  {/* Thông tin liên hệ */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Contact className="h-4 w-4" />
                      Thông tin liên hệ
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Phone className="h-3 w-3" />
                          Điện thoại
                        </span>
                        <span className="font-medium">{data.phone || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Mail className="h-3 w-3" />
                          Email
                        </span>
                        <span className="font-medium">{data.email || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Globe className="h-3 w-3" />
                          Website
                        </span>
                        <span className="font-medium">{data.website || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Fax</span>
                        <span className="font-medium">{data.fax || 'Chưa cập nhật'}</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Thông tin nhân sự */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Users className="h-4 w-4" />
                      Thông tin nhân sự
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Người đại diện</span>
                        <span className="font-medium">{data.representative || 'Chưa cập nhật'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số biên chế</span>
                        <span className="font-medium">
                          {data.payrollNumber ? `${data.payrollNumber} người` : 'Chưa cập nhật'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số phòng</span>
                        <span className="font-medium">
                          {data.roomNumber ? `${data.roomNumber} phòng` : 'Chưa cập nhật'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số câu lạc bộ</span>
                        <span className="font-medium">
                          {data.clubNumber ? `${data.clubNumber} câu lạc bộ` : 'Chưa cập nhật'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Thông tin thành lập */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Calendar className="h-4 w-4" />
                      Thông tin thành lập
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Ngày thành lập</span>
                        <span className="font-medium">
                          {data.establishmentDate
                            ? new Date(data.establishmentDate).toLocaleDateString('vi-VN')
                            : 'Chưa cập nhật'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Ngày quyết định</span>
                        <span className="font-medium">
                          {data.decisionDate
                            ? new Date(data.decisionDate).toLocaleDateString('vi-VN')
                            : 'Chưa cập nhật'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Số quyết định</span>
                        <span className="font-medium">{data.decisionNumber || 'Chưa cập nhật'}</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Thông tin quản trị */}
                  <div>
                    <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                      <Settings className="h-4 w-4" />
                      Thông tin quản trị
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Nhóm quản trị</span>
                        <span className="font-medium"></span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-sm">Ngày hết hạn hợp đồng</span>
                        <span className="font-medium">
                          {data.deadlineContractDate
                            ? new Date(data.deadlineContractDate).toLocaleDateString('vi-VN')
                            : 'Chưa cập nhật'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Lĩnh vực pháp lý */}
                  {(data.legalFieldId || data.legalFormId || data.rangeActivitiesId) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                          <Scale className="h-4 w-4" />
                          Lĩnh vực pháp lý
                        </h4>
                        <div className="space-y-2">
                          {data.legalFieldId && (
                            <div className="flex items-center justify-between">
                              <span className="text-muted-foreground text-sm">Lĩnh vực</span>
                              <span className="font-medium">{data.legalFieldId}</span>
                            </div>
                          )}
                          {data.legalFormId && (
                            <div className="flex items-center justify-between">
                              <span className="text-muted-foreground text-sm">Hình thức</span>
                              <span className="font-medium">{data.legalFormId}</span>
                            </div>
                          )}
                          {data.rangeActivitiesId && (
                            <div>
                              <span className="text-muted-foreground mb-1 block text-sm">Phạm vi hoạt động</span>
                              <p className="text-sm">{data.rangeActivitiesId}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Kinh nghiệm và ghi chú */}
                  {(data.experience || data.note) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="mb-2 flex items-center gap-2 text-base font-medium">
                          <FileText className="h-4 w-4" />
                          Thông tin bổ sung
                        </h4>
                        <div className="space-y-2">
                          {data.experience && (
                            <div>
                              <span className="text-muted-foreground mb-1 block text-sm">Kinh nghiệm</span>
                              <p className="text-sm">{data.experience}</p>
                            </div>
                          )}
                          {data.note && (
                            <div>
                              <span className="text-muted-foreground mb-1 block text-sm">Ghi chú</span>
                              <p className="text-sm">{data.note}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>

                <div className="rounded-b-lg border-t p-4" style={{ backgroundColor: '#F5F5F5' }}>
                  <div className="text-muted-foreground flex justify-between text-xs">
                    <span>Tạo: {data.createdAt ? new Date(data.createdAt).toLocaleDateString('vi-VN') : 'N/A'}</span>
                    <span>
                      Cập nhật: {data.updatedAt ? new Date(data.updatedAt).toLocaleDateString('vi-VN') : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Phòng ban và Chi nhánh */}
            <div className="space-y-6">
              {/* Phòng ban */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Phòng ban
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsAddPhongBanModalOpen(true)}>
                      <Plus className="h-4 w-4" />
                      Thêm phòng ban
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {phongBanList.length} phòng ban</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : phongBanList.length > 0 ? (
                    <div className="space-y-4">
                      {phongBanList.slice(0, 5).map((phongBan, index) => (
                        <div key={phongBan.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">{phongBan.name}</p>
                              <p className="text-muted-foreground text-xs">Mã: {phongBan.code}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  phongBan.status === 1
                                    ? 'default'
                                    : phongBan.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === phongBan.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < Math.min(phongBanList.length, 5) - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có phòng ban nào</p>
                    </div>
                  )}

                  {phongBanList.length > 5 && (
                    <>
                      <hr className="border-border my-4" />
                      <div className="flex justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600"
                          onClick={() => {
                            router.push(`/organization-unit/phong-ban?parentId=${id}`)
                          }}
                        >
                          Xem thêm ({phongBanList.length - 5} phòng ban)
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Chi nhánh */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Chi nhánh
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsAddChiNhanhModalOpen(true)}>
                      <Plus className="h-4 w-4" />
                      Thêm chi nhánh
                    </Button>
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">Số lượng: {chiNhanhList.length} chi nhánh</p>
                </CardHeader>
                <CardContent className="p-4">
                  {childrenLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loading />
                    </div>
                  ) : chiNhanhList.length > 0 ? (
                    <div className="space-y-4">
                      {chiNhanhList.slice(0, 5).map((chiNhanh, index) => (
                        <div key={chiNhanh.id}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">{chiNhanh.name}</p>
                              <p className="text-muted-foreground text-xs">
                                Mã: {chiNhanh.code} | Địa chỉ: {chiNhanh.address || 'Chưa cập nhật'}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  chiNhanh.status === 1
                                    ? 'default'
                                    : chiNhanh.status === 2
                                      ? 'secondary'
                                      : 'destructive'
                                }
                              >
                                {STATUS_SO_TU_PHAP_OPTIONS.find(s => s.value === chiNhanh.status)?.label ||
                                  'Không xác định'}
                              </Badge>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < Math.min(chiNhanhList.length, 5) - 1 && <hr className="border-border mt-4" />}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground flex items-center justify-center py-8">
                      <p className="text-sm">Chưa có chi nhánh nào</p>
                    </div>
                  )}

                  {chiNhanhList.length > 5 && (
                    <>
                      <hr className="border-border my-4" />
                      <div className="flex justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600"
                          onClick={() => {
                            router.push(`/organization-unit/chi-nhanh?parentId=${id}`)
                          }}
                        >
                          Xem thêm ({chiNhanhList.length - 5} chi nhánh)
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </AdminPageLayout>

      {/* Edit Dialog */}
      <Dialog open={isEditMode} onOpenChange={setIsEditMode}>
        <DialogContent className="flex max-h-[85vh] flex-col gap-0 p-0 sm:max-w-[700px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Chỉnh sửa Sở Tư pháp</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <div className="overflow-y-auto">
            <SoTuPhapForm
              initialData={data}
              organizationType={data.type}
              onSuccess={handleEditSuccess}
              onCancel={() => setIsEditMode(false)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Chi Nhanh Modal */}
      <ChiNhanhModal
        isOpen={isAddChiNhanhModalOpen}
        onClose={() => setIsAddChiNhanhModalOpen(false)}
        parentId={id}
        onSuccess={handleChiNhanhSuccess}
        fixedParent={data ? { id: data.id, name: data.name, code: data.code } : null}
      />

      {/* Add Phong Ban Modal */}
      <PhongBanModal
        isOpen={isAddPhongBanModalOpen}
        onClose={() => setIsAddPhongBanModalOpen(false)}
        parentId={id}
        onSuccess={handlePhongBanSuccess}
        fixedParent={data ? { id: data.id, name: data.name, code: data.code } : null}
      />
    </>
  )
}
