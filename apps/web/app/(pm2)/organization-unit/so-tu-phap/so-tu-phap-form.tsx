'use client'

import { ORGANIZATION_TYPE_OPTIONS } from '@/constants/organization-types'
import { ADMIN_GROUP_SO_TU_PHAP_OPTIONS, Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { Required } from '@workspace/ui/mi/required'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  code: z.string().min(1, '<PERSON><PERSON> cơ quan là bắt buộc'),
  name: z.string().min(1, 'Tên cơ quan là bắt buộc'),
  fullName: z.string().optional(),
  representative: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  website: z.string().url('Website không hợp lệ').optional().or(z.literal('')),

  // Địa phương
  oldProvinceId: z.string().optional(),
  cityId: z.number().optional(),
  oldDistrictId: z.string().optional(),
  communeId: z.number().optional(),

  // Cấu trúc cây
  parentId: z.string().min(1, 'Đơn vị quản lý là bắt buộc'),

  // Trạng thái và phân loại
  type: z.number().optional(),
  status: z.number(),

  // Thành lập
  establishmentDate: z.string().optional(),
  decisionDate: z.string().optional(),
  decisionNumber: z.string().optional(),

  // Lĩnh vực pháp lý
  legalField: z.string().optional(),
  legalFieldId: z.string().optional(),
  legalForm: z.string().optional(),
  legalFormId: z.string().optional(),

  // Phạm vi hoạt động
  rangeActivities: z.string().optional(),
  rangeActivitiesId: z.string().optional(),

  // Tổ chức
  payrollNumber: z.number().optional(),
  roomNumber: z.number().optional(),
  clubNumber: z.number().optional(),

  // Quản trị
  adminGroup: z.number().optional(),
  adminStatusId: z.number().optional(),
  experience: z.string().optional(),
  deadlineContractDate: z.string().optional(),
  note: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

interface SoTuPhapFormProps {
  initialData?: Organization | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
}

export function SoTuPhapForm({ initialData, onSuccess, onCancel, organizationType }: SoTuPhapFormProps) {
  const [parentOptions, setParentOptions] = useState<Array<{ id: string; code: string; name: string }>>([])
  const [isLoadingParents, setIsLoadingParents] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: initialData?.code || '',
      name: initialData?.name || '',
      fullName: initialData?.fullName || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      fax: initialData?.fax || '',
      email: initialData?.email || '',
      website: initialData?.website || '',
      representative: initialData?.representative || '',

      // Địa phương
      oldProvinceId: initialData?.oldProvinceId || '',
      cityId: initialData?.cityId || undefined,
      oldDistrictId: initialData?.oldDistrictId || '',
      communeId: initialData?.communeId || undefined,

      // Cấu trúc cây
      parentId: initialData?.parentId || '',

      // Trạng thái và phân loại
      type: initialData?.type || organizationType || undefined,
      status: initialData?.status ?? 1,

      // Thành lập
      establishmentDate: initialData?.establishmentDate || '',
      decisionDate: initialData?.decisionDate || '',
      decisionNumber: initialData?.decisionNumber || '',

      // Lĩnh vực pháp lý
      legalFieldId: initialData?.legalFieldId || '',
      legalFormId: initialData?.legalFormId || '',

      // Phạm vi hoạt động
      rangeActivitiesId: initialData?.rangeActivitiesId || '',

      // Tổ chức
      payrollNumber: initialData?.payrollNumber || undefined,
      roomNumber: initialData?.roomNumber || undefined,
      clubNumber: initialData?.clubNumber || undefined,

      // Quản trị
      adminGroup: Number(initialData?.adminGroup?.categoryId) || undefined,
      adminStatusId: initialData?.adminStatusId || undefined,
      experience: initialData?.experience || '',
      deadlineContractDate: initialData?.deadlineContractDate || '',
      note: initialData?.note || '',
    },
  })

  // Fetch parent organizations - Sở Tư pháp chỉ có thể là con của Bộ Tư pháp
  useEffect(() => {
    const fetchParentOptions = async () => {
      setIsLoadingParents(true)

      try {
        const response = await fetch('/ac-apis/organization-units/type/1')

        if (!response.ok) {
          throw new Error('Failed to fetch parent organizations')
        }
        const data = await response.json()
        setParentOptions(data.data || data.items || [])
      } catch (error) {
        console.error('Error fetching parent organizations:', error)
        toast.error('Không thể tải danh sách đơn vị quản lý')
      } finally {
        setIsLoadingParents(false)
      }
    }

    fetchParentOptions()
  }, [])

  const onSubmit = async (data: FormData) => {
    try {
      const url = initialData ? `/ac-apis/organization-units/${initialData.id}` : '/ac-apis/organization-units'

      const method = initialData ? 'PATCH' : 'POST'

      const submitData = {
        ...data,
        type: organizationType || data.type,
        ...(initialData && { id: initialData.id }),
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorText = await response.text()

        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || errorData.message || response.statusText)
        } catch {
          throw new Error(response.statusText || `${initialData ? 'Cập nhật' : 'Tạo'} sở tư pháp thất bại`)
        }
      }

      toast.success(`${initialData ? 'Cập nhật' : 'Tạo'} sở tư pháp thành công`)
      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <Form {...form}>
      <form id="organization-form" onSubmit={form.handleSubmit(onSubmit)} className="flex h-full flex-col">
        {/* Scrollable content area */}
        <div className="min-h-0 flex-1 space-y-6 overflow-y-auto px-6 py-4">
          {/* Thông tin cơ bản */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Thông tin cơ bản</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Mã cơ quan <Required />
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập mã cơ quan" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Tên cơ quan <Required />
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên cơ quan" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đầy đủ</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên đầy đủ" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="representative"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Người đại diện</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên người đại diện" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số điện thoại</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập số điện thoại" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số fax</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập số fax" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" placeholder="Nhập email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập website" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Đơn vị quản lý <Required />
                  </FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={value => field.onChange(value)}
                      defaultValue={field.value}
                      disabled={isLoadingParents}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={isLoadingParents ? 'Đang tải...' : 'Chọn đơn vị quản lý'} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {parentOptions.map(parent => (
                          <SelectItem key={parent.id} value={parent.id}>
                            {parent.code} - {parent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Địa chỉ <Required />
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Nhập địa chỉ" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Trạng thái và phân loại */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Trạng thái và phân loại</h3>
            <div className="grid grid-cols-2 gap-4"></div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={() => {
                  const organizationTypeLabel = organizationType
                    ? ORGANIZATION_TYPE_OPTIONS.find(opt => opt.value === organizationType)?.label
                    : 'Không xác định'

                  return (
                    <FormItem>
                      <FormLabel>Loại cơ quan </FormLabel>
                      <FormControl>
                        <Input value={organizationTypeLabel} disabled className="bg-muted" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Trạng thái <Required />
                    </FormLabel>
                    <Select
                      onValueChange={value => field.onChange(Number(value))}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn trạng thái" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Thông tin thành lập */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Thông tin thành lập</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="establishmentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày thành lập</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="decisionDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày quyết định</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="decisionNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số quyết định</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Nhập số quyết định" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Thông tin tổ chức */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Thông tin tổ chức</h3>
            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="payrollNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số biên chế</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" min={0} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="roomNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số phòng</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" min={0} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clubNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số câu lạc bộ</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" min={0} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="adminGroup"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nhóm quản trị</FormLabel>
                  <Select onValueChange={value => field.onChange(Number(value))} defaultValue={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn nhóm quản trị" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ADMIN_GROUP_SO_TU_PHAP_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Lĩnh vực pháp lý */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Lĩnh vực pháp lý</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="legalField"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lĩnh vực pháp lý</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập lĩnh vực pháp lý" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="legalForm"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hình thức pháp lý</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập hình thức pháp lý" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="rangeActivities"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phạm vi hoạt động</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Nhập phạm vi hoạt động" rows={2} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Thông tin bổ sung */}
          <div className="space-y-4">
            <h3 className="text-muted-foreground text-sm font-medium">Thông tin bổ sung</h3>
            <FormField
              control={form.control}
              name="experience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kinh nghiệm</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Nhập thông tin kinh nghiệm" rows={2} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deadlineContractDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ngày hết hạn hợp đồng</FormLabel>
                  <FormControl>
                    <Input {...field} type="date" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ghi chú</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Nhập ghi chú" rows={3} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
