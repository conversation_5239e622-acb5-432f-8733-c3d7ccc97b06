import { OrganizationUnit } from '@/lib/types'
import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@workspace/ui/components/badge'

export const getOrganizationGroupColumn = (
  options: { label: string; value: string | number }[]
): ColumnDef<OrganizationUnit> => ({
  accessorKey: 'organizationGroupId',
  meta: {
    title: 'Nhóm tổ chức',
    filter: {
      type: 'combobox',
      position: 'advanced',
      placeholder: 'Chọn nhóm tổ chức',
      options: options.map(option => ({
        label: option.label,
        value: option.value.toString(),
      })),
    },
  },
  cell: ({ row }) => {
    const groupName = (row.original as OrganizationUnit).organizationGroup?.name

    return groupName ? <Badge variant="secondary">{groupName}</Badge> : <span className="text-muted-foreground">-</span>
  },
})
