'use client'

import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Any, Category } from '@/lib/types'
import { categoryService } from '@/services/categoryService'
import { useEffect, useState } from 'react'

import { getOrganizationGroupColumn } from '../organization-columns'
import { OrganizationList } from '../organization-list'
import { ToChucHopDongTGPLForm } from './to-chuc-hop-dong-tgpl-form'

function ToChucDangKyTGPLPage() {
  const [organizationGroups, setOrganizationGroups] = useState<{ label: string; value: string | number }[]>([])

  // Fetch organization groups từ API
  useEffect(() => {
    const fetchOrganizationGroups = async () => {
      try {
        const result = (await categoryService.getCategories('NTC')) as (Category & { id: string })[]
        const options =
          result
            ?.map(item => ({
              value: item.categoryId || item.id || item.code || '',
              label: item.name || '',
            }))
            .filter(option => option.value && option.label) || []

        setOrganizationGroups(options)
      } catch (error) {
        console.error('Error fetching organization groups:', error)
      }
    }

    fetchOrganizationGroups()
  }, [])

  // Tạo columns với filter options từ API
  const customColumns = organizationGroups.length > 0 ? [getOrganizationGroupColumn(organizationGroups)] : []

  return (
    <OrganizationList
      type={ORGANIZATION_TYPE.TO_CHUC_KY_HOP_DONG_TGPL}
      title="Quản lý Tổ chức ký hợp đồng TGPL"
      breadcrumbLabel="Tổ chức ký hợp đồng TGPL"
      breadcrumbHref="/organization-unit/to-chuc-hop-dong-tgpl"
      FormComponent={ToChucHopDongTGPLForm as Any}
      customColumns={{
        after: customColumns as Any,
      }}
    />
  )
}
export { ToChucDangKyTGPLPage }
