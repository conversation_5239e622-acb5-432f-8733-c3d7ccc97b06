'use client'

import { UploadDocument } from '@/components/common/multi-file-upload-dialog'
import { AcFilePreviewButton } from '@/components/staff/components/ac-file-preview-button'
import { UploadedFileSummary } from '@/components/staff/upload/use-staff-upload'
import { SearchComboBox } from '@/components/ui/search-combobox'
import { ORGANIZATION_TYPE } from '@/constants/organization-types'
import { Organization, STATUS_SO_TU_PHAP_OPTIONS } from '@/constants/so-tu-phap'
import { flattenAndCleanArray, normalizeToArray } from '@/lib/array-utils'
import { Category } from '@/lib/services/types'
import { Any } from '@/lib/types'
import { formatDate } from '@/lib/utils'
import { categoryService } from '@/services/categoryService'
import { apiService } from '@/services/common/api-service'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { RadioGroup, RadioGroupItem } from '@workspace/ui/components/radio-group'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { Label } from '@workspace/ui/mi'
import { Combobox } from '@workspace/ui/mi/combobox'
import { Required } from '@workspace/ui/mi/required'
import { CalendarIcon, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

// Extend Organization type to include attachments
interface OrganizationWithAttachments extends Organization {
  attachments?: Array<{
    url?: string
    fileKey?: string
    fileId?: string
    bucket?: string
    type?: string
    [key: string]: any
  }>
}

type OptionData = {
  value: string
  label: string
}

const formSchema = z.object({
  // Thông tin tổ chức - Section 1
  name: z.string().min(1, 'Tên tổ chức là bắt buộc'),
  fullName: z.string().min(1, 'Tên đầy đủ là bắt buộc'),
  code: z.string().min(1, 'Mã tổ chức là bắt buộc'),
  establishmentDate: z.date({
    error: 'Ngày thành lập là bắt buộc',
  }),
  representative: z.string().optional(),
  organizationGroupId: z.string().min(1, 'Nhóm tổ chức là bắt buộc'),
  legalFieldId: z.string().min(1, 'Lĩnh vực trợ giúp là bắt buộc'),
  legalFormId: z.array(z.string()),
  status: z.string().min(1, 'Trạng thái hoạt động là bắt buộc'),
  rangeActivitiesId: z.string().optional(),
  experience: z.string().optional(),
  contractNumber: z.string().min(1, 'Số hợp đồng là bắt buộc'),
  issueDate: z.date({
    error: 'Ngày hiệu lực là bắt buộc',
  }),
  expiryDate: z.date({
    error: 'Ngày hết hạn là bắt buộc',
  }),
  renewalMonths: z.string().min(1, 'Số tháng gia hạn là bắt buộc'),
  renewedExpiryDate: z.date({
    error: 'Ngày hết hạn sau gia hạn là bắt buộc',
  }),
  contractTerminationReason: z.string().min(1, 'Lý do chấm dứt hợp đồng là bắt buộc'),

  // Thông tin liên hệ - Section 2
  oldProvinceId: z.string().min(1, 'Tỉnh / Thành phố là bắt buộc'),
  oldDistrictId: z.string().min(1, 'Quận / Huyện là bắt buộc'),
  oldWardId: z.string().min(1, 'Phường / Xã là bắt buộc'),
  address: z.string().min(1, 'Địa chỉ là bắt buộc'),
  phone: z.string().optional(),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  website: z.string().url('Website không hợp lệ').optional().or(z.literal('')),

  // Ghi chú
  note: z.string().optional(),

  // Đính kèm
  attachments: z
    .array(
      z.object({
        name: z.string(),
        url: z.string(),
      })
    )
    .optional(),

  // Thông tin hợp đồng - Section 3
  contractFile: z.any().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ToChucHopDongTGPLFormProps {
  initialData?: OrganizationWithAttachments | null
  onSuccess: () => void
  onCancel: () => void
  organizationType?: number
  onSubmitStart?: () => void
  onSubmitEnd?: () => void
}

interface CategoryOption {
  value: string
  label: string
}

export function ToChucHopDongTGPLForm({
  initialData,
  onSuccess,
  organizationType = ORGANIZATION_TYPE.TO_CHUC_KY_HOP_DONG_TGPL,
  onSubmitStart,
  onSubmitEnd,
}: ToChucHopDongTGPLFormProps) {
  const queryClient = useQueryClient()
  const [provinces, setProvinces] = useState<OptionData[]>([])
  const [districts, setDistricts] = useState<OptionData[]>([])
  const [wards, setWards] = useState<OptionData[]>([])
  const [legalFormOptions, setLegalFormOptions] = useState<OptionData[]>([])
  const [loadingProvinces, setLoadingProvinces] = useState(false)
  const [loadingDistricts, setLoadingDistricts] = useState(false)
  const [loadingWards, setLoadingWards] = useState(false)

  // States for other comboboxes
  const [organizationGroups, setOrganizationGroups] = useState<CategoryOption[]>([])
  const [legalFields, setLegalFields] = useState<CategoryOption[]>([])
  const [rangeActivitiesOptions, setRangeActivitiesOptions] = useState<CategoryOption[]>([])
  const [loadingOrganizationGroups, setLoadingOrganizationGroups] = useState(false)
  const [loadingLegalFields, setLoadingLegalFields] = useState(false)

  const [attachedFiles, setAttachedFiles] = useState<UploadedFileSummary[]>([])

  // Khi mở form, nếu initialData.attachments có giá trị thì merge vào attachedFiles
  useEffect(() => {
    const initFiles = Array.isArray(initialData?.attachments)
      ? initialData.attachments.map(f => ({
          url: f.url || f.fileKey || '',
          fileKey: f.fileKey || '',
          fileId: f.fileId || '',
          bucket: f.bucket,
          type: f.type,
        }))
      : []

    if (initFiles.length > 0) {
      setAttachedFiles(prev => {
        const merged = [...initFiles, ...prev]

        return merged.filter((f, i, arr) => arr.findIndex(x => (x.url || x.fileKey) === (f.url || f.fileKey)) === i)
      })
    }
  }, [initialData?.attachments])
  const [previewContract, setPreviewContract] = useState<{
    fileId: string
    downloadUrl: string
    fileName?: string
    contentType?: string
  } | null>(null)

  // Đồng bộ attachedFiles vào field attachments của form
  useEffect(() => {
    // Chỉ lấy name và url (hoặc fileKey) cho đúng schema
    const files = attachedFiles.map(f => ({
      name: f.fileKey || f.url || f.fileId || '',
      url: f.url || f.fileKey || '',
    }))
    form.setValue('attachments', files)
  }, [attachedFiles])

  const contractFileInfo =
    initialData?.contractFileId || initialData?.contractFileRef
      ? {
          fileId: initialData.contractFileId,
          fileKey: initialData.contractFileRef,
          url: initialData.contractFileRef,
        }
      : null

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || '',
      fullName: initialData?.fullName || '',
      code: initialData?.code || '',
      establishmentDate: initialData?.establishmentDate ? new Date(initialData.establishmentDate) : undefined,
      representative: initialData?.representative || '',
      organizationGroupId:
        typeof initialData?.organizationGroup === 'object'
          ? initialData.organizationGroup?.categoryId
          : initialData?.organizationGroupId || '',
      legalFieldId:
        typeof initialData?.legalField === 'object'
          ? initialData.legalField?.categoryId
          : initialData?.legalFieldId || '',
      legalFormId: normalizeToArray(initialData?.legalFormId),
      status: initialData?.status?.toString() || '1',
      rangeActivitiesId:
        typeof initialData?.rangeActivities === 'object'
          ? initialData.rangeActivities?.categoryId
          : initialData?.rangeActivitiesId || '',
      experience: initialData?.experience || '',
      oldProvinceId:
        typeof initialData?.oldProvince === 'object'
          ? initialData.oldProvince?.categoryId
          : initialData?.oldProvinceId || '',
      oldDistrictId:
        typeof initialData?.oldDistrict === 'object'
          ? initialData.oldDistrict?.categoryId
          : initialData?.oldDistrictId || '',
      oldWardId:
        typeof initialData?.oldWard === 'object' ? initialData.oldWard?.categoryId : initialData?.oldWardId || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      website: initialData?.website || '',
      note: initialData?.note || '',
      attachments: [],
      contractFile: null,
      contractNumber: initialData?.contractNumber || '',
      issueDate: initialData?.issueDate ? new Date(initialData.issueDate) : undefined,
      expiryDate: initialData?.expiryDate ? new Date(initialData.expiryDate) : undefined,
      renewalMonths: initialData?.renewalMonths?.toString() || '',
      renewedExpiryDate: initialData?.renewedExpiryDate ? new Date(initialData.renewedExpiryDate) : undefined,
      contractTerminationReason: initialData?.contractTerminationReason || '',
    },
  })

  // Fetch provinces when form loads
  useEffect(() => {
    fetchProvinces()
    fetchOrganizationGroups()
    fetchLegalFields()
    fetchLegalFormOptions()
    // Set static options
    setRangeActivitiesOptions([
      { value: 'toan-quoc', label: 'Toàn quốc' },
      { value: 'khu-vuc', label: 'Khu vực' },
      { value: 'dia-phuong', label: 'Địa phương' },
    ])
  }, [])

  // Fetch districts and wards when form loads with initialData
  useEffect(() => {
    const loadInitialData = async () => {
      if (initialData?.oldProvinceId) {
        await fetchDistricts(initialData.oldProvinceId)
      }

      if (initialData?.oldDistrictId) {
        await fetchWards(initialData.oldDistrictId)
      }
    }
    loadInitialData()
  }, [initialData?.oldProvinceId, initialData?.oldDistrictId])

  // Watch oldProvinceId changes để load districts tương ứng
  const selectedProvinceId = form.watch('oldProvinceId')
  useEffect(() => {
    if (selectedProvinceId) {
      fetchDistricts(selectedProvinceId)
      // Chỉ reset nếu không phải initialData
      const isInitialLoad = initialData?.oldProvinceId === selectedProvinceId

      if (!isInitialLoad) {
        form.setValue('oldDistrictId', '')
        form.setValue('oldWardId', '')
        setWards([])
      }
    } else {
      setDistricts([])
      setWards([])
      form.setValue('oldDistrictId', '')
      form.setValue('oldWardId', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProvinceId])

  // Watch oldDistrictId changes để load wards tương ứng
  const selectedDistrictId = form.watch('oldDistrictId')
  useEffect(() => {
    if (selectedDistrictId) {
      fetchWards(selectedDistrictId)
      // Chỉ reset oldWardId nếu không phải initialData
      const isInitialLoad = initialData?.oldDistrictId === selectedDistrictId

      if (!isInitialLoad) {
        form.setValue('oldWardId', '')
      }
    } else {
      setWards([])
      form.setValue('oldWardId', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDistrictId])

  const fetchLegalFormOptions = async () => {
    try {
      const result = (await categoryService.getCategories('TG')) as Category[]
      const legalFormOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setLegalFormOptions(legalFormOptions)
    } catch (error) {
      console.error('Error fetching legal form options:', error)
      setLegalFormOptions([])
      toast.error('Không thể tải danh sách hình thức pháp lý')
    }
  }

  const fetchProvinces = async () => {
    setLoadingProvinces(true)

    try {
      const result = (await categoryService.getCategories('TP')) as Category[]
      const provinceOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setProvinces(provinceOptions)
    } catch (error) {
      console.error('Error fetching provinces:', error)
      toast.error('Không thể tải danh sách tỉnh/thành phố')
    } finally {
      setLoadingProvinces(false)
    }
  }

  const fetchDistricts = async (oldProvinceId?: string) => {
    setLoadingDistricts(true)

    try {
      const filter = oldProvinceId ? { type: 'QH', parentId: oldProvinceId } : { type: 'QH' }
      const result = (await apiService.get('/ac-apis/categories/method/all', {
        params: { filter: JSON.stringify(filter) },
      })) as (Category & { id: string })[]
      const districtOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || item.id || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setDistricts(districtOptions)
    } catch (error) {
      console.error('Error fetching districts:', error)
      toast.error('Không thể tải danh sách quận/huyện')
    } finally {
      setLoadingDistricts(false)
    }
  }

  const fetchWards = async (oldDistrictId?: string) => {
    setLoadingWards(true)

    try {
      const filter = oldDistrictId ? { type: 'PX', parentId: oldDistrictId } : { type: 'PX' }
      const result = (await apiService.get('/ac-apis/categories/method/all', {
        params: { filter: JSON.stringify(filter) },
      })) as (Category & { id: string })[]
      const wardOptions =
        result
          ?.map(item => ({
            value: String(item.categoryId || item.id || ''),
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setWards(wardOptions)
    } catch (error) {
      console.error('Error fetching wards:', error)
      toast.error('Không thể tải danh sách phường/xã')
    } finally {
      setLoadingWards(false)
    }
  }

  const fetchOrganizationGroups = async () => {
    setLoadingOrganizationGroups(true)

    try {
      const result = (await categoryService.getCategories('NTC')) as Category[]
      const options =
        result
          ?.map(item => ({
            value: item.categoryId || item.id || item.code || '',
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setOrganizationGroups(options)
    } catch (error) {
      console.error('Error fetching organization groups:', error)
      // No data available
      setOrganizationGroups([])
    } finally {
      setLoadingOrganizationGroups(false)
    }
  }

  const fetchLegalFields = async () => {
    setLoadingLegalFields(true)

    try {
      const result = (await categoryService.getCategories('LV')) as Category[]
      const options =
        result
          ?.map(item => ({
            value: item.categoryId || item.id || item.code || '',
            label: item.name || '',
          }))
          .filter(option => option.value && option.label) || []
      setLegalFields(options)
    } catch (error) {
      console.error('Error fetching legal fields:', error)
      // No data available
      setLegalFields([])
    } finally {
      setLoadingLegalFields(false)
    }
  }

  const onSubmit = async (values: FormValues) => {
    onSubmitStart?.()

    try {
      const cleanedValues = Object.entries(values).reduce((acc, [key, value]) => {
        // Chỉ loại bỏ nếu là chuỗi rỗng
        if (value !== '') {
          // Loại bỏ array rỗng
          if (Array.isArray(value) && value.length === 0) {
            return acc
          }
          acc[key] = value
        }

        return acc
      }, {} as Any)

      const submitData = {
        ...cleanedValues,
        type: organizationType,
        status: Number(values.status),
        legalFormId: flattenAndCleanArray(values.legalFormId),
        oldProvinceId: values.oldProvinceId || undefined,
        oldDistrictId: values.oldDistrictId || undefined,
        oldWardId: values.oldWardId || undefined,
        establishmentDate: values.establishmentDate ? values.establishmentDate.toISOString() : undefined,
        renewalMonths: values.renewalMonths ? Number(values.renewalMonths) : undefined,
        issueDate: values.issueDate ? values.issueDate.toISOString() : undefined,
        expiryDate: values.expiryDate ? values.expiryDate.toISOString() : undefined,
        renewedExpiryDate: values.renewedExpiryDate ? values.renewedExpiryDate.toISOString() : undefined,
        // attachments: lấy đủ thông tin file
        attachments: attachedFiles.map(f => ({
          fileId: f.fileId || '',
          fileKey: f.fileKey || '',
          url: f.url || '',
          name: f.fileKey || f.url || f.fileId || '',
        })),
      }

      // Đồng bộ file hợp đồng TGPL từ state previewContract nếu có
      if (previewContract?.fileId) {
        submitData.contractFileId = previewContract.fileId
      }

      if (previewContract?.downloadUrl) {
        submitData.contractFileRef = previewContract.downloadUrl
      }

      Object.keys(submitData).forEach(key => {
        if ((submitData as Any)[key] === undefined) {
          delete (submitData as Any)[key]
        }
      })

      console.log('Submit data:', submitData)

      const url = initialData ? `/ac-apis/organization-units/${initialData.id}` : '/ac-apis/organization-units'

      const method = initialData ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      toast.success(
        initialData ? 'Cập nhật tổ chức ký hợp đồng TGPL thành công' : 'Tạo tổ chức ký hợp đồng TGPL thành công'
      )

      queryClient.invalidateQueries({
        queryKey: ['organizations', organizationType.toString()],
      })

      onSuccess()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(
        initialData
          ? 'Có lỗi xảy ra khi cập nhật tổ chức ký hợp đồng TGPL'
          : 'Có lỗi xảy ra khi tạo tổ chức ký hợp đồng TGPL'
      )
    } finally {
      onSubmitEnd?.()
    }
  }

  const uploadOverrides = { pathPrefix: `organization-units/contracts` }

  return (
    <div className="flex h-full flex-col">
      <Form {...form}>
        <div className="flex-1 overflow-y-auto">
          <form id="organization-form" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-0">
              {/* Thông tin tổ chức */}
              <div className="border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium text-blue-600">Thông tin tổ chức</h3>
              </div>
              <div className="space-y-4 px-6 py-4">
                <h4 className="text-sm font-medium">Thông tin cơ bản</h4>

                {/* Row 1 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tên tổ chức <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên tổ chức" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tên đầy đủ <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên đầy đủ" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 2 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mã tổ chức <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập mã" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="establishmentDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày thành lập <Required />
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full justify-start text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="representative"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Người đứng đầu</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập họ tên" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 3 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="organizationGroupId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nhóm tổ chức <Required />
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            options={organizationGroups}
                            value={field.value}
                            onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder={loadingOrganizationGroups ? 'Đang tải...' : 'Chọn'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="legalFieldId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lĩnh vực trợ giúp <Required />
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            options={legalFields}
                            value={field.value}
                            onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder={loadingLegalFields ? 'Đang tải...' : 'Chọn'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="legalFormId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Hình thức trợ giúp</FormLabel>
                        <FormControl>
                          <Combobox
                            options={legalFormOptions}
                            value={field.value}
                            onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder={loadingLegalFields ? 'Đang tải...' : 'Chọn'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 4 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="rangeActivitiesId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phạm vi hoạt động</FormLabel>
                        <FormControl>
                          <Combobox
                            options={rangeActivitiesOptions}
                            value={field.value || ''}
                            onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                            placeholder="Chọn"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="experience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Kinh nghiệm năng lực</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Thông tin liên hệ */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Thông tin liên hệ</h3>
              </div>
              <div className="space-y-4 px-6 py-4">
                {/* Row 1 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="oldProvinceId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tỉnh / Thành phố <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={provinces}
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder={loadingProvinces ? 'Đang tải...' : 'Chọn tỉnh/thành phố'}
                            searchPlaceholder="Tìm kiếm tỉnh/thành phố..."
                            emptyMessage="Không tìm thấy tỉnh/thành phố."
                            disabled={loadingProvinces}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="oldDistrictId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Quận / Huyện <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={selectedProvinceId ? districts : []}
                            value={field.value}
                            onValueChange={value => {
                              if (selectedProvinceId) {
                                field.onChange(value)
                              }
                            }}
                            placeholder={
                              loadingDistricts
                                ? 'Đang tải...'
                                : !selectedProvinceId
                                  ? 'Vui lòng chọn tỉnh/thành phố trước'
                                  : districts.length === 0
                                    ? 'Không có dữ liệu'
                                    : 'Chọn quận/huyện'
                            }
                            searchPlaceholder="Tìm kiếm quận/huyện..."
                            emptyMessage="Không tìm thấy quận/huyện."
                            disabled={loadingDistricts || !selectedProvinceId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="oldWardId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Phường / Xã <Required />
                        </FormLabel>
                        <FormControl>
                          <SearchComboBox
                            options={selectedDistrictId ? wards : []}
                            value={field.value}
                            onValueChange={value => {
                              if (selectedDistrictId) {
                                field.onChange(value)
                              }
                            }}
                            placeholder={
                              loadingWards
                                ? 'Đang tải...'
                                : !selectedDistrictId
                                  ? 'Vui lòng chọn quận/huyện trước'
                                  : wards.length === 0
                                    ? 'Không có dữ liệu'
                                    : 'Chọn phường/xã'
                            }
                            searchPlaceholder="Tìm kiếm phường/xã..."
                            emptyMessage="Không tìm thấy phường/xã."
                            disabled={loadingWards || !selectedDistrictId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 2 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Địa chỉ <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập địa chỉ" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Nhập email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập địa chỉ website" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Trạng thái hoạt động <Required />
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onValueChange={field.onChange}
                            className="mt-2 flex flex-row gap-6"
                          >
                            {STATUS_SO_TU_PHAP_OPTIONS.map(option => (
                              <div key={option.value} className="flex items-center gap-3">
                                <RadioGroupItem value={option.value.toString()} id={option.value.toString()} />
                                <Label htmlFor={option.value.toString()}>{option.label}</Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Ghi chú */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Ghi chú</h3>
              </div>
              <div className="px-6 py-4">
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea placeholder="Nhập ghi chú" rows={4} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Đính kèm */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Đính kèm</h3>
              </div>
              <div className="px-6 py-4">
                <UploadDocument
                  config={{ defaultPathPrefix: 'organization-units/attachments' }}
                  title="Tải lên tệp đính kèm"
                  description="Chọn các tệp cần đính kèm cho tổ chức."
                  multiple={true}
                  onUploadedFiles={(files: UploadedFileSummary[]) => {
                    // Lọc trùng theo url hoặc fileKey
                    const uniqueFiles = files.filter(
                      (f: UploadedFileSummary, i: number, arr: UploadedFileSummary[]) =>
                        arr.findIndex((x: UploadedFileSummary) => (x.url || x.fileKey) === (f.url || f.fileKey)) === i
                    )
                    setAttachedFiles(uniqueFiles)
                  }}
                />
                <div className="mt-2 flex flex-col gap-2">
                  {attachedFiles.map((file, idx) => (
                    <div
                      key={file.fileId || file.url || idx}
                      className="flex items-center gap-2 rounded border px-2 py-1"
                    >
                      <div className="flex-1 truncate">
                        {file.fileId ? (
                          <AcFilePreviewButton fileId={file.fileId} className="text-primary" />
                        ) : file.url ? (
                          <a href={file.url} target="_blank" rel="noreferrer" className="text-primary underline">
                            Xem file
                          </a>
                        ) : (
                          <span className="text-muted-foreground">{file.fileKey || file.url || file.fileId}</span>
                        )}
                      </div>
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        onClick={() => setAttachedFiles(files => files.filter((_, i) => i !== idx))}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
                <p className="mt-2 text-sm text-gray-500 italic">(Có thể tải lên nhiều file đính kèm)</p>
              </div>

              {/* Thông tin hợp đồng */}
              <div className="border-t border-b bg-gray-50 px-6 py-3">
                <h3 className="text-base font-medium">Thông tin hợp đồng</h3>
              </div>
              <div className="grid grid-cols-1 gap-4 px-6 py-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="contractNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Số hợp đồng <Required />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập số hợp đồng" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="issueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày hiệu lực <Required />
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="expiryDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày hết hạn <Required />
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="renewalMonths"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Số tháng gia hạn <Required />
                      </FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Nhập số tháng gia hạn" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="renewedExpiryDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày hết hạn sau gia hạn <Required />
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? formatDate(field.value) : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contractTerminationReason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Lý do chấm dứt hợp đồng <Required />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nhập lý do chấm dứt hợp đồng" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="px-6 py-4">
                <FormLabel>Tệp hợp đồng</FormLabel>
                {/* Chỉ hiển thị nút upload nếu chưa có hợp đồng */}
                {!contractFileInfo && !previewContract ? (
                  <UploadDocument
                    config={{ defaultPathPrefix: 'organization-units/contracts' }}
                    title="Tải lên hợp đồng"
                    description="Chọn tệp hợp đồng cho tổ chức."
                    multiple={false}
                    onUploadedFiles={(files: UploadedFileSummary[]) => {
                      if (files.length > 0 && files[0]) {
                        const file = files[0]
                        setPreviewContract({
                          fileId: file.fileId || '',
                          downloadUrl: file.url || '',
                          fileName: file.fileKey || '',
                          contentType: file.type || '',
                        })
                      }
                    }}
                  />
                ) : null}
                {contractFileInfo || previewContract ? (
                  <div className="border-muted flex flex-col gap-2 rounded-md border px-3 py-2 text-sm">
                    <div className="flex items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="font-semibold">
                          {previewContract?.fileName ??
                            contractFileInfo?.fileKey ??
                            previewContract?.downloadUrl ??
                            contractFileInfo?.url}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {(previewContract?.fileId ?? contractFileInfo?.fileId) ? (
                            <AcFilePreviewButton
                              fileId={(previewContract?.fileId ?? contractFileInfo?.fileId) || ''}
                              className="text-primary"
                            />
                          ) : (previewContract?.downloadUrl ?? contractFileInfo?.url) ? (
                            <a
                              href={previewContract?.downloadUrl ?? contractFileInfo?.url}
                              target="_blank"
                              rel="noreferrer"
                              className="text-primary underline"
                            >
                              Xem hợp đồng
                            </a>
                          ) : null}
                        </div>
                        {(previewContract?.fileId ?? contractFileInfo?.fileId) ? (
                          <div className="text-muted-foreground text-xs">
                            File ID: {(previewContract?.fileId ?? contractFileInfo?.fileId) || ''}
                          </div>
                        ) : null}
                      </div>
                      <Button type="button" size="icon" variant="ghost" onClick={() => setPreviewContract(null)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : null}
                <p className="mt-2 text-sm text-gray-500 italic">(Loại file: .pdf, .docx)</p>
              </div>
            </div>
          </form>
        </div>
      </Form>
    </div>
  )
}
