'use client'

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Building2 } from 'lucide-react'

import { OrganizationHierarchyView } from './organization-hierarchy-view'

interface OrganizationHierarchyModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  organizationId: string | null
  organizationName?: string
}

export function OrganizationHierarchyModal({
  open,
  onOpenChange,
  organizationId,
  organizationName,
}: OrganizationHierarchyModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="!h-[95vh] !max-h-none !w-[80vw] !max-w-none overflow-y-auto p-6 sm:!h-[95vh] sm:!w-[80vw] sm:!max-w-none"
        style={{
          width: '80vw !important',
          height: '95vh !important',
          maxWidth: 'none !important',
          maxHeight: 'none !important',
        }}
      >
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {organizationName ? `Cấu trúc tổ chức - ${organizationName}` : 'Cấu trúc tổ chức'}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <div className="min-h-0 flex-1">
          {organizationId && <OrganizationHierarchyView organizationId={organizationId} compact={true} />}
        </div>
      </DialogContent>
    </Dialog>
  )
}
