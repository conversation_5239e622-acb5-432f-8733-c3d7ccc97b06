'use client'

import { RenderDateTime } from '@/components/render-datetime'
import { useDeleteCategoryMutation } from '@/lib/hooks'
import { categoryApi, getCategoryId } from '@/lib/services/category-api'
import { Category } from '@/lib/services/types'
import { Any, AnyRecord } from '@/lib/types'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { useDebounce } from '@workspace/ui/hooks/use-debounce'
import { useExportExcel } from '@workspace/ui/hooks/use-export-excel'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { ExportButton } from '@workspace/ui/mi/export-button'
import { DataTable, DataTableColumnHeader, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { Edit, FolderTree, Loader2, PlusIcon, Trash2 } from 'lucide-react'
import moment from 'moment'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { CategoryForm } from './category-form'
import {
  CATEGORY_STATUS_LABELS,
  CATEGORY_STATUS_VARIANTS,
  CategoryStatus,
  getCategoryStatusOptions,
} from './category-status'
import { CATEGORY_TYPES, getCategoryTypeByValue } from './category-types'

interface CategoryPageProps {
  categoryType?: string
}

export function CategoryPage({ categoryType }: CategoryPageProps) {
  const t = useTranslations()
  const queryClient = useQueryClient()
  const { exportToExcel } = useExportExcel<Category>()

  // State for dialog management
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [isExporting, setIsExporting] = useState(false)

  // API Mutations
  const deleteMutation = useDeleteCategoryMutation()

  // Type config for badge variants
  const typeConfig: Record<string, { label: string; variant: Any }> = {
    CDH: { label: 'Chức danh', variant: 'default' },
    CVDN: { label: 'Chức vụ từng đảm nhiệm', variant: 'default' },
    DTK: { label: 'Dân tộc chi tiết', variant: 'secondary' },
    DTG: { label: 'Đối tượng', variant: 'outline' },
    HTDT: { label: 'Hình thức đào tạo', variant: 'default' },
    TG: { label: 'Hình thức trợ giúp', variant: 'default' },
    TGK: { label: 'Hình thức trợ giúp chi tiết', variant: 'default' },
    LV: { label: 'Lĩnh vực trợ giúp', variant: 'default' },
    LVC: { label: 'Lĩnh vực trợ giúp chi tiết', variant: 'default' },
    LHDT: { label: 'Loại hình đào tạo', variant: 'default' },
    NTC: { label: 'Nhóm tổ chức', variant: 'secondary' },
    NC: { label: 'Nơi cấp', variant: 'outline' },
    PX: { label: 'Phường/xã', variant: 'secondary' },
    QH: { label: 'Quận/huyện', variant: 'secondary' },
    TP: { label: 'Tỉnh/TP', variant: 'secondary' },
    TDCT: { label: 'Trình độ chính trị', variant: 'default' },
    TDNV: { label: 'Trình độ nghiệp vụ', variant: 'default' },
    TDNN: { label: 'Trình độ Ngoại ngữ', variant: 'default' },
    TDTH: { label: 'Trình độ tin học', variant: 'default' },
    VTCT: { label: 'Vị trí công tác', variant: 'outline' },
    PHU_CAP: { label: 'Phụ cấp', variant: 'secondary' },
    NGACH: { label: 'Ngạch', variant: 'secondary' },
    BAC_LUONG: { label: 'Bậc lương', variant: 'secondary' },
    HINH_THUC_HUONG_LUONG: {
      label: 'Hình thức hưởng lương',
      variant: 'secondary',
    },
    HINH_THUC_KHEN_THUONG: {
      label: 'Hình thức khen thưởng',
      variant: 'default',
    },
    HINH_THUC_KY_LUAT: { label: 'Hình thức kỷ luật', variant: 'destructive' },
    HANG_CHUC_DANH_NGHE_NGHIEP_VIEN_CHUC: {
      label: 'Hạng chức danh nghề nghiệp viên chức',
      variant: 'default',
    },
    NOI_DUNG_TAP_HUAN_HUONG_DAN: {
      label: 'Nội dung tập huấn, hướng dẫn',
      variant: 'default',
    },
    QUYEN_TRUY_CAP_DU_LIEU: {
      label: 'Quyền truy cập dữ liệu',
      variant: 'default',
    },

    // PM02 bổ sung
    SO_TU_PHAP: { label: 'Sở Tư pháp', variant: 'secondary' },
    TRUNG_TAM_TGPL: { label: 'Trung tâm TGPL', variant: 'default' },
    CHI_NHANH_TGPL: { label: 'Chi nhánh TGPL', variant: 'default' },

    // PM01
    CAC_LOAI_GIAY_TO_CHUNG_MINH: {
      label: 'Các loại giấy tờ chứng minh',
      variant: 'secondary',
    },
    LOAI_DON_VI_PHOI_HOP: {
      label: 'Loại đơn vị phối hợp',
      variant: 'secondary',
    },
    CSDL_HE_THONG_XAC_MINH: {
      label: 'CSDL/Hệ thống xác minh',
      variant: 'secondary',
    },
    DON_VI_PHOI_HOP_CAP_TW: {
      label: 'Đơn vị phối hợp cấp TW',
      variant: 'secondary',
    },
    DON_VI_PHOI_HOP_TAI_DON_VI: {
      label: 'Đơn vị phối hợp tại đơn vị',
      variant: 'secondary',
    },
    QUAN_LY_QUYEN_TRUY_CAP_DU_LIEU: {
      label: 'Quản lý quyền truy cập dữ liệu',
      variant: 'default',
    },
    LINH_VUC_TGPL: { label: 'Lĩnh vực TGPL', variant: 'default' },
    HINH_THUC_TGPL: { label: 'Hình thức TGPL', variant: 'default' },
    LOAI_HINH_DAO_TAO_TGPL: {
      label: 'Hình thức (loại hình) đào tạo TGPL',
      variant: 'default',
    },
    DOI_TUONG_TGPL: { label: 'Đối tượng TGPL', variant: 'outline' },
    GIAI_DOAN_THEO_HINH_THUC_TGPL: {
      label: 'Giai đoạn theo hình thức TGPL',
      variant: 'default',
    },
    CHI_TIEU_KHAO_SAT_LAY_Y_KIEN_CUA_NGUOI_KHAC_DUOC_TGPL: {
      label: 'Chỉ tiêu khảo sát lấy ý kiến của người được TGPL',
      variant: 'secondary',
    },
    NHOM_LOAI_TAI_LIEU: { label: 'Nhóm loại tài liệu', variant: 'secondary' },
    LOAI_TAI_LIEU: { label: 'Loại tài liệu', variant: 'secondary' },
    LOAI_TAI_LIEU_THEO_HINH_THUC_TGPL: {
      label: 'Loại tài liệu theo hình thức TGPL',
      variant: 'secondary',
    },
    CAC_LOAI_HINH_TIEP_NHAN_YEU_CAU: {
      label: 'Các loại hình tiếp nhận yêu cầu',
      variant: 'default',
    },
    KENH_TIEP_NHAN_YEU_CAU_TGPL_BAN_DAU: {
      label: 'Kênh tiếp nhận yêu cầu TGPL ban đầu',
      variant: 'default',
    },
    LOAI_HINH_CONG_VIEC_THUC_HIEN_TGPL: {
      label: 'Loại hình công việc thực hiện TGPL',
      variant: 'default',
    },
    LOAI_CONG_VIEC_THUC_HIEN_TGPL: {
      label: 'Loại công việc thực hiện TGPL',
      variant: 'default',
    },
    CONG_VIEC_THEO_LINH_VUC_HINH_THUC_THUC_HIEN_TGPL: {
      label: 'Công việc theo lĩnh vực/hình thức thực hiện TGPL',
      variant: 'default',
    },
    TAI_LIEU_BAT_BUOC_KHI_KET_THUC: {
      label: 'Tài liệu bắt buộc khi kết thúc',
      variant: 'secondary',
    },
    LOAI_LIEN_KET_VU_VIEC: {
      label: 'Loại liên kết vụ việc',
      variant: 'secondary',
    },

    // PM04
    CAC_NHOM_TAI_LIEU: { label: 'Các nhóm tài liệu', variant: 'secondary' },
    CAC_LOAI_HINH_TAI_LIEU_KHAC: {
      label: 'Các loại hình tài liệu khác',
      variant: 'secondary',
    },
    CONG_CU_TRA_CUU: { label: 'Công cụ tra cứu', variant: 'secondary' },
    KY_HIEU_HO_SO: { label: 'Ký hiệu hồ sơ', variant: 'secondary' },
    THOI_HAN_BAO_QUAN_HO_SO: {
      label: 'Thời hạn bảo quản hồ sơ',
      variant: 'secondary',
    },
    CHU_GIAI: { label: 'Chú giải', variant: 'secondary' },
    TRANG_THAI_HO_SO: { label: 'Trạng thái hồ sơ', variant: 'secondary' },
    THOI_HAN_NOP_LUU_HO_SO: {
      label: 'Thời hạn nộp lưu hồ sơ',
      variant: 'secondary',
    },
    TINH_TRANG_VAT_LY_CUA_VAN_BAN: {
      label: 'Tình trạng vật lý của văn bản',
      variant: 'secondary',
    },
    TINH_TRANG_VAT_LY_PHIM: {
      label: 'Tình trạng vật lý phim',
      variant: 'secondary',
    },
    CHAT_LUONG_PHIM_AM_THANH: {
      label: 'Chất lượng phim, âm thanh',
      variant: 'secondary',
    },
    GHI_CHU: { label: 'Ghi chú', variant: 'secondary' },
    LY_DO_HUY: { label: 'Lý do hủy', variant: 'destructive' },
    CO_QUAN_CHU_QUAN: { label: 'Cơ quan chủ quản', variant: 'default' },

    // PM06
    QUAN_LY_TICH_HOP_VOI_HE_THONG_PHO_BIEN_GIAO_DUC_PHAP_LUAT_LAY_THONG_TIN_DANH_MUC_DUNG_CHUNG: {
      label: 'Quản lý tích hợp với Hệ thống phổ biến giáo dục pháp luật lấy thông tin danh mục dùng chung',
      variant: 'default',
    },
  }

  // State for selected category ID for detail query
  const [, setSelectedCategoryId] = useState<string | null>(null)

  const [isLoadingDetail, setIsLoadingDetail] = useState(false)

  // Search state với debounce
  const [searchKeyword] = useState('')
  const debouncedSearchKeyword = useDebounce(searchKeyword, 500) // 0.5s delay

  // Handlers for CRUD operations
  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteMutation.mutateAsync(categoryId)

      setIsDeleteDialogOpen(false)
      setSelectedCategory(null)

      await queryClient.invalidateQueries({
        queryKey: ['enhanced-table', categoryType ? `categories-${categoryType}` : 'categories'],
      })
    } catch {
      // Error handling is done in the mutation
    }
  }

  // Handler to fetch and edit category
  const handleEditCategory = async (categoryId: string) => {
    try {
      setIsLoadingDetail(true)
      setSelectedCategoryId(categoryId)

      // Call API chi tiết để lấy đầy đủ thông tin
      const categoryDetail = await categoryApi.getCategoryDetail(categoryId)
      setSelectedCategory(categoryDetail)
      setIsEditDialogOpen(true)
    } catch {
      // Clear states on error
      setSelectedCategoryId('')
    } finally {
      setIsLoadingDetail(false)
    }
  }

  // Handler to fetch and delete category
  const handlePrepareDeleteCategory = async (categoryId: string) => {
    try {
      setIsLoadingDetail(true)
      setSelectedCategoryId(categoryId)

      const categoryDetail = await categoryApi.getCategoryDetail(categoryId)
      setSelectedCategory(categoryDetail)

      // Hiển thị confirm dialog
      setIsDeleteDialogOpen(true)
    } catch {
      toast.error('Không thể lấy thông tin danh mục để xóa')
      // Clear states on error
      setSelectedCategoryId('')
    } finally {
      setIsLoadingDetail(false)
    }
  }

  const handleDeleteSelectedCategory = async () => {
    if (!selectedCategory) {
      toast.error('Không có danh mục nào được chọn để xóa')

      return
    }

    const categoryId = selectedCategory.id || selectedCategory._id || selectedCategory.categoryId

    if (!categoryId) {
      toast.error('Không tìm thấy ID danh mục để xóa')

      return
    }

    await handleDeleteCategory(categoryId)
  }

  const handleExportExcel = async () => {
    try {
      setIsExporting(true)
      const currentState = table.getState()
      const filterObject: AnyRecord = {}

      currentState.columnFilters.forEach(f => {
        if (Array.isArray(f.value) && f.value.length > 0) {
          filterObject[f.id] = f.value.join(',')
        } else if (typeof f.value === 'string' && f.value) {
          filterObject[f.id] = f.value
        } else if (f.value && typeof f.value === 'object' && 'from' in f.value) {
          const dateRange = f.value as {
            from?: Date
            to?: Date
          }

          if (dateRange.from) {
            const fromStr = moment(dateRange.from).format('YYYY-MM-DD')
            const toStr = moment(dateRange.to || dateRange.from).format('YYYY-MM-DD')
            filterObject[f.id] = `${fromStr},${toStr}`
          }
        }
      })

      if (categoryType) {
        filterObject.type = categoryType
      }

      const queryParams = new URLSearchParams()

      if (Object.keys(filterObject).length > 0) {
        queryParams.set('filter', JSON.stringify(filterObject))
      }

      if (currentState.sorting.length > 0) {
        const sort = currentState.sorting[0]

        if (sort) {
          queryParams.set('sortBy', sort.id)
          queryParams.set('sortDir', sort.desc ? 'desc' : 'asc')
        }
      }

      if (currentState.globalFilter) {
        queryParams.set('keyword', currentState.globalFilter)
      }

      const response = await fetch(`/ac-apis/categories/method/all?${queryParams.toString()}`)

      if (!response.ok) throw new Error('Failed to fetch data')

      const allData = await response.json()

      const categoryTypeName = CATEGORY_TYPES.find(type => type.value === categoryType)?.label || 'Danh mục'

      const removeVietnameseDiacritics = (str: string) => {
        return str
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .replace(/đ/g, 'd')
          .replace(/Đ/g, 'D')
          .toLowerCase()
      }

      exportToExcel(allData, {
        fileName: `danh_muc_${removeVietnameseDiacritics(categoryTypeName).replace(/\s+/g, '_')}_${moment().format('YYYYMMDD_HHmmss')}`,
        sheetName: categoryTypeName,
        headers: ['Tên danh mục', 'Mã', 'Danh mục cha', 'Mô tả', 'Trạng thái', 'Ngày tạo', 'Ngày cập nhật'],
        dataKey: ['name', 'code', 'parent.name', 'description', 'status', 'createdAt', 'updatedAt'],
      })

      toast.success('Xuất file Excel thành công')
    } catch {
      toast.error('Có lỗi xảy ra khi xuất file Excel')
    } finally {
      setIsExporting(false)
    }
  }

  const baseColumns: ColumnDef<Category>[] = [
    selectColumn(t),
    {
      accessorKey: 'name',
      meta: {
        title: 'Tên danh mục',
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tên danh mục" />,
      cell: ({ row }) => {
        return <div className="font-medium">{row.original.name}</div>
      },
    },
    {
      accessorKey: 'code',
      meta: {
        title: 'Mã',
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Mã" />,
      cell: ({ row }) => {
        return (
          <Badge variant="outline" className="font-mono">
            {row.original.code}
          </Badge>
        )
      },
    },
    {
      accessorKey: 'description',
      meta: {
        title: 'Mô tả',
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Mô tả" />,
      cell: ({ row }) => {
        const description = row.original.description

        if (!description) {
          return <span className="text-muted-foreground">—</span>
        }

        return (
          <div className="max-w-[300px] truncate" title={description}>
            {description}
          </div>
        )
      },
    },
    {
      accessorKey: 'parent.name',
      meta: {
        title: 'Danh mục cha',
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Danh mục cha" />,
      cell: ({ row }) => {
        const parent = row.original.parent

        return parent ? (
          <div className="flex items-center gap-1">
            <FolderTree className="text-muted-foreground h-3 w-3" />
            <span>{parent.name}</span>
          </div>
        ) : (
          <span className="text-muted-foreground">—</span>
        )
      },
    },
  ]

  // Add type column only when categoryType is not specified
  if (!categoryType) {
    baseColumns.push({
      accessorKey: 'type',
      meta: {
        title: 'Kiểu',
        filter: {
          type: 'select',
          options: CATEGORY_TYPES.map(type => ({
            label: type.label,
            value: type.value,
          })),
        },
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Kiểu" />,
      cell: ({ row }) => {
        const type = row.original.type
        const config = typeConfig[type]

        if (!config) {
          return <span className="text-muted-foreground">{type}</span>
        }

        return <Badge variant={config.variant}>{config.label}</Badge>
      },
    })
  }

  // Add status column
  baseColumns.push({
    accessorKey: 'status',
    meta: {
      title: 'Trạng thái',
      filter: {
        type: 'select',
        options: getCategoryStatusOptions(),
      },
    },
    header: ({ column }) => <DataTableColumnHeader column={column} title="Trạng thái" />,
    cell: ({ row }) => {
      const status = row.original.status || CategoryStatus.INIT
      const label = CATEGORY_STATUS_LABELS[status]
      const variant = CATEGORY_STATUS_VARIANTS[status]

      if (!label) {
        return <span className="text-muted-foreground">{status}</span>
      }

      return <Badge variant={variant}>{label}</Badge>
    },
  })

  const columns: ColumnDef<Category>[] = [
    ...baseColumns,

    {
      accessorKey: 'createdAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: 'Ngày tạo',
        filter: {
          type: 'date-range',
        },
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày tạo" />,
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('createdAt')} showTime={showTime} />
      },
    },
    {
      accessorKey: 'updatedAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: 'Ngày cập nhật',
        filter: {
          type: 'date-range',
        },
      },
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày cập nhật" />,
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('updatedAt')} showTime={showTime} />
      },
    },
    {
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const category = row.original

        const categoryId = getCategoryId(category)

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!categoryId) {
                  toast.error('Không tìm thấy ID danh mục')

                  return
                }
                handleEditCategory(categoryId)
              }}
              disabled={isLoadingDetail}
            >
              {isLoadingDetail ? <Loader2 className="h-4 w-4 animate-spin" /> : <Edit className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!categoryId) {
                  toast.error('Không tìm thấy ID danh mục')

                  return
                }
                handlePrepareDeleteCategory(categoryId)
              }}
              disabled={isLoadingDetail}
            >
              {isLoadingDetail ? (
                <Loader2 className="text-destructive h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="text-destructive h-4 w-4" />
              )}
            </Button>
          </div>
        )
      },
    },
  ]

  const { table } = useEnhancedTable<Category>({
    columns,
    pageName: categoryType ? `categories-${categoryType}` : 'categories',
    keyObject: categoryType ? { categoryType } : {},
    queryFn: async state => {
      // Custom params generation with filter as JSON object
      const params = new URLSearchParams()

      // Add pagination
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))

      // Add sorting
      if (state.sorting.length > 0) {
        const sort = state.sorting[0]

        if (sort) {
          params.append('orderBy', sort.id)
          params.append('orderDir', sort.desc ? 'DESC' : 'ASC')
        }
      }

      // Add keyword search
      if (debouncedSearchKeyword) {
        params.append('keyword', debouncedSearchKeyword)
      }

      // Build filter object
      const filterObj: AnyRecord = {}

      // Add default category type filter if specified
      if (categoryType) {
        filterObj.type = categoryType
      }

      state.filter.forEach(filter => {
        if (Array.isArray(filter.value) && filter.value.length > 0) {
          filterObj[filter.id] = filter.value.join(',')
        } else if (typeof filter.value === 'string' && filter.value) {
          filterObj[filter.id] = filter.value
        } else if (filter.value && typeof filter.value === 'object' && 'from' in filter.value) {
          // Date range using moment
          const dateRange = filter.value as { from?: Date; to?: Date }

          if (dateRange.from) {
            // Format date only
            const fromStr = moment(dateRange.from).format('YYYY-MM-DD')
            const toStr = moment(dateRange.to || dateRange.from).format('YYYY-MM-DD')

            filterObj[filter.id] = `${fromStr},${toStr}`
          }
        }
      })

      // Add filter as JSON string
      if (Object.keys(filterObj).length > 0) {
        params.append('filter', JSON.stringify(filterObj))
      }

      // Use the API service instead of direct fetch
      const filter: AnyRecord = {}

      // Add category type to filter if specified
      if (categoryType) {
        filter.type = categoryType
      }

      // Add other filters from filterObj
      Object.entries(filterObj).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filter[key] = value
        }
      })

      const apiParams = {
        page: state.pagination.pageIndex + 1,
        pageSize: state.pagination.pageSize,
        keyword: state.keyword,
        orderBy: state.sorting[0]?.id,
        orderDir: state.sorting[0]?.desc ? ('DESC' as const) : ('ASC' as const),
        filter: Object.keys(filter).length > 0 ? filter : undefined,
      }

      const response = await categoryApi.getCategories(apiParams)

      // Handle different possible response formats
      const responseData = response as Any

      // Try different possible field names for data array
      const items =
        responseData.data ||
        responseData.content ||
        responseData.items ||
        responseData.list ||
        (Array.isArray(responseData) ? responseData : [])

      // Try different possible field names for total count
      const totalItems =
        responseData.total ||
        responseData.totalElements ||
        responseData.totalCount ||
        responseData.count ||
        (Array.isArray(responseData) ? responseData.length : 0)

      // Try different possible field names for page size
      const pageSize = responseData.pageSize || responseData.size || responseData.limit || apiParams.pageSize || 10

      // Transform response to match useEnhancedTable expected format
      const transformedResponse = {
        items: items || [],
        totalItems: totalItems || 0,
        totalPages: Math.ceil((totalItems || 0) / pageSize),
      }

      return transformedResponse
    },
    initialState: {
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['categories', debouncedSearchKeyword, categoryType || ''],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={(() => {
          const breadcrumbs = [
            {
              label: 'Danh mục',
              href: categoryType ? '#' : '/categories',
            },
          ]

          if (categoryType) {
            const typeInfo = getCategoryTypeByValue(categoryType)

            if (typeInfo) {
              breadcrumbs.push({
                label: typeInfo.label,
                href: `/categories/${typeInfo.slug}`,
              })
            }
          }

          return breadcrumbs
        })()}
      >
        <AdminPageContent
          title="Danh sách danh mục"
          subtitle=""
          actions={[
            <ExportButton
              key="export"
              variant="outline"
              isExporting={isExporting}
              onClick={handleExportExcel}
              disabled={isExporting}
            />,
            <AddButton
              tooltip="Tạo danh mục"
              key="create"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={() => setIsCreateDialogOpen(true)}
            />,
          ]}
        >
          {/* <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Folder className="h-5 w-5" />
                  Danh mục
                </CardTitle>
                <div className="flex gap-2">
                  {categoryType && (EXPORTABLE_CATEGORY_TYPES as readonly string[]).includes(categoryType) && (
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={isExporting}
                      onClick={async () => {
                        try {
                          setIsExporting(true)
                          const currentState = table.getState()
                          const filterObject: AnyRecord = {}

                          currentState.columnFilters.forEach(f => {
                            if (Array.isArray(f.value) && f.value.length > 0) {
                              filterObject[f.id] = f.value.join(',')
                            } else if (typeof f.value === 'string' && f.value) {
                              filterObject[f.id] = f.value
                            } else if (f.value && typeof f.value === 'object' && 'from' in f.value) {
                              const dateRange = f.value as {
                                from?: Date
                                to?: Date
                              }

                              if (dateRange.from) {
                                const fromStr = moment(dateRange.from).format('YYYY-MM-DD')
                                const toStr = moment(dateRange.to || dateRange.from).format('YYYY-MM-DD')
                                filterObject[f.id] = `${fromStr},${toStr}`
                              }
                            }
                          })

                          if (categoryType) {
                            filterObject.type = categoryType
                          }

                          const queryParams = new URLSearchParams()

                          if (Object.keys(filterObject).length > 0) {
                            queryParams.set('filter', JSON.stringify(filterObject))
                          }

                          if (currentState.sorting.length > 0) {
                            const sort = currentState.sorting[0]

                            if (sort) {
                              queryParams.set('sortBy', sort.id)
                              queryParams.set('sortDir', sort.desc ? 'desc' : 'asc')
                            }
                          }

                          if (currentState.globalFilter) {
                            queryParams.set('keyword', currentState.globalFilter)
                          }

                          const response = await fetch(`/ac-apis/categories/method/all?${queryParams.toString()}`)

                          if (!response.ok) throw new Error('Failed to fetch data')

                          const allData = await response.json()

                          const categoryTypeName =
                            CATEGORY_TYPES.find(type => type.value === categoryType)?.label || 'Danh mục'

                          const removeVietnameseDiacritics = (str: string) => {
                            return str
                              .normalize('NFD')
                              .replace(/[\u0300-\u036f]/g, '')
                              .replace(/đ/g, 'd')
                              .replace(/Đ/g, 'D')
                              .toLowerCase()
                          }

                          exportToExcel(allData, {
                            fileName: `danh_muc_${removeVietnameseDiacritics(categoryTypeName).replace(/\s+/g, '_')}_${moment().format('YYYYMMDD_HHmmss')}`,
                            sheetName: categoryTypeName,
                            headers: [
                              'Tên danh mục',
                              'Mã',
                              'Danh mục cha',
                              'Mô tả',
                              'Trạng thái',
                              'Ngày tạo',
                              'Ngày cập nhật',
                            ],
                            dataKey: ['name', 'code', 'parent.name', 'description', 'status', 'createdAt', 'updatedAt'],
                          })

                          toast.success('Xuất file Excel thành công')
                        } catch {
                          toast.error('Có lỗi xảy ra khi xuất file Excel')
                        } finally {
                          setIsExporting(false)
                        }
                      }}
                    >
                      {isExporting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                      {isExporting ? 'Đang xuất...' : 'Xuất Excel'}
                    </Button>
                  )}
                  <Button size="sm" onClick={() => setIsCreateDialogOpen(true)}>
                    <PlusIcon className="h-4 w-4" />
                    Tạo danh mục
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center space-x-2">
                <Input
                  placeholder="Tìm kiếm danh mục..."
                  value={searchKeyword}
                  onChange={e => setSearchKeyword(e.target.value)}
                  className="max-w-sm"
                />
              </div>
              <DataTable table={table} />
            </CardContent>
          </Card> */}

          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create Category Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Tạo danh mục mới</DialogTitle>
          </DialogHeader>
          <CategoryForm
            categoryType={categoryType}
            onSuccess={async () => {
              setIsCreateDialogOpen(false)

              await queryClient.invalidateQueries({
                queryKey: ['enhanced-table', categoryType ? `categories-${categoryType}` : 'categories'],
              })
            }}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa danh mục</DialogTitle>
          </DialogHeader>
          <CategoryForm
            initialData={selectedCategory}
            categoryType={categoryType}
            onSuccess={async () => {
              setIsEditDialogOpen(false)
              setSelectedCategory(null) // Clear selected category after update
              setSelectedCategoryId('') // Clear selected category ID
              setIsLoadingDetail(false) // Clear loading state

              await queryClient.invalidateQueries({
                queryKey: ['enhanced-table', categoryType ? `categories-${categoryType}` : 'categories'],
              })
            }}
            onCancel={() => setIsEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Category Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xóa danh mục</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa danh mục &quot;{selectedCategory?.name}&quot;? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSelectedCategory}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
