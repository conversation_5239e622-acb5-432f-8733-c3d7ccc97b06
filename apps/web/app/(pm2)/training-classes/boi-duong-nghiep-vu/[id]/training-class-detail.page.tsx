'use client'

import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { Organization } from '@/constants/so-tu-phap'
import { Staff, STAFF_TYPE_OPTIONS } from '@/constants/staff'
import { Category, StaffType, TrainingClass } from '@/lib/types'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { toast } from '@workspace/ui/components/toast'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Combobox } from '@workspace/ui/mi/combobox'
import { DataTable, selectColumn } from '@workspace/ui/mi/table'
import { ArrowLeft, Edit, Plus, Users } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

import { TrainingClassModal } from '../training-class-modal'

interface TrainingClassDetailPageProps {
  id: string
}

export function TrainingClassDetailPage({ id }: TrainingClassDetailPageProps) {
  const router = useRouter()
  const t = useTranslations()
  const queryClient = useQueryClient()
  const [trainingClass, setTrainingClass] = useState<TrainingClass | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false)

  // Staff modal states
  const [selectedOrganizationUnitId, setSelectedOrganizationUnitId] = useState('')
  const [selectedActivityStatusId, setSelectedActivityStatusId] = useState('')
  const [staffOptions, setStaffOptions] = useState<{ value: string; label: string }[]>([])
  const [loadingStaff, setLoadingStaff] = useState(false)
  const [selectedStaffIds, setSelectedStaffIds] = useState<string[]>([])
  const [organizationUnits, setOrganizationUnits] = useState<Organization[]>([])
  const [loadingOrganizationUnits, setLoadingOrganizationUnits] = useState(false)
  const [activityStatuses, setActivityStatuses] = useState<Category[]>([])
  const [loadingActivityStatuses, setLoadingActivityStatuses] = useState(false)

  const fetchTrainingClassDetail = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/ac-apis/training-class/${id}`)

      if (!response.ok) {
        throw new Error('Không thể tải thông tin chi tiết')
      }
      const data = await response.json()
      setTrainingClass(data)
    } catch (error) {
      console.error('Error fetching training class detail:', error)
      toast.error('Không thể tải thông tin chi tiết')
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    fetchTrainingClassDetail()
  }, [fetchTrainingClassDetail])

  const handleEdit = () => {
    setIsEditDialogOpen(true)
  }

  const handleEditSuccess = useCallback(() => {
    setIsEditDialogOpen(false)
    fetchTrainingClassDetail()
    toast.success('Cập nhật lớp bồi dưỡng thành công')
  }, [fetchTrainingClassDetail])

  // Staff management functions
  const fetchOrganizationUnits = async () => {
    try {
      setLoadingOrganizationUnits(true)
      const response = await fetch('/ac-apis/organization-units/method/all-by-type/3')

      if (!response.ok) {
        throw new Error('Không thể tải danh sách trung tâm')
      }
      const data = await response.json()
      setOrganizationUnits(Array.isArray(data) ? data : data?.items || [])
    } catch (error) {
      console.error('Error fetching organization units:', error)
      toast.error('Không thể tải danh sách trung tâm')
      setOrganizationUnits([])
    } finally {
      setLoadingOrganizationUnits(false)
    }
  }

  const fetchActivityStatuses = async () => {
    try {
      setLoadingActivityStatuses(true)
      const filter = { type: 'TRANG_THAI_HOAT_DONG_CUA_CAN_BO' }
      const query = encodeURIComponent(JSON.stringify(filter))

      const response = await fetch(`/ac-apis/categories?filter=${query}`)

      if (!response.ok) {
        throw new Error('Không thể tải danh sách trạng thái hoạt động')
      }

      const data = await response.json()
      setActivityStatuses(Array.isArray(data) ? data : data?.items || [])
    } catch (error) {
      console.error('Error fetching activity statuses:', error)
      toast.error('Không thể tải danh sách trạng thái hoạt động')
      setActivityStatuses([])
    } finally {
      setLoadingActivityStatuses(false)
    }
  }

  const fetchStaffList = useCallback(async () => {
    try {
      setLoadingStaff(true)
      const filter: Record<string, string> = { organizationUnitId: selectedOrganizationUnitId }

      if (selectedActivityStatusId) {
        filter.activityStatusId = selectedActivityStatusId
      }
      const query = encodeURIComponent(JSON.stringify(filter))
      const response = await fetch(`/ac-apis/staffs/method/all?filter=${query}`)

      if (!response.ok) {
        throw new Error('Không thể tải danh sách cán bộ')
      }

      const data = await response.json()
      const items = Array.isArray(data) ? data : data?.items || []
      const options = items
        .map((s: Staff) => ({ value: String(s.id || ''), label: s.fullName || '' }))
        .filter((o: { value: string; label: string }) => o.value && o.label)
      setStaffOptions(options)
    } catch (error) {
      console.error('Error fetching staff list:', error)
      toast.error('Không thể tải danh sách cán bộ')
      setStaffOptions([])
    } finally {
      setLoadingStaff(false)
    }
  }, [selectedOrganizationUnitId, selectedActivityStatusId])

  // Handle filter changes
  const handleOrganizationUnitChange = (value: string) => {
    setSelectedOrganizationUnitId(value)
    // Reset dependent data
    setStaffOptions([])
    setSelectedActivityStatusId('')
  }

  const handleActivityStatusChange = (value: string) => {
    setSelectedActivityStatusId(value)
  }

  // Fetch staff when filters change (with debounce for keyword)
  useEffect(() => {
    if (selectedOrganizationUnitId) {
      fetchStaffList()
    }
  }, [selectedOrganizationUnitId, selectedActivityStatusId, fetchStaffList])

  // Load organization units and activity statuses when modal opens
  useEffect(() => {
    if (isAddStaffDialogOpen) {
      fetchOrganizationUnits()
      fetchActivityStatuses()
    }
  }, [isAddStaffDialogOpen])

  const handleAddStaff = () => {
    setIsAddStaffDialogOpen(true)
  }

  const handleAddStaffSuccess = async () => {
    if (selectedStaffIds.length === 0) {
      toast.error('Vui lòng chọn ít nhất một cán bộ')

      return
    }

    try {
      setLoadingStaff(true)

      // Call API to add staff to training class
      const response = await fetch(`/ac-apis/training-class/${id}/participants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          staffIds: selectedStaffIds,
        }),
      })

      if (!response.ok) {
        throw new Error('Không thể thêm cán bộ vào lớp đào tạo')
      }

      toast.success(`Đã thêm ${selectedStaffIds.length} cán bộ vào lớp đào tạo`)

      // Reset form and close modal
      setSelectedOrganizationUnitId('')
      setSelectedActivityStatusId('')
      setStaffOptions([])
      setSelectedStaffIds([])
      setIsAddStaffDialogOpen(false)

      queryClient.invalidateQueries({
        queryKey: ['training-class-staff', id],
      })
    } catch (error) {
      console.error('Error adding staff:', error)
      toast.error('Không thể thêm cán bộ vào lớp đào tạo')
    } finally {
      setLoadingStaff(false)
    }
  }

  const handleBack = () => {
    router.push('training-class/boi-duong-nghiep-vu')
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Chưa cập nhật'

    return new Date(dateString).toLocaleDateString('vi-VN')
  }

  // Staff table columns for the right side
  const staffColumns: ColumnDef<Staff>[] = [
    selectColumn(t, {}),
    {
      accessorKey: 'fullName',
      meta: {
        title: 'Họ và tên',
        filter: {
          type: 'text',
          placeholder: 'Tìm kiếm họ tên',
          debounceMs: 200,
        },
      },
      cell: ({ row }) => {
        return (
          <RenderInteractiveLink
            onClick={() => {
              // Navigate to staff detail if needed
              console.log('Navigate to staff:', row.original.id)
            }}
          >
            {row.original.fullName}
          </RenderInteractiveLink>
        )
      },
    },
    {
      accessorKey: 'email',
      meta: {
        title: 'Email',
      },
      cell: ({ row }) => {
        return <span className="text-sm">{row.original.email || '-'}</span>
      },
    },
    {
      accessorKey: 'phone',
      meta: {
        title: 'Số điện thoại',
      },
      cell: ({ row }) => {
        return <span className="text-sm">{row.original.phone || '-'}</span>
      },
    },
    {
      accessorKey: 'staffType',
      meta: {
        title: 'Loại nhân sự',
      },
      cell: ({ row }) => {
        const type = row.original.staffType as StaffType | undefined

        const label = STAFF_TYPE_OPTIONS.find(opt => opt.value === type)?.label || '-'

        return <span className="text-sm">{label}</span>
      },
    },
    {
      accessorKey: 'dateOfBirth',
      meta: {
        title: 'Ngày sinh',
      },
      cell: ({ row }) => {
        return (
          <span className="text-sm">
            <RenderDateTime datetime={String(row.original.dateOfBirth)} showTime={false} />
          </span>
        )
      },
    },
  ]

  // Staff table configuration
  const { table } = useEnhancedTable<TrainingClass>({
    columns: staffColumns as ColumnDef<TrainingClass>[],
    pageName: `training-class-staff-${id}`,
    keyObject: { trainingClassId: id },
    queryFn: async state => {
      const params = generateQueryParams(state, {
        baseParams: {},
      })

      try {
        const res = await fetch(`/ac-apis/training-class/${id}/participants?${params}`, {
          cache: 'no-store',
        })

        if (!res.ok) {
          console.error('API error:', res.status, res.statusText)

          return { items: [], totalPages: 0, totalItems: 0 }
        }
        const data = await res.json()

        return {
          items: data,
          totalPages: Math.ceil(data.length / (state.pagination?.pageSize || 10)),
          totalItems: data.length,
        }
      } catch (e) {
        console.error('Error fetching staff:', e)

        return { items: [], totalPages: 0, totalItems: 0 }
      }
    },
    initialState: {
      sorting: [{ id: 'fullName', desc: false }],
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
    },
    enabled: true, // Always enabled for fake data
    queryKey: ['training-class-staff', id],
  })

  if (loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tham gia', href: 'training-class' },
          { label: 'Bồi dưỡng nghiệp vụ', href: 'training-class/boi-duong-nghiep-vu' },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
            <p className="text-muted-foreground mt-4">Đang tải thông tin...</p>
          </div>
        </div>
      </AdminPageLayout>
    )
  }

  if (!trainingClass) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Quản lý tham gia', href: 'training-class' },
          { label: 'Bồi dưỡng nghiệp vụ', href: 'training-class/boi-duong-nghiep-vu' },
          { label: 'Chi tiết', href: '#' },
        ]}
      >
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <p className="text-muted-foreground">Không tìm thấy thông tin lớp bồi dưỡng</p>
            <Button variant="outline" onClick={handleBack} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </div>
        </div>
      </AdminPageLayout>
    )
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Quản lý tham gia', href: 'training-class' },
        { label: 'Bồi dưỡng nghiệp vụ', href: 'training-class/boi-duong-nghiep-vu' },
        { label: 'Chi tiết', href: '#' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with title and actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleBack} size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-semibold">Thông tin chi tiết lớp bồi dưỡng</h1>
          </div>
          <Button onClick={handleEdit} variant="outline" size="sm">
            <Edit className="mr-2 h-4 w-4" />
            Cập nhật
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-[30%_70%]">
          {/* Left block - Training Class info */}
          <div className="overflow-hidden rounded-lg border border-[#E5E5E5]">
            {/* Header with gradient background */}
            <div
              className="px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <h2 className="text-base font-medium">
                Tên lớp đào tạo: <span className="font-semibold">{trainingClass.trainingClassName}</span>
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Tổ chức đào tạo: {trainingClass.trainingOrganization?.name || 'Chưa cập nhật'}
              </p>
            </div>

            {/* Content */}
            <div className="space-y-8 p-6">
              {/* Thông tin cơ bản */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Thông tin cơ bản</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Tên lớp đào tạo</span>
                    <span className="font-medium">{trainingClass.trainingClassName}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Tổ chức đào tạo</span>
                    <span className="font-medium">{trainingClass.trainingOrganization?.name || 'Chưa cập nhật'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Loại hình đào tạo</span>
                    <span className="font-medium">{trainingClass.trainingType?.name || 'Chưa cập nhật'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Hình thức đào tạo</span>
                    <span className="font-medium">{trainingClass.trainingFormat?.name || 'Chưa cập nhật'}</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Số giờ đào tạo</span>
                    <span className="font-medium">{trainingClass.trainingHours} giờ</span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Chứng chỉ</span>
                    <span className="font-medium">{trainingClass.certificate || 'Chưa cập nhật'}</span>
                  </div>
                </div>
              </div>

              {/* Thông tin thời gian */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Thông tin thời gian</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Ngày bắt đầu</span>
                    <span className="font-medium">
                      <RenderDateTime datetime={trainingClass.startDate} showTime={false} />
                    </span>
                  </div>
                  <div className="grid grid-cols-[180px_1fr] gap-4">
                    <span className="text-gray-600">Ngày kết thúc</span>
                    <span className="font-medium">
                      <RenderDateTime datetime={trainingClass.endDate} showTime={false} />
                    </span>
                  </div>
                </div>
              </div>

              {/* Ghi chú */}
              <div>
                <h3 className="mb-4 text-base font-semibold">Ghi chú</h3>
                <p className="text-gray-400 italic">{trainingClass.note || 'Chưa cập nhật'}</p>
              </div>
            </div>

            {/* Footer with update date */}
            <div
              className="px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <p className="text-sm text-gray-600">Ngày cập nhật: {formatDate(trainingClass.updatedAt)}</p>
            </div>
          </div>

          {/* Right block - Staff list */}
          <div className="flex flex-col overflow-hidden rounded-lg border border-[#E5E5E5]">
            {/* Header with gradient background */}
            <div
              className="flex items-center justify-between px-6 py-4"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            >
              <h2 className="flex items-center gap-2 text-base font-medium">
                <Users className="h-4 w-4" />
                Danh sách cán bộ tham gia
              </h2>
              <Button onClick={handleAddStaff} variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Thêm mới
              </Button>
            </div>

            {/* Content - Staff Table */}
            <div className="flex-1 p-6">
              {table ? (
                <div className="space-y-4">
                  <div className="text-muted-foreground text-sm">
                    Tổng số: {table.getFilteredRowModel().rows.length} cán bộ
                  </div>
                  <DataTable table={table} />
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">Đang tải table...</p>
                </div>
              )}
            </div>

            {/* Footer border */}
            <div
              className="h-[52px]"
              style={{
                background:
                  'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F5F5F5',
              }}
            />
          </div>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="flex max-h-[85vh] min-w-[520px] flex-col gap-0 p-0 sm:max-w-[700px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Chỉnh sửa lớp bồi dưỡng</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <div className="overflow-y-auto">
            <TrainingClassModal
              isOpen={isEditDialogOpen}
              initialData={trainingClass}
              onSuccess={handleEditSuccess}
              onClose={() => setIsEditDialogOpen(false)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Staff Dialog */}
      <Dialog open={isAddStaffDialogOpen} onOpenChange={setIsAddStaffDialogOpen}>
        <DialogContent className="flex max-h-[85vh] min-w-[480px] flex-col gap-0 p-0 sm:max-w-[600px]">
          <DialogHeader className="shrink-0 border-b px-6 py-4">
            <DialogTitle>Thêm cán bộ</DialogTitle>
            <DialogDescription>Thêm cán bộ, nhân viên vào lớp đào tạo</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto p-6">
            <div className="space-y-4">
              {/* Bước 1: Chọn trung tâm */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Bước 1: Chọn trung tâm</label>
                <select
                  className="w-full rounded-md border border-gray-300 p-2"
                  value={selectedOrganizationUnitId}
                  onChange={e => handleOrganizationUnitChange(e.target.value)}
                  disabled={loadingOrganizationUnits}
                >
                  <option value="">{loadingOrganizationUnits ? 'Đang tải...' : 'Chọn trung tâm'}</option>
                  {organizationUnits.map(unit => (
                    <option key={unit.id} value={unit.id}>
                      {unit.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Bước 2: Chọn cán bộ */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Bước 2: Chọn cán bộ</label>
                <Combobox
                  multiple
                  options={staffOptions}
                  value={selectedStaffIds}
                  onChange={value => {
                    if (Array.isArray(value)) setSelectedStaffIds(value as string[])
                  }}
                  placeholder={
                    selectedOrganizationUnitId ? (loadingStaff ? 'Đang tải...' : 'Chọn cán bộ') : 'Chọn trung tâm trước'
                  }
                />

                {/* Danh sách đã chọn */}
                {selectedStaffIds.length > 0 && (
                  <div className="rounded-md border bg-white p-3">
                    <div className="mb-2 text-sm font-medium">Đã chọn ({selectedStaffIds.length})</div>
                    <div className="flex flex-wrap gap-2">
                      {selectedStaffIds.map((id: string) => {
                        const label =
                          staffOptions.find((o: { value: string; label: string }) => o.value === id)?.label || id

                        return (
                          <span
                            key={id}
                            className="inline-flex items-center gap-2 rounded-full border px-3 py-1 text-xs"
                          >
                            {label}
                            <Button
                              type="button"
                              className="text-gray-500 hover:text-red-600"
                              onClick={() => setSelectedStaffIds(prev => prev.filter(x => x !== id))}
                              aria-label="Remove"
                            >
                              ×
                            </Button>
                          </span>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* Bước 3: Trạng thái hoạt động */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Bước 3: Chọn trạng thái hoạt động</label>
                <Select
                  value={selectedActivityStatusId}
                  onValueChange={val => handleActivityStatusChange(val)}
                  disabled={!selectedOrganizationUnitId || loadingActivityStatuses}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={loadingActivityStatuses ? 'Đang tải...' : 'Tất cả trạng thái'} />
                  </SelectTrigger>
                  <SelectContent>
                    {activityStatuses.map(status => (
                      <SelectItem key={status.categoryId} value={status.categoryId}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Lưu ý */}
              <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
                <p className="flex items-start gap-2 text-sm text-blue-700">
                  <span className="text-blue-500">ℹ</span>
                  Lưu ý: Chọn lần lượt theo thứ tự từ trên xuống
                </p>
              </div>
            </div>

            {/* Buttons */}
            <div className="mt-6 flex justify-end gap-3 border-t pt-4">
              <Button variant="outline" onClick={() => setIsAddStaffDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={handleAddStaffSuccess} disabled={selectedStaffIds.length === 0 || loadingStaff}>
                {loadingStaff ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                    Đang thêm...
                  </>
                ) : (
                  `Thêm ${selectedStaffIds.length > 0 ? `(${selectedStaffIds.length})` : ''}`
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </AdminPageLayout>
  )
}
