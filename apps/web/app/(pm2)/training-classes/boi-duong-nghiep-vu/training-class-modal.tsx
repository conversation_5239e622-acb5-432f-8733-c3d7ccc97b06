'use client'

import { Organization } from '@/constants/so-tu-phap'
import { Category } from '@/lib/services/types'
import { TrainingClass } from '@/lib/types'
import { categoryService } from '@/services/categoryService'
import { organizationService } from '@/services/organizationService'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { toast } from '@workspace/ui/components/toast'
import { cn } from '@workspace/ui/lib/utils'
import { Combobox } from '@workspace/ui/mi/combobox'
import { Required } from '@workspace/ui/mi/required'
import { CalendarIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z
  .object({
    trainingClassName: z.string().min(1, 'Tên lớp đào tạo là bắt buộc'),
    trainingOrganizationId: z.string().min(1, 'Tổ chức đào tạo là bắt buộc'),
    trainingTypeId: z.string().min(1, 'Loại hình đào tạo là bắt buộc'),
    trainingFormatId: z.string().min(1, 'Hình thức đào tạo là bắt buộc'),
    certificate: z.string().optional(),
    startDate: z.date({
      error: 'Ngày bắt đầu là bắt buộc',
    }),
    endDate: z.date({
      error: 'Ngày kết thúc là bắt buộc',
    }),
    trainingHours: z.string().min(1, 'Số giờ đào tạo là bắt buộc'),
  })
  .refine(
    data => {
      if (data.startDate && data.endDate) {
        return data.endDate > data.startDate
      }

      return true
    },
    {
      message: 'Ngày kết thúc phải sau ngày bắt đầu',
      path: ['endDate'],
    }
  )

type FormValues = z.infer<typeof formSchema>

interface TrainingClassModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  initialData?: TrainingClass
}

interface CategoryOption {
  value: string
  label: string
}

export function TrainingClassModal({ isOpen, onClose, onSuccess, initialData }: TrainingClassModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [organizations, setOrganizations] = useState<CategoryOption[]>([])
  const [trainingTypes, setTrainingTypes] = useState<CategoryOption[]>([])
  const [trainingFormats, setTrainingFormats] = useState<CategoryOption[]>([])
  const [loadingOrganizations, setLoadingOrganizations] = useState(false)
  const [loadingTypes, setLoadingTypes] = useState(false)
  const [loadingFormats, setLoadingFormats] = useState(false)

  const isEditMode = !!initialData

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      trainingClassName: initialData?.trainingClassName || '',
      trainingOrganizationId: initialData?.trainingOrganizationId || '',
      trainingTypeId: initialData?.trainingTypeId || initialData?.trainingType?.categoryId || '',
      trainingFormatId: initialData?.trainingFormatId || initialData?.trainingFormat?.categoryId || '',
      certificate: initialData?.certificate || '',
      startDate: initialData?.startDate ? new Date(initialData.startDate) : undefined,
      endDate: initialData?.endDate ? new Date(initialData.endDate) : undefined,
      trainingHours: initialData?.trainingHours?.toString() || '',
    },
  })

  // Fetch data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchOrganizations()
      fetchTrainingTypes()
      fetchTrainingFormats()
    }
  }, [isOpen])

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset({
        trainingClassName: initialData.trainingClassName || '',
        trainingOrganizationId: initialData.trainingOrganizationId || initialData.trainingOrganization?.id || '',
        trainingTypeId: initialData.trainingTypeId || initialData.trainingType?.categoryId || '',
        trainingFormatId: initialData.trainingFormatId || initialData.trainingFormat?.categoryId || '',
        certificate: initialData.certificate || '',
        startDate: initialData.startDate ? new Date(initialData.startDate) : undefined,
        endDate: initialData.endDate ? new Date(initialData.endDate) : undefined,
        trainingHours: initialData.trainingHours?.toString() || '',
      })
    }
  }, [initialData, form])

  const fetchOrganizations = async () => {
    setLoadingOrganizations(true)

    try {
      const response = (await organizationService.getAllOrganizationTypes(3)) as Organization[]

      setOrganizations(
        response.map((item: Organization) => ({
          value: item.id || '',
          label: item.name || '',
        }))
      )
    } catch (error) {
      console.error('Error fetching organizations:', error)
      setOrganizations([])
    } finally {
      setLoadingOrganizations(false)
    }
  }

  const fetchTrainingTypes = async () => {
    setLoadingTypes(true)

    try {
      // Feth training types from categories with LHDT filter
      const result = (await categoryService.getCategories('LHDT')) as Category[]
      const typeOptions = result?.map((item: Category) => ({
        value: item.categoryId || item.id || item.code || '',
        label: item.name || '',
      }))

      setTrainingTypes(typeOptions)
    } catch (error) {
      console.error('Error fetching training types:', error)
      setTrainingTypes([])
    } finally {
      setLoadingTypes(false)
    }
  }

  const fetchTrainingFormats = async () => {
    setLoadingFormats(true)

    try {
      // Fetch training formats from categories with HTDT filter
      const result = (await categoryService.getCategories('HTDT')) as Category[]
      const formatOptions = result?.map((item: Category) => ({
        value: item.categoryId || item.id || item.code || '',
        label: item.name || '',
      }))
      setTrainingFormats(formatOptions)
    } catch (error) {
      console.error('Error fetching training formats:', error)
      setTrainingFormats([])
    } finally {
      setLoadingFormats(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      const payload = {
        trainingClassName: values.trainingClassName,
        trainingOrganizationId: values.trainingOrganizationId,
        trainingTypeId: values.trainingTypeId,
        trainingFormatId: values.trainingFormatId,
        certificate: values.certificate || '',
        startDate: values.startDate.toISOString(),
        endDate: values.endDate.toISOString(),
        trainingHours: values.trainingHours ? parseInt(values.trainingHours) : 0,
      }

      const url = isEditMode ? `/ac-apis/training-class/${initialData.id}` : '/ac-apis/training-class'

      const method = isEditMode ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Có lỗi xảy ra khi ${isEditMode ? 'cập nhật' : 'tạo'} lớp bồi dưỡng`)
      }

      toast.success(`Đã ${isEditMode ? 'cập nhật' : 'tạo'} lớp bồi dưỡng thành công`)
      handleClose()
      onSuccess()
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} training class:`, error)
      toast.error(
        error instanceof Error ? error.message : `Có lỗi xảy ra khi ${isEditMode ? 'cập nhật' : 'tạo'} lớp bồi dưỡng`
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-[90vw] max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Cập nhật lớp đào tạo' : 'Thêm mới lớp đào tạo'}</DialogTitle>
          <DialogDescription className="text-muted-foreground text-sm">
            Lớp đào tạo bồi dưỡng nghiệp vụ cho cán bộ, nhân viên
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            {/* Row 1: Tên lớp đào tạo và Tổ chức đào tạo */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="trainingClassName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Tên lớp đào tạo <Required />
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trainingOrganizationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Tổ chức đào tạo <Required />
                    </FormLabel>
                    <FormControl>
                      <Combobox
                        options={organizations}
                        value={field.value || ''}
                        onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                        placeholder={loadingOrganizations ? 'Đang tải...' : 'Chọn'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Row 2: Loại hình đào tạo và Hình thức đào tạo */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="trainingTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Loại hình đào tạo <Required />
                    </FormLabel>
                    <FormControl>
                      <Combobox
                        options={trainingTypes}
                        value={field.value || ''}
                        onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                        placeholder={loadingTypes ? 'Đang tải...' : 'Chọn'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trainingFormatId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Hình thức đào tạo <Required />
                    </FormLabel>
                    <FormControl>
                      <Combobox
                        options={trainingFormats}
                        value={field.value || ''}
                        onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                        placeholder={loadingFormats ? 'Đang tải...' : 'Chọn'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Row 3: Văn bằng, chứng chỉ */}
            <FormField
              control={form.control}
              name="certificate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Văn bằng, chứng chỉ</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập nội dung" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Thời gian đào tạo section */}
            <div className="space-y-4">
              <h3 className="text-base font-medium text-blue-600">Thời gian đào tạo</h3>

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày bắt đầu <Required />
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? field.value.toLocaleDateString('vi-VN') : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày kết thúc <Required />
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? field.value.toLocaleDateString('vi-VN') : <span>Chọn ngày</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="trainingHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Số giờ đào tạo <Required />
                      </FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Nhập số" min="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </form>
        </Form>

        {/* Footer buttons */}
        <div className="flex justify-end gap-3 border-t pt-4">
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Hủy
          </Button>
          <Button type="submit" onClick={form.handleSubmit(onSubmit)} disabled={isSubmitting}>
            {isSubmitting ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
