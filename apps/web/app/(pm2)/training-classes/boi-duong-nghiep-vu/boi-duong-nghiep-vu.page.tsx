'use client'

import { RenderColumn } from '@/components/render-column'
import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderText } from '@/components/render-text'
import { Category, TrainingClass } from '@/lib/types'
import { categoryService } from '@/services/categoryService'
import { PERMISSIONS } from '@ac/permissions'
import { useQueryClient } from '@tanstack/react-query'
import { ColumnDef, createColumnHelper } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { toast } from '@workspace/ui/components/toast'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { Users } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { TrainingClassModal } from './training-class-modal'

export function BoiDuongNghiepVuPage() {
  const t = useTranslations()
  const queryClient = useQueryClient()

  // State for dialog management
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedTrainingClass, setSelectedTrainingClass] = useState<TrainingClass>()

  const handleDeleteTrainingClass = async () => {
    if (!selectedTrainingClass) return

    try {
      const response = await fetch(`/ac-apis/training-class/${selectedTrainingClass.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete training class')
      }

      toast.success('Đã xóa lớp bồi dưỡng thành công')
      queryClient.invalidateQueries({
        queryKey: ['training-classes'],
      })
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error('Error deleting training class:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra khi xóa lớp bồi dưỡng')
    }
  }

  const columns: ColumnDef<TrainingClass>[] = [
    selectColumn(t),
    {
      accessorKey: 'trainingClassName',
      meta: {
        title: 'Thông tin lớp đào tạo',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderInteractiveLink
              onClick={() => {
                window.location.href = `training-class/boi-duong-nghiep-vu/${row.original.id}`
              }}
            >
              {row.original.trainingClassName}
            </RenderInteractiveLink>
            <RenderText text={row.original?.trainingFormat?.name || '-'} prefix="Hình thức đào tạo" />
          </RenderColumn>
        )
      },
    },

    {
      accessorKey: 'trainingOrganizationId',
      enableHiding: true,
      meta: {
        title: 'Tổ chức đào tạo',
        showToggle: true,
        filter: {
          type: 'combobox',
          placeholder: 'Tìm kiếm tổ chức đào tạo',
          apiRequest: async (keyword?: string) => {
            const params = new URLSearchParams()

            if (keyword) params.append('keyword', keyword.trim())
            const response = await fetch(`/ac-apis/organization-units/type/3?${params}`)
            const data = await response.json()
            const items = data.items || (Array.isArray(data) ? data : [])

            return (
              items.map((item: { id: string; code?: string; name: string }) => ({
                value: item.id,
                label: item.code ? `${item.code} - ${item.name}` : item.name,
              })) || []
            )
          },
        },
      },
      enableColumnFilter: true,
      enableSorting: false,
      header: () => null,
      cell: ({ row }) => {
        const organizationName = row.original?.trainingOrganization?.name || '-'

        return (
          <RenderColumn>
            <RenderText text={organizationName} prefix="Tên tổ chức" />
            <RenderText text={row.original?.trainingType?.name || '-'} prefix="Loại hình đào tạo" />
          </RenderColumn>
        )
      },
    },

    {
      accessorKey: 'startDate',
      size: TABLE_SIZE.S200,
      meta: {
        title: 'Thời gian đào tạo',
        filter: {
          type: 'date-range',
        },
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderDateTime datetime={row.original.startDate} inline showTime={false} prefix="Bắt đầu" />
            <RenderDateTime datetime={row.original.endDate} inline showTime={false} prefix="Kết thúc" />
            <RenderText text={`${row.original.trainingHours} giờ`} prefix="Số giờ đào tạo" />
          </RenderColumn>
        )
      },
    },
    {
      accessorKey: 'trainingTypeId',
      meta: {
        title: 'Loại hình đào tạo',
        showToggle: true,
        filter: {
          type: 'combobox',
          placeholder: 'Chọn loại hình đào tạo',
          apiRequest: async () => {
            const response = (await categoryService.getCategories('LHDT')) as Category[]
            const items = Array.isArray(response) ? response : []

            return (
              items.map((item: { categoryId?: string; id?: string; code?: string; name: string }) => ({
                value: item.categoryId || item.id || item.code || '',
                label: item.name || '',
              })) || []
            )
          },
        },
      },
      enableColumnFilter: true,
      enableSorting: false,
      cell: ({ row }) => <RenderText text={row.original?.trainingType?.name || '-'} prefix="Loại hình đào tạo" />,
      enableHiding: true,
    },
    {
      accessorKey: 'trainingFormatId',
      meta: {
        title: 'Hình thức đào tạo',
        showToggle: true,
        filter: {
          type: 'combobox',
          placeholder: 'Chọn hình thức đào tạo',
          apiRequest: async () => {
            const response = (await categoryService.getCategories('HTDT')) as Category[]
            const items = Array.isArray(response) ? response : []

            return (
              items.map((item: { categoryId?: string; id?: string; code?: string; name?: string }) => ({
                value: item.categoryId || item.id || item.code || '',
                label: item.name || '',
              })) || []
            )
          },
        },
      },
      enableColumnFilter: true,
      enableSorting: false,
      cell: ({ row }) => <RenderText text={row.original?.trainingFormat?.name || '-'} prefix="Hình thức đào tạo" />,
      enableHiding: true,
    },
    {
      accessorKey: 'date',
      meta: {
        title: 'Trạng thái',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderDateTime datetime={row.original.createdAt} inline showTime={false} prefix="Ngày tạo" />
            <RenderDateTime datetime={row.original.updatedAt} inline showTime={false} prefix="Ngày cập nhật" />
          </RenderColumn>
        )
      },
    },
    {
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const trainingClass = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM02_UPDATE_TRAINING_CLASS}
              onClick={() => {
                setSelectedTrainingClass(trainingClass)
                setIsEditDialogOpen(true)
              }}
            />

            <DeleteButtonIcon
              permission={PERMISSIONS.PM02_DELETE_TRAINING_CLASS}
              onClick={() => {
                setSelectedTrainingClass(trainingClass)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    },
  ]

  const { table } = useEnhancedTable<TrainingClass>({
    columns,
    pageName: 'training-classes',
    keyObject: {},
    queryFn: async state => {
      const params = columns.filter(Boolean) as ColumnDef<TrainingClass>[]
      const query = generateQueryParams(state, {
        columns: params,
      })
      const res = await fetch(`/ac-apis/training-class?${query}`, { cache: 'no-store' })

      return res.json()
    },
    initialState: {
      columnVisibility: {
        trainingTypeId: false,
        trainingFormatId: false,
        trainingOrganizationId: false,
      },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['training-classes'],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: 'Quản lý tham gia',
            href: 'training-class',
          },
          {
            label: 'Bồi dưỡng nghiệp vụ',
            href: 'training-class/boi-duong-nghiep-vu',
          },
        ]}
      >
        <AdminPageContent
          title="Bồi dưỡng nghiệp vụ"
          subtitle="Quản lý các lớp bồi dưỡng nghiệp vụ cho cán bộ"
          icon={<Users className="h-5 w-5" />}
          actions={[
            <AddButton
              key="create-training-class"
              onClick={() => setIsCreateDialogOpen(true)}
              tooltip={t('action.add.label')}
            />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create Training Class Modal */}
      <TrainingClassModal
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={() => {
          setIsCreateDialogOpen(false)

          if (table.options.meta?.reload) {
            table.options.meta.reload()
          }
        }}
      />

      {/* Edit Training Class Modal */}
      <TrainingClassModal
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        initialData={selectedTrainingClass}
        onSuccess={() => {
          setIsEditDialogOpen(false)

          if (table.options.meta?.reload) {
            table.options.meta.reload()
          }
        }}
      />

      {/* Delete Training Class Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xóa lớp bồi dưỡng</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa lớp bồi dưỡng &quot;{selectedTrainingClass?.trainingClassName}&quot; không? Hành
              động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTrainingClass}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
