'use client'

import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, Code, Eye, Zap } from 'lucide-react'
import { useState } from 'react'

import { type FormElement, useFormBuilder } from './form-builder'

interface ConditionEditorProps {
  element: FormElement
  onUpdateCondition: (condition: string) => void
}

export function ConditionEditor({ element, onUpdateCondition }: ConditionEditorProps) {
  const t = useTranslations('e-form')
  const { elements } = useFormBuilder()
  const [isOpen, setIsOpen] = useState(false)
  const [conditionMode, setConditionMode] = useState<'visual' | 'code'>('visual')
  const [visualCondition, setVisualCondition] = useState({
    field: '',
    operator: 'equals',
    value: '',
  })

  const getAllFormFields = (
    elements: FormElement[],
    prefix = ''
  ): Array<{ name: string; label: string; type: string }> => {
    const fields: Array<{ name: string; label: string; type: string }> = []

    elements.forEach(element => {
      if (element.name && ['input', 'select', 'checkbox', 'radio', 'textarea'].includes(element.type)) {
        fields.push({
          name: prefix + element.name,
          label: element.label || element.name,
          type: element.type,
        })
      }

      if (element.children) {
        fields.push(...getAllFormFields(element.children, prefix))
      }
    })

    return fields
  }

  const formFields = getAllFormFields(elements)

  const generateConditionFromVisual = () => {
    if (!visualCondition.field || !visualCondition.value) return ''

    const { field, operator, value } = visualCondition

    switch (operator) {
      case 'equals':
        return `${field} = "${value}"`
      case 'not_equals':
        return `${field} != "${value}"`
      case 'contains':
        return `$contains(${field}, "${value}")`
      case 'greater_than':
        return `${field} > ${value}`
      case 'less_than':
        return `${field} < ${value}`
      case 'is_empty':
        return `$not($exists(${field})) or ${field} = ""`
      case 'is_not_empty':
        return `$exists(${field}) and ${field} != ""`
      default:
        return ''
    }
  }

  const handleVisualConditionChange = () => {
    const condition = generateConditionFromVisual()
    onUpdateCondition(condition)
  }

  const operators = [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'less_than', label: 'Less Than' },
    { value: 'is_empty', label: 'Is Empty' },
    { value: 'is_not_empty', label: 'Is Not Empty' },
  ]

  return (
    <Card className="shadow-none">
      <CardHeader className="hover:bg-accent/50 cursor-pointer transition-colors" onClick={() => setIsOpen(!isOpen)}>
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            {t('conditionalLogic')}
            {element.visibilityCondition && (
              <Badge variant="secondary" className="text-xs">
                {t('active')}
              </Badge>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </CardTitle>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-4 px-3 pt-2">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant={conditionMode === 'visual' ? 'default' : 'outline'}
              onClick={() => setConditionMode('visual')}
              className="flex-1 gap-2"
            >
              <Eye className="h-3 w-3" />
              {t('visual')}
            </Button>
            <Button
              size="sm"
              variant={conditionMode === 'code' ? 'default' : 'outline'}
              onClick={() => setConditionMode('code')}
              className="flex-1 gap-2"
            >
              <Code className="h-3 w-3" />
              {t('code')}
            </Button>
          </div>

          {conditionMode === 'visual' ? (
            <div className="space-y-3">
              <div>
                <Label className="text-xs">{t('showThisElementWhen')}:</Label>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs">{t('field')}</Label>
                  <select
                    className="w-full rounded border p-2 text-sm"
                    value={visualCondition.field}
                    onChange={e => setVisualCondition(prev => ({ ...prev, field: e.target.value }))}
                  >
                    <option value="">{t('selectField')}</option>
                    {formFields.map(field => (
                      <option key={field.name} value={field.name}>
                        {field.label} ({field.name})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label className="text-xs">{t('operator')}</Label>
                  <select
                    className="w-full rounded border p-2 text-sm"
                    value={visualCondition.operator}
                    onChange={e => setVisualCondition(prev => ({ ...prev, operator: e.target.value }))}
                  >
                    {operators.map(op => (
                      <option key={op.value} value={op.value}>
                        {op.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label className="text-xs">{t('value')}</Label>
                  <Input
                    className="text-sm"
                    value={visualCondition.value}
                    onChange={e => setVisualCondition(prev => ({ ...prev, value: e.target.value }))}
                    placeholder={t('enterValue')}
                    disabled={['is_empty', 'is_not_empty'].includes(visualCondition.operator)}
                  />
                </div>
              </div>

              <Button size="sm" onClick={handleVisualConditionChange} className="w-full">
                {t('applyCondition')}
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              <div>
                <Label className="text-xs">{t('jsonataExpression')}</Label>
                <Textarea
                  className="font-mono text-sm"
                  value={element.visibilityCondition || ''}
                  onChange={e => onUpdateCondition(e.target.value)}
                  placeholder={t('enterJsonataExpression')}
                  rows={3}
                />
              </div>

              <div className="text-muted-foreground text-xs">
                <p className="mb-1 font-medium">{t('examples')}:</p>
                <ul className="space-y-1">
                  <li>
                    • <code>agree = true</code> - {t('showWhenCheckboxIsChecked')}
                  </li>
                  <li>
                    • <code>age &gt; 18</code> - {t('showWhenAgeIsGreaterThan')}
                  </li>
                  <li>
                    • <code>$contains(email, &quot;@&quot;)</code> - {t('showWhenEmailContains')}
                  </li>
                </ul>
              </div>
            </div>
          )}

          {element.visibilityCondition && (
            <div className="border-t pt-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-xs">{t('currentCondition')}:</span>
                <Button size="sm" variant="ghost" onClick={() => onUpdateCondition('')} className="h-6 text-xs">
                  {t('clear')}
                </Button>
              </div>
              <code className="bg-muted mt-1 block rounded p-2 text-xs">{element.visibilityCondition}</code>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
