'use client'

import { ChevronLeft, ChevronRight } from 'lucide-react'
import type React from 'react'
import { useCallback, useEffect, useRef, useState } from 'react'

interface GridResizeHandleProps {
  onResize: (deltaColumns: number) => void
  disabled?: boolean
  currentSpan: number
}

export function GridResizeHandle({ onResize, disabled = false, currentSpan }: GridResizeHandleProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragDelta, setDragDelta] = useState(0)
  const [columnDelta, setColumnDelta] = useState(0)
  const dragStartXRef = useRef(0)
  const handleRef = useRef<HTMLDivElement>(null)
  const columnWidthRef = useRef(60) // Set a reasonable default
  const isDraggingRef = useRef(false)
  const columnDeltaRef = useRef(0) // Add ref for column delta

  // Calculate approximate column width based on parent container
  const calculateColumnWidth = () => {
    if (!handleRef.current) return 60

    // Try to find the grid container
    let parentElement = handleRef.current.parentElement

    while (parentElement && !parentElement.style.gridTemplateColumns) {
      parentElement = parentElement.parentElement
    }

    if (parentElement) {
      const parentWidth = parentElement.getBoundingClientRect().width
      // Use a smaller divisor for more sensitive dragging
      const approximateColumnWidth = Math.max(parentWidth / 20, 40) // Minimum 40px per "column unit"
      columnWidthRef.current = approximateColumnWidth
      console.log('Calculated column width:', approximateColumnWidth)

      return approximateColumnWidth
    }

    // Fallback to a smaller value for more sensitive dragging
    columnWidthRef.current = 60

    return 60
  }

  const handleDrag = useCallback((e: MouseEvent) => {
    console.log('handleDrag called', isDraggingRef.current)

    if (!isDraggingRef.current) {
      console.log('Not dragging, returning')

      return
    }

    console.log('Processing drag')

    // Calculate the drag distance
    const currentDelta = e.clientX - dragStartXRef.current
    setDragDelta(currentDelta)

    // Calculate column delta based on drag distance with more sensitive calculation
    const colWidth = columnWidthRef.current
    const newColumnDelta = Math.round(currentDelta / colWidth)

    console.log('Drag details:', {
      currentDelta,
      colWidth,
      newColumnDelta,
      startX: dragStartXRef.current,
      currentX: e.clientX,
    })

    // Update the column delta state
    setColumnDelta(newColumnDelta)
    columnDeltaRef.current = newColumnDelta
  }, [])

  const handleDragEnd = useCallback(() => {
    console.log('handleDragEnd called', columnDeltaRef.current)

    if (!isDraggingRef.current) {
      console.log('Not dragging, returning from dragEnd')

      return
    }

    // Apply the resize if there's a change
    if (columnDeltaRef.current !== 0) {
      console.log('Applying resize:', columnDeltaRef.current)
      onResize(columnDeltaRef.current)
    }

    // Reset state
    setIsDragging(false)
    isDraggingRef.current = false
    setDragDelta(0)
    setColumnDelta(0)
    columnDeltaRef.current = 0

    // Remove event listeners
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', handleDragEnd)
    document.removeEventListener('mouseleave', handleDragEnd)
    window.removeEventListener('blur', handleDragEnd)

    console.log('Event listeners removed')
  }, [onResize, handleDrag]) // Removed columnDelta dependency

  const handleDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    console.log('handleDragStart called')
    e.preventDefault()
    e.stopPropagation()

    // Calculate column width at the start of dragging
    const colWidth = calculateColumnWidth()
    console.log('Column width calculated:', colWidth)

    setIsDragging(true)
    isDraggingRef.current = true
    dragStartXRef.current = e.clientX
    columnDeltaRef.current = 0

    // Add event listeners for drag and drag end
    document.addEventListener('mousemove', handleDrag)
    document.addEventListener('mouseup', handleDragEnd)
    document.addEventListener('mouseleave', handleDragEnd)
    window.addEventListener('blur', handleDragEnd)

    console.log('Event listeners added')
  }

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      console.log('Cleanup effect running')
      document.removeEventListener('mousemove', handleDrag)
      document.removeEventListener('mouseup', handleDragEnd)
      document.removeEventListener('mouseleave', handleDragEnd)
      window.removeEventListener('blur', handleDragEnd)
    }
  }, [handleDrag, handleDragEnd])

  // Initialize column width on mount
  useEffect(() => {
    calculateColumnWidth()
  }, [])

  if (disabled) return null

  return (
    <div
      ref={handleRef}
      className="absolute top-1/2 -right-2 z-10 flex -translate-y-1/2 transform flex-col gap-1 opacity-0 transition-opacity group-hover:opacity-100"
    >
      {/* Draggable handle */}
      <div
        className={`bg-primary relative flex h-16 w-2 cursor-ew-resize items-center justify-center rounded-full ${
          isDragging ? 'bg-primary/80 opacity-100' : 'opacity-70 hover:opacity-100'
        }`}
        onMouseDown={handleDragStart}
        style={{
          transform: isDragging ? `translateX(${Math.min(Math.max(dragDelta, -100), 100)}px)` : undefined,
        }}
      >
        {/* Visual indicator for drag direction */}
        {isDragging && (
          <div
            className="absolute rounded bg-slate-900 px-2 py-1 text-xs font-medium whitespace-nowrap text-white shadow-lg"
            style={{
              top: '-30px',
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            {/* {columnDelta > 0 ? `+${columnDelta}` : columnDelta} columns */}
            {Math.max(1, Math.min(12, currentSpan + columnDelta))} columns
          </div>
        )}
      </div>

      {/* Keep the buttons for precise adjustments */}
      <div className="mt-1 flex flex-col gap-1">
        <button
          type="button"
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex h-4 w-4 items-center justify-center rounded-full transition-colors"
          onClick={() => onResize(1)}
          title="Increase column span"
        >
          <ChevronRight className="h-3 w-3" />
        </button>
        <button
          type="button"
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex h-4 w-4 items-center justify-center rounded-full transition-colors"
          onClick={() => onResize(-1)}
          title="Decrease column span"
        >
          <ChevronLeft className="h-3 w-3" />
        </button>
      </div>
    </div>
  )
}
