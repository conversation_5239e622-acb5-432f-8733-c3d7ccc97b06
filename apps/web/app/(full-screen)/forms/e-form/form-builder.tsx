'use client'

import { useConfirmLeave } from '@/hooks/use-confirm-leave'
import { useLocalSocket } from '@/hooks/use-local-socket'
import { evaluateCondition } from '@/lib/json-form/condition-evaluator'
import { formGeneratorCodeService } from '@/lib/json-form/form-generator-code-service'
import { templateService } from '@/lib/json-form/template-service'
import { Any } from '@/lib/types'
import { closestCenter, DndContext, type DragEndEvent, DragOverlay, type DragStartEvent } from '@dnd-kit/core'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@workspace/ui/components/resizable'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import {
  AlignLeft,
  Calendar,
  CheckSquare,
  ChevronDown,
  Circle,
  Code,
  Columns,
  Container,
  Database,
  FileText,
  Heading1,
  Heading2,
  Heading3,
  ImageIcon,
  LayoutGrid,
  List,
  Minus,
  Mouse<PERSON>ointer,
  Sliders,
  Square,
  Star,
  Table,
  Type,
  Upload,
} from 'lucide-react'
import type React from 'react'
import { createContext, useCallback, useContext, useEffect, useState } from 'react'

import { Canvas } from './canvas'
import { CodeGenerationModal, type CodeGenerationOptions, type CodeGenerationType } from './code-generation-modal'
import { ComponentPalette } from './component-palette'
import { ExportImportControls } from './export-import-controls'
import { FormDataPanel } from './form-data-panel'
import { HistoryManager } from './history-manager'
import { PreviewControls } from './preview-controls'
import { PropertiesPanel } from './properties-panel'
import { ResponsiveControls } from './responsive-controls'
import { TemplateManager } from './template-manager'

const ELEMENT_TYPES = [
  'grid',
  'column',
  'container',
  'array',
  'input',
  'select',
  'textarea',
  'button',
  'checkbox',
  'radio',
  'image',
  'h1',
  'h2',
  'h3',
  'paragraph',
  'span',
  'divider',
  'spacer',
  'select-api',
  'date-picker',
  'file-upload',
  'rating',
  'slider',
  'html',
  'table',
  'detail',
]

export const iconMap: Record<ElementType, React.ReactNode> = {
  grid: <LayoutGrid className="h-4 w-4" />,
  column: <Columns className="h-4 w-4" />,
  container: <Square className="h-4 w-4" />,
  array: <List className="h-4 w-4" />,
  input: <Type className="h-4 w-4" />,
  select: <ChevronDown className="h-4 w-4" />,
  textarea: <AlignLeft className="h-4 w-4" />,
  button: <MousePointer className="h-4 w-4" />,
  checkbox: <CheckSquare className="h-4 w-4" />,
  radio: <Circle className="h-4 w-4" />,
  image: <ImageIcon className="h-4 w-4" />,
  h1: <Heading1 className="h-4 w-4" />,
  h2: <Heading2 className="h-4 w-4" />,
  h3: <Heading3 className="h-4 w-4" />,
  paragraph: <FileText className="h-4 w-4" />,
  span: <FileText className="h-4 w-4" />,
  divider: <Minus className="h-4 w-4" />,
  spacer: <Container className="h-4 w-4" />,
  'select-api': <Database className="h-4 w-4" />,
  'date-picker': <Calendar className="h-4 w-4" />,
  'file-upload': <Upload className="h-4 w-4" />,
  rating: <Star className="h-4 w-4" />,
  slider: <Sliders className="h-4 w-4" />,
  html: <Code className="h-4 w-4" />,
  table: <Table className="h-4 w-4" />,
  detail: <FileText className="h-4 w-4" />,
}

export type ElementType =
  | 'grid'
  | 'column'
  | 'container'
  | 'array'
  | 'input'
  | 'select'
  | 'textarea'
  | 'button'
  | 'checkbox'
  | 'radio'
  | 'image'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'paragraph'
  | 'span'
  | 'divider'
  | 'spacer'
  | 'select-api'
  | 'date-picker'
  | 'file-upload'
  | 'rating'
  | 'slider'
  | 'html'
  | 'table'
  | 'detail'

export type Breakpoint = 'desktop' | 'tablet' | 'mobile'

interface ElementStyle {
  width?: string
  height?: string
  margin?: string
  padding?: string
  backgroundColor?: string
  color?: string
  fontSize?: string
  fontWeight?: string
  border?: string
  borderRadius?: string
  display?: string
  flexDirection?: string
  justifyContent?: string
  alignItems?: string
  gap?: string
  gridTemplateColumns?: string
  gridGap?: string
  order?: string
}

interface ResponsiveStyles {
  desktop: ElementStyle
  tablet: ElementStyle
  mobile: ElementStyle
}

export interface AutoLayoutConfig {
  desktop: { columns: number; gap: string }
  tablet: { columns: number; gap: string }
  mobile: { columns: number; gap: string }
}

export interface ColumnConfig {
  maxColumns: number
  gap: string
  responsive: boolean
  allowReorder: boolean
  children: {
    [childId: string]: {
      [breakpoint in Breakpoint]?: {
        span: number
        order: number
      }
    }
  }
}

export interface ArrayConfig {
  displayMode: {
    desktop: 'table' | 'cards'
    tablet: 'table' | 'cards'
    mobile: 'table' | 'cards'
  }
  addView: 'inline' | 'modal'
  minItems: number
  maxItems: number
  allowAdd: boolean
  allowRemove: boolean
  allowReorder: boolean
  showMoveButtons: boolean
  cardColumns: {
    desktop: number
    tablet: number
    mobile: number
  }
  tableHeaders: boolean
  addButtonText: string
  removeButtonText: string
}

export interface ValidationRule {
  type: 'required' | 'email' | 'regex' | 'min' | 'max' | 'custom'
  value?: Any
  message: string
}

interface ValidationError {
  elementId: string
  elementName: string
  message: string
}

interface EventHook {
  type: 'onChange' | 'onClick' | 'onMount' | 'onSubmit'
  action: string
  code?: string
}

export interface FormElementJson {
  version: string
  elements: FormElement[]
  templates: Template[]
  theme: Theme
  metadata: {
    createdAt: string
    breakpoints: string[]
    features: string[]
  }
}
export interface ContainerConfig {
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  verticalAlign: 'top' | 'center' | 'bottom'
  textAlign?: 'left' | 'center' | 'right' | 'justify'
  gap: string
  children: {
    [childId: string]: {
      [breakpoint in Breakpoint]?: {
        span?: number
        start?: number
        end?: number
        order?: number
      }
    }
  }
}

export interface SelectOption {
  label: string
  value: string
  data?: Any
}

export interface FormElement {
  id: string
  type: ElementType
  label?: string
  name?: string
  placeholder?: string
  required?: boolean
  options?: SelectOption[]
  htmlContent?: string
  children?: FormElement[]
  styles: ResponsiveStyles
  className?: string
  customAttributes?: Record<string, Any>
  layout?: 'vertical' | 'horizontal'
  labelAlign?: 'left' | 'right'

  // Advanced features
  visibilityCondition?: string
  autoLayout?: AutoLayoutConfig
  columnConfig?: ColumnConfig
  arrayConfig?: ArrayConfig
  validationRules?: ValidationRule[]
  eventHooks?: EventHook[]
  apiConfig?: {
    url?: string
    emptyText?: string
    method?: string
    headers?: Record<string, string>
  }
  templateId?: string
  category?: string
  containerConfig?: ContainerConfig
  metadata?: Any
}

interface FormData {
  [key: string]: Any
}

export interface Template {
  id: string
  name: string
  description: string
  elements: FormElement[]
  thumbnail?: string
  createdAt?: Date
  updatedAt?: Date
  status: number // 1: active, -1: deleted
}

export interface Theme {
  id: string
  name: string
  colors: {
    primary: string
    secondary: string
    background: string
    foreground: string
    muted: string
  }
  fonts: {
    heading: string
    body: string
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
}

interface FormBuilderContextType {
  elements: FormElement[]
  setElements: React.Dispatch<React.SetStateAction<FormElement[]>>
  selectedElement: FormElement | null
  setSelectedElement: React.Dispatch<React.SetStateAction<FormElement | null>>
  currentBreakpoint: Breakpoint
  setCurrentBreakpoint: React.Dispatch<React.SetStateAction<Breakpoint>>
  draggedElement: FormElement | null
  setDraggedElement: React.Dispatch<React.SetStateAction<FormElement | null>>
  isPreviewMode: boolean
  setIsPreviewMode: React.Dispatch<React.SetStateAction<boolean>>
  formData: FormData
  setFormData: React.Dispatch<React.SetStateAction<FormData>>
  templates: Template[]
  setTemplates: React.Dispatch<React.SetStateAction<Template[]>>
  currentTheme: Theme
  setCurrentTheme: React.Dispatch<React.SetStateAction<Theme>>
  history: FormElement[][]
  historyIndex: number
  addToHistory: (elements: FormElement[]) => void
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
  validationErrors: ValidationError[]
  setValidationErrors: React.Dispatch<React.SetStateAction<ValidationError[]>>
  onFormSubmit?: (formData: FormData) => void | Promise<void>
  validateForm: () => boolean
  findElementById: (id: string) => FormElement | null
  formElements: FormElement[]
}

const FormBuilderContext = createContext<FormBuilderContextType | null>(null)

export const useFormBuilder = () => {
  const context = useContext(FormBuilderContext)

  if (!context) {
    throw new Error('useFormBuilder must be used within FormBuilderProvider')
  }

  return context
}

const defaultTheme: Theme = {
  id: 'default',
  name: 'Default',
  colors: {
    primary: '#0f172a',
    secondary: '#64748b',
    background: '#ffffff',
    foreground: '#0f172a',
    muted: '#f1f5f9',
  },
  fonts: {
    heading: 'Inter, sans-serif',
    body: 'Inter, sans-serif',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
}

interface FormBuilderProps {
  onFormSubmit?: (formData: FormData) => void | Promise<void>
}

export function FormBuilder({ onFormSubmit }: FormBuilderProps = {}) {
  const t = useTranslations('e-form')
  const [elements, setElements] = useState<FormElement[]>([])
  const [selectedElement, setSelectedElement] = useState<FormElement | null>(null)
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('desktop')
  const [draggedElement, setDraggedElement] = useState<FormElement | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [formData, setFormData] = useState<FormData>({})
  const [templates, setTemplates] = useState<Template[]>([])
  const [currentTheme, setCurrentTheme] = useState<Theme>(defaultTheme)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])

  // History management
  const [history, setHistory] = useState<FormElement[][]>([[]])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [isDirty, setIsDirty] = useState(false)

  const { sendFiles } = useLocalSocket()

  // Code generation modal state
  const [showCodeGenerationModal, setShowCodeGenerationModal] = useState(false)

  // Add confirm leave hook
  useConfirmLeave(isDirty)

  const generateId = () => {
    const randomText = Math.random().toString(36).substring(2, 8) // 6 character random string

    return `element_${randomText}`
  }

  const addToHistory = useCallback(
    (newElements: FormElement[]) => {
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newElements)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
      setIsDirty(true)
    },
    [history, historyIndex]
  )

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setElements(history[historyIndex - 1] as FormElement[])
      setIsDirty(true)
    }
  }, [history, historyIndex])

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setElements(JSON.parse(JSON.stringify(history[historyIndex + 1])))
      setIsDirty(true)
    }
  }, [history, historyIndex])

  const canUndo = historyIndex > 0
  const canRedo = historyIndex < history.length - 1

  // Find element by ID (recursive)
  const findElementById = useCallback(
    (id: string): FormElement | null => {
      const findRecursive = (elements: FormElement[]): FormElement | null => {
        for (const element of elements) {
          if (element.id === id) {
            return element
          }

          if (element.children && element.children.length > 0) {
            const found = findRecursive(element.children)

            if (found) return found
          }
        }

        return null
      }

      return findRecursive(elements)
    },
    [elements]
  )

  // Form validation function
  const validateForm = useCallback((): boolean => {
    const errors: ValidationError[] = []

    const validateElement = (element: FormElement) => {
      // Skip validation for non-form elements or elements without names
      if (
        !element.name ||
        ![
          'input',
          'select',
          'textarea',
          'checkbox',
          'radio',
          'date-picker',
          'file-upload',
          'rating',
          'slider',
          'array',
        ].includes(element.type)
      ) {
        return
      }

      const value = formData[element.name]
      const rules = element.validationRules || []

      // Check each validation rule
      for (const rule of rules) {
        let isValid = true
        const errorMessage = rule.message

        switch (rule.type) {
          case 'required':
            if (element.type === 'array') {
              isValid = Array.isArray(value) && value.length > 0
            } else {
              isValid = value !== undefined && value !== null && value !== '' && value !== false
            }
            break
          case 'email':
            if (value) {
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              isValid = emailRegex.test(String(value))
            }
            break
          case 'regex':
            if (value && rule.value) {
              const regex = new RegExp(rule.value)
              isValid = regex.test(String(value))
            }
            break
          case 'min':
            if (value !== undefined && value !== null && value !== '') {
              if (element.type === 'array' && Array.isArray(value)) {
                isValid = value.length >= Number(rule.value)
              } else if (typeof value === 'string') {
                isValid = value.length >= Number(rule.value)
              } else {
                isValid = Number(value) >= Number(rule.value)
              }
            }
            break
          case 'max':
            if (value !== undefined && value !== null && value !== '') {
              if (element.type === 'array' && Array.isArray(value)) {
                isValid = value.length <= Number(rule.value)
              } else if (typeof value === 'string') {
                isValid = value.length <= Number(rule.value)
              } else {
                isValid = Number(value) <= Number(rule.value)
              }
            }
            break
          case 'custom':
            // For custom validation, you could implement a function evaluator here
            break
        }

        if (!isValid) {
          errors.push({
            elementId: element.id,
            elementName: element.name,
            message: errorMessage,
          })
          break // Stop at first validation error for this element
        }
      }

      // Recursively validate children
      if (element.children) {
        element.children.forEach(validateElement)
      }
    }

    // Validate all elements
    elements.forEach(validateElement)

    setValidationErrors(errors)

    return errors.length === 0
  }, [elements, formData])

  const createDefaultElement = (type: ElementType): FormElement => {
    const defaultStyles: ResponsiveStyles = {
      desktop: { width: '100%', padding: '8px', margin: '4px 0' },
      tablet: { width: '100%', padding: '8px', margin: '4px 0' },
      mobile: { width: '100%', padding: '8px', margin: '4px 0' },
    }

    const defaultAutoLayout: AutoLayoutConfig = {
      desktop: { columns: 4, gap: '16px' },
      tablet: { columns: 2, gap: '12px' },
      mobile: { columns: 1, gap: '8px' },
    }

    const defaultColumnConfig: ColumnConfig = {
      maxColumns: 12,
      gap: '16px',
      responsive: true,
      allowReorder: false,
      children: {},
    }

    const defaultArrayConfig: ArrayConfig = {
      displayMode: {
        desktop: 'table',
        tablet: 'table',
        mobile: 'cards',
      },
      addView: 'inline',
      minItems: 0,
      maxItems: 10,
      allowAdd: true,
      allowRemove: true,
      allowReorder: true,
      showMoveButtons: true,
      cardColumns: {
        desktop: 2,
        tablet: 2,
        mobile: 1,
      },
      tableHeaders: true,
      addButtonText: t('addItem'),
      removeButtonText: t('remove'),
    }

    const baseElement: FormElement = {
      id: generateId(),
      type,
      styles: defaultStyles,
      validationRules: [],
      eventHooks: [],
    }

    switch (type) {
      case 'grid':
        return {
          ...baseElement,
          label: t('grid'),
          children: [],
          autoLayout: defaultAutoLayout,
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gridGap: '16px',
              border: '1px dashed #ccc',
              borderRadius: '4px',
              padding: '16px',
            },
            tablet: {
              ...defaultStyles.tablet,
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gridGap: '12px',
              border: '1px dashed #ccc',
              borderRadius: '4px',
              padding: '16px',
            },
            mobile: {
              ...defaultStyles.mobile,
              display: 'grid',
              gridTemplateColumns: '1fr',
              gridGap: '8px',
              border: '1px dashed #ccc',
              borderRadius: '4px',
              padding: '16px',
            },
          },
          category: 'layout',
        }

      case 'column':
        return {
          ...baseElement,
          label: t('columnLayout'),
          children: [],
          columnConfig: defaultColumnConfig,
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              display: 'grid',
              gridTemplateColumns: 'repeat(12, 1fr)',
              gridGap: '16px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
            tablet: {
              ...defaultStyles.tablet,
              display: 'grid',
              gridTemplateColumns: 'repeat(12, 1fr)',
              gridGap: '12px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
            mobile: {
              ...defaultStyles.mobile,
              display: 'grid',
              gridTemplateColumns: 'repeat(12, 1fr)',
              gridGap: '8px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
          },
          category: 'layout',
        }

      case 'array':
        return {
          ...baseElement,
          label: t('arrayInput'),
          name: `array_${Math.random().toString(36).substring(2, 7)}`,
          children: [],
          arrayConfig: defaultArrayConfig,
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              border: '1px dashed #10b981',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#ecfdf5',
            },
            tablet: {
              ...defaultStyles.tablet,
              border: '1px dashed #10b981',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#ecfdf5',
            },
            mobile: {
              ...defaultStyles.mobile,
              border: '1px dashed #10b981',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#ecfdf5',
            },
          },
          category: 'form',
          validationRules: [{ type: 'required', message: t('atLeastOneItemIsRequired') }],
        }

      case 'input':
        return {
          ...baseElement,
          label: t('inputField'),
          name: `input_${Math.random().toString(36).substring(2, 7)}`,
          placeholder: t('enterText'),
          layout: 'vertical',
          labelAlign: 'left',
          category: 'form',
          validationRules: [{ type: 'required', message: t('thisFieldIsRequired') }],
        }

      case 'select':
        return {
          ...baseElement,
          label: t('selectField'),
          name: `select_${Math.random().toString(36).substring(2, 7)}`,
          options: [
            { label: `${t('option')} 1`, value: 'option1' },
            { label: `${t('option')} 2`, value: 'option2' },
            { label: `${t('option')} 3`, value: 'option3' },
          ],
          category: 'form',
        }

      case 'select-api':
        return {
          ...baseElement,
          label: t('apiSelect'),
          name: `api_select_${Math.random().toString(36).substring(2, 7)}`,
          apiConfig: {
            url: 'https://jsonplaceholder.typicode.com/users',
            emptyText: t('noOptionsFound'),
            method: 'GET',
          },
          category: 'form',
        }

      case 'h1':
        return {
          ...baseElement,
          label: t('heading1'),
          htmlContent: t('heading1'),
          category: 'html',
          styles: {
            desktop: { ...defaultStyles.desktop, fontSize: '2rem', fontWeight: 'bold' },
            tablet: { ...defaultStyles.tablet, fontSize: '1.8rem', fontWeight: 'bold' },
            mobile: { ...defaultStyles.mobile, fontSize: '1.6rem', fontWeight: 'bold' },
          },
        }

      case 'h2':
        return {
          ...baseElement,
          label: t('heading2'),
          htmlContent: t('heading2'),
          category: 'html',
          styles: {
            desktop: { ...defaultStyles.desktop, fontSize: '1.5rem', fontWeight: 'bold' },
            tablet: { ...defaultStyles.tablet, fontSize: '1.4rem', fontWeight: 'bold' },
            mobile: { ...defaultStyles.mobile, fontSize: '1.3rem', fontWeight: 'bold' },
          },
        }

      case 'paragraph':
        return {
          ...baseElement,
          label: t('paragraph'),
          htmlContent: t('thisIsAParagraphOfText'),
          category: 'html',
        }

      case 'divider':
        return {
          ...baseElement,
          label: t('divider'),
          category: 'html',
          styles: {
            desktop: { ...defaultStyles.desktop, height: '1px', backgroundColor: '#e2e8f0', width: '100%' },
            tablet: { ...defaultStyles.tablet, height: '1px', backgroundColor: '#e2e8f0', width: '100%' },
            mobile: { ...defaultStyles.mobile, height: '1px', backgroundColor: '#e2e8f0', width: '100%' },
          },
        }

      case 'date-picker':
        return {
          ...baseElement,
          label: t('datePicker'),
          name: `date_${Math.random().toString(36).substring(2, 7)}`,
          category: 'form',
        }

      case 'file-upload':
        return {
          ...baseElement,
          label: t('fileUpload'),
          name: `file_${Math.random().toString(36).substring(2, 7)}`,
          category: 'form',
        }

      case 'rating':
        return {
          ...baseElement,
          label: t('rating'),
          name: `rating_${Math.random().toString(36).substring(2, 7)}`,
          customAttributes: { max: 5 },
          category: 'form',
        }

      case 'slider':
        return {
          ...baseElement,
          label: t('slider'),
          name: `slider_${Math.random().toString(36).substring(2, 7)}`,
          customAttributes: { min: 0, max: 100, step: 1 },
          category: 'form',
        }
      case 'html':
        return {
          ...baseElement,
          label: t('htmlContent'),
          htmlContent: t('htmlContentDefault'),
          category: 'html',
        }
      case 'textarea':
        return {
          ...baseElement,
          label: t('textarea'),
          name: `textarea_${Math.random().toString(36).substring(2, 7)}`,
          placeholder: t('enterLongText'),
          category: 'form',
        }
      case 'button':
        return {
          ...baseElement,
          label: t('button'),
          htmlContent: t('clickMe'),
          category: 'form',
          customAttributes: { buttonType: 'button' },
        }
      case 'checkbox':
        return {
          ...baseElement,
          label: t('checkbox'),
          name: `checkbox_${Math.random().toString(36).substring(2, 7)}`,
          category: 'form',
        }
      case 'radio':
        return {
          ...baseElement,
          label: t('radioButton'),
          name: `radio_${Math.random().toString(36).substring(2, 7)}`,
          options: [
            { label: `${t('option')} 1`, value: 'option1' },
            { label: `${t('option')} 2`, value: 'option2' },
          ],
          category: 'form',
        }
      case 'image':
        return {
          ...baseElement,
          label: t('image'),
          htmlContent: '/placeholder.svg?height=200&width=300',
          category: 'media',
        }
      case 'container':
        return {
          ...baseElement,
          label: t('container'),
          children: [],
          containerConfig: {
            columns: {
              desktop: 12,
              tablet: 6,
              mobile: 4,
            },
            verticalAlign: 'top',
            textAlign: 'left',
            gap: '16px',
            children: {},
          },
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              display: 'grid',
              gridTemplateColumns: 'repeat(12, 1fr)',
              gap: '16px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
            tablet: {
              ...defaultStyles.tablet,
              display: 'grid',
              gridTemplateColumns: 'repeat(6, 1fr)',
              gap: '12px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
            mobile: {
              ...defaultStyles.mobile,
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '8px',
              border: '1px dashed #3b82f6',
              borderRadius: '4px',
              padding: '16px',
              backgroundColor: '#eff6ff',
            },
          },
          category: 'layout',
        }
      case 'table':
        return {
          ...baseElement,
          label: t('table'),
          name: `table_${Math.random().toString(36).substring(2, 7)}`,
          category: 'data',
          metadata: {
            columns: [
              {
                id: `col_${Math.random().toString(36).substring(2, 5)}`,
                label: `${t('column')} 1`,
                key: 'column1',
                dataType: 'text',
                sortable: true,
                filterable: true,
                width: 'auto',
              },
              {
                id: `col_${Math.random().toString(36).substring(2, 5)}`,
                label: `${t('column')} 2`,
                key: 'column2',
                dataType: 'text',
                sortable: true,
                filterable: true,
                width: 'auto',
              },
              {
                id: `col_${Math.random().toString(36).substring(2, 5)}`,
                label: t('actions'),
                key: 'actions',
                dataType: 'actions',
                sortable: false,
                filterable: false,
                width: '120px',
              },
            ],
            showHeader: true,
            showPagination: true,
            pageSize: 10,
            striped: true,
            bordered: true,
            hover: true,
            responsive: true,
          },
          apiConfig: {
            url: '',
            method: 'GET',
            headers: {},
          },
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              width: '100%',
            },
            tablet: {
              ...defaultStyles.tablet,
              width: '100%',
            },
            mobile: {
              ...defaultStyles.mobile,
              width: '100%',
            },
          },
        }
      case 'detail':
        return {
          ...baseElement,
          label: t('detail'),
          name: `detail_${Math.random().toString(36).substring(2, 7)}`,
          category: 'data',
          metadata: {
            rows: [
              {
                id: `row_${Math.random().toString(36).substring(2, 5)}`,
                name: t('id'),
                value: '123456',
                type: 'text',
              },
              {
                id: `row_${Math.random().toString(36).substring(2, 5)}`,
                name: t('name'),
                value: t('sampleData'),
                type: 'text',
              },
              {
                id: `row_${Math.random().toString(36).substring(2, 5)}`,
                name: t('createdDate'),
                value: '2024-01-15T10:30:00Z',
                type: 'datetime',
              },
            ],
            columns: [
              {
                id: 'col_name',
                label: t('fieldName'),
                key: 'name',
                dataType: 'text',
                sortable: false,
                filterable: false,
                width: '33%',
              },
              {
                id: 'col_value',
                label: t('information'),
                key: 'value',
                dataType: 'text',
                sortable: false,
                filterable: false,
                width: '67%',
              },
            ],
            showHeader: true,
            showPagination: false,
            striped: true,
            bordered: true,
            hover: true,
          },
          styles: {
            desktop: {
              ...defaultStyles.desktop,
              width: '100%',
            },
            tablet: {
              ...defaultStyles.tablet,
              width: '100%',
            },
            mobile: {
              ...defaultStyles.mobile,
              width: '100%',
            },
          },
        }
      default:
        return { ...baseElement, category: 'other' }
    }
  }

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event

    // Kiểm tra xem đang kéo element mới hay element đã tồn tại
    if (active.data.current?.type && ELEMENT_TYPES.includes(active.data.current.type as ElementType)) {
      // Đang kéo element mới từ ComponentPalette
      const elementType = active.data.current.type as ElementType
      const newElement = createDefaultElement(elementType)
      setDraggedElement(newElement)
    } else {
      // Đang kéo element đã tồn tại
      const existingElement = findElementById(active.id as string)

      if (existingElement) {
        setDraggedElement(existingElement)
      }
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!over) {
      setDraggedElement(null)

      return
    }

    const draggedElement = findElementById(active.id as string)
    const isNewElement = active.data.current?.type && ELEMENT_TYPES.includes(active.data.current.type as ElementType)

    if (isNewElement) {
      // Xử lý thêm element mới
      const elementType = active.data.current?.type as ElementType
      const newElement = createDefaultElement(elementType)

      let newElements: FormElement[]

      // Nếu có element đang được chọn
      if (selectedElement) {
        // Kiểm tra xem element đang chọn có phải là layout element hoặc array-input không
        const isLayoutElement = ['grid', 'column', 'container', 'array'].includes(selectedElement.type)

        if (isLayoutElement) {
          // Thêm vào bên trong layout element
          newElements = addElementToContainer(elements, selectedElement.id, newElement)
        } else {
          // Thêm vào kế tiếp element đang chọn
          const insertAfterElement = (elements: FormElement[]): FormElement[] => {
            return elements
              .map(element => {
                if (element.id === selectedElement.id) {
                  return [element, newElement]
                } else if (element.children) {
                  return {
                    ...element,
                    children: insertAfterElement(element.children),
                  }
                }

                return element
              })
              .flat()
          }
          newElements = insertAfterElement(elements)
        }
      } else if (over.id === 'canvas') {
        // Nếu không có element nào được chọn, thêm vào canvas
        newElements = [...elements, newElement]
      } else if (over.data.current?.accepts?.includes('element')) {
        // Thêm vào container được chọn
        const containerId = over.id as string
        newElements = addElementToContainer(elements, containerId, newElement)
      } else {
        setDraggedElement(null)

        return
      }

      setElements(newElements)
      addToHistory(newElements)
    } else if (draggedElement) {
      // Xử lý di chuyển element đã tồn tại
      const moveElementToContainer = (elements: FormElement[], elementId: string, targetId: string): FormElement[] => {
        // Xóa element khỏi vị trí cũ
        const removeElement = (elements: FormElement[]): FormElement[] => {
          return elements.filter(element => {
            if (element.id === elementId) {
              return false
            }

            if (element.children) {
              element.children = removeElement(element.children)
            }

            return true
          })
        }

        // Thêm element vào vị trí mới
        const addElement = (elements: FormElement[]): FormElement[] => {
          return elements.map(element => {
            if (element.id === targetId) {
              return {
                ...element,
                children: [...(element.children || []), draggedElement],
              }
            }

            if (element.children) {
              return {
                ...element,
                children: addElement(element.children),
              }
            }

            return element
          })
        }

        const elementsWithoutElement = removeElement(elements)

        return addElement(elementsWithoutElement)
      }

      let newElements: FormElement[]

      if (over.id === 'canvas') {
        // Di chuyển lên canvas
        const elementsWithoutElement = elements.filter(el => el.id !== draggedElement.id)
        newElements = [...elementsWithoutElement, draggedElement]
      } else if (over.data.current?.accepts?.includes('element')) {
        // Di chuyển vào container
        newElements = moveElementToContainer(elements, draggedElement.id, over.id as string)
      } else {
        setDraggedElement(null)

        return
      }

      setElements(newElements)
      addToHistory(newElements)
    }

    setDraggedElement(null)
  }

  const addElementToContainer = (
    elements: FormElement[],
    containerId: string,
    newElement: FormElement
  ): FormElement[] => {
    return elements.map(element => {
      if (
        element.id === containerId &&
        (element.type === 'grid' || element.type === 'column' || element.type === 'container')
      ) {
        const updatedElement = {
          ...element,
          children: [...(element.children || []), newElement],
        }

        // Khởi tạo cấu hình cho container nếu là container type
        if (element.type === 'container' && element.containerConfig) {
          const containerConfig = { ...element.containerConfig }

          if (!containerConfig.children[newElement.id]) {
            containerConfig.children[newElement.id] = {
              desktop: { span: 1, order: 0 },
              tablet: { span: 1, order: 0 },
              mobile: { span: 1, order: 0 },
            }
          }
          updatedElement.containerConfig = containerConfig
        }

        return updatedElement
      } else if (element.children) {
        return {
          ...element,
          children: addElementToContainer(element.children, containerId, newElement),
        }
      }

      return element
    })
  }

  const updateElement = (elementId: string, updates: Partial<FormElement>) => {
    const updateElementRecursive = (elements: FormElement[]): FormElement[] => {
      return elements.map(element => {
        if (element.id === elementId) {
          return { ...element, ...updates }
        } else if (element.children) {
          return {
            ...element,
            children: updateElementRecursive(element.children),
          }
        }

        return element
      })
    }

    const newElements = updateElementRecursive(elements)
    setElements(newElements)

    // Only add to history for significant changes, not just selection
    if (Object.keys(updates).some(key => key !== 'id')) {
      addToHistory(newElements)
    }

    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => (prev ? { ...prev, ...updates } : null))
    }
  }

  const deleteElement = (elementId: string) => {
    const deleteElementRecursive = (elements: FormElement[]): FormElement[] => {
      return elements.filter(element => {
        if (element.id === elementId) {
          return false
        } else if (element.children) {
          element.children = deleteElementRecursive(element.children)
        }

        return true
      })
    }

    const newElements = deleteElementRecursive(elements)
    setElements(newElements)
    addToHistory(newElements)

    if (selectedElement?.id === elementId) {
      setSelectedElement(null)
    }
  }

  const moveElement = (elementId: string, direction: 'up' | 'down') => {
    const moveElementRecursive = (elements: FormElement[]): FormElement[] => {
      const index = elements.findIndex(el => el.id === elementId)

      if (index !== -1) {
        const newElements = [...elements]
        const targetIndex = direction === 'up' ? index - 1 : index + 1

        if (targetIndex >= 0 && targetIndex < newElements.length) {
          ;[newElements[index], newElements[targetIndex] as FormElement] = [
            newElements[targetIndex] as FormElement,
            newElements[index] as FormElement,
          ]
        }

        return newElements
      }

      return elements.map(element => ({
        ...element,
        children: element.children ? moveElementRecursive(element.children) : undefined,
      }))
    }

    const newElements = moveElementRecursive(elements)
    setElements(newElements)
    addToHistory(newElements)
  }

  const duplicateElement = (elementId: string) => {
    const findAndDuplicateElement = (elements: FormElement[]): FormElement[] => {
      const index = elements.findIndex(el => el.id === elementId)

      if (index !== -1) {
        const elementToDuplicate = elements[index]
        const duplicatedElement = JSON.parse(JSON.stringify(elementToDuplicate))
        duplicatedElement.id = generateId()

        // Update IDs recursively for children
        const updateIds = (element: FormElement): FormElement => {
          element.id = generateId()

          if (element.children) {
            element.children = element.children.map(updateIds)
          }

          return element
        }

        if (duplicatedElement.children) {
          duplicatedElement.children = duplicatedElement.children.map(updateIds)
        }

        const newElements = [...elements]
        newElements.splice(index + 1, 0, duplicatedElement)

        return newElements
      }

      return elements.map(element => ({
        ...element,
        children: element.children ? findAndDuplicateElement(element.children) : undefined,
      }))
    }

    const newElements = findAndDuplicateElement(elements)
    setElements(newElements)
    addToHistory(newElements)
  }

  const saveAsTemplate = async (name: string, description: string, elementIds: string[]) => {
    const extractElements = (elements: FormElement[], ids: string[]): FormElement[] => {
      return elements.filter(el => ids.includes(el.id))
    }

    const templateElements = extractElements(elements, elementIds)
    const newTemplate: Template = {
      id: generateId(),
      name,
      description,
      elements: JSON.parse(JSON.stringify(templateElements)),
      status: 1,
    }

    await templateService.saveTemplate(newTemplate)

    setTemplates(prev => [...prev, newTemplate])
  }

  const downloadReactCode = () => {
    setShowCodeGenerationModal(true)
  }

  const handleCodeGeneration = (type: CodeGenerationType, options: CodeGenerationOptions) => {
    const schema: FormElementJson = {
      version: '2.1',
      elements,
      templates,
      theme: currentTheme,
      metadata: {
        createdAt: new Date().toISOString(),
        breakpoints: ['desktop', 'tablet', 'mobile'],
        features: ['conditional-logic', 'auto-layout', 'validation', 'templates', 'column-layout', 'array-input'],
      },
    }

    let files: { [fileName: string]: string }

    try {
      if (type === 'form') {
        files = formGeneratorCodeService.generateReactCode(
          schema,
          options.moduleName,
          options.componentName,
          options.framework,
          options
        )
      } else if (type === 'detail') {
        files = formGeneratorCodeService.generateDetailComponent(
          schema,
          options.moduleName,
          options.componentName,
          options.framework,
          options
        )
      } else if (type === 'table') {
        files = formGeneratorCodeService.generateTableComponent(
          schema,
          options.moduleName,
          options.componentName,
          options.framework,
          options
        )
      } else {
        // Fallback to form generation
        files = formGeneratorCodeService.generateReactCode(
          schema,
          options.moduleName,
          options.componentName,
          options.framework,
          options
        )
      }

      // downloadZipFiles(files, options.moduleName, options.componentName)
      sendFiles(files)

      setShowCodeGenerationModal(false)
    } catch (error) {
      console.error('Error generating code:', error)
      alert(error instanceof Error ? error.message : t('errorGeneratingCode'))
    }
  }

  // const downloadZipFiles = async (files: { [fileName: string]: string }, moduleName: string, formName: string) => {
  //   try {
  //     // Dynamically import JSZip to avoid SSR issues
  //     const JSZip = (await import('jszip')).default
  //     const zip = new JSZip()

  //     // Add all files to zip
  //     Object.entries(files).forEach(([fileName, content]) => {
  //       zip.file(fileName, content)
  //     })

  //     // Generate zip file
  //     const zipBlob = await zip.generateAsync({ type: 'blob' })

  //     // Create download link
  //     const url = URL.createObjectURL(zipBlob)
  //     const a = document.createElement('a')
  //     a.href = url

  //     // Generate filename with timestamp
  //     const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
  //     a.download = `${moduleName}-${formName}-form-${timestamp}.zip`

  //     a.click()
  //     URL.revokeObjectURL(url)
  //   } catch (error) {
  //     console.error('Error creating zip file:', error)
  //     alert('Có lỗi xảy ra khi tạo file zip. Vui lòng thử lại.')
  //   }
  // }

  const exportToJSON = () => {
    const schema: FormElementJson = {
      version: '2.1',
      elements,
      templates,
      theme: currentTheme,
      metadata: {
        createdAt: new Date().toISOString(),
        breakpoints: ['desktop', 'tablet', 'mobile'],
        features: ['conditional-logic', 'auto-layout', 'validation', 'templates', 'column-layout', 'array-input'],
      },
    }

    const blob = new Blob([JSON.stringify(schema, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'advanced-form-schema.json'
    a.click()
    URL.revokeObjectURL(url)
  }

  const importFromJSON = (jsonString: string) => {
    try {
      const schema = JSON.parse(jsonString)

      if (schema.elements && Array.isArray(schema.elements)) {
        setElements(schema.elements)
        addToHistory(schema.elements)
        setSelectedElement(null)

        if (schema.templates) {
          setTemplates(schema.templates)
        }

        if (schema.theme) {
          setCurrentTheme(schema.theme)
        }
      }
    } catch (error) {
      console.error('Invalid JSON format:', error)
      alert(t('invalidJsonFormat'))
    }
  }

  // Check element visibility based on conditions
  const isElementVisible = (element: FormElement): boolean => {
    if (!element.visibilityCondition) return true

    return evaluateCondition(element.visibilityCondition, formData)
  }

  // Keyboard event handlers
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if focus is on an input, textarea, or other form element
      const activeElement = document.activeElement
      const isInputFocused =
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.tagName === 'SELECT' ||
          (activeElement as HTMLElement)?.isContentEditable ||
          activeElement.getAttribute('contenteditable') === 'true')

      // Only handle element deletion if not focused on an input and using modifier key
      if (!isInputFocused && selectedElement && !isPreviewMode) {
        // Handle Ctrl+Delete (Windows) or Cmd+Delete (macOS) to remove selected element
        if ((event.ctrlKey || event.metaKey) && (event.key === 'Delete' || event.key === 'Backspace')) {
          event.preventDefault()
          deleteElement(selectedElement.id)
        }
      }

      // Escape key to unselect element (works regardless of focus)
      if (event.key === 'Escape') {
        setSelectedElement(null)
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedElement, isPreviewMode])

  const value: FormBuilderContextType = {
    elements,
    setElements,
    selectedElement,
    setSelectedElement,
    currentBreakpoint,
    setCurrentBreakpoint,
    draggedElement,
    setDraggedElement,
    isPreviewMode,
    setIsPreviewMode,
    formData,
    setFormData,
    templates,
    setTemplates,
    currentTheme,
    setCurrentTheme,
    history,
    historyIndex,
    addToHistory,
    undo,
    redo,
    canUndo,
    canRedo,
    validationErrors,
    setValidationErrors,
    onFormSubmit,
    validateForm,
    findElementById,
    formElements: elements,
  }

  return (
    <FormBuilderContext.Provider value={value}>
      <DndContext collisionDetection={closestCenter} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <div className="bg-background flex h-screen flex-col">
          {/* Main Content Area */}
          <ResizablePanelGroup direction="horizontal" className="min-h-0 flex-1">
            {/* Left Sidebar - Component Palette */}
            {!isPreviewMode && (
              <>
                <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                  <div className="bg-card flex h-full flex-col border-r">
                    <div className="border-b p-4">
                      <h2 className="text-lg font-semibold">{t('eFormBuilder')}</h2>
                    </div>

                    {/* Templates at the top */}
                    <TemplateManager
                      onApplyTemplate={template => {
                        const newElements = [...elements, ...template.elements]
                        setElements(newElements)
                        addToHistory(newElements)
                      }}
                      onSaveTemplate={saveAsTemplate}
                      selectedElementIds={selectedElement ? [selectedElement.id] : []}
                    />

                    {/* Component Palette */}
                    <div className="flex-1 overflow-hidden">
                      <ComponentPalette />
                    </div>
                  </div>
                </ResizablePanel>
                <ResizableHandle />
              </>
            )}

            {/* Center - Canvas */}
            <ResizablePanel defaultSize={isPreviewMode ? 100 : 60} minSize={30}>
              <div className="flex h-full min-w-0 flex-col">
                <div className="flex items-center justify-between border-b p-4">
                  <div className="flex items-center gap-4">
                    <ResponsiveControls />
                    <PreviewControls
                      isPreviewMode={isPreviewMode}
                      onTogglePreview={() => setIsPreviewMode(!isPreviewMode)}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <HistoryManager />
                    <ExportImportControls
                      onDownloadReactCode={downloadReactCode}
                      onExport={exportToJSON}
                      onImport={importFromJSON}
                    />
                  </div>
                </div>
                <div className="flex-1 overflow-auto">
                  <Canvas
                    updateElement={updateElement}
                    deleteElement={deleteElement}
                    moveElement={moveElement}
                    duplicateElement={duplicateElement}
                    isElementVisible={isElementVisible}
                  />
                </div>
              </div>
            </ResizablePanel>

            {/* Right Sidebar - Properties Panel */}
            {!isPreviewMode && (
              <>
                <ResizableHandle />
                <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                  <div className="bg-card flex h-full flex-col border-l">
                    <PropertiesPanel updateElement={updateElement} />
                  </div>
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>

          {/* Bottom Panel - Form Data */}
          <FormDataPanel />
        </div>

        <DragOverlay>
          {draggedElement && (
            <div className="rounded border bg-white p-2 opacity-80 shadow-lg">{draggedElement.label}</div>
          )}
        </DragOverlay>
      </DndContext>

      {/* Code Generation Modal */}
      <CodeGenerationModal
        open={showCodeGenerationModal}
        onOpenChange={setShowCodeGenerationModal}
        onGenerate={handleCodeGeneration}
      />
    </FormBuilderContext.Provider>
  )
}
