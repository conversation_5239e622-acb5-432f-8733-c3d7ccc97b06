'use client'

import { templateService } from '@/lib/json-form/template-service'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@workspace/ui/components/collapsible'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Switch } from '@workspace/ui/components/switch'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import {
  BookDashed,
  Check,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Download,
  Library,
  Plus,
  Search,
  Trash2,
  Upload,
} from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import { Template } from './form-builder'
import type { FormElement } from './form-builder'

interface TemplateManagerProps {
  onApplyTemplate: (template: Template) => void
  onSaveTemplate: (name: string, description: string, elementIds: string[]) => Promise<void>
  selectedElementIds: string[]
}

export function TemplateManager({ onApplyTemplate, onSaveTemplate, selectedElementIds }: TemplateManagerProps) {
  const t = useTranslations('e-form')
  const [templates, setTemplates] = useState<Template[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isSaving, setIsSaving] = useState(false)
  const [newTemplateName, setNewTemplateName] = useState('')
  const [newTemplateDescription, setNewTemplateDescription] = useState('')
  const [isNewTemplateDialogOpen, setIsNewTemplateDialogOpen] = useState(false)
  const [isApplyTemplateDialogOpen, setIsApplyTemplateDialogOpen] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [templateToDelete, setTemplateToDelete] = useState<Template | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [shouldRenameIds, setShouldRenameIds] = useState(true)
  const [shouldRenameInputs, setShouldRenameInputs] = useState(true)
  const [inputPrefix, setInputPrefix] = useState('')
  const [showDeleted, setShowDeleted] = useState(false)
  const [templateToEdit, setTemplateToEdit] = useState<Template | null>(null)
  const [editName, setEditName] = useState('')
  const [editDescription, setEditDescription] = useState('')
  const [editStatus, setEditStatus] = useState(1)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [showImportConfirm, setShowImportConfirm] = useState(false)
  const [overwrittenCount, setOverwrittenCount] = useState(0)
  const [importCount, setImportCount] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const loadTemplates = async () => {
    const result = await templateService.searchTemplates(searchQuery, currentPage, 5, showDeleted ? -1 : 1)
    setTemplates(result.templates)
    setTotalPages(result.totalPages)
  }

  useEffect(() => {
    loadTemplates()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, currentPage, showDeleted])

  const handleSaveTemplate = async () => {
    if (!newTemplateName.trim()) return

    setIsSaving(true)

    try {
      // const template: Template = {
      //   id: Math.random().toString(36).substring(2, 8),
      //   name: newTemplateName,
      //   description: newTemplateDescription,
      //   elements: [],
      //   status: 1,
      //   createdAt: new Date(),
      // }

      await onSaveTemplate(newTemplateName, newTemplateDescription, selectedElementIds)

      setNewTemplateName('')
      setNewTemplateDescription('')
      setIsNewTemplateDialogOpen(false)
      loadTemplates()
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return

    if (templateToDelete.status === -1) {
      // Xóa vĩnh viễn
      await templateService.deleteTemplatePermanently(templateToDelete.id)
    } else {
      // Chuyển status về -1
      await templateService.updateTemplateStatus(templateToDelete.id, -1)
    }
    setTemplateToDelete(null)
    loadTemplates()
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    setCurrentPage(1) // Reset to page 1 when searching
  }

  const handleApplyTemplate = (template: Template) => {
    setSelectedTemplate(template)
    setIsApplyTemplateDialogOpen(true)
  }

  const processTemplate = (template: Template) => {
    let processedElements = JSON.parse(JSON.stringify(template.elements))

    if (shouldRenameIds) {
      processedElements = processedElements.map((element: FormElement) => {
        const updateIds = (el: FormElement): FormElement => {
          const newId = `${el.type}_${Math.random().toString(36).substring(2, 8)}`

          return {
            ...el,
            id: newId,
            children: el.children ? el.children.map(updateIds) : undefined,
          }
        }

        return updateIds(element)
      })
    }

    if (shouldRenameInputs) {
      processedElements = processedElements.map((element: FormElement) => {
        const updateNames = (el: FormElement): FormElement => {
          if (el.name) {
            return {
              ...el,
              name: `${el.name}${inputPrefix}`,
              children: el.children ? el.children.map(updateNames) : undefined,
            }
          }

          return {
            ...el,
            children: el.children ? el.children.map(updateNames) : undefined,
          }
        }

        return updateNames(element)
      })
    }

    return {
      ...template,
      elements: processedElements,
    }
  }

  const handleConfirm = () => {
    if (selectedTemplate) {
      if (shouldRenameInputs && !inputPrefix.trim()) {
        alert('Vui lòng nhập Input name prefix hoặc tắt tùy chọn Rename input names')

        return
      }
      const processedTemplate = processTemplate(selectedTemplate)
      onApplyTemplate(processedTemplate)
      setIsApplyTemplateDialogOpen(false)
      setSelectedTemplate(null)
      setShouldRenameIds(true)
      setShouldRenameInputs(true)
      setInputPrefix('')
    }
  }

  const handleEditTemplate = async () => {
    if (!templateToEdit) return

    try {
      const updatedTemplate = {
        ...templateToEdit,
        name: editName,
        description: editDescription,
        status: editStatus,
        updatedAt: new Date(),
      }
      await templateService.updateTemplate(updatedTemplate)
      setTemplateToEdit(null)
      loadTemplates()
    } catch (error) {
      console.error('Error updating template:', error)
    }
  }

  const handleExportTemplates = async () => {
    try {
      const templates = await templateService.getAllTemplates()
      const activeTemplates = templates.filter(t => t.status === 1)

      const blob = new Blob([JSON.stringify(activeTemplates, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `templates-${new Date().toISOString().slice(0, 19).replace(/[:]/g, '')}.json`
      a.click()
      URL.revokeObjectURL(url)

      toast.success('Templates exported successfully', {
        description: 'Templates exported successfully',
        duration: 3000,
        position: 'top-right',
      })
    } catch (error) {
      console.error('Error exporting templates:', error)
      toast.error('Failed to export templates', {
        description: 'Failed to export templates',
        duration: 3000,
        position: 'top-right',
      })
    }
  }

  const getImportFileInfo = async (file: File) => {
    const text = await file.text()

    return JSON.parse(text) as Template[]
  }

  const handleImportTemplates = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]

    if (!file) return

    try {
      const importedTemplates = await getImportFileInfo(file)

      // Kiểm tra format
      if (!Array.isArray(importedTemplates)) {
        throw new Error('Invalid template format')
      }

      // Kiểm tra mảng rỗng
      if (importedTemplates.length === 0) {
        toast.error('No templates found in the file', {
          description: 'No templates found in the file',
          duration: 3000,
          position: 'top-right',
        })

        return
      }

      // Kiểm tra số lượng template sẽ bị ghi đè
      const currentTemplates = await templateService.getAllTemplates()
      const existingIds = new Set(currentTemplates.map(t => t.id))
      const overwrittenCount = importedTemplates.filter(t => existingIds.has(t.id)).length

      setImportFile(file)
      setImportCount(importedTemplates.length)
      setOverwrittenCount(overwrittenCount)
      setShowImportConfirm(true)
    } catch (error) {
      console.error('Error parsing import file:', error)
      toast.error('Invalid template file format')
    }

    event.target.value = ''
  }

  const handleConfirmImport = async (withBackup: boolean) => {
    if (!importFile) return

    try {
      const text = await importFile.text()
      const importedTemplates = JSON.parse(text) as Template[]

      // Kiểm tra format
      if (!Array.isArray(importedTemplates)) {
        throw new Error('Invalid template format')
      }

      // Backup trước khi import nếu có templates active và người dùng chọn backup
      if (withBackup) {
        const currentTemplates = await templateService.getAllTemplates()
        const activeTemplates = currentTemplates.filter(t => t.status === 1)

        if (activeTemplates.length > 0) {
          const backupBlob = new Blob([JSON.stringify(activeTemplates, null, 2)], { type: 'application/json' })
          const backupUrl = URL.createObjectURL(backupBlob)
          const backupA = document.createElement('a')
          backupA.href = backupUrl
          backupA.download = `templates-backup-${new Date().toISOString().slice(0, 19).replace(/[:]/g, '')}.json`
          backupA.click()
          URL.revokeObjectURL(backupUrl)
        }
      }

      // Import templates
      for (const template of importedTemplates) {
        await templateService.saveTemplate(template)
      }

      toast.success('Templates imported successfully')
      loadTemplates()
    } catch (error) {
      console.error('Error importing templates:', error)
      toast.error('Failed to import templates')
    }

    setImportFile(null)
    setShowImportConfirm(false)
  }

  const handleImportClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="border-b">
      <Card className="overflow-hidden rounded-none border-none shadow-none">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger className="w-full text-left">
            <CardHeader className="hover:bg-accent/50 cursor-pointer py-3 transition-colors">
              <CardTitle className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Library className="h-4 w-4" />
                  <span>{t('templates')}</span>
                  {templates.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {templates.length}
                    </Badge>
                  )}
                </div>
                <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent className="space-y-4 p-2 pt-2">
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={showDeleted ? 'secondary' : 'outline'}
                    onClick={() => setShowDeleted(!showDeleted)}
                    className="gap-1"
                  >
                    <BookDashed className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleExportTemplates} className="gap-1">
                    <Download className="h-3 w-3" />
                  </Button>
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".json"
                      onChange={handleImportTemplates}
                      className="hidden"
                    />
                    <Button size="sm" variant="outline" className="gap-1" onClick={handleImportClick}>
                      <Upload className="h-3 w-3" />
                    </Button>
                  </div>
                  <Dialog open={isNewTemplateDialogOpen} onOpenChange={setIsNewTemplateDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline" className="gap-1">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Save</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">{t('templateName')}</Label>
                          <Input
                            id="name"
                            value={newTemplateName}
                            onChange={e => setNewTemplateName(e.target.value)}
                            placeholder="Enter template name"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description">{t('description')}</Label>
                          <Textarea
                            id="description"
                            value={newTemplateDescription}
                            onChange={e => setNewTemplateDescription(e.target.value)}
                            placeholder="Enter template description"
                          />
                        </div>
                        <div className="text-muted-foreground text-sm">
                          {selectedElementIds.length > 0 ? (
                            <p>
                              {selectedElementIds.length} {t('elementsWillBeSavedInThisTemplate')}
                            </p>
                          ) : (
                            <p className="text-red-500">{t('pleaseSelectAtLeastOneElementToSaveAsTemplate')}</p>
                          )}
                        </div>
                        <Button
                          onClick={handleSaveTemplate}
                          disabled={isSaving || !newTemplateName.trim() || selectedElementIds.length === 0}
                        >
                          {isSaving ? t('saving') : t('saveTemplate')}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              {(templates.length > 0 || searchQuery.length > 0) && (
                <div className="relative">
                  <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
                  <Input
                    placeholder="Search templates..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="pl-8"
                  />
                </div>
              )}

              <div className="space-y-2">
                {templates.length === 0 ? (
                  <div className="text-muted-foreground py-6 text-center">
                    <p className="text-sm">No templates saved yet.</p>
                    <p className="mt-1 text-xs">Select elements and click &quot;+&quot; to create a template.</p>
                  </div>
                ) : (
                  templates.map(template => (
                    <Card key={template.id}>
                      <CardHeader className="p-2">
                        <div className="flex items-start justify-between">
                          <div
                            className="hover:bg-accent/50 cursor-pointer rounded-lg p-2 transition-colors"
                            onClick={() => {
                              setTemplateToEdit(template)
                              setEditName(template.name)
                              setEditDescription(template.description || '')
                              setEditStatus(template.status)
                            }}
                          >
                            <CardTitle className={`text-sm ${template.status === -1 ? 'line-through' : ''}`}>
                              {template.name}
                            </CardTitle>
                            <CardDescription className={`text-xs ${template.status === -1 ? 'line-through' : ''}`}>
                              {template.description || 'No description'}
                            </CardDescription>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleApplyTemplate(template)}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => setTemplateToDelete(template)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))
                )}
              </div>

              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-muted-foreground text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      <Dialog open={!!templateToDelete} onOpenChange={() => setTemplateToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{templateToDelete?.status === -1 ? 'Delete Permanently' : 'Delete Template'}</DialogTitle>
            <DialogDescription>
              {templateToDelete?.status === -1
                ? 'Are you sure you want to permanently delete this template? This action cannot be undone.'
                : `Are you sure you want to delete "${templateToDelete?.name}"? This will move it to the trash.`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setTemplateToDelete(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTemplate}>
              {templateToDelete?.status === -1 ? 'Delete Permanently' : 'Move to Trash'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isApplyTemplateDialogOpen} onOpenChange={setIsApplyTemplateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Apply Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="rename-ids">Rename element IDs</Label>
              <Switch id="rename-ids" checked={shouldRenameIds} onCheckedChange={setShouldRenameIds} />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="rename-inputs">Rename input names</Label>
              <Switch id="rename-inputs" checked={shouldRenameInputs} onCheckedChange={setShouldRenameInputs} />
            </div>
            {shouldRenameInputs && (
              <div className="space-y-2">
                <Label htmlFor="input-prefix">Input name prefix</Label>
                <Input
                  id="input-prefix"
                  value={inputPrefix}
                  onChange={e => setInputPrefix(e.target.value)}
                  placeholder="Enter prefix..."
                  required={shouldRenameInputs}
                />
                {shouldRenameInputs && !inputPrefix.trim() && (
                  <p className="text-sm text-red-500">Vui lòng nhập prefix hoặc tắt tùy chọn Rename input names</p>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApplyTemplateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirm}>Apply</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={!!templateToEdit} onOpenChange={() => setTemplateToEdit(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Template Name</Label>
              <Input
                id="edit-name"
                value={editName}
                onChange={e => setEditName(e.target.value)}
                placeholder="Enter template name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={editDescription}
                onChange={e => setEditDescription(e.target.value)}
                placeholder="Enter template description"
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="edit-status">Status</Label>
              <Select value={editStatus.toString()} onValueChange={value => setEditStatus(Number(value))}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Active</SelectItem>
                  <SelectItem value="-1">Deleted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setTemplateToEdit(null)}>
              Cancel
            </Button>
            <Button onClick={handleEditTemplate} disabled={!editName.trim()}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showImportConfirm} onOpenChange={setShowImportConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Templates</DialogTitle>
            <DialogDescription>
              <p className="mb-2">
                {overwrittenCount > 0
                  ? `This will import ${importCount} templates. ${overwrittenCount} existing templates will be overwritten.`
                  : `This will import ${importCount} templates.`}
              </p>
              <p>Do you want to backup your current templates before importing?</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImportConfirm(false)}>
              Cancel
            </Button>
            <Button variant="outline" onClick={() => handleConfirmImport(false)}>
              Import without backup
            </Button>
            <Button onClick={() => handleConfirmImport(true)}>Backup & Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
