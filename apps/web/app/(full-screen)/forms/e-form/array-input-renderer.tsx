'use client'

import { Any } from '@/lib/types'
import { useDroppable } from '@dnd-kit/core'
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@workspace/ui/components/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table'
import { toast } from '@workspace/ui/components/toast'
import { AlertCircle, ArrowDown, ArrowUp, GripVertical, Plus, Trash2 } from 'lucide-react'
import React from 'react'
import { useState } from 'react'

import { ElementRenderer } from './element-renderer'
import { type FormElement, useFormBuilder } from './form-builder'

interface ArrayInputRendererProps {
  element: FormElement
  updateElement: (elementId: string, updates: Partial<FormElement>) => void
  deleteElement: (elementId: string) => void
  moveElement: (elementId: string, direction: 'up' | 'down') => void
  duplicateElement: (elementId: string) => void
  isElementVisible: (element: FormElement) => boolean
}

interface ArrayItemProps {
  item: Record<string, Any>
  index: number
  element: FormElement
  onUpdate: (index: number, data: Record<string, Any>) => void
  onRemove: (index: number) => void
  onMoveUp: (index: number) => void
  onMoveDown: (index: number) => void
  displayMode: 'table' | 'cards'
  cardColumns: number
  allowRemove: boolean
  allowReorder: boolean
  showMoveButtons: boolean
  removeButtonText: string
  isFirstItem: boolean
  isLastItem: boolean
}

// Define supported form elements for arrays
const SUPPORTED_ARRAY_ELEMENTS = [
  'input',
  'select',
  'textarea',
  'checkbox',
  'radio',
  'date-picker',
  'file-upload',
  'rating',
  'slider',
  'select-api',
]

function ArrayItem({
  item,
  index,
  element,
  onUpdate,
  onRemove,
  onMoveUp,
  onMoveDown,
  displayMode,
  // cardColumns,
  allowRemove,
  allowReorder,
  showMoveButtons,
  // removeButtonText,
  isFirstItem,
  isLastItem,
}: ArrayItemProps) {
  const { isPreviewMode } = useFormBuilder()
  const [itemData, setItemData] = useState(item)

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: `array-item-${index}`,
    disabled: !allowReorder || !isPreviewMode,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  // Update local state when item prop changes
  React.useEffect(() => {
    setItemData(item)
  }, [item])

  const handleFieldChange = (fieldName: string, value: Any) => {
    const newData = { ...itemData, [fieldName]: value }
    setItemData(newData)
    onUpdate(index, newData)
  }

  const renderField = (child: FormElement) => {
    if (!child.name) return null

    const fieldValue = itemData[child.name] || ''

    // Create a modified version of the child element for rendering with array-specific data handling
    const modifiedChild = {
      ...child,
      id: `${child.id}-${index}`, // Unique ID for this array item
    }

    return (
      <div key={`${child.id}-${index}`} className="w-full">
        <ArrayFieldRenderer
          element={modifiedChild}
          originalElement={child}
          value={fieldValue}
          onChange={value => handleFieldChange(child.name!, value)}
          index={index}
        />
      </div>
    )
  }

  if (displayMode === 'table') {
    return (
      <TableRow ref={setNodeRef} style={style} className="group">
        {(allowReorder || showMoveButtons) && isPreviewMode && (
          <TableCell className="w-16">
            <div className="flex items-center gap-1">
              {allowReorder && (
                <div {...attributes} {...listeners} className="cursor-grab">
                  <GripVertical className="text-muted-foreground h-4 w-4" />
                </div>
              )}
              {showMoveButtons && (
                <div className="flex flex-col">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-5 w-5 p-0"
                    onClick={() => onMoveUp(index)}
                    disabled={isFirstItem}
                  >
                    <ArrowUp className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-5 w-5 p-0"
                    onClick={() => onMoveDown(index)}
                    disabled={isLastItem}
                  >
                    <ArrowDown className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </TableCell>
        )}
        {element.children?.map(child => (
          <TableCell key={`${child.id}-${index}`} className="p-2">
            {renderField(child)}
          </TableCell>
        ))}
        {allowRemove && isPreviewMode && (
          <TableCell className="w-16">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onRemove(index)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </TableCell>
        )}
      </TableRow>
    )
  }

  // Card view
  return (
    <Card ref={setNodeRef} style={style} className="group relative">
      <CardContent className="p-4">
        {(allowReorder || allowRemove || showMoveButtons) && isPreviewMode && (
          <div className="absolute top-2 right-2 flex items-center gap-1">
            {allowReorder && (
              <div {...attributes} {...listeners} className="cursor-grab p-1">
                <GripVertical className="text-muted-foreground h-4 w-4" />
              </div>
            )}
            {showMoveButtons && (
              <div className="flex flex-col">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => onMoveUp(index)}
                  disabled={isFirstItem}
                >
                  <ArrowUp className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => onMoveDown(index)}
                  disabled={isLastItem}
                >
                  <ArrowDown className="h-3 w-3" />
                </Button>
              </div>
            )}
            {allowRemove && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onRemove(index)}
                className="p-1 text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
        <div className="space-y-3">{element.children?.map(child => renderField(child))}</div>
      </CardContent>
    </Card>
  )
}

// New component to render array fields with isolated data
function ArrayFieldRenderer({
  element,
  originalElement,
  value,
  onChange,
  index,
}: {
  element: FormElement
  originalElement: FormElement
  value: Any
  onChange: (value: Any) => void
  index: number
}) {
  const { isPreviewMode, setSelectedElement, selectedElement, validationErrors } = useFormBuilder()
  const isSelected = selectedElement?.id === originalElement.id

  const handleClick = (e: React.MouseEvent) => {
    if (!isPreviewMode) {
      e.stopPropagation()
      e.preventDefault()
      setSelectedElement(originalElement)
    }
  }

  // Get validation error for this element
  const elementError = validationErrors.find(error => error.elementId === originalElement.id)
  const hasError = !!elementError

  const renderInput = () => {
    switch (element.type) {
      case 'input':
        return (
          <div>
            {element.label && (
              <label className="mb-2 block text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            <input
              type="text"
              placeholder={element.placeholder}
              value={value || ''}
              onChange={e => onChange(e.target.value)}
              className={`w-full rounded-md border px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none ${
                hasError ? 'border-red-500' : 'border-gray-300'
              }`}
              {...element.customAttributes}
            />
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'select':
        return (
          <div>
            {element.label && (
              <label className="mb-2 block text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            <select
              value={value || ''}
              onChange={e => onChange(e.target.value)}
              className={`w-full rounded-md border px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none ${
                hasError ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">Select an option</option>
              {element.options?.map((option, idx) => (
                <option key={idx} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'textarea':
        return (
          <div>
            {element.label && (
              <label className="mb-2 block text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            <textarea
              placeholder={element.placeholder}
              value={value || ''}
              onChange={e => onChange(e.target.value)}
              className={`w-full rounded-md border px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none ${
                hasError ? 'border-red-500' : 'border-gray-300'
              }`}
              rows={3}
              {...element.customAttributes}
            />
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={element.id}
              checked={Boolean(value)}
              onChange={e => onChange(e.target.checked)}
              className={`rounded focus:ring-2 focus:ring-blue-500 ${hasError ? 'border-red-500' : 'border-gray-300'}`}
            />
            {element.label && (
              <label htmlFor={element.id} className="text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'radio':
        return (
          <div>
            {element.label && (
              <label className="mb-2 block text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            <div className={`space-y-2 ${hasError ? 'rounded border border-red-500 p-2' : ''}`}>
              {element.options?.map((option, idx) => (
                <div key={idx} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={`${element.id}-${idx}`}
                    name={`${element.name}-${index}`}
                    value={option.value}
                    checked={value === option}
                    onChange={e => onChange(e.target.value)}
                    className="focus:ring-2 focus:ring-blue-500"
                  />
                  <label htmlFor={`${element.id}-${idx}`} className="text-sm">
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'rating': {
        const maxRating = element.customAttributes?.max || 5
        const currentRating = value || 0

        return (
          <div>
            {element.label && (
              <label className="mb-2 block text-sm font-medium">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </label>
            )}
            <div className="flex items-center gap-1">
              {Array.from({ length: maxRating }).map((_, starIndex) => (
                <button
                  key={starIndex}
                  type="button"
                  onClick={() => onChange(starIndex + 1)}
                  className="rounded p-1 hover:bg-gray-100"
                >
                  {starIndex + 1 <= currentRating ? (
                    <span className="text-lg text-yellow-400">★</span>
                  ) : (
                    <span className="text-lg text-gray-300">★</span>
                  )}
                </button>
              ))}
            </div>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )
      }

      case 'slider': {
        const min = element.customAttributes?.min || 0
        const max = element.customAttributes?.max || 100
        const step = element.customAttributes?.step || 1
        const sliderValue = value || min

        return (
          <div>
            {element.label && (
              <div className="mb-2 flex items-center justify-between">
                <label className="text-sm font-medium">
                  {element.label}
                  {element.required && <span className="ml-1 text-red-500">*</span>}
                </label>
                <span className="text-sm font-medium">{sliderValue}</span>
              </div>
            )}
            <input
              type="range"
              min={min}
              max={max}
              step={step}
              value={sliderValue}
              onChange={e => onChange(Number(e.target.value))}
              className={`w-full ${hasError ? 'accent-red-500' : 'accent-blue-500'}`}
            />
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )
      }

      default:
        return (
          <div>
            <p className="text-sm text-gray-500">Unsupported field type: {element.type}</p>
          </div>
        )
    }
  }

  return (
    <div
      className={`relative ${isSelected && !isPreviewMode ? 'ring-primary rounded ring-2' : ''} ${
        !isPreviewMode ? 'cursor-pointer' : ''
      }`}
      onClick={handleClick}
    >
      {renderInput()}
      {/* Show selection indicator for array fields */}
      {isSelected && !isPreviewMode && (
        <div className="bg-primary text-primary-foreground absolute -top-6 left-0 z-20 rounded px-2 py-1 text-xs">
          {originalElement.label || originalElement.type} (Array Field)
        </div>
      )}
    </div>
  )
}

export function ArrayInputRenderer({
  element,
  updateElement,
  deleteElement,
  moveElement,
  duplicateElement,
  isElementVisible,
}: ArrayInputRendererProps) {
  const {
    currentBreakpoint,
    isPreviewMode,
    formData,
    setFormData,
    validationErrors,
    selectedElement,
    setSelectedElement,
  } = useFormBuilder()
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [newItemData, setNewItemData] = useState<Record<string, Any>>({})

  const arrayConfig = element.arrayConfig || {
    displayMode: { desktop: 'table', tablet: 'table', mobile: 'cards' },
    addView: 'inline',
    minItems: 0,
    maxItems: 10,
    allowAdd: true,
    allowRemove: true,
    allowReorder: true,
    showMoveButtons: true,
    cardColumns: { desktop: 2, tablet: 2, mobile: 1 },
    tableHeaders: true,
    addButtonText: 'Add Item',
    removeButtonText: 'Remove',
  }

  const { isOver, setNodeRef: setDroppableRef } = useDroppable({
    id: element.id,
    data: { accepts: ['element'] },
    disabled: isPreviewMode,
  })

  // Get current array data
  const arrayData = (formData[element.name || ''] as Record<string, Any>[]) || []
  const displayMode = arrayConfig.displayMode[currentBreakpoint]
  const cardColumns = arrayConfig.cardColumns[currentBreakpoint]

  // Get validation error for this element
  const elementError = validationErrors.find(error => error.elementId === element.id)

  // Check if element is supported in arrays
  const isElementSupported = (elementType: string): boolean => {
    return SUPPORTED_ARRAY_ELEMENTS.includes(elementType)
  }

  // Validate and clean up children - remove unsupported elements
  const validateAndCleanChildren = (children: FormElement[]): FormElement[] => {
    const validChildren: FormElement[] = []
    const removedElements: string[] = []

    children.forEach(child => {
      if (isElementSupported(child.type)) {
        validChildren.push(child)
      } else {
        removedElements.push(child.label || child.type)
      }
    })

    // Show notification if elements were removed
    if (removedElements.length > 0) {
      // toast({
      //   title: 'Unsupported Elements Removed',
      //   description: `The following elements are not supported in arrays and were removed: ${removedElements.join(', ')}. Only form input elements are allowed.`,
      //   variant: 'destructive',
      // })
      toast.error(
        `The following elements are not supported in arrays and were removed: ${removedElements.join(', ')}. Only form input elements are allowed.`
      )
    }

    return validChildren
  }

  // Clean up children whenever element updates
  React.useEffect(() => {
    if (element.children && element.children.length > 0) {
      const validChildren = validateAndCleanChildren(element.children)

      if (validChildren.length !== element.children.length) {
        updateElement(element.id, { children: validChildren })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [element.children])

  const initializeNewItem = () => {
    const newItem: Record<string, Any> = {}
    element.children?.forEach(child => {
      if (child.name) {
        newItem[child.name] = ''
      }
    })
    setNewItemData(newItem)
  }

  const addItem = () => {
    if (arrayData.length >= arrayConfig.maxItems) return

    if (arrayConfig.addView === 'modal') {
      initializeNewItem()
      setIsAddModalOpen(true)
    } else {
      // Inline add
      const newItem: Record<string, Any> = {}
      element.children?.forEach(child => {
        if (child.name) {
          newItem[child.name] = ''
        }
      })

      const newArrayData = [...arrayData, newItem]
      setFormData({ ...formData, [element.name || '']: newArrayData })
    }
  }

  const confirmAddItem = () => {
    const newArrayData = [...arrayData, newItemData]
    setFormData({ ...formData, [element.name || '']: newArrayData })
    setIsAddModalOpen(false)
    setNewItemData({})
  }

  const removeItem = (index: number) => {
    if (arrayData.length <= arrayConfig.minItems) return

    const newArrayData = arrayData.filter((_, i) => i !== index)
    setFormData({ ...formData, [element.name || '']: newArrayData })
  }

  const updateItem = (index: number, itemData: Record<string, Any>) => {
    const newArrayData = [...arrayData]
    newArrayData[index] = itemData
    setFormData({ ...formData, [element.name || '']: newArrayData })
  }

  const moveItemUp = (index: number) => {
    if (index === 0) return
    const newArrayData = arrayMove(arrayData, index, index - 1)
    setFormData({ ...formData, [element.name || '']: newArrayData })
  }

  const moveItemDown = (index: number) => {
    if (index === arrayData.length - 1) return
    const newArrayData = arrayMove(arrayData, index, index + 1)
    setFormData({ ...formData, [element.name || '']: newArrayData })
  }

  // const reorderItems = (oldIndex: number, newIndex: number) => {
  //   const newArrayData = arrayMove(arrayData, oldIndex, newIndex)
  //   setFormData({ ...formData, [element.name || '']: newArrayData })
  // }

  const canAddMore = arrayData.length < arrayConfig.maxItems
  const canRemove = arrayData.length > arrayConfig.minItems

  const handleClick = (e: React.MouseEvent) => {
    if (isPreviewMode) return
    e.stopPropagation()
    setSelectedElement(element)
  }

  const renderChildren = (children: FormElement[]) => {
    return children.map(child => {
      // Skip rendering if child has a visibility condition that evaluates to false
      if (!isElementVisible(child)) return null

      return (
        <ElementRenderer
          key={child.id}
          element={child}
          updateElement={updateElement}
          deleteElement={deleteElement}
          moveElement={moveElement}
          duplicateElement={duplicateElement}
          isElementVisible={isElementVisible}
          isNested={true}
        />
      )
    })
  }

  const renderModalFields = () => {
    if (!element.children) return null

    return element.children.map(child => {
      if (!child.name) return null

      const fieldValue = newItemData[child.name] || ''

      return (
        <div key={child.id} className="space-y-2">
          <label className="text-sm font-medium">{child.label || child.name}</label>
          <ArrayFieldRenderer
            element={child}
            originalElement={child}
            value={fieldValue}
            onChange={value => setNewItemData({ ...newItemData, [child.name!]: value })}
            index={-1} // Modal doesn't need index
          />
        </div>
      )
    })
  }

  const renderArrayContent = () => {
    if (!isPreviewMode) {
      // Design mode - show drop zone and configuration like Grid
      return (
        <div
          ref={setDroppableRef}
          className={`min-h-32 rounded-lg border-2 border-dashed p-4 ${
            isOver ? 'border-green-400 bg-green-50' : 'border-green-300'
          }`}
          onClick={handleClick}
        >
          {element.children && element.children.length > 0 ? (
            <div className="space-y-2">
              <div className="mb-2 text-sm font-medium text-green-700">Array Template Fields:</div>
              {renderChildren(element.children)}
              <div className="text-muted-foreground mt-4 rounded bg-green-50 p-2 text-xs">
                <strong>Display Settings:</strong>
                <br />
                Mode: {displayMode}
                {displayMode === 'cards' && ` (${cardColumns} columns)`}
                <br />
                Add View: {arrayConfig.addView}
                <br />
                <strong>Supported Elements:</strong> Form inputs only (input, select, textarea, checkbox, radio,
                date-picker, file-upload, rating, slider)
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground text-center">
              <p className="text-sm font-medium">Drop form elements here</p>
              <p className="mt-1 text-xs">Only form input elements are supported in arrays</p>
              <div className="mt-3 text-xs">
                <strong>Supported:</strong> Input, Select, Textarea, Checkbox, Radio, Date Picker, File Upload, Rating,
                Slider
              </div>
              <div className="mt-1 text-xs text-red-600">
                <strong>Not Supported:</strong> Buttons, Grids, Columns, Arrays, HTML elements
              </div>
            </div>
          )}
        </div>
      )
    }

    // Preview mode - show actual array interface
    if (!element.children || element.children.length === 0) {
      return (
        <div className="text-muted-foreground py-8 text-center">
          <AlertCircle className="mx-auto mb-2 h-8 w-8" />
          <p className="text-sm">No fields configured for this array</p>
        </div>
      )
    }

    if (displayMode === 'table') {
      return (
        <div className="space-y-4">
          <Table>
            {arrayConfig.tableHeaders && (
              <TableHeader>
                <TableRow>
                  {(arrayConfig.allowReorder || arrayConfig.showMoveButtons) && (
                    <TableHead className="w-16">Actions</TableHead>
                  )}
                  {element.children.map(child => (
                    <TableHead key={child.id}>{child.label || child.name}</TableHead>
                  ))}
                  {arrayConfig.allowRemove && <TableHead className="w-16">Remove</TableHead>}
                </TableRow>
              </TableHeader>
            )}
            <TableBody>
              <SortableContext
                items={arrayData.map((_, i) => `array-item-${i}`)}
                strategy={verticalListSortingStrategy}
              >
                {arrayData.map((item, index) => (
                  <ArrayItem
                    key={index}
                    item={item}
                    index={index}
                    element={element}
                    onUpdate={updateItem}
                    onRemove={removeItem}
                    onMoveUp={moveItemUp}
                    onMoveDown={moveItemDown}
                    displayMode="table"
                    cardColumns={cardColumns}
                    allowRemove={canRemove && arrayConfig.allowRemove}
                    allowReorder={arrayConfig.allowReorder}
                    showMoveButtons={arrayConfig.showMoveButtons}
                    removeButtonText={arrayConfig.removeButtonText}
                    isFirstItem={index === 0}
                    isLastItem={index === arrayData.length - 1}
                  />
                ))}
              </SortableContext>
            </TableBody>
          </Table>
        </div>
      )
    }

    // Card view
    return (
      <div className="space-y-4">
        <SortableContext items={arrayData.map((_, i) => `array-item-${i}`)} strategy={verticalListSortingStrategy}>
          <div
            className="grid gap-4"
            style={{
              gridTemplateColumns: `repeat(${cardColumns}, 1fr)`,
            }}
          >
            {arrayData.map((item, index) => (
              <ArrayItem
                key={index}
                item={item}
                index={index}
                element={element}
                onUpdate={updateItem}
                onRemove={removeItem}
                onMoveUp={moveItemUp}
                onMoveDown={moveItemDown}
                displayMode="cards"
                cardColumns={cardColumns}
                allowRemove={canRemove && arrayConfig.allowRemove}
                allowReorder={arrayConfig.allowReorder}
                showMoveButtons={arrayConfig.showMoveButtons}
                removeButtonText={arrayConfig.removeButtonText}
                isFirstItem={index === 0}
                isLastItem={index === arrayData.length - 1}
              />
            ))}
          </div>
        </SortableContext>
      </div>
    )
  }

  const isSelected = selectedElement?.id === element.id

  return (
    <div className={`space-y-4 ${isSelected && !isPreviewMode ? 'ring-primary rounded-lg ring-2' : ''}`}>
      {element.label && (
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">
            {element.label}
            {element.required && <span className="ml-1 text-red-500">*</span>}
          </h3>
          {isPreviewMode && (
            <Badge variant="outline" className="text-xs">
              {arrayData.length} / {arrayConfig.maxItems} items
            </Badge>
          )}
        </div>
      )}

      {renderArrayContent()}

      {isPreviewMode && arrayConfig.allowAdd && canAddMore && (
        <>
          {arrayConfig.addView === 'modal' ? (
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
              <DialogTrigger asChild>
                <Button onClick={addItem} variant="outline" size="sm" className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  {arrayConfig.addButtonText}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Item</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {renderModalFields()}
                  <div className="flex gap-2 pt-4">
                    <Button onClick={confirmAddItem} className="flex-1">
                      Add Item
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddModalOpen(false)} className="flex-1">
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          ) : (
            <Button onClick={addItem} variant="outline" size="sm" className="w-full">
              <Plus className="mr-2 h-4 w-4" />
              {arrayConfig.addButtonText}
            </Button>
          )}
        </>
      )}

      {elementError && <p className="text-sm text-red-500">{elementError.message}</p>}

      {isPreviewMode && (
        <div className="text-muted-foreground text-xs">
          <div>Items: {arrayData.length}</div>
          <div>
            Range: {arrayConfig.minItems} - {arrayConfig.maxItems}
          </div>
          <div>Mode: {displayMode}</div>
          <div>Add View: {arrayConfig.addView}</div>
        </div>
      )}
    </div>
  )
}
