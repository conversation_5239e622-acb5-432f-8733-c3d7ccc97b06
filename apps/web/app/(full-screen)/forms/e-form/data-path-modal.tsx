'use client'

import { type ApiEndpoint, type ApiSchema } from '@/lib/json-form/json-form-api-service'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { ArrowRight, Database, Info } from 'lucide-react'
import { useState } from 'react'

interface DataPathModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  endpoint: ApiEndpoint
  schema: ApiSchema
  onConfirm: (endpoint: ApiEndpoint, schema: ApiSchema, dataPath: string) => void
}

export function DataPathModal({ open, onOpenChange, endpoint, schema, onConfirm }: DataPathModalProps) {
  const [dataPath, setDataPath] = useState('items')

  const handleConfirm = () => {
    onConfirm(endpoint, schema, dataPath)
    onOpenChange(false)
  }

  const handleClose = () => {
    onOpenChange(false)
    setDataPath('data.items')
  }

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'POST':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'PUT':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PATCH':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'WS':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Cấu hình đường dẫn dữ liệu
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Endpoint Info */}
          <Card className="bg-muted/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Endpoint đã chọn</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className={`font-mono text-xs ${getMethodColor(endpoint.method)}`}>
                  {endpoint.method}
                </Badge>
                <code className="bg-muted rounded px-2 py-1 font-mono text-sm">{endpoint.path}</code>
              </div>
            </CardContent>
          </Card>

          {/* Data Path Input */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="dataPath" className="text-sm font-medium">
                Đường dẫn dữ liệu
              </Label>
              <div className="mt-2">
                <Input
                  id="dataPath"
                  value={dataPath}
                  onChange={e => setDataPath(e.target.value)}
                  placeholder="Ví dụ: data.items, data, response.items"
                  className="font-mono"
                />
              </div>
              <p className="text-muted-foreground mt-1 text-xs">Đường dẫn đến mảng dữ liệu trong response</p>
            </div>

            {/* Examples */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Info className="h-4 w-4" />
                  Ví dụ cấu trúc response
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      data.items
                    </Badge>
                    <span className="text-muted-foreground text-xs">
                      → Cho response dạng {`{ data: { items: [...] } }`}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      data
                    </Badge>
                    <span className="text-muted-foreground text-xs">→ Cho response dạng {`{ data: [...] }`}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      items
                    </Badge>
                    <span className="text-muted-foreground text-xs">→ Cho response dạng {`{ items: [...] }`}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button onClick={handleConfirm} className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              Tạo bảng
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
