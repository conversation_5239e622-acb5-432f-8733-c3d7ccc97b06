'use client'

import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Switch } from '@workspace/ui/components/switch'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, Grid3X3, Monitor, Smartphone, Tablet } from 'lucide-react'
import type React from 'react'
import { useState } from 'react'

import type { AutoLayoutConfig, Breakpoint, FormElement } from './form-builder'

interface BoxLayoutConfigProps {
  element: FormElement
  onUpdateLayout: (autoLayout: AutoLayoutConfig) => void
}

export function BoxLayoutConfig({ element, onUpdateLayout }: BoxLayoutConfigProps) {
  const t = useTranslations('e-form')
  const [isOpen, setIsOpen] = useState(true)
  const [customResponsive, setCustomResponsive] = useState(false)

  if (element.type !== 'grid') return null

  const autoLayout = element.autoLayout || {
    desktop: { columns: 4, gap: '16px' },
    tablet: { columns: 2, gap: '12px' },
    mobile: { columns: 1, gap: '8px' },
  }

  const columnOptions = [1, 2, 3, 4, 6]

  const updateColumns = (breakpoint: Breakpoint, columns: number) => {
    const newAutoLayout = {
      ...autoLayout,
      [breakpoint]: {
        ...autoLayout[breakpoint],
        columns,
      },
    }
    onUpdateLayout(newAutoLayout)
  }

  const updateGap = (breakpoint: Breakpoint, gap: string) => {
    const newAutoLayout = {
      ...autoLayout,
      [breakpoint]: {
        ...autoLayout[breakpoint],
        gap,
      },
    }
    onUpdateLayout(newAutoLayout)
  }

  const setUniformColumns = (columns: number) => {
    const newAutoLayout = {
      desktop: { ...autoLayout.desktop, columns },
      tablet: { ...autoLayout.tablet, columns: Math.min(columns, 3) },
      mobile: { ...autoLayout.mobile, columns: Math.min(columns, 2) },
    }
    onUpdateLayout(newAutoLayout)
  }

  const ColumnButton = ({ columns, breakpoint }: { columns: number; breakpoint?: Breakpoint }) => {
    const isActive = breakpoint ? autoLayout[breakpoint].columns === columns : false
    const isUniformActive =
      !breakpoint &&
      autoLayout.desktop.columns === columns &&
      autoLayout.tablet.columns === Math.min(columns, 3) &&
      autoLayout.mobile.columns === Math.min(columns, 2)

    return (
      <Button
        size="sm"
        variant={isActive || isUniformActive ? 'default' : 'outline'}
        onClick={() => (breakpoint ? updateColumns(breakpoint, columns) : setUniformColumns(columns))}
        className="flex h-auto flex-col items-center gap-1 py-2"
      >
        <div className="flex gap-0.5">
          {Array.from({ length: Math.min(columns, 4) }).map((_, i) => (
            <div key={i} className="h-2 w-2 rounded-sm bg-current opacity-70" />
          ))}
          {columns > 4 && <span className="text-xs">+</span>}
        </div>
        <span className="text-xs">{columns}</span>
      </Button>
    )
  }

  const ResponsiveConfig = ({ breakpoint, icon }: { breakpoint: Breakpoint; icon: React.ReactNode }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        {icon}
        <span className="text-sm font-medium capitalize">{breakpoint}</span>
        <Badge variant="secondary" className="text-xs">
          {autoLayout[breakpoint].columns} {t('columns')}
        </Badge>
      </div>

      <div>
        <Label className="text-xs">{t('columns')}</Label>
        <div className="mt-1 flex gap-1">
          {columnOptions.map(columns => (
            <ColumnButton key={columns} columns={columns} breakpoint={breakpoint} />
          ))}
        </div>
      </div>

      <div>
        <Label className="text-xs">{t('gap')}</Label>
        <div className="mt-1 flex gap-2">
          {['8px', '12px', '16px', '24px', '32px'].map(gap => (
            <Button
              key={gap}
              size="sm"
              variant={autoLayout[breakpoint].gap === gap ? 'default' : 'outline'}
              onClick={() => updateGap(breakpoint, gap)}
              className="px-2 text-xs"
            >
              {gap}
            </Button>
          ))}
        </div>
        <Input
          className="mt-2 text-xs"
          value={autoLayout[breakpoint].gap}
          onChange={e => updateGap(breakpoint, e.target.value)}
          placeholder="Custom gap (e.g., 20px)"
        />
      </div>
    </div>
  )

  return (
    <Card className="shadow-none">
      <CardHeader className="hover:bg-accent/50 cursor-pointer transition-colors" onClick={() => setIsOpen(!isOpen)}>
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Grid3X3 className="h-4 w-4" />
            {t('layoutConfiguration')}
            <Badge variant="secondary" className="text-xs">
              {autoLayout.desktop.columns}×{autoLayout.tablet.columns}×{autoLayout.mobile.columns}
            </Badge>
          </div>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </CardTitle>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-6 px-3">
          {/* Quick Column Selection */}
          <div>
            <div className="mb-3 flex items-center justify-between">
              <Label className="text-sm font-medium">{t('quickSetup')}</Label>
              <span className="text-muted-foreground text-xs">{t('desktopToTabletToMobile')}</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {columnOptions.map(columns => (
                <ColumnButton key={columns} columns={columns} />
              ))}
            </div>
            <p className="text-muted-foreground mt-2 text-xs">
              {t('autoAdjusts')}: {autoLayout.desktop.columns} {t('desktop')} →{' '}
              {Math.min(autoLayout.desktop.columns, 3)} {t('tablet')} → {Math.min(autoLayout.desktop.columns, 2)}{' '}
              {t('mobile')}
            </p>
          </div>

          {/* Custom Responsive Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">{t('customResponsiveSettings')}</Label>
              <p className="text-muted-foreground text-xs">{t('configureEachBreakpointIndividually')}</p>
            </div>
            <Switch checked={customResponsive} onCheckedChange={setCustomResponsive} />
          </div>

          {/* Custom Responsive Configuration */}
          {customResponsive && (
            <div className="space-y-6 border-t pt-4">
              <ResponsiveConfig breakpoint="desktop" icon={<Monitor className="h-4 w-4" />} />

              <ResponsiveConfig breakpoint="tablet" icon={<Tablet className="h-4 w-4" />} />

              <ResponsiveConfig breakpoint="mobile" icon={<Smartphone className="h-4 w-4" />} />
            </div>
          )}

          {/* Layout Preview */}
          <div className="border-t pt-4">
            <Label className="mb-3 block text-sm font-medium">{t('layoutPreview')}</Label>
            <div className="space-y-3">
              {(['desktop', 'tablet', 'mobile'] as Breakpoint[]).map(breakpoint => (
                <div key={breakpoint} className="flex items-center gap-3">
                  <div className="text-muted-foreground w-16 text-xs capitalize">{breakpoint}</div>
                  <div
                    className="flex-1 rounded border p-2"
                    style={{
                      display: 'grid',
                      gridTemplateColumns: `repeat(${autoLayout[breakpoint].columns}, 1fr)`,
                      gap: '2px',
                    }}
                  >
                    {Array.from({ length: Math.min(autoLayout[breakpoint].columns, 8) }).map((_, i) => (
                      <div key={i} className="bg-primary/20 h-4 rounded-sm" />
                    ))}
                  </div>
                  <div className="text-muted-foreground w-12 text-xs">{autoLayout[breakpoint].gap}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
