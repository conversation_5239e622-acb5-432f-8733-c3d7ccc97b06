'use client'

import { Button } from '@workspace/ui/components/button'
import { Redo2, Undo2 } from 'lucide-react'
import { useEffect } from 'react'

import { useFormBuilder } from './form-builder'

export function HistoryManager() {
  const { undo, redo, canUndo, canRedo, historyIndex, history } = useFormBuilder()

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey && event.key === 'z') {
        event.preventDefault()

        if (canUndo) undo()
      } else if (
        ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'Z') ||
        ((event.ctrlKey || event.metaKey) && event.key === 'y')
      ) {
        event.preventDefault()

        if (canRedo) redo()
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [undo, redo, canUndo, canRedo])

  return (
    <div className="flex items-center gap-1">
      <Button
        size="sm"
        variant="outline"
        onClick={undo}
        disabled={!canUndo}
        className="px-2"
        title={`Undo (Ctrl+Z) - ${historyIndex}/${history.length - 1}`}
      >
        <Undo2 className="h-4 w-4" />
        <span className="sr-only">Undo</span>
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={redo}
        disabled={!canRedo}
        className="px-2"
        title={`Redo (Ctrl+Shift+Z) - ${historyIndex}/${history.length - 1}`}
      >
        <Redo2 className="h-4 w-4" />
        <span className="sr-only">Redo</span>
      </Button>

      {/* History indicator */}
      <div className="text-muted-foreground ml-2 text-xs">
        {historyIndex + 1}/{history.length}
      </div>
    </div>
  )
}
