'use client'

import { Any } from '@/lib/types'
import { useDroppable } from '@dnd-kit/core'
import { useSortable } from '@dnd-kit/sortable'
import { Button } from '@workspace/ui/components/button'
import { Calendar } from '@workspace/ui/components/calendar'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover'
import { RadioGroup, RadioGroupItem } from '@workspace/ui/components/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Slider } from '@workspace/ui/components/slider'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { cn } from '@workspace/ui/lib/utils'
import { format } from 'date-fns'
import { AlertCircle, ArrowDown, ArrowUp, CalendarIcon, Copy, GripVertical, Star, Trash2, Upload } from 'lucide-react'
import type React from 'react'

import { ArrayInputRenderer } from './array-input-renderer'
import { DetailRenderer } from './detail-renderer'
import { type FormElement, useFormBuilder } from './form-builder'
import { GridColumnIndicator } from './grid-column-indicator'
import { GridResizeHandle } from './grid-resize-handle'
import { TableRenderer } from './table-renderer'

/* eslint-disable @next/next/no-img-element */

interface ElementRendererProps {
  element: FormElement
  updateElement: (elementId: string, updates: Partial<FormElement>) => void
  deleteElement: (elementId: string) => void
  moveElement: (elementId: string, direction: 'up' | 'down') => void
  duplicateElement: (elementId: string) => void
  isElementVisible: (element: FormElement) => boolean
  isNested?: boolean
  columnSpan?: number
  columnOrder?: number
}

function useElementDroppable(element: FormElement, isPreviewMode: boolean) {
  return useDroppable({
    id: element.id,
    data: {
      accepts: ['element'],
      type: element.type,
    },
    disabled: isPreviewMode,
  })
}

export function ElementRenderer({
  element,
  updateElement,
  deleteElement,
  moveElement,
  duplicateElement,
  isElementVisible,
  isNested = false,
  columnSpan,
  columnOrder,
}: ElementRendererProps) {
  const t = useTranslations('e-form')
  const {
    selectedElement,
    setSelectedElement,
    currentBreakpoint,
    isPreviewMode,
    formData,
    setFormData,
    validationErrors,
    onFormSubmit,
    validateForm,
  } = useFormBuilder()
  const isSelected = selectedElement?.id === element.id

  // Always call hooks, but disable them in preview mode
  const {
    attributes,
    listeners,
    setNodeRef: setSortableRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: element.id,
    data: { type: 'element' },
    disabled: isPreviewMode,
  })

  const { setNodeRef: setDroppableRef, isOver } = useElementDroppable(element, isPreviewMode)

  const style: React.CSSProperties = isPreviewMode
    ? {}
    : {
        transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
        transition,
        opacity: isDragging ? 0.5 : 1,
      }

  const currentStyles = element.styles[currentBreakpoint]

  // Check responsive visibility
  const isResponsiveVisible = () => {
    const responsiveVisibility = element.customAttributes?.responsiveVisibility

    if (!responsiveVisibility) return true

    switch (currentBreakpoint) {
      case 'desktop':
        return responsiveVisibility.desktop !== false
      case 'tablet':
        return responsiveVisibility.tablet !== false
      case 'mobile':
        return responsiveVisibility.mobile !== false
      default:
        return true
    }
  }

  // Don't render if hidden on current breakpoint
  if (!isResponsiveVisible()) return null

  const getElementStyles = (): React.CSSProperties => {
    const styles: React.CSSProperties = {}

    Object.entries(currentStyles).forEach(([key, value]) => {
      if (value) {
        styles[key as keyof React.CSSProperties] = value
      }
    })

    // Apply column span and order if provided (for children in column layouts)
    if (columnSpan !== undefined) {
      styles.gridColumn = `span ${columnSpan}`
    }

    if (columnOrder !== undefined) {
      styles.order = columnOrder.toString()
    }

    // Remove dashed borders in preview mode for containers
    if (isPreviewMode && (element.type === 'grid' || element.type === 'column' || element.type === 'array')) {
      if ((styles.border as string)?.includes('dashed')) {
        delete styles.border
      }

      if (styles.backgroundColor === '#eff6ff' || styles.backgroundColor === '#ecfdf5') {
        delete styles.backgroundColor
      }
    }

    // Apply text formatting for HTML elements
    const textFormatting = element.customAttributes?.textFormatting

    if (textFormatting && ['h1', 'h2', 'h3', 'paragraph', 'html', 'span'].includes(element.type)) {
      if (textFormatting.bold) styles.fontWeight = 'bold'

      if (textFormatting.italic) styles.fontStyle = 'italic'

      if (textFormatting.underline) styles.textDecoration = 'underline'

      if (textFormatting.alignment) styles.textAlign = textFormatting.alignment as Any

      if (textFormatting.fontSize) styles.fontSize = textFormatting.fontSize

      if (textFormatting.fontWeight) styles.fontWeight = textFormatting.fontWeight

      if (textFormatting.color) styles.color = textFormatting.color

      if (textFormatting.backgroundColor) styles.backgroundColor = textFormatting.backgroundColor
    }

    return styles
  }

  const handleClick = (e: React.MouseEvent) => {
    console.log('Element clicked:', element.id, element.label || element.type, { isPreviewMode, isNested })

    if (isPreviewMode) return

    e.stopPropagation()
    console.log('Setting selected element:', element.id)
    setSelectedElement(element)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    deleteElement(element.id)
  }

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation()
    duplicateElement(element.id)
  }

  const handleMoveUp = (e: React.MouseEvent) => {
    e.stopPropagation()
    moveElement(element.id, 'up')
  }

  const handleMoveDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    moveElement(element.id, 'down')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (element.name) {
      setFormData({ ...formData, [element.name]: e.target.value })
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    if (element.name) {
      setFormData({ ...formData, [element.name]: checked })
    }
  }

  const handleButtonClick = async (e: React.MouseEvent) => {
    const buttonType = element.customAttributes?.buttonType || 'button'

    if (buttonType === 'submit') {
      e.preventDefault()

      // Validate the form
      const isValid = validateForm()

      if (isValid && onFormSubmit) {
        try {
          await onFormSubmit(formData)
        } catch (error) {
          console.error('Form submission error:', error)
        }
      }
    }
  }

  // Get validation error for this element
  const elementError = validationErrors.find(error => error.elementId === element.id)

  const renderChildren = (children: FormElement[]) => {
    return children.map(child => {
      // Skip rendering if child has a visibility condition that evaluates to false
      if (!isElementVisible(child)) return null

      return (
        <ElementRenderer
          key={child.id}
          element={child}
          updateElement={updateElement}
          deleteElement={deleteElement}
          moveElement={moveElement}
          duplicateElement={duplicateElement}
          isElementVisible={isElementVisible}
          isNested={true}
        />
      )
    })
  }

  const renderColumnChildren = (children: FormElement[]) => {
    return children.map(child => {
      // Skip rendering if child has a visibility condition that evaluates to false
      if (!isElementVisible(child)) return null

      // Get column span and order for this child
      const childColumnConfig = element.columnConfig?.children[child.id]
      const childBreakpointConfig = childColumnConfig?.[currentBreakpoint]
      const span = childBreakpointConfig?.span || 1
      const order = childBreakpointConfig?.order || 0

      // Apply column styles directly to the child element's style
      // const childWithColumnStyles = {
      //   ...child,
      //   styles: {
      //     ...child.styles,
      //     [currentBreakpoint]: {
      //       ...child.styles[currentBreakpoint],
      //       gridColumn: `span ${span}`,
      //       order: order.toString(),
      //     },
      //   },
      // }

      // Render the child with column styles
      return (
        <div key={child.id} className="group relative" style={{ gridColumn: `span ${span}`, order }}>
          {!isPreviewMode && (
            <GridColumnIndicator
              columns={span}
              maxColumns={element.columnConfig?.maxColumns || 12}
              isSelected={selectedElement?.id === child.id}
            />
          )}
          <ElementRenderer
            element={child}
            updateElement={updateElement}
            deleteElement={deleteElement}
            moveElement={moveElement}
            duplicateElement={duplicateElement}
            isElementVisible={isElementVisible}
            isNested={true}
          />
          {!isPreviewMode && selectedElement?.id === child.id && (
            <GridResizeHandle
              currentSpan={span}
              onResize={deltaColumns => {
                const newSpan = Math.max(1, Math.min(12, span + deltaColumns))

                if (element.columnConfig) {
                  const childConfig = element.columnConfig.children[child.id] || {}
                  const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

                  updateElement(element.id, {
                    columnConfig: {
                      ...element.columnConfig,
                      children: {
                        ...element.columnConfig.children,
                        [child.id]: {
                          ...childConfig,
                          [currentBreakpoint]: {
                            ...breakpointConfig,
                            span: newSpan,
                          },
                        },
                      },
                    },
                  })
                }
              }}
            />
          )}
        </div>
      )
    })
  }

  const renderElement = () => {
    const elementStyles = getElementStyles()
    const className = `${element.className || ''} ${isSelected && !isPreviewMode ? 'ring-2 ring-primary' : ''}`
    const hasError = !!elementError

    switch (element.type) {
      case 'grid': {
        // Apply auto-layout styles
        let gridStyles = { ...elementStyles }

        if (element.autoLayout) {
          const autoLayout = element.autoLayout[currentBreakpoint]
          gridStyles = {
            ...gridStyles,
            display: 'grid',
            gridTemplateColumns: `repeat(${autoLayout.columns}, 1fr)`,
            gap: autoLayout.gap,
          }
        }

        return (
          <div
            ref={isPreviewMode ? undefined : setDroppableRef}
            style={gridStyles}
            className={`${className} ${isOver && !isPreviewMode ? 'ring-2 ring-blue-400' : ''} group relative`}
          >
            {element.children && element.children.length > 0
              ? renderChildren(element.children)
              : !isPreviewMode && (
                  <div className="text-muted-foreground col-span-full py-8 text-center">
                    <p>{t('dropComponentsHere')}</p>
                  </div>
                )}
          </div>
        )
      }

      case 'column': {
        // Apply column layout styles
        let columnStyles = { ...elementStyles }

        if (element.columnConfig) {
          columnStyles = {
            ...columnStyles,
            display: 'grid',
            gridTemplateColumns: `repeat(${element.columnConfig.maxColumns}, 1fr)`,
            gap: element.columnConfig.gap,
          }
        }

        return (
          <div
            ref={isPreviewMode ? undefined : setDroppableRef}
            style={columnStyles}
            className={`${className} ${isOver && !isPreviewMode ? 'ring-2 ring-blue-400' : ''} group relative`}
          >
            {element.children && element.children.length > 0
              ? renderColumnChildren(element.children)
              : !isPreviewMode && (
                  <div className="text-muted-foreground col-span-full py-8 text-center">
                    <p>{t('dropComponentsHere')}</p>
                    <p className="mt-1 text-xs">{t('createResponsiveColumnLayouts')}</p>
                  </div>
                )}
          </div>
        )
      }

      case 'array':
        return (
          <div
            ref={isPreviewMode ? undefined : setDroppableRef}
            style={elementStyles}
            className={`${className} ${isOver && !isPreviewMode ? 'bg-green-50 ring-2 ring-green-400' : ''}`}
          >
            <ArrayInputRenderer
              element={element}
              updateElement={updateElement}
              deleteElement={deleteElement}
              moveElement={moveElement}
              duplicateElement={duplicateElement}
              isElementVisible={isElementVisible}
            />
          </div>
        )

      case 'input':
        return (
          <div style={elementStyles} className={className}>
            <div className={element.layout === 'horizontal' ? 'flex items-center gap-4' : ''}>
              {element.label && (
                <Label className={element.layout === 'horizontal' ? 'min-w-[120px]' : 'mb-2 block'}>
                  {element.label}
                  {element.required && <span className="ml-1 text-red-500">*</span>}
                </Label>
              )}
              <div className={element.layout === 'horizontal' ? 'flex-1' : ''}>
                <Input
                  placeholder={element.placeholder}
                  name={element.name}
                  required={element.required}
                  value={formData[element.name || ''] || ''}
                  onChange={handleInputChange}
                  className={hasError ? 'border-red-500' : ''}
                  {...element.customAttributes}
                />
                {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
              </div>
            </div>
          </div>
        )

      case 'select':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <Select
              value={formData[element.name || ''] || ''}
              onValueChange={value => setFormData({ ...formData, [element.name || '']: value })}
            >
              <SelectTrigger className={hasError ? 'border-red-500' : ''}>
                <SelectValue placeholder={t('selectAnOption')} />
              </SelectTrigger>
              <SelectContent>
                {element.options?.map((option: Any, index: number) => (
                  <SelectItem key={index} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'select-api':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <Select
              value={formData[element.name || ''] || ''}
              onValueChange={value => setFormData({ ...formData, [element.name || '']: value })}
            >
              <SelectTrigger className={hasError ? 'border-red-500' : ''}>
                <SelectValue placeholder={t('loadingOptions')} />
              </SelectTrigger>
              <SelectContent>
                <div className="text-muted-foreground flex items-center justify-center p-2 text-sm">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  {element.apiConfig?.emptyText || t('noOptionsAvailable')}
                </div>
              </SelectContent>
            </Select>
            {!isPreviewMode && (
              <p className="text-muted-foreground mt-1 text-xs">
                API URL: {element.apiConfig?.url || t('notConfigured')}
              </p>
            )}
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'textarea':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <Textarea
              placeholder={element.placeholder}
              name={element.name}
              required={element.required}
              value={formData[element.name || ''] || ''}
              onChange={handleInputChange}
              className={hasError ? 'border-red-500' : ''}
              {...element.customAttributes}
            />
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'button': {
        const buttonType = element.customAttributes?.buttonType || 'button'

        return (
          <Button
            style={elementStyles}
            className={className}
            type={buttonType as 'button' | 'submit' | 'reset'}
            onClick={handleButtonClick}
          >
            {element.htmlContent || t('button')}
          </Button>
        )
      }

      case 'checkbox':
        return (
          <div style={elementStyles} className={`${className} flex items-center space-x-2`}>
            <Checkbox
              id={element.id}
              name={element.name}
              checked={Boolean(formData[element.name || ''])}
              onCheckedChange={handleCheckboxChange}
              className={hasError ? 'border-red-500' : ''}
            />
            {element.label && (
              <Label htmlFor={element.id}>
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'radio':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <RadioGroup
              name={element.name}
              value={formData[element.name || ''] || ''}
              onValueChange={value => setFormData({ ...formData, [element.name || '']: value })}
              className={hasError ? 'rounded border border-red-500 p-2' : ''}
            >
              {element.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={option.value} id={`${element.id}-${index}`} />
                  <Label htmlFor={`${element.id}-${index}`}>{option.label}</Label>
                </div>
              ))}
            </RadioGroup>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'date-picker':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !formData[element.name || ''] && 'text-muted-foreground',
                    hasError && 'border-red-500'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData[element.name || ''] ? (
                    format(new Date(formData[element.name || '']), 'PPP')
                  ) : (
                    <span>{t('pickADate')}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData[element.name || ''] ? new Date(formData[element.name || '']) : undefined}
                  onSelect={date => setFormData({ ...formData, [element.name || '']: date })}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'file-upload':
        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <div
              className={`flex flex-col items-center justify-center rounded-md border-2 border-dashed p-6 ${hasError ? 'border-red-500' : ''}`}
            >
              <Upload className="text-muted-foreground mb-2 h-8 w-8" />
              <p className="text-sm font-medium">{t('clickToUploadOrDragAndDrop')}</p>
              <p className="text-muted-foreground mt-1 text-xs">{t('svgPngJpgGifMax2Mb')}</p>
              <input type="file" className="hidden" />
            </div>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )

      case 'rating': {
        const maxRating = element.customAttributes?.max || 5
        const currentRating = formData[element.name || ''] || 0

        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <Label className="mb-2 block">
                {element.label}
                {element.required && <span className="ml-1 text-red-500">*</span>}
              </Label>
            )}
            <div className="flex items-center gap-1">
              {Array.from({ length: maxRating }).map((_, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1"
                  onClick={() => setFormData({ ...formData, [element.name || '']: index + 1 })}
                >
                  {index + 1 <= currentRating ? (
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ) : (
                    <Star className="text-muted-foreground h-5 w-5" />
                  )}
                </Button>
              ))}
            </div>
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )
      }

      case 'slider': {
        const min = element.customAttributes?.min || 0
        const max = element.customAttributes?.max || 100
        const step = element.customAttributes?.step || 1
        const sliderValue = formData[element.name || ''] || min

        return (
          <div style={elementStyles} className={className}>
            {element.label && (
              <div className="mb-2 flex items-center justify-between">
                <Label>
                  {element.label}
                  {element.required && <span className="ml-1 text-red-500">*</span>}
                </Label>
                <span className="text-sm font-medium">{sliderValue}</span>
              </div>
            )}
            <Slider
              min={min}
              max={max}
              step={step}
              value={[sliderValue]}
              onValueChange={value => setFormData({ ...formData, [element.name || '']: value[0] })}
              className={hasError ? 'accent-red-500' : ''}
            />
            {elementError && <p className="mt-1 text-sm text-red-500">{elementError.message}</p>}
          </div>
        )
      }

      case 'h1':
        return (
          <h1 style={elementStyles} className={className}>
            {element.htmlContent || t('heading1')}
          </h1>
        )

      case 'h2':
        return (
          <h2 style={elementStyles} className={className}>
            {element.htmlContent || t('heading2')}
          </h2>
        )

      case 'h3':
        return (
          <h3 style={elementStyles} className={className}>
            {element.htmlContent || t('heading3')}
          </h3>
        )

      case 'paragraph':
        return (
          <p style={elementStyles} className={className}>
            {element.htmlContent || t('paragraphText')}
          </p>
        )

      case 'span':
        return (
          <span style={elementStyles} className={className}>
            {element.htmlContent || t('spanText')}
          </span>
        )

      case 'divider':
        return <div style={elementStyles} className={`${className} my-4`} />

      case 'image':
        return (
          <div style={elementStyles} className={className}>
            {element.label && <Label className="mb-2 block">{element.label}</Label>}
            <img
              src={element.htmlContent || '/placeholder.svg?height=200&width=300'}
              alt={element.label || t('image')}
              className="h-auto max-w-full"
            />
          </div>
        )

      case 'html':
        return (
          <div
            style={elementStyles}
            className={className}
            dangerouslySetInnerHTML={{ __html: element.htmlContent || '' }}
          />
        )

      case 'table':
        return (
          <div style={elementStyles} className={className}>
            {element.label && <Label className="mb-2 block">{element.label}</Label>}
            <TableRenderer
              element={element}
              isPreview={isPreviewMode}
              onEdit={(row: Any, index: number) => {
                console.log('Edit row:', row, index)
              }}
              onDelete={(row: Any, index: number) => {
                console.log('Delete row:', row, index)
              }}
              onView={(row: Any, index: number) => {
                console.log('View row:', row, index)
              }}
              onCustomAction={(action: string, row: Any, index: number) => {
                console.log('Custom action:', action, row, index)
              }}
            />
          </div>
        )

      case 'detail':
        return (
          <div style={elementStyles} className={className}>
            <DetailRenderer element={element} isPreview={isPreviewMode} />
          </div>
        )

      case 'container': {
        const containerStyles = {
          ...elementStyles,
          display: 'grid',
          gridTemplateColumns: `repeat(${element.containerConfig?.columns[currentBreakpoint] || 12}, 1fr)`,
          gap: element.containerConfig?.gap || '16px',
          alignItems: element.containerConfig?.verticalAlign || 'top',
          textAlign: element.containerConfig?.textAlign || 'left',
        }

        // Loại bỏ các style dư thừa khi ở chế độ preview
        if (isPreviewMode) {
          delete containerStyles.border
          delete containerStyles.borderRadius
          delete containerStyles.backgroundColor
          delete containerStyles.padding
        }

        return (
          <div
            ref={setDroppableRef}
            style={containerStyles}
            className={cn(
              className,
              'group relative',
              !isPreviewMode && 'min-h-[100px]',
              !isPreviewMode && isOver && 'bg-blue-50/50 ring-2 ring-blue-400',
              !isPreviewMode && !element.children?.length && 'border-2 border-dashed border-blue-200'
            )}
          >
            {element.children && element.children.length > 0
              ? element.children.map(child => {
                  const childConfig = element.containerConfig?.children[child.id]
                  const breakpointConfig = childConfig?.[currentBreakpoint]
                  const span = breakpointConfig?.span || 1
                  const start = breakpointConfig?.start || undefined
                  const end = breakpointConfig?.end || undefined

                  return (
                    <div
                      key={child.id}
                      style={{
                        gridColumn: start && end ? `${start} / ${end}` : `span ${span}`,
                      }}
                    >
                      <ElementRenderer
                        element={child}
                        updateElement={updateElement}
                        deleteElement={deleteElement}
                        moveElement={moveElement}
                        duplicateElement={duplicateElement}
                        isElementVisible={isElementVisible}
                        isNested={true}
                      />
                    </div>
                  )
                })
              : !isPreviewMode && (
                  <div className="text-muted-foreground col-span-full py-8 text-center">
                    <p>{t('dropComponentsHere')}</p>
                    <p className="mt-1 text-xs">{t('createResponsiveLayoutsWithGrid')}</p>
                  </div>
                )}
          </div>
        )
      }

      default:
        return (
          <div style={elementStyles} className={className}>
            {t('unknownElementType')}: {element.type}
          </div>
        )
    }
  }

  return (
    <div
      ref={isPreviewMode ? undefined : setSortableRef}
      style={style}
      {...(isPreviewMode ? {} : attributes)}
      className={`group relative rounded-md ${isNested ? 'ml-0' : ''} ${!isPreviewMode && !isNested ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      {/* Element Controls - Only show in edit mode */}
      {isSelected && !isPreviewMode && (
        <div className="bg-primary text-primary-foreground absolute -top-8 left-0 z-10 flex items-center gap-1 rounded px-2 py-1 text-xs">
          <GripVertical className="h-3 w-3 cursor-grab" {...listeners} />
          <span>{element.label || element.type}</span>
          <div className="ml-2 flex items-center gap-1">
            <Button size="sm" variant="ghost" className="h-4 w-4 p-0" onClick={handleMoveUp}>
              <ArrowUp className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" className="h-4 w-4 p-0" onClick={handleMoveDown}>
              <ArrowDown className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" className="h-4 w-4 p-0" onClick={handleDuplicate}>
              <Copy className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" className="hover:bg-destructive h-4 w-4 p-0" onClick={handleDelete}>
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {renderElement()}
    </div>
  )
}
