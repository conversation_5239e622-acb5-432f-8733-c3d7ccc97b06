'use client'

import { Button } from '@workspace/ui/components/button'
import { AppWindowMac, Monitor, Smartphone } from 'lucide-react'
import type React from 'react'

import { type Breakpoint, useFormBuilder } from './form-builder'

export function ResponsiveControls() {
  const { currentBreakpoint, setCurrentBreakpoint } = useFormBuilder()

  const breakpoints: Array<{ key: Breakpoint; label: string; icon: React.ReactNode }> = [
    { key: 'desktop', label: 'Desktop', icon: <Monitor className="h-4 w-4" /> },
    { key: 'tablet', label: 'Tablet', icon: <AppWindowMac className="h-4 w-4" /> },
    { key: 'mobile', label: 'Mobile', icon: <Smartphone className="h-4 w-4" /> },
  ]

  return (
    <div className="flex items-center gap-1 rounded-md border p-1">
      {breakpoints.map(breakpoint => (
        <Button
          key={breakpoint.key}
          size="sm"
          variant={currentBreakpoint === breakpoint.key ? 'default' : 'ghost'}
          onClick={() => setCurrentBreakpoint(breakpoint.key)}
          className="gap-1"
        >
          {breakpoint.icon}
          <span className="hidden sm:inline">{breakpoint.label}</span>
        </Button>
      ))}
    </div>
  )
}
