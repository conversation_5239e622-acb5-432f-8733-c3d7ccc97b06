'use client'

import { <PERSON><PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { ArrowRight, FormInput, Settings } from 'lucide-react'
import { useState } from 'react'

import { type CodeGenerationOptions } from './code-generation-modal'

interface FormCodeOptionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (options: CodeGenerationOptions) => void
}

export function FormCodeOptionsModal({ open, onOpenChange, onConfirm }: FormCodeOptionsModalProps) {
  const [moduleName, setModuleName] = useState('user-management')
  const [componentName, setComponentName] = useState('UserForm')
  const [framework, setFramework] = useState<'shadcn' | 'mui' | 'antd'>('shadcn')
  const [formType, setFormType] = useState<'create' | 'edit' | 'both'>('both')
  const [validation, setValidation] = useState(true)
  const [apiIntegration, setApiIntegration] = useState(true)
  const [modelName, setModelName] = useState('user')
  const [prefixPageFolder, setPrefixPageFolder] = useState('(admin)')

  const handleConfirm = () => {
    if (!moduleName.trim() || !componentName.trim()) {
      alert('Vui lòng nhập đầy đủ tên module và component')

      return
    }

    const options: CodeGenerationOptions = {
      moduleName: moduleName.trim(),
      componentName: componentName.trim(),
      framework,
      formType,
      validation,
      apiIntegration,
      modelName: modelName.trim(),
      prefixPageFolder: prefixPageFolder.trim(),
    }

    onConfirm(options)
    onOpenChange(false)
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FormInput className="h-5 w-5" />
            Cấu hình Form Component
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Settings className="h-4 w-4" />
                Thông tin cơ bản
              </CardTitle>
              <CardDescription>Cấu hình tên module và component sẽ được tạo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="moduleName">Tên module</Label>
                  <Input
                    id="moduleName"
                    value={moduleName}
                    onChange={e => setModuleName(e.target.value)}
                    placeholder="vd: user-management"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="componentName">Tên component</Label>
                  <Input
                    id="componentName"
                    value={componentName}
                    onChange={e => setComponentName(e.target.value)}
                    placeholder="vd: UserForm"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng PascalCase</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="modelName">Tên model</Label>
                  <Input
                    id="modelName"
                    value={modelName}
                    onChange={e => setModelName(e.target.value)}
                    placeholder="vd: user"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prefixPageFolder">Tên thư mục (prefix)</Label>
                  <Input
                    id="prefixPageFolder"
                    value={prefixPageFolder}
                    onChange={e => setPrefixPageFolder(e.target.value)}
                    placeholder="vd: (admin)"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="framework">UI Framework</Label>
                <Select value={framework} onValueChange={(value: 'shadcn' | 'mui' | 'antd') => setFramework(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn UI framework" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shadcn">ShadCN UI (Khuyến nghị)</SelectItem>
                    <SelectItem value="mui">Material UI</SelectItem>
                    <SelectItem value="antd">Ant Design</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Form Type */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Loại form</CardTitle>
              <CardDescription>Chọn loại form bạn muốn tạo</CardDescription>
            </CardHeader>
            <CardContent>
              <Select value={formType} onValueChange={(value: 'create' | 'edit' | 'both') => setFormType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại form" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="create">Chỉ form tạo mới</SelectItem>
                  <SelectItem value="edit">Chỉ form cập nhật</SelectItem>
                  <SelectItem value="both">Cả tạo mới và cập nhật</SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Advanced Features */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Tính năng nâng cao</CardTitle>
              <CardDescription>Chọn các tính năng bổ sung cho form</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox id="validation" checked={validation} onCheckedChange={checked => setValidation(!!checked)} />
                <Label htmlFor="validation" className="text-sm">
                  Bao gồm validation schema (Zod)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="apiIntegration"
                  checked={apiIntegration}
                  onCheckedChange={checked => setApiIntegration(!!checked)}
                />
                <Label htmlFor="apiIntegration" className="text-sm">
                  Tích hợp API calls
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button onClick={handleConfirm} className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              Tạo Form Code
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
