'use client'

import { type ApiEndpoint, type ApiSchema } from '@/lib/json-form/json-form-api-service'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { ArrowRight, Construction, FileText, FormInput, Info, Table } from 'lucide-react'
import { useState } from 'react'

import { DataPathModal } from './data-path-modal'

interface EndpointUsageModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  endpoint: ApiEndpoint | null
  schema: ApiSchema | null
  onSelectUsageType: (endpoint: ApiEndpoint, schema: ApiSchema, usageType: UsageType, dataPath?: string) => void
}

export type UsageType = 'detail' | 'table' | 'form'

interface UsageOption {
  type: UsageType
  title: string
  description: string
  icon: React.ReactNode
  examples: string[]
  status: 'available' | 'construction'
}

export function EndpointUsageModal({
  open,
  onOpenChange,
  endpoint,
  schema,
  onSelectUsageType,
}: EndpointUsageModalProps) {
  const [selectedType, setSelectedType] = useState<UsageType | null>(null)
  const [showDataPathModal, setShowDataPathModal] = useState(false)

  const usageOptions: UsageOption[] = [
    {
      type: 'table',
      title: 'Hiển thị dạng bảng',
      description: 'Tạo bảng hiển thị danh sách dữ liệu với phân trang',
      icon: <Table className="h-6 w-6" />,
      examples: ['Danh sách bài viết', 'Bảng người dùng', 'Danh sách đơn hàng'],
      status: 'available',
    },
    {
      type: 'form',
      title: 'Form tạo/cập nhật',
      description: 'Tạo form để người dùng nhập liệu, tạo mới hoặc cập nhật dữ liệu',
      icon: <FormInput className="h-6 w-6" />,
      examples: ['Form tạo bài viết', 'Form đăng ký', 'Form cập nhật profile'],
      status: 'available',
    },
    {
      type: 'detail',
      title: 'Hiển thị chi tiết',
      description: 'Tạo màn hình hiển thị thông tin chi tiết của một đối tượng',
      icon: <FileText className="h-6 w-6" />,
      examples: ['Chi tiết bài viết', 'Thông tin user', 'Chi tiết sản phẩm'],
      status: 'available',
    },
  ]

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'POST':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'PUT':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PATCH':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'WS':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getApiTypeBadge = (type: string) => {
    switch (type) {
      case 'swagger':
        return (
          <Badge variant="outline" className="border-blue-200 bg-blue-50 text-xs text-blue-700">
            Swagger
          </Badge>
        )
      case 'graphql':
        return (
          <Badge variant="outline" className="border-pink-200 bg-pink-50 text-xs text-pink-700">
            GraphQL
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="text-xs">
            {type}
          </Badge>
        )
    }
  }

  const handleSelectType = (type: UsageType) => {
    if (!endpoint || !schema) return

    const option = usageOptions.find(opt => opt.type === type)

    if (option?.status === 'construction') {
      return // Không cho chọn nếu đang xây dựng
    }

    if (type === 'table') {
      setShowDataPathModal(true)

      return
    }

    onSelectUsageType(endpoint, schema, type, undefined)
    onOpenChange(false)
    setSelectedType(null)
  }

  const handleDataPathConfirm = (endpoint: ApiEndpoint, schema: ApiSchema, dataPath: string) => {
    // Gọi callback với thông tin bổ sung về dataPath
    onSelectUsageType(endpoint, schema, 'table', dataPath)
    onOpenChange(false)
    setSelectedType(null)
    setShowDataPathModal(false)
  }

  const handleClose = () => {
    onOpenChange(false)
    setSelectedType(null)
    setShowDataPathModal(false)
  }

  if (!endpoint || !schema) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowRight className="h-5 w-5" />
            Chọn kiểu màn hình sử dụng
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Endpoint Info */}
          <Card className="bg-muted/20">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Endpoint đã chọn</CardTitle>
                <div className="flex items-center gap-2">
                  {getApiTypeBadge(schema.type)}
                  <Badge variant="secondary" className="text-xs">
                    {schema.name}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className={`font-mono text-xs ${getMethodColor(endpoint.method)}`}>
                  {endpoint.method}
                </Badge>
                <code className="bg-muted rounded px-2 py-1 font-mono text-sm">{endpoint.path}</code>
                {endpoint.summary && <span className="text-muted-foreground text-sm">- {endpoint.summary}</span>}
              </div>

              {/* GraphQL specific info */}
              {(endpoint.query || endpoint.mutation || endpoint.subscription) && (
                <div className="mt-2 text-sm">
                  {endpoint.query && <span className="font-medium text-green-700">Query: {endpoint.query}</span>}
                  {endpoint.mutation && (
                    <span className="font-medium text-blue-700">Mutation: {endpoint.mutation}</span>
                  )}
                  {endpoint.subscription && (
                    <span className="font-medium text-purple-700">Subscription: {endpoint.subscription}</span>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Usage Type Selection */}
          <div>
            <h3 className="mb-4 text-lg font-semibold">Chọn kiểu màn hình bạn muốn tạo:</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {usageOptions.map(option => (
                <Card
                  key={option.type}
                  className={`cursor-pointer transition-all hover:shadow-md ${selectedType === option.type ? 'ring-primary ring-2' : ''} ${
                    option.status === 'construction' ? 'opacity-60' : 'hover:border-primary/50'
                  }`}
                  onClick={() => {
                    if (option.status === 'available') {
                      setSelectedType(option.type)
                    }
                  }}
                >
                  <CardHeader className="pb-3 text-center">
                    <div className="mb-2 flex justify-center">
                      <div
                        className={`rounded-full p-3 ${option.status === 'construction' ? 'bg-muted text-muted-foreground' : 'bg-primary/10 text-primary'}`}
                      >
                        {option.status === 'construction' ? <Construction className="h-6 w-6" /> : option.icon}
                      </div>
                    </div>
                    <CardTitle className="text-base">{option.title}</CardTitle>
                    {option.status === 'construction' && (
                      <Badge variant="secondary" className="mx-auto w-fit text-xs">
                        Đang xây dựng
                      </Badge>
                    )}
                  </CardHeader>
                  <CardContent className="pt-0">
                    <CardDescription className="mb-3 text-center">{option.description}</CardDescription>
                    <div className="space-y-1">
                      <div className="text-muted-foreground flex items-center gap-1 text-xs">
                        <Info className="h-3 w-3" />
                        <span>Ví dụ:</span>
                      </div>
                      {option.examples.map((example, index) => (
                        <div key={index} className="text-muted-foreground pl-4 text-xs">
                          • {example}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button
              onClick={() => selectedType && handleSelectType(selectedType)}
              disabled={!selectedType}
              className="flex items-center gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              Tạo màn hình
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Data Path Modal for Table */}
      <DataPathModal
        open={showDataPathModal}
        onOpenChange={setShowDataPathModal}
        endpoint={endpoint}
        schema={schema}
        onConfirm={handleDataPathConfirm}
      />
    </Dialog>
  )
}
