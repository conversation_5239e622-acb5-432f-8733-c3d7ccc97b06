'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@workspace/ui/components/collapsible'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Slider } from '@workspace/ui/components/slider'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { ChevronDown, LayoutGrid } from 'lucide-react'
import { useState } from 'react'

import { type ContainerConfig, type FormElement, useFormBuilder } from './form-builder'

interface ContainerLayoutConfigProps {
  element: FormElement
  onUpdateElement: (updates: Partial<FormElement>) => void
}

export function ContainerLayoutConfig({ element, onUpdateElement }: ContainerLayoutConfigProps) {
  const { currentBreakpoint } = useFormBuilder()
  const [isOpen, setIsOpen] = useState(true)

  const containerConfig = element.containerConfig || {
    columns: {
      desktop: 12,
      tablet: 6,
      mobile: 4,
    },
    verticalAlign: 'top',
    gap: '16px',
    children: {},
  }

  const updateContainerConfig = (updates: Partial<ContainerConfig>) => {
    onUpdateElement({
      containerConfig: {
        ...containerConfig,
        ...updates,
      },
    })
  }

  const updateChildSpan = (childId: string, span: number) => {
    if (!element.containerConfig) return

    const childConfig = element.containerConfig.children[childId] || {}
    const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

    onUpdateElement({
      containerConfig: {
        ...element.containerConfig,
        children: {
          ...element.containerConfig.children,
          [childId]: {
            ...childConfig,
            [currentBreakpoint]: {
              ...breakpointConfig,
              span,
            },
          },
        },
      },
    })
  }

  const updateChildPosition = (childId: string, start?: number, end?: number) => {
    if (!element.containerConfig) return

    const childConfig = element.containerConfig.children[childId] || {}
    const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

    onUpdateElement({
      containerConfig: {
        ...element.containerConfig,
        children: {
          ...element.containerConfig.children,
          [childId]: {
            ...childConfig,
            [currentBreakpoint]: {
              ...breakpointConfig,
              start,
              end,
            },
          },
        },
      },
    })
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <Card className="shadow-none">
        <CollapsibleTrigger className="w-full text-left">
          <CardHeader className="py-6">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-sm">
                <LayoutGrid className="h-4 w-4" />
                <span>Container Layout Configuration</span>
              </CardTitle>
              <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="space-y-4 px-3 pt-0">
            <Tabs defaultValue="general">
              <TabsList className="mb-2 grid grid-cols-2">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="columns">Columns</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Desktop Columns</Label>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-xs">{containerConfig.columns.desktop} columns</span>
                    </div>
                    <Slider
                      min={1}
                      max={12}
                      step={1}
                      value={[containerConfig.columns.desktop]}
                      onValueChange={value =>
                        updateContainerConfig({
                          columns: {
                            ...containerConfig.columns,
                            desktop: value[0] as number,
                          },
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tablet Columns</Label>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-xs">{containerConfig.columns.tablet} columns</span>
                    </div>
                    <Slider
                      min={1}
                      max={12}
                      step={1}
                      value={[containerConfig.columns.tablet]}
                      onValueChange={value =>
                        updateContainerConfig({
                          columns: {
                            ...containerConfig.columns,
                            tablet: value[0] as number,
                          },
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Mobile Columns</Label>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-xs">{containerConfig.columns.mobile} columns</span>
                    </div>
                    <Slider
                      min={1}
                      max={12}
                      step={1}
                      value={[containerConfig.columns.mobile]}
                      onValueChange={value =>
                        updateContainerConfig({
                          columns: {
                            ...containerConfig.columns,
                            mobile: value[0] as number,
                          },
                        })
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Vertical Alignment</Label>
                  <Select
                    value={containerConfig.verticalAlign}
                    onValueChange={value =>
                      updateContainerConfig({
                        verticalAlign: value as 'top' | 'center' | 'bottom',
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select alignment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="top">Top</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="bottom">Bottom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Text Alignment</Label>
                  <Select
                    value={containerConfig.textAlign || 'left'}
                    onValueChange={value =>
                      updateContainerConfig({
                        textAlign: value as 'left' | 'center' | 'right' | 'justify',
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select text alignment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                      <SelectItem value="justify">Justify</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="gap">Gap</Label>
                    <span className="text-muted-foreground text-xs">{containerConfig.gap}</span>
                  </div>
                  <Input
                    id="gap"
                    value={containerConfig.gap}
                    onChange={e => updateContainerConfig({ gap: e.target.value })}
                    placeholder="16px"
                  />
                </div>
              </TabsContent>

              <TabsContent value="columns" className="space-y-4">
                {element.children && element.children.length > 0 ? (
                  element.children.map((child, index) => {
                    const childConfig = containerConfig.children[child.id] || {}
                    const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }
                    const span = breakpointConfig.span || 1
                    const start = breakpointConfig.start
                    const end = breakpointConfig.end

                    return (
                      <div key={child.id} className="border-border space-y-2 border-b pb-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">
                            {child.label || `Column ${index + 1}`} ({span}/12)
                          </Label>
                          <div className="text-muted-foreground text-xs">ID: {child.id}</div>
                        </div>

                        <div className="space-y-4">
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`span-${child.id}`} className="text-xs">
                                Span
                              </Label>
                              <span className="text-xs font-medium">
                                {span}/{containerConfig.columns[currentBreakpoint]}
                              </span>
                            </div>
                            <Slider
                              id={`span-${child.id}`}
                              min={1}
                              max={containerConfig.columns[currentBreakpoint]}
                              step={1}
                              value={[span]}
                              onValueChange={value => updateChildSpan(child.id, value[0] as number)}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            <div className="space-y-1">
                              <Label htmlFor={`start-${child.id}`} className="text-xs">
                                Start Column
                              </Label>
                              <Input
                                id={`start-${child.id}`}
                                type="number"
                                min={1}
                                max={containerConfig.columns[currentBreakpoint]}
                                value={start || ''}
                                onChange={e => {
                                  const value = e.target.value ? parseInt(e.target.value) : undefined
                                  updateChildPosition(child.id, value, end)
                                }}
                                placeholder="Auto"
                              />
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor={`end-${child.id}`} className="text-xs">
                                End Column
                              </Label>
                              <Input
                                id={`end-${child.id}`}
                                type="number"
                                min={1}
                                max={containerConfig.columns[currentBreakpoint] + 1}
                                value={end || ''}
                                onChange={e => {
                                  const value = e.target.value ? parseInt(e.target.value) : undefined
                                  updateChildPosition(child.id, start, value)
                                }}
                                placeholder="Auto"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })
                ) : (
                  <div className="text-muted-foreground py-4 text-center">
                    <p>No columns added yet.</p>
                    <p className="mt-1 text-xs">Drag components into this Container to create columns.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  )
}
