'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Separator } from '@workspace/ui/components/separator'
import { Switch } from '@workspace/ui/components/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import {
  AlignCenter,
  AlignJustify,
  AlignLeft,
  AlignRight,
  Bold,
  ChevronDown,
  Eye,
  EyeOff,
  Italic,
  Monitor,
  Smartphone,
  Tablet,
  Type,
  Underline,
} from 'lucide-react'
import type React from 'react'
import { useState } from 'react'

import type { FormElement } from './form-builder'

interface HtmlElementConfigProps {
  element: FormElement
  onUpdateElement: (updates: Partial<FormElement>) => void
}

interface ResponsiveVisibility {
  desktop: boolean
  tablet: boolean
  mobile: boolean
}

export function HtmlElementConfig({ element, onUpdateElement }: HtmlElementConfigProps) {
  const t = useTranslations('e-form')
  const [isOpen, setIsOpen] = useState(true) // Changed from false to true
  const [activeTab, setActiveTab] = useState('content')

  // Only show for HTML elements
  const isHtmlElement = ['h1', 'h2', 'h3', 'paragraph', 'html', 'span'].includes(element.type)

  if (!isHtmlElement) return null

  // Get current responsive visibility settings
  const getResponsiveVisibility = (): ResponsiveVisibility => {
    return (
      element.customAttributes?.responsiveVisibility || {
        desktop: true,
        tablet: true,
        mobile: true,
      }
    )
  }

  // Get current text formatting
  const getTextFormatting = () => {
    return (
      element.customAttributes?.textFormatting || {
        bold: false,
        italic: false,
        underline: false,
        alignment: 'left',
        fontSize: '',
        fontWeight: '',
        color: '',
        backgroundColor: '',
      }
    )
  }

  const updateResponsiveVisibility = (device: keyof ResponsiveVisibility, visible: boolean) => {
    const currentVisibility = getResponsiveVisibility()
    const newVisibility = { ...currentVisibility, [device]: visible }

    onUpdateElement({
      customAttributes: {
        ...element.customAttributes,
        responsiveVisibility: newVisibility,
      },
    })
  }

  const updateTextFormatting = (property: string, value: Any) => {
    const currentFormatting = getTextFormatting()
    const newFormatting = { ...currentFormatting, [property]: value }

    onUpdateElement({
      customAttributes: {
        ...element.customAttributes,
        textFormatting: newFormatting,
      },
    })
  }

  const updateContent = (content: string) => {
    onUpdateElement({ htmlContent: content })
  }

  const applyTextFormatting = (type: 'bold' | 'italic' | 'underline') => {
    const currentFormatting = getTextFormatting()
    updateTextFormatting(type, !currentFormatting[type])
  }

  const setAlignment = (alignment: string) => {
    updateTextFormatting('alignment', alignment)
  }

  const responsiveVisibility = getResponsiveVisibility()
  const textFormatting = getTextFormatting()

  // Generate inline styles based on formatting
  const getFormattedStyles = () => {
    const styles: React.CSSProperties = {}

    if (textFormatting.bold) styles.fontWeight = 'bold'

    if (textFormatting.italic) styles.fontStyle = 'italic'

    if (textFormatting.underline) styles.textDecoration = 'underline'

    if (textFormatting.alignment) styles.textAlign = textFormatting.alignment as Any

    if (textFormatting.fontSize) styles.fontSize = textFormatting.fontSize

    if (textFormatting.fontWeight) styles.fontWeight = textFormatting.fontWeight

    if (textFormatting.color) styles.color = textFormatting.color

    if (textFormatting.backgroundColor) styles.backgroundColor = textFormatting.backgroundColor

    return styles
  }

  const fontSizePresets = ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px']
  const fontWeightPresets = [
    { label: t('light'), value: '300' },
    { label: t('normal'), value: '400' },
    { label: t('medium'), value: '500' },
    { label: t('semiBold'), value: '600' },
    { label: t('bold'), value: '700' },
    { label: t('extraBold'), value: '800' },
  ]

  const colorPresets = [
    '#000000',
    '#374151',
    '#6B7280',
    '#9CA3AF',
    '#D1D5DB',
    '#EF4444',
    '#F97316',
    '#EAB308',
    '#22C55E',
    '#3B82F6',
    '#8B5CF6',
    '#EC4899',
    '#14B8A6',
    '#F59E0B',
    '#10B981',
  ]

  return (
    <Card className="shadow-none">
      <CardHeader className="hover:bg-accent/50 cursor-pointer transition-colors" onClick={() => setIsOpen(!isOpen)}>
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            {t('htmlElementConfiguration')}
            <div className="flex gap-1">
              {!responsiveVisibility.desktop && (
                <Badge variant="outline" className="text-xs">
                  {t('hiddenDesktop')}
                </Badge>
              )}
              {!responsiveVisibility.tablet && (
                <Badge variant="outline" className="text-xs">
                  {t('hiddenTablet')}
                </Badge>
              )}
              {!responsiveVisibility.mobile && (
                <Badge variant="outline" className="text-xs">
                  {t('hiddenMobile')}
                </Badge>
              )}
            </div>
          </div>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </CardTitle>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">{t('content')}</TabsTrigger>
              <TabsTrigger value="styling">{t('styling')}</TabsTrigger>
              <TabsTrigger value="visibility">{t('visibility')}</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-4">
              <div>
                <Label className="text-sm font-medium">{t('content')}</Label>
                <div className="mt-2 space-y-3">
                  {/* Rich Text Toolbar */}
                  <div className="bg-muted/50 flex items-center gap-1 rounded-md border p-2">
                    <Button
                      size="sm"
                      variant={textFormatting.bold ? 'default' : 'ghost'}
                      onClick={() => applyTextFormatting('bold')}
                      className="h-8 w-8 p-0"
                    >
                      <Bold className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant={textFormatting.italic ? 'default' : 'ghost'}
                      onClick={() => applyTextFormatting('italic')}
                      className="h-8 w-8 p-0"
                    >
                      <Italic className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant={textFormatting.underline ? 'default' : 'ghost'}
                      onClick={() => applyTextFormatting('underline')}
                      className="h-8 w-8 p-0"
                    >
                      <Underline className="h-4 w-4" />
                    </Button>

                    <Separator orientation="vertical" className="mx-1 h-6" />

                    <Button
                      size="sm"
                      variant={textFormatting.alignment === 'left' ? 'default' : 'ghost'}
                      onClick={() => setAlignment('left')}
                      className="h-8 w-8 p-0"
                    >
                      <AlignLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant={textFormatting.alignment === 'center' ? 'default' : 'ghost'}
                      onClick={() => setAlignment('center')}
                      className="h-8 w-8 p-0"
                    >
                      <AlignCenter className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant={textFormatting.alignment === 'right' ? 'default' : 'ghost'}
                      onClick={() => setAlignment('right')}
                      className="h-8 w-8 p-0"
                    >
                      <AlignRight className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant={textFormatting.alignment === 'justify' ? 'default' : 'ghost'}
                      onClick={() => setAlignment('justify')}
                      className="h-8 w-8 p-0"
                    >
                      <AlignJustify className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Content Input */}
                  <Textarea
                    value={element.htmlContent || ''}
                    onChange={e => updateContent(e.target.value)}
                    placeholder={t('enterContentPlaceholder', {
                      type: element.type,
                    })}
                    rows={4}
                    className="font-mono text-sm"
                  />

                  {/* Live Preview */}
                  <div className="bg-muted/20 rounded-md border p-4">
                    <Label className="text-muted-foreground mb-2 block text-xs">{t('preview')}</Label>
                    {element.type === 'h1' && (
                      <h1 style={getFormattedStyles()}>{element.htmlContent || t('heading1')}</h1>
                    )}
                    {element.type === 'h2' && (
                      <h2 style={getFormattedStyles()}>{element.htmlContent || t('heading2')}</h2>
                    )}
                    {element.type === 'h3' && (
                      <h3 style={getFormattedStyles()}>{element.htmlContent || t('heading3')}</h3>
                    )}
                    {element.type === 'paragraph' && (
                      <p style={getFormattedStyles()}>{element.htmlContent || t('paragraphText')}</p>
                    )}
                    {element.type === 'html' && (
                      <div
                        style={getFormattedStyles()}
                        dangerouslySetInnerHTML={{ __html: element.htmlContent || t('htmlContent') }}
                      />
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="styling" className="space-y-4">
              {/* Font Configuration */}
              <div>
                <Label className="mb-3 block text-sm font-medium">{t('fontConfiguration')}</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs">{t('fontSize')}</Label>
                    <div className="mt-1 mb-2 flex flex-wrap gap-1">
                      {fontSizePresets.map(size => (
                        <Button
                          key={size}
                          size="sm"
                          variant={textFormatting.fontSize === size ? 'default' : 'outline'}
                          onClick={() => updateTextFormatting('fontSize', size)}
                          className="h-7 px-2 text-xs"
                        >
                          {size}
                        </Button>
                      ))}
                    </div>
                    <Input
                      value={textFormatting.fontSize || ''}
                      onChange={e => updateTextFormatting('fontSize', e.target.value)}
                      placeholder={t('customSizePlaceholder')}
                      className="text-xs"
                    />
                  </div>

                  <div>
                    <Label className="text-xs">{t('fontWeight')}</Label>
                    <div className="mt-1 mb-2 flex flex-wrap gap-1">
                      {fontWeightPresets.map(weight => (
                        <Button
                          key={weight.value}
                          size="sm"
                          variant={textFormatting.fontWeight === weight.value ? 'default' : 'outline'}
                          onClick={() => updateTextFormatting('fontWeight', weight.value)}
                          className="h-7 px-2 text-xs"
                        >
                          {weight.label}
                        </Button>
                      ))}
                    </div>
                    <Input
                      value={textFormatting.fontWeight || ''}
                      onChange={e => updateTextFormatting('fontWeight', e.target.value)}
                      placeholder={t('customWeightPlaceholder')}
                      className="text-xs"
                    />
                  </div>
                </div>
              </div>

              {/* Color Configuration */}
              <div>
                <Label className="mb-3 block text-sm font-medium">{t('colors')}</Label>
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs">{t('textColor')}</Label>
                    <div className="mt-1 mb-2 flex flex-wrap gap-1">
                      {colorPresets.map(color => (
                        <button
                          key={color}
                          onClick={() => updateTextFormatting('color', color)}
                          className={`h-6 w-6 rounded border-2 ${
                            textFormatting.color === color ? 'border-primary' : 'border-muted'
                          }`}
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <Input
                      type="text"
                      value={textFormatting.color || ''}
                      onChange={e => updateTextFormatting('color', e.target.value)}
                      placeholder="#000000"
                      className="text-xs"
                    />
                  </div>

                  <div>
                    <Label className="text-xs">{t('backgroundColor')}</Label>
                    <div className="mt-1 mb-2 flex flex-wrap gap-1">
                      <button
                        onClick={() => updateTextFormatting('backgroundColor', '')}
                        className={`h-6 w-6 rounded border-2 ${
                          !textFormatting.backgroundColor ? 'border-primary' : 'border-muted'
                        } relative bg-transparent`}
                        title={t('noBackground')}
                      >
                        <div className="absolute inset-0 rounded bg-red-500 opacity-20"></div>
                        <div className="absolute inset-0 flex items-center justify-center text-xs text-red-500">×</div>
                      </button>
                      {colorPresets.map(color => (
                        <button
                          key={color}
                          onClick={() => updateTextFormatting('backgroundColor', color)}
                          className={`h-6 w-6 rounded border-2 ${
                            textFormatting.backgroundColor === color ? 'border-primary' : 'border-muted'
                          }`}
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <Input
                      type="text"
                      value={textFormatting.backgroundColor || ''}
                      onChange={e => updateTextFormatting('backgroundColor', e.target.value)}
                      placeholder="#ffffff"
                      className="text-xs"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="visibility" className="space-y-4">
              <div>
                <Label className="mb-3 block text-sm font-medium">{t('responsiveVisibility')}</Label>
                <p className="text-muted-foreground mb-4 text-xs">{t('controlWhichDevicesThisElementAppearsOn')}</p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="flex items-center gap-3">
                      <Monitor className="text-muted-foreground h-5 w-5" />
                      <div>
                        <div className="text-sm font-medium">{t('desktop')}</div>
                        <div className="text-muted-foreground text-xs">{t('showOnDesktopDevices')}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {responsiveVisibility.desktop ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-red-600" />
                      )}
                      <Switch
                        checked={responsiveVisibility.desktop}
                        onCheckedChange={checked => updateResponsiveVisibility('desktop', checked)}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="flex items-center gap-3">
                      <Tablet className="text-muted-foreground h-5 w-5" />
                      <div>
                        <div className="text-sm font-medium">{t('tablet')}</div>
                        <div className="text-muted-foreground text-xs">{t('showOnTabletDevices')}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {responsiveVisibility.tablet ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-red-600" />
                      )}
                      <Switch
                        checked={responsiveVisibility.tablet}
                        onCheckedChange={checked => updateResponsiveVisibility('tablet', checked)}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between rounded-md border p-3">
                    <div className="flex items-center gap-3">
                      <Smartphone className="text-muted-foreground h-5 w-5" />
                      <div>
                        <div className="text-sm font-medium">{t('mobile')}</div>
                        <div className="text-muted-foreground text-xs">{t('showOnMobileDevices')}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {responsiveVisibility.mobile ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-red-600" />
                      )}
                      <Switch
                        checked={responsiveVisibility.mobile}
                        onCheckedChange={checked => updateResponsiveVisibility('mobile', checked)}
                      />
                    </div>
                  </div>
                </div>

                {/* Visibility Summary */}
                <div className="bg-muted/50 mt-4 rounded-md p-3">
                  <div className="mb-2 text-xs font-medium">{t('visibilitySummary')}</div>
                  <div className="flex gap-2">
                    {responsiveVisibility.desktop && <Badge variant="secondary">{t('desktop')}</Badge>}
                    {responsiveVisibility.tablet && <Badge variant="secondary">{t('tablet')}</Badge>}
                    {responsiveVisibility.mobile && <Badge variant="secondary">{t('mobile')}</Badge>}
                    {!responsiveVisibility.desktop && !responsiveVisibility.tablet && !responsiveVisibility.mobile && (
                      <Badge variant="destructive">{t('hiddenOnAllDevices')}</Badge>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  )
}
