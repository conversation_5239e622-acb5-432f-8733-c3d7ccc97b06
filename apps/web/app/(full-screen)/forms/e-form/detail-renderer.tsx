'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Card, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table'
import React from 'react'

import { FormElement } from './form-builder'

interface DetailRow {
  name: string
  value: Any
  type?: string
}

interface DetailRendererProps {
  element: FormElement
  data?: Any
  isPreview?: boolean
}

export function DetailRenderer({ element, data, isPreview = false }: DetailRendererProps) {
  // Lấy rows từ metadata hoặc tạo từ data
  const rows: DetailRow[] = element.metadata?.rows || []

  // Nếu có data thực tế, override rows từ data
  const actualRows = data ? generateRowsFromData(data) : isPreview ? [] : rows

  // Sample data if no data provided and not in preview mode
  const sampleRows: DetailRow[] = [
    { name: 'ID', value: 'doc_123456', type: 'text' },
    { name: 'Tên', value: 'Tài liệu mẫu', type: 'text' },
    { name: '<PERSON><PERSON><PERSON>', value: 'document_set', type: 'text' },
    { name: 'Chia sẻ', value: 0, type: 'number' },
    { name: 'Template ID', value: 'template_789', type: 'text' },
    { name: 'Ngày tạo', value: '2024-01-15T10:30:00Z', type: 'datetime' },
    { name: 'Ngày cập nhật', value: '2024-01-16T14:20:00Z', type: 'datetime' },
  ]

  const displayRows = actualRows.length > 0 ? actualRows : isPreview ? [] : sampleRows

  const formatValue = (value: Any, type?: string) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">Không có dữ liệu</span>
    }

    switch (type) {
      case 'datetime':
        try {
          return new Date(value).toLocaleString('vi-VN')
        } catch {
          return String(value)
        }

      case 'date':
        try {
          return new Date(value).toLocaleDateString('vi-VN')
        } catch {
          return String(value)
        }
      case 'number':
        return Number(value).toLocaleString('vi-VN')
      case 'boolean':
        return value ? (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Có
          </Badge>
        ) : (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800">
            Không
          </Badge>
        )
      case 'array':
        if (Array.isArray(value)) {
          return (
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              Array ({value.length} items)
            </Badge>
          )
        }

        return String(value)
      case 'object':
        if (typeof value === 'object') {
          return (
            <Badge variant="outline" className="bg-purple-50 text-purple-700">
              Object
            </Badge>
          )
        }

        return String(value)
      default:
        return String(value)
    }
  }

  const tableClasses = [element.metadata?.bordered !== false && 'border', 'w-full'].filter(Boolean).join(' ')

  const showHeader = element.metadata?.showHeader !== false
  const striped = element.metadata?.striped !== false
  const hover = element.metadata?.hover !== false

  const rowClasses = [striped && 'even:bg-muted/50', hover && 'hover:bg-muted/30'].filter(Boolean).join(' ')

  return (
    <div className="w-full space-y-4">
      {/* Header */}
      {element.label && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">{element.label}</CardTitle>
          </CardHeader>
        </Card>
      )}

      {/* Detail Table */}
      <div className="overflow-hidden rounded-md border">
        <Table className={tableClasses}>
          {showHeader && (
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/3 font-semibold">Tên trường</TableHead>
                <TableHead className="font-semibold">Thông tin</TableHead>
              </TableRow>
            </TableHeader>
          )}
          <TableBody>
            {displayRows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={2} className="text-muted-foreground py-8 text-center">
                  {isPreview ? 'Không có dữ liệu để hiển thị' : 'Chưa có dữ liệu'}
                </TableCell>
              </TableRow>
            ) : (
              displayRows.map((row, index) => (
                <TableRow key={index} className={rowClasses}>
                  <TableCell className="text-sm font-medium">{row.name}</TableCell>
                  <TableCell>{formatValue(row.value, row.type)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Info */}
      {!isPreview && displayRows.length > 0 && (
        <div className="text-muted-foreground text-center text-xs">Tổng {displayRows.length} trường dữ liệu</div>
      )}
    </div>
  )
}

/**
 * Generate rows from data object
 */
function generateRowsFromData(data: Any): DetailRow[] {
  const rows: DetailRow[] = []

  // Nếu data có structure như { data: {...} }, lấy data.data
  const actualData = data?.data || data

  if (typeof actualData === 'object' && actualData !== null) {
    Object.entries(actualData).forEach(([key, value]) => {
      // Skip nested objects và arrays phức tạp cho detail view
      if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object') {
        // Hiển thị array of objects như một badge
        rows.push({
          name: humanizeFieldName(key),
          value: value,
          type: 'array',
        })
      } else if (typeof value === 'object' && value !== null) {
        // Hiển thị object như một badge
        rows.push({
          name: humanizeFieldName(key),
          value: value,
          type: 'object',
        })
      } else {
        // Hiển thị primitive values
        rows.push({
          name: humanizeFieldName(key),
          value: value,
          type: detectValueType(value),
        })
      }
    })
  }

  return rows
}

/**
 * Detect value type for formatting
 */
function detectValueType(value: Any): string {
  if (typeof value === 'boolean') return 'boolean'

  if (typeof value === 'number') return 'number'

  if (typeof value === 'string') {
    // Check for date patterns
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      return 'datetime'
    }

    if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
      return 'date'
    }
  }

  if (Array.isArray(value)) return 'array'

  if (typeof value === 'object' && value !== null) return 'object'

  return 'text'
}

/**
 * Convert field names to human readable format
 */
function humanizeFieldName(fieldName: string): string {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/[_-]/g, ' ')
    .toLowerCase()
    .replace(/^\w/, c => c.toUpperCase())
    .trim()
}
