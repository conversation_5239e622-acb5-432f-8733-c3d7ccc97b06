'use client'

interface GridColumnIndicatorProps {
  columns: number
  maxColumns: number
  isSelected: boolean
}

export function GridColumnIndicator({ columns, maxColumns, isSelected }: GridColumnIndicatorProps) {
  return (
    <div
      className={`absolute -top-3 left-0 rounded px-1.5 py-0.5 text-[10px] font-medium ${
        isSelected ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
      }`}
    >
      {columns}/{maxColumns}
    </div>
  )
}
