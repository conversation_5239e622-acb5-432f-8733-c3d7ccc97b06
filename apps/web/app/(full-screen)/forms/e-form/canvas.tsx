'use client'

import { useDroppable } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useTranslations } from '@workspace/ui/hooks/use-translations'

import { ElementRenderer } from './element-renderer'
import { type FormElement, useFormBuilder } from './form-builder'

interface CanvasProps {
  updateElement: (elementId: string, updates: Partial<FormElement>) => void
  deleteElement: (elementId: string) => void
  moveElement: (elementId: string, direction: 'up' | 'down') => void
  duplicateElement: (elementId: string) => void
  isElementVisible: (element: FormElement) => boolean
}

export function Canvas({ updateElement, deleteElement, moveElement, duplicateElement, isElementVisible }: CanvasProps) {
  const t = useTranslations('e-form')
  const { elements, currentBreakpoint, isPreviewMode, setSelectedElement } = useFormBuilder()
  const { isOver, setNodeRef } = useDroppable({
    id: 'canvas',
    data: { accepts: ['element'] },
    disabled: isPreviewMode,
  })

  const getBreakpointClass = () => {
    switch (currentBreakpoint) {
      case 'mobile':
        return 'max-w-sm mx-auto'
      case 'tablet':
        return 'max-w-2xl mx-auto'
      default:
        return 'max-w-full'
    }
  }

  const renderElements = () => {
    return elements.map(element => {
      // Skip rendering if element has a visibility condition that evaluates to false
      if (!isElementVisible(element)) return null

      return (
        <ElementRenderer
          key={element.id}
          element={element}
          updateElement={updateElement}
          deleteElement={deleteElement}
          moveElement={moveElement}
          duplicateElement={duplicateElement}
          isElementVisible={isElementVisible}
        />
      )
    })
  }

  return (
    <div className={`${isPreviewMode ? 'p-2' : 'p-6'} min-h-full`}>
      <div className={`${getBreakpointClass()} transition-all duration-300`}>
        <div
          ref={isPreviewMode ? undefined : setNodeRef}
          className={`min-h-96 ${
            isPreviewMode ? '' : 'rounded-lg border-2 border-dashed p-4'
          } transition-colors ${isOver && !isPreviewMode ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'} ${
            elements.length === 0 ? 'flex items-center justify-center' : ''
          }`}
          onClick={() => !isPreviewMode && setSelectedElement(null)}
        >
          {elements.length === 0 ? (
            !isPreviewMode && (
              <div className="text-muted-foreground text-center">
                <p className="text-lg font-medium">{t('dropComponentsHere')}</p>
                <p className="text-sm">{t('dragComponentsFromTheLeftPanelToStartBuildingYourForm')}</p>
              </div>
            )
          ) : isPreviewMode ? (
            <div className="space-y-2 rounded-md border border-dashed border-gray-300">{renderElements()}</div>
          ) : (
            <SortableContext items={elements.map(el => el.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-2">{renderElements()}</div>
            </SortableContext>
          )}
        </div>
      </div>
    </div>
  )
}
