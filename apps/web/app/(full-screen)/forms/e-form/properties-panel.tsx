'use client'

import { Any } from '@/lib/types'
import { <PERSON><PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Switch } from '@workspace/ui/components/switch'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { MousePointerClick, Plus, Trash2 } from 'lucide-react'

import { ArrayLayoutConfig } from './array-layout-config'
import { BoxLayoutConfig } from './box-layout-config'
import { ColumnLayoutConfig } from './column-layout-config'
import { ConditionEditor } from './condition-editor'
import { ContainerLayoutConfig } from './container-layout-config'
import { DetailConfig } from './detail-config'
import { type FormElement, SelectOption, useFormBuilder } from './form-builder'
import { HtmlElementConfig } from './html-element-config'
import { TableConfig } from './table-config'
import { ThemeManager } from './theme-manager'
import { TreeLayout } from './tree-layout'
import { ValidationManager } from './validation-manager'

interface PropertiesPanelProps {
  updateElement: (elementId: string, updates: Partial<FormElement>) => void
}

export function PropertiesPanel({ updateElement }: PropertiesPanelProps) {
  const t = useTranslations('e-form')
  const { selectedElement, currentBreakpoint, setSelectedElement } = useFormBuilder()

  console.log(
    'PropertiesPanel render - selectedElement:',
    selectedElement?.id,
    selectedElement?.label || selectedElement?.type
  )

  if (!selectedElement) {
    return (
      <div className="flex h-full flex-col">
        <div className="text-muted-foreground flex border-b p-4">
          <MousePointerClick className="mr-2 h-6 w-6" /> <span>{t('selectAnElementToEditItsProperties')}</span>
        </div>

        <div className="flex-1 overflow-auto">
          <TreeLayout onSelectElement={setSelectedElement} />
        </div>

        {/* Theme Manager is always visible */}
        <div className="mt-auto overflow-y-auto p-3">
          <ThemeManager />
        </div>
      </div>
    )
  }

  console.log('Showing properties for element:', selectedElement.id, selectedElement.type)

  const currentStyles = selectedElement.styles[currentBreakpoint]

  const updateProperty = (property: keyof FormElement, value: Any) => {
    updateElement(selectedElement.id, { [property]: value })
  }

  const updateStyle = (property: string, value: string) => {
    const newStyles = {
      ...selectedElement.styles,
      [currentBreakpoint]: {
        ...currentStyles,
        [property]: value,
      },
    }
    updateElement(selectedElement.id, { styles: newStyles })
  }

  const updateCustomAttribute = (attribute: string, value: Any) => {
    const newCustomAttributes = {
      ...selectedElement.customAttributes,
      [attribute]: value,
    }
    updateElement(selectedElement.id, { customAttributes: newCustomAttributes })
  }

  const addOption = () => {
    const currentOptions = selectedElement.options || []
    updateProperty('options', [
      ...currentOptions,
      {
        label: `Option ${currentOptions.length + 1}`,
        value: `option_${currentOptions.length + 1}`,
      },
    ])
  }

  const updateOptionLabel = (index: number, label: string) => {
    const currentOptions = selectedElement.options || []
    const newOptions = [...currentOptions]
    newOptions[index] = { ...newOptions[index], label } as SelectOption
    updateProperty('options', newOptions)
  }

  const updateOptionValue = (index: number, value: string) => {
    const currentOptions = selectedElement.options || []
    const newOptions = [...currentOptions]
    newOptions[index] = { ...newOptions[index], value } as SelectOption
    updateProperty('options', newOptions)
  }

  const removeOption = (index: number) => {
    const currentOptions = selectedElement.options || []
    const newOptions = currentOptions.filter((_, i) => i !== index)
    updateProperty('options', newOptions)
  }

  const createDefaultElement = (type: 'grid') => ({
    id: `element_${Math.random().toString(36).substring(2, 8)}`,
    type,
    label: 'Grid',
    children: [],
    styles: {
      desktop: { width: '100%', padding: '16px', border: '1px dashed #ccc', borderRadius: '4px' },
      tablet: { width: '100%', padding: '16px', border: '1px dashed #ccc', borderRadius: '4px' },
      mobile: { width: '100%', padding: '16px', border: '1px dashed #ccc', borderRadius: '4px' },
    },
    validationRules: [],
    eventHooks: [],
    category: 'layout',
  })

  return (
    <div className="flex h-full flex-col">
      {/* Fixed Header */}
      <div className="bg-card flex-shrink-0 border-b p-4">
        <h3 className="mb-2 text-lg font-medium">{selectedElement.type.toUpperCase()}</h3>
        <p className="text-muted-foreground text-sm">ID: {selectedElement.id}</p>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-auto">
        <div className="space-y-4 p-3">
          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">{t('general')}</TabsTrigger>
              <TabsTrigger value="style">{t('style')}</TabsTrigger>
              <TabsTrigger value="layout">{t('layout')}</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="text-sm">{t('basicProperties')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 px-3">
                  <div>
                    <Label htmlFor="label">{t('label')}</Label>
                    <Input
                      id="label"
                      value={selectedElement.label || ''}
                      onChange={e => updateProperty('label', e.target.value)}
                    />
                  </div>

                  {['input', 'select', 'textarea', 'checkbox', 'radio', 'array'].includes(selectedElement.type) && (
                    <div>
                      <Label htmlFor="name">{t('name')}</Label>
                      <Input
                        id="name"
                        value={selectedElement.name || ''}
                        onChange={e => updateProperty('name', e.target.value)}
                      />
                    </div>
                  )}

                  {['input', 'textarea'].includes(selectedElement.type) && (
                    <div>
                      <Label htmlFor="placeholder">{t('placeholder')}</Label>
                      <Input
                        id="placeholder"
                        value={selectedElement.placeholder || ''}
                        onChange={e => updateProperty('placeholder', e.target.value)}
                      />
                    </div>
                  )}

                  {['html', 'button', 'image'].includes(selectedElement.type) &&
                    !['h1', 'h2', 'h3', 'paragraph'].includes(selectedElement.type) && (
                      <div>
                        <Label htmlFor="content">
                          {selectedElement.type === 'image' ? t('imageUrl') : t('content')}
                        </Label>
                        <Textarea
                          id="content"
                          value={selectedElement.htmlContent || ''}
                          onChange={e => updateProperty('htmlContent', e.target.value)}
                          rows={3}
                        />
                      </div>
                    )}

                  {selectedElement.type === 'button' && (
                    <div>
                      <Label htmlFor="buttonType">{t('buttonType')}</Label>
                      <Select
                        value={selectedElement.customAttributes?.buttonType || 'button'}
                        onValueChange={value => updateCustomAttribute('buttonType', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('selectButtonType')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="button">{t('button')}</SelectItem>
                          <SelectItem value="submit">{t('submit')}</SelectItem>
                          <SelectItem value="reset">{t('reset')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {['input', 'select', 'textarea', 'checkbox', 'radio', 'array'].includes(selectedElement.type) && (
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="required"
                        checked={selectedElement.required || false}
                        onCheckedChange={checked => updateProperty('required', checked)}
                      />
                      <Label htmlFor="required">{t('required')}</Label>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="className">{t('cssClass')}</Label>
                    <Input
                      id="className"
                      value={selectedElement.className || ''}
                      onChange={e => updateProperty('className', e.target.value)}
                    />
                  </div>
                </CardContent>
              </Card>

              {['select', 'radio'].includes(selectedElement.type) && (
                <Card className="shadow-none">
                  <CardHeader>
                    <CardTitle className="text-sm">{t('options')}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {selectedElement.options?.map((option, index) => (
                      <div key={index} className="flex flex-col gap-2 rounded-md border p-3">
                        <div className="flex items-center gap-2">
                          <Label className="text-muted-foreground w-12 text-xs">{t('label')}:</Label>
                          <Input
                            value={option.label || ''}
                            onChange={e => updateOptionLabel(index, e.target.value)}
                            placeholder={`Label ${index + 1}`}
                            className="flex-1"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Label className="text-muted-foreground w-12 text-xs">{t('value')}:</Label>
                          <Input
                            value={option.value || ''}
                            onChange={e => updateOptionValue(index, e.target.value)}
                            placeholder={`value_${index + 1}`}
                            className="flex-1"
                          />
                          <Button size="sm" variant="outline" onClick={() => removeOption(index)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button size="sm" variant="outline" onClick={addOption} className="w-full">
                      <Plus className="mr-2 h-4 w-4" />
                      {t('addOption')}
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="style" className="space-y-4">
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="text-sm">
                    {t('appearance')} ({currentBreakpoint})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="width">{t('width')}</Label>
                      <Input
                        id="width"
                        value={currentStyles.width || ''}
                        onChange={e => updateStyle('width', e.target.value)}
                        placeholder="100%"
                      />
                    </div>
                    <div>
                      <Label htmlFor="height">{t('height')}</Label>
                      <Input
                        id="height"
                        value={currentStyles.height || ''}
                        onChange={e => updateStyle('height', e.target.value)}
                        placeholder="auto"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="backgroundColor">{t('backgroundColor')}</Label>
                    <Input
                      id="backgroundColor"
                      value={currentStyles.backgroundColor || ''}
                      onChange={e => updateStyle('backgroundColor', e.target.value)}
                      placeholder="#ffffff"
                    />
                  </div>

                  <div>
                    <Label htmlFor="color">{t('textColor')}</Label>
                    <Input
                      id="color"
                      value={currentStyles.color || ''}
                      onChange={e => updateStyle('color', e.target.value)}
                      placeholder="#000000"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="fontSize">{t('fontSize')}</Label>
                      <Input
                        id="fontSize"
                        value={currentStyles.fontSize || ''}
                        onChange={e => updateStyle('fontSize', e.target.value)}
                        placeholder="14px"
                      />
                    </div>
                    <div>
                      <Label htmlFor="fontWeight">{t('fontWeight')}</Label>
                      <Input
                        id="fontWeight"
                        value={currentStyles.fontWeight || ''}
                        onChange={e => updateStyle('fontWeight', e.target.value)}
                        placeholder="normal"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="border">{t('border')}</Label>
                    <Input
                      id="border"
                      value={currentStyles.border || ''}
                      onChange={e => updateStyle('border', e.target.value)}
                      placeholder="1px solid #ccc"
                    />
                  </div>

                  <div>
                    <Label htmlFor="borderRadius">{t('borderRadius')}</Label>
                    <Input
                      id="borderRadius"
                      value={currentStyles.borderRadius || ''}
                      onChange={e => updateStyle('borderRadius', e.target.value)}
                      placeholder="4px"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="layout" className="space-y-4">
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="text-sm">
                    {t('spacingAndLayout')} ({currentBreakpoint})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="margin">{t('margin')}</Label>
                    <Input
                      id="margin"
                      value={currentStyles.margin || ''}
                      onChange={e => updateStyle('margin', e.target.value)}
                      placeholder="8px"
                    />
                  </div>

                  <div>
                    <Label htmlFor="padding">{t('padding')}</Label>
                    <Input
                      id="padding"
                      value={currentStyles.padding || ''}
                      onChange={e => updateStyle('padding', e.target.value)}
                      placeholder="16px"
                    />
                  </div>

                  {selectedElement.type === 'grid' && (
                    <>
                      <div>
                        <Label htmlFor="display">{t('display')}</Label>
                        <Input
                          id="display"
                          value={currentStyles.display || ''}
                          onChange={e => updateStyle('display', e.target.value)}
                          placeholder="flex"
                        />
                      </div>

                      <div>
                        <Label htmlFor="flexDirection">{t('flexDirection')}</Label>
                        <Input
                          id="flexDirection"
                          value={currentStyles.flexDirection || ''}
                          onChange={e => updateStyle('flexDirection', e.target.value)}
                          placeholder="column"
                        />
                      </div>

                      <div>
                        <Label htmlFor="justifyContent">{t('justifyContent')}</Label>
                        <Input
                          id="justifyContent"
                          value={currentStyles.justifyContent || ''}
                          onChange={e => updateStyle('justifyContent', e.target.value)}
                          placeholder="flex-start"
                        />
                      </div>

                      <div>
                        <Label htmlFor="alignItems">{t('alignItems')}</Label>
                        <Input
                          id="alignItems"
                          value={currentStyles.alignItems || ''}
                          onChange={e => updateStyle('alignItems', e.target.value)}
                          placeholder="stretch"
                        />
                      </div>

                      <div>
                        <Label htmlFor="gap">{t('gap')}</Label>
                        <Input
                          id="gap"
                          value={currentStyles.gap || ''}
                          onChange={e => updateStyle('gap', e.target.value)}
                          placeholder="8px"
                        />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {selectedElement.type === 'input' && (
                <Card className="shadow-none">
                  <CardHeader>
                    <CardTitle>{t('layout')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Label>{t('layoutDirection')}</Label>
                        <Select
                          value={selectedElement.layout || 'vertical'}
                          onValueChange={value =>
                            updateElement(selectedElement.id, { layout: value as 'vertical' | 'horizontal' })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectLayout')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="vertical">{t('vertical')}</SelectItem>
                            <SelectItem value="horizontal">{t('horizontal')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {selectedElement.layout === 'horizontal' && (
                        <div className="flex items-center space-x-2">
                          <Label>{t('labelAlign')}</Label>
                          <Select
                            value={selectedElement.labelAlign || 'left'}
                            onValueChange={value => updateProperty('labelAlign', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={t('selectAlignment')} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="left">{t('left')}</SelectItem>
                              <SelectItem value="right">{t('right')}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          {/* HTML Element Configuration */}
          {selectedElement && (
            <HtmlElementConfig
              element={selectedElement}
              onUpdateElement={updates => updateElement(selectedElement.id, updates)}
            />
          )}

          {/* Grid Layout Configuration */}
          {selectedElement && selectedElement.type === 'grid' && (
            <BoxLayoutConfig
              element={selectedElement}
              onUpdateLayout={autoLayout => updateElement(selectedElement.id, { autoLayout })}
            />
          )}

          {/* Column Layout Configuration */}
          {selectedElement && selectedElement.type === 'column' && (
            <ColumnLayoutConfig
              element={selectedElement}
              onUpdateElement={updates => updateElement(selectedElement.id, updates)}
              onApplyPreset={preset => {
                // Create Grid elements for each column in the preset
                const newChildren = preset.spans.map((span, index) => {
                  const gridElement = createDefaultElement('grid')
                  gridElement.label = `Column ${index + 1}`

                  return gridElement
                })

                // Update column config with proper spans
                const columnConfig = selectedElement.columnConfig || {
                  maxColumns: 12,
                  gap: '16px',
                  responsive: true,
                  allowReorder: false,
                  children: {},
                }

                newChildren.forEach((child, index) => {
                  columnConfig.children[child.id] = {
                    desktop: { span: preset.spans[index] as number, order: 0 },
                    tablet: { span: Math.min(preset.spans[index] as number, 6), order: 0 },
                    mobile: { span: 12, order: 0 },
                  }
                })

                updateElement(selectedElement.id, {
                  children: newChildren,
                  columnConfig,
                })
              }}
            />
          )}

          {/* Array Layout Configuration */}
          {selectedElement && selectedElement.type === 'array' && (
            <ArrayLayoutConfig
              element={selectedElement}
              onUpdateElement={updates => updateElement(selectedElement.id, updates)}
            />
          )}

          {/* Table Configuration */}
          {selectedElement && selectedElement.type === 'table' && (
            <TableConfig element={selectedElement} onUpdate={updates => updateElement(selectedElement.id, updates)} />
          )}

          {/* Detail Configuration */}
          {selectedElement && selectedElement.type === 'detail' && (
            <DetailConfig element={selectedElement} onUpdate={updates => updateElement(selectedElement.id, updates)} />
          )}

          {/* Container Layout Configuration */}
          {selectedElement && selectedElement.type === 'container' && (
            <ContainerLayoutConfig
              element={selectedElement}
              onUpdateElement={updates => updateElement(selectedElement.id, updates)}
            />
          )}

          {/* Conditional Logic - Moved to same level as Layout Configuration */}
          <ConditionEditor
            element={selectedElement}
            onUpdateCondition={condition => updateElement(selectedElement.id, { visibilityCondition: condition })}
          />

          {/* Validation Manager */}
          <ValidationManager
            element={selectedElement}
            onUpdateValidation={rules => updateElement(selectedElement.id, { validationRules: rules })}
          />

          {/* Theme Manager - Moved to same level as Layout Configuration */}
          <ThemeManager />
        </div>
      </div>
    </div>
  )
}
