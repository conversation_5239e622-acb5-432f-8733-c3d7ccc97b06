'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'
import { Input } from '@workspace/ui/components/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table'
import { ChevronLeft, ChevronRight, Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react'
import React, { useEffect, useMemo, useState } from 'react'

import { FormElement } from './form-builder'

interface TableColumn {
  id: string
  label: string
  key: string
  dataType: 'text' | 'datetime' | 'date' | 'number' | 'boolean' | 'object' | 'array' | 'actions'
  sortable: boolean
  filterable: boolean
  width: string
}

interface TableRendererProps {
  element: FormElement
  data?: Any[]
  isPreview?: boolean
  onEdit?: (row: Any, index: number) => void
  onDelete?: (row: Any, index: number) => void
  onView?: (row: Any, index: number) => void
  onCustomAction?: (action: string, row: Any, index: number) => void
}

export function TableRenderer({
  element,
  data = [],
  isPreview = false,
  onEdit,
  onDelete,
  onView,
  onCustomAction,
}: TableRendererProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState<string | null>(null)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [apiData, setApiData] = useState<Any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Fetch data from API when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (!element.apiConfig?.url || element.apiConfig.url.trim() === '' || element.metadata?.dataPath === undefined)
        return

      setIsLoading(true)

      try {
        const response = await fetch(element.apiConfig?.url || '', {
          method: element.apiConfig?.method || 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...(element.apiConfig?.headers || {}),
          },
        })

        if (response.ok) {
          const responseData = await response.json()
          setApiData(responseData)
        } else {
          console.error('Failed to fetch data:', response.statusText)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [element.apiConfig?.url, element.metadata?.dataPath, element.apiConfig?.headers])

  const columns: TableColumn[] = element.metadata?.columns || []
  const showHeader = element.metadata?.showHeader !== false
  const showPagination = element.metadata?.showPagination !== false
  const pageSize = element.metadata?.pageSize || 10
  const striped = element.metadata?.striped !== false
  const bordered = element.metadata?.bordered !== false
  const hover = element.metadata?.hover !== false

  // Sample data - memoized to prevent infinite re-renders
  const sampleData = useMemo(
    () => [
      { id: 1, column1: 'Dữ liệu mẫu 1', column2: 'Giá trị 1', createdAt: '2024-01-15', status: 'active' },
      { id: 2, column1: 'Dữ liệu mẫu 2', column2: 'Giá trị 2', createdAt: '2024-01-16', status: 'inactive' },
      { id: 3, column1: 'Dữ liệu mẫu 3', column2: 'Giá trị 3', createdAt: '2024-01-17', status: 'active' },
    ],
    []
  )

  // Xử lý dữ liệu từ API response theo dataPath
  const processApiData = useMemo(() => {
    if (element.metadata?.dataPath === undefined || !apiData) return []

    try {
      const dataPath = element.metadata.dataPath

      if (dataPath === 'data') {
        return Array.isArray(apiData) ? apiData : []
      }

      // Xử lý dataPath như "data.items", "items", etc.
      const pathParts = dataPath.split('.')
      let targetData = apiData

      for (const part of pathParts) {
        if (targetData && typeof targetData === 'object' && targetData[part] !== undefined) {
          targetData = targetData[part]
        } else {
          return []
        }
      }

      return Array.isArray(targetData) ? targetData : []
    } catch (error) {
      console.error('Error processing API data with dataPath:', error)

      return []
    }
  }, [apiData, element.metadata?.dataPath])

  const actualData = useMemo(() => {
    if (data.length > 0) return data

    if (processApiData.length > 0) return processApiData

    return isPreview ? [] : sampleData
  }, [data, processApiData, isPreview, sampleData])

  // Process data with filtering and sorting - memoized to prevent infinite re-renders
  const processedData = useMemo(() => {
    let filtered = [...actualData]

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(row =>
          String(row[key] || '')
            .toLowerCase()
            .includes(value.toLowerCase())
        )
      }
    })

    // Apply sorting
    if (sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[sortBy]
        const bValue = b[sortBy]

        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1

        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1

        return 0
      })
    }

    return filtered
  }, [actualData, filters, sortBy, sortOrder])

  const totalPages = Math.ceil(processedData.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedData = showPagination ? processedData.slice(startIndex, endIndex) : processedData

  const handleSort = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey)

    if (!column?.sortable) return

    if (sortBy === columnKey) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(columnKey)
      setSortOrder('asc')
    }
  }

  const handleFilter = (columnKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value,
    }))
    setCurrentPage(1)
  }

  const formatCellValue = (value: Any, dataType: string) => {
    if (value === null || value === undefined) return '-'

    switch (dataType) {
      case 'datetime':
        return new Date(value).toLocaleString('vi-VN')
      case 'date':
        return new Date(value).toLocaleDateString('vi-VN')
      case 'number':
        return Number(value).toLocaleString('vi-VN')
      case 'boolean':
        return value ? <Badge variant="default">Có</Badge> : <Badge variant="secondary">Không</Badge>
      case 'object':
      case 'array':
        return (
          <Badge variant="outline">
            {dataType === 'array' ? `Array (${Array.isArray(value) ? value.length : 0})` : 'Object'}
          </Badge>
        )
      case 'actions':
        return (
          <div className="flex items-center gap-2">
            {onView && (
              <Button variant="ghost" size="sm" onClick={() => onView(value, 0)}>
                <Eye className="h-4 w-4" />
              </Button>
            )}
            {onEdit && (
              <Button variant="ghost" size="sm" onClick={() => onEdit(value, 0)}>
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button variant="ghost" size="sm" onClick={() => onDelete(value, 0)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onCustomAction?.('export', value, 0)}>Xuất dữ liệu</DropdownMenuItem>
                <DropdownMenuItem onClick={() => onCustomAction?.('duplicate', value, 0)}>Nhân bản</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      default:
        return String(value)
    }
  }

  const tableClasses = [bordered && 'border', 'w-full'].filter(Boolean).join(' ')

  const rowClasses = [striped && 'even:bg-muted/50', hover && 'hover:bg-muted/30'].filter(Boolean).join(' ')

  return (
    <div className="w-full space-y-4">
      {/* Filters */}
      {columns.some(col => col.filterable) && (
        <div className="flex flex-wrap gap-2">
          {columns
            .filter(col => col.filterable && col.dataType !== 'actions')
            .map(column => (
              <div key={`filter-${column.id}`} className="min-w-[200px] flex-1">
                <Input
                  placeholder={`Lọc theo ${column.label}...`}
                  value={filters[column.key] || ''}
                  onChange={e => handleFilter(column.key, e.target.value)}
                  className="text-sm"
                />
              </div>
            ))}
        </div>
      )}

      {/* Table */}
      <div className="overflow-hidden rounded-md border">
        <Table className={tableClasses}>
          {showHeader && (
            <TableHeader>
              <TableRow>
                {columns.map(column => (
                  <TableHead
                    key={column.id}
                    style={{ width: column.width !== 'auto' ? column.width : undefined }}
                    className={column.sortable ? 'cursor-pointer select-none' : ''}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-2">
                      {column.label}
                      {column.sortable && sortBy === column.key && (
                        <span className="text-xs">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
          )}
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-muted-foreground py-8 text-center">
                  Đang tải dữ liệu...
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-muted-foreground py-8 text-center">
                  {isPreview ? 'Không có dữ liệu để hiển thị' : 'Chưa có dữ liệu'}
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => (
                <TableRow key={row.id || index} className={rowClasses}>
                  {columns.map(column => (
                    <TableCell key={`${row.id || index}-${column.id}`}>
                      {column.dataType === 'actions'
                        ? formatCellValue(row, column.dataType)
                        : formatCellValue(row[column.key], column.dataType)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-muted-foreground text-sm">
            Hiển thị {startIndex + 1} - {Math.min(endIndex, processedData.length)} của {processedData.length} kết quả
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Trước
            </Button>
            <span className="text-sm">
              Trang {currentPage} / {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Sau
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
