'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { ArrowRight, CreditCard, FileText, Grid, Layout, List } from 'lucide-react'
import { useState } from 'react'

import { type CodeGenerationOptions } from './code-generation-modal'

interface DetailCodeOptionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (options: CodeGenerationOptions) => void
}

export function DetailCodeOptionsModal({ open, onOpenChange, onConfirm }: DetailCodeOptionsModalProps) {
  const [moduleName, setModuleName] = useState('user-management')
  const [componentName, setComponentName] = useState('UserDetail')
  const [prefixPageFolder, setPrefixPageFolder] = useState('(admin)')
  const [framework, setFramework] = useState<'shadcn' | 'mui' | 'antd'>('shadcn')
  const [layout, setLayout] = useState<'card' | 'list' | 'grid'>('card')
  const [showActions, setShowActions] = useState(true)
  const [modelName, setModelName] = useState('user')

  const handleConfirm = () => {
    if (!moduleName.trim() || !componentName.trim()) {
      alert('Vui lòng nhập đầy đủ tên module và component')

      return
    }

    const options: CodeGenerationOptions = {
      moduleName: moduleName.trim(),
      componentName: componentName.trim(),
      prefixPageFolder: prefixPageFolder.trim(),
      framework,
      layout,
      showActions,
      modelName: modelName.trim(),
    }

    onConfirm(options)
    onOpenChange(false)
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  const layoutOptions = [
    {
      value: 'card' as const,
      label: 'Card Layout',
      description: 'Hiển thị thông tin trong các card',
      icon: <CreditCard className="h-4 w-4" />,
    },
    {
      value: 'list' as const,
      label: 'List Layout',
      description: 'Hiển thị thông tin dạng danh sách',
      icon: <List className="h-4 w-4" />,
    },
    {
      value: 'grid' as const,
      label: 'Grid Layout',
      description: 'Hiển thị thông tin dạng lưới',
      icon: <Grid className="h-4 w-4" />,
    },
  ]

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Cấu hình Detail Component
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Layout className="h-4 w-4" />
                Thông tin cơ bản
              </CardTitle>
              <CardDescription>Cấu hình tên module và component sẽ được tạo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="moduleName">Tên module</Label>
                  <Input
                    id="moduleName"
                    value={moduleName}
                    onChange={e => setModuleName(e.target.value)}
                    placeholder="vd: user-management"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="componentName">Tên component</Label>
                  <Input
                    id="componentName"
                    value={componentName}
                    onChange={e => setComponentName(e.target.value)}
                    placeholder="vd: UserDetail"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng PascalCase</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="modelName">Tên model</Label>
                  <Input
                    id="modelName"
                    value={modelName}
                    onChange={e => setModelName(e.target.value)}
                    placeholder="vd: user"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prefixPageFolder">Tên thư mục (prefix)</Label>
                  <Input
                    id="prefixPageFolder"
                    value={prefixPageFolder}
                    onChange={e => setPrefixPageFolder(e.target.value)}
                    placeholder="vd: (admin)"
                  />
                  <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="framework">UI Framework</Label>
                <Select value={framework} onValueChange={(value: 'shadcn' | 'mui' | 'antd') => setFramework(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn UI framework" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shadcn">ShadCN UI (Khuyến nghị)</SelectItem>
                    <SelectItem value="mui">Material UI</SelectItem>
                    <SelectItem value="antd">Ant Design</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Layout Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Layout hiển thị</CardTitle>
              <CardDescription>Chọn cách hiển thị thông tin chi tiết</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {layoutOptions.map(option => (
                <Card
                  key={option.value}
                  className={`cursor-pointer transition-all hover:shadow-sm ${layout === option.value ? 'ring-primary ring-2' : 'hover:border-primary/50'}`}
                  onClick={() => setLayout(option.value)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 text-primary rounded-full p-2">{option.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-medium">{option.label}</h4>
                        <p className="text-muted-foreground text-sm">{option.description}</p>
                      </div>
                      {layout === option.value && (
                        <div className="bg-primary flex h-4 w-4 items-center justify-center rounded-full">
                          <div className="h-2 w-2 rounded-full bg-white" />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>

          {/* Additional Features */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Tính năng bổ sung</CardTitle>
              <CardDescription>Cấu hình các tính năng cho component detail</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showActions"
                  checked={showActions}
                  onCheckedChange={checked => setShowActions(!!checked)}
                />
                <Label htmlFor="showActions" className="text-sm">
                  Hiển thị các action buttons (Edit, Delete, etc.)
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button onClick={handleConfirm} className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              Tạo Detail Code
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
