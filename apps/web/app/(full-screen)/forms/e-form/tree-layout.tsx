import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { cn } from '@workspace/ui/lib/utils'
import { DiamondIcon, DiamondMinus, DiamondPlus } from 'lucide-react'
import { useState } from 'react'

import { type FormElement, useFormBuilder } from './form-builder'
import { iconMap } from './form-builder'

interface TreeLayoutProps {
  onSelectElement: (element: FormElement) => void
}

export function TreeLayout({ onSelectElement }: TreeLayoutProps) {
  const t = useTranslations('e-form')
  const { elements } = useFormBuilder()
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  const toggleNode = (elementId: string) => {
    const newExpandedNodes = new Set(expandedNodes)

    if (newExpandedNodes.has(elementId)) {
      newExpandedNodes.delete(elementId)
    } else {
      newExpandedNodes.add(elementId)
    }
    setExpandedNodes(newExpandedNodes)
  }

  const renderElement = (element: FormElement, level: number = 0) => {
    const hasChildren = element.children && element.children.length > 0
    const isExpanded = expandedNodes.has(element.id)

    return (
      <div key={element.id} className="select-none">
        <div
          className={cn('hover:bg-accent/50 flex cursor-pointer items-center gap-1 rounded-sm px-2 py-1', 'text-sm')}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => onSelectElement(element)}
        >
          {hasChildren ? (
            <button
              onClick={e => {
                e.stopPropagation()
                toggleNode(element.id)
              }}
              className="hover:bg-accent rounded-sm"
            >
              {isExpanded ? <DiamondMinus className="h-4 w-4" /> : <DiamondPlus className="h-4 w-4" />}
            </button>
          ) : (
            <DiamondIcon className="h-4 w-4" />
          )}
          {iconMap[element.type] || <DiamondIcon className="h-4 w-4" />}
          <span className="truncate">
            {element.type} - {element.id}
          </span>
        </div>
        {hasChildren && isExpanded && <div>{element.children?.map(child => renderElement(child, level + 1))}</div>}
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="text-muted-foreground border-b px-4 py-3 text-center">
        <p>{t('treeLayout')}</p>
      </div>
      <div className="flex-1 overflow-auto py-2">{elements.map(element => renderElement(element))}</div>
    </div>
  )
}
