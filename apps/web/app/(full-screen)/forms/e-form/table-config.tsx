'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { ArrowDown, ArrowUp, GripVertical, Plus, Trash2 } from 'lucide-react'
import React, { useState } from 'react'

import { FormElement } from './form-builder'

interface TableColumn {
  id: string
  label: string
  key: string
  dataType: 'text' | 'datetime' | 'date' | 'number' | 'boolean' | 'object' | 'array' | 'actions'
  sortable: boolean
  filterable: boolean
  width: string
}

interface TableConfigProps {
  element: FormElement
  onUpdate: (updates: Partial<FormElement>) => void
}

const DATA_TYPE_OPTIONS = [
  { value: 'text', label: 'Văn bản' },
  { value: 'number', label: 'Số' },
  { value: 'date', label: 'Ngày' },
  { value: 'datetime', label: 'Ngày giờ' },
  { value: 'boolean', label: 'True/False' },
  { value: 'object', label: 'Object' },
  { value: 'array', label: 'Array' },
  { value: 'actions', label: 'Actions' },
]

const WIDTH_OPTIONS = [
  { value: 'auto', label: 'Tự động' },
  { value: '80px', label: '80px' },
  { value: '120px', label: '120px' },
  { value: '150px', label: '150px' },
  { value: '200px', label: '200px' },
  { value: '250px', label: '250px' },
  { value: '300px', label: '300px' },
]

export function TableConfig({ element, onUpdate }: TableConfigProps) {
  const [columns, setColumns] = useState<TableColumn[]>(element.metadata?.columns || [])

  const generateId = () => `col_${Math.random().toString(36).substring(2, 7)}`

  const addColumn = () => {
    const newColumn: TableColumn = {
      id: generateId(),
      label: `Cột ${columns.length + 1}`,
      key: `column${columns.length + 1}`,
      dataType: 'text',
      sortable: true,
      filterable: true,
      width: 'auto',
    }

    const newColumns = [...columns, newColumn]
    setColumns(newColumns)
    updateTableMetadata(newColumns)
  }

  const removeColumn = (columnId: string) => {
    const newColumns = columns.filter(col => col.id !== columnId)
    setColumns(newColumns)
    updateTableMetadata(newColumns)
  }

  const updateColumn = (columnId: string, updates: Partial<TableColumn>) => {
    const newColumns = columns.map(col => (col.id === columnId ? { ...col, ...updates } : col))
    setColumns(newColumns)
    updateTableMetadata(newColumns)
  }

  const moveColumn = (columnId: string, direction: 'up' | 'down') => {
    const index = columns.findIndex(col => col.id === columnId)

    if (index === -1) return

    const newIndex = direction === 'up' ? index - 1 : index + 1

    if (newIndex < 0 || newIndex >= columns.length) return

    const newColumns = [...columns]
    ;[newColumns[index], newColumns[newIndex] as TableColumn] = [
      newColumns[newIndex] as TableColumn,
      newColumns[index] as TableColumn,
    ]

    setColumns(newColumns)
    updateTableMetadata(newColumns)
  }

  const updateTableMetadata = (newColumns: TableColumn[]) => {
    const metadata = {
      ...element.metadata,
      columns: newColumns,
    }

    onUpdate({ metadata })
  }

  const updateTableSetting = (key: string, value: Any) => {
    const metadata = {
      ...element.metadata,
      [key]: value,
    }

    onUpdate({ metadata })
  }

  return (
    <div className="space-y-6">
      {/* Table Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Cài đặt bảng</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="showHeader"
                checked={element.metadata?.showHeader !== false}
                onCheckedChange={checked => updateTableSetting('showHeader', checked)}
              />
              <Label htmlFor="showHeader" className="text-sm">
                Hiện header
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="showPagination"
                checked={element.metadata?.showPagination !== false}
                onCheckedChange={checked => updateTableSetting('showPagination', checked)}
              />
              <Label htmlFor="showPagination" className="text-sm">
                Phân trang
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="striped"
                checked={element.metadata?.striped !== false}
                onCheckedChange={checked => updateTableSetting('striped', checked)}
              />
              <Label htmlFor="striped" className="text-sm">
                Dòng xen kẽ
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="bordered"
                checked={element.metadata?.bordered !== false}
                onCheckedChange={checked => updateTableSetting('bordered', checked)}
              />
              <Label htmlFor="bordered" className="text-sm">
                Viền
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="hover"
                checked={element.metadata?.hover !== false}
                onCheckedChange={checked => updateTableSetting('hover', checked)}
              />
              <Label htmlFor="hover" className="text-sm">
                Hover
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="responsive"
                checked={element.metadata?.responsive !== false}
                onCheckedChange={checked => updateTableSetting('responsive', checked)}
              />
              <Label htmlFor="responsive" className="text-sm">
                Responsive
              </Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm">Số dòng mỗi trang</Label>
            <Select
              value={String(element.metadata?.pageSize || 10)}
              onValueChange={value => updateTableSetting('pageSize', Number(value))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-sm">Đường dẫn dữ liệu</Label>
            <Input
              value={element.metadata?.dataPath || 'data.items'}
              onChange={e => updateTableSetting('dataPath', e.target.value)}
              placeholder="Ví dụ: data.items, data, items"
              className="h-8"
            />
            <p className="text-muted-foreground text-xs">Đường dẫn đến mảng dữ liệu trong response API</p>
          </div>
        </CardContent>
      </Card>

      {/* Columns Configuration */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-sm">Cấu hình cột ({columns.length})</CardTitle>
          <Button onClick={addColumn} size="sm" className="h-8">
            <Plus className="mr-1 h-4 w-4" />
            Thêm cột
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {columns.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <p>Chưa có cột nào</p>
              <p className="text-sm">Nhấn &quot;Thêm cột&quot; để bắt đầu</p>
            </div>
          ) : (
            columns.map((column, index) => (
              <Card key={column.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GripVertical className="text-muted-foreground h-4 w-4 cursor-move" />
                      <Badge variant={column.dataType === 'actions' ? 'destructive' : 'default'}>
                        {DATA_TYPE_OPTIONS.find(opt => opt.value === column.dataType)?.label}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveColumn(column.id, 'up')}
                        disabled={index === 0}
                      >
                        <ArrowUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveColumn(column.id, 'down')}
                        disabled={index === columns.length - 1}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => removeColumn(column.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs">Tên hiển thị</Label>
                      <Input
                        value={column.label}
                        onChange={e => updateColumn(column.id, { label: e.target.value })}
                        placeholder="Nhập tên cột..."
                        className="h-8"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs">Key dữ liệu</Label>
                      <Input
                        value={column.key}
                        onChange={e => updateColumn(column.id, { key: e.target.value })}
                        placeholder="column_key"
                        className="h-8"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs">Loại dữ liệu</Label>
                      <Select
                        value={column.dataType}
                        onValueChange={(value: Any) => updateColumn(column.id, { dataType: value })}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {DATA_TYPE_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs">Độ rộng</Label>
                      <Select value={column.width} onValueChange={value => updateColumn(column.id, { width: value })}>
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {WIDTH_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {column.dataType !== 'actions' && (
                    <div className="flex gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`sortable-${column.id}`}
                          checked={column.sortable}
                          onCheckedChange={checked => updateColumn(column.id, { sortable: checked as boolean })}
                        />
                        <Label htmlFor={`sortable-${column.id}`} className="text-xs">
                          Sắp xếp
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`filterable-${column.id}`}
                          checked={column.filterable}
                          onCheckedChange={checked => updateColumn(column.id, { filterable: checked as boolean })}
                        />
                        <Label htmlFor={`filterable-${column.id}`} className="text-xs">
                          Lọc
                        </Label>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  )
}
