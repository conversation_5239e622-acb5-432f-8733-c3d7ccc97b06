'use client'

import { Alert, AlertDescription } from '@workspace/ui/components/alert'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@workspace/ui/components/collapsible'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Slider } from '@workspace/ui/components/slider'
import { Switch } from '@workspace/ui/components/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { ChevronDown, Columns, LayoutGrid } from 'lucide-react'
import { useState } from 'react'

import { type ColumnPreset, ColumnPresetSelector } from './column-preset-selector'
import { type ColumnConfig, type FormElement, useFormBuilder } from './form-builder'

interface ColumnLayoutConfigProps {
  element: FormElement
  onUpdateElement: (updates: Partial<FormElement>) => void
  onApplyPreset: (preset: ColumnPreset) => void
}

export function ColumnLayoutConfig({ element, onUpdateElement, onApplyPreset }: ColumnLayoutConfigProps) {
  const { currentBreakpoint } = useFormBuilder()
  const [isOpen, setIsOpen] = useState(true)
  const [isPresetOpen, setIsPresetOpen] = useState(true)

  // Initialize column config if it doesn't exist
  const columnConfig = element.columnConfig || {
    maxColumns: 12,
    gap: '16px',
    responsive: true,
    allowReorder: false,
    children: {},
  }

  const updateColumnConfig = (updates: Partial<ColumnConfig>) => {
    onUpdateElement({
      columnConfig: {
        ...columnConfig,
        ...updates,
      },
    })
  }

  const updateChildSpan = (childId: string, span: number) => {
    if (!element.columnConfig) return

    const childConfig = element.columnConfig.children[childId] || {}
    const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

    onUpdateElement({
      columnConfig: {
        ...element.columnConfig,
        children: {
          ...element.columnConfig.children,
          [childId]: {
            ...childConfig,
            [currentBreakpoint]: {
              ...breakpointConfig,
              span,
            },
          },
        },
      },
    })
  }

  const updateChildOrder = (childId: string, order: number) => {
    if (!element.columnConfig) return

    const childConfig = element.columnConfig.children[childId] || {}
    const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

    onUpdateElement({
      columnConfig: {
        ...element.columnConfig,
        children: {
          ...element.columnConfig.children,
          [childId]: {
            ...childConfig,
            [currentBreakpoint]: {
              ...breakpointConfig,
              order,
            },
          },
        },
      },
    })
  }

  const getTotalSpans = (): number => {
    if (!element.children || !element.columnConfig) return 0

    return element.children.reduce((total, child) => {
      const childConfig = element.columnConfig?.children[child.id]
      const span = childConfig?.[currentBreakpoint]?.span || 1

      return total + span
    }, 0)
  }

  const totalSpans = getTotalSpans()
  const isOverflowing = totalSpans > 12
  const hasEmptySpace = totalSpans < 12 && element.children && element.children.length > 0

  const distributeColumns = () => {
    if (!element.children || !element.columnConfig) return

    const childCount = element.children.length

    if (childCount === 0) return

    const baseSpan = Math.floor(12 / childCount)
    const remainder = 12 % childCount

    const newColumnConfig = { ...element.columnConfig }

    element.children.forEach((child, index) => {
      const span = index < remainder ? baseSpan + 1 : baseSpan
      const childConfig = newColumnConfig.children[child.id] || {}
      const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

      newColumnConfig.children[child.id] = {
        ...childConfig,
        [currentBreakpoint]: {
          ...breakpointConfig,
          span,
        },
      }
    })

    onUpdateElement({ columnConfig: newColumnConfig })
  }

  const resetColumnOrder = () => {
    if (!element.children || !element.columnConfig) return

    const newColumnConfig = { ...element.columnConfig }

    element.children.forEach(child => {
      const childConfig = newColumnConfig.children[child.id] || {}
      const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }

      newColumnConfig.children[child.id] = {
        ...childConfig,
        [currentBreakpoint]: {
          ...breakpointConfig,
          order: 0,
        },
      }
    })

    onUpdateElement({ columnConfig: newColumnConfig })
  }

  return (
    <>
      <Collapsible open={isPresetOpen} onOpenChange={setIsPresetOpen}>
        <Card className="shadow-none">
          <CollapsibleTrigger className="w-full text-left">
            <CardHeader className="py-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <LayoutGrid className="h-4 w-4" />
                  <span>Quick Presets</span>
                </CardTitle>
                <ChevronDown className={`h-4 w-4 transition-transform ${isPresetOpen ? 'rotate-180' : ''}`} />
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <p className="text-muted-foreground mb-3 text-xs">
                Select a preset to automatically create columns with the specified layout.
              </p>
              <ColumnPresetSelector onSelect={onApplyPreset} />
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <Card className="shadow-none">
          <CollapsibleTrigger className="w-full text-left">
            <CardHeader className="py-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Columns className="h-4 w-4" />
                  <span>Column Layout Configuration</span>
                </CardTitle>
                <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4 pt-0">
              <Tabs defaultValue="general">
                <TabsList className="mb-2 grid grid-cols-2">
                  <TabsTrigger value="general">General</TabsTrigger>
                  <TabsTrigger value="columns">Columns</TabsTrigger>
                </TabsList>

                <TabsContent value="general" className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="gap">Column Gap</Label>
                      <span className="text-muted-foreground text-xs">{columnConfig.gap}</span>
                    </div>
                    <Input
                      id="gap"
                      value={columnConfig.gap}
                      onChange={e => updateColumnConfig({ gap: e.target.value })}
                      placeholder="16px"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="responsive"
                      checked={columnConfig.responsive}
                      onCheckedChange={checked => updateColumnConfig({ responsive: checked })}
                    />
                    <Label htmlFor="responsive">Responsive columns</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="allowReorder"
                      checked={columnConfig.allowReorder}
                      onCheckedChange={checked => updateColumnConfig({ allowReorder: checked })}
                    />
                    <Label htmlFor="allowReorder">Allow column reordering</Label>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" onClick={distributeColumns} className="flex-1">
                      Distribute Evenly
                    </Button>
                    {columnConfig.allowReorder && (
                      <Button size="sm" variant="outline" onClick={resetColumnOrder} className="flex-1">
                        Reset Order
                      </Button>
                    )}
                  </div>

                  {isOverflowing && (
                    <Alert variant="destructive" className="py-2">
                      <AlertDescription>
                        Total column spans exceed 12. Some columns may not display correctly.
                      </AlertDescription>
                    </Alert>
                  )}

                  {hasEmptySpace && (
                    <Alert className="py-2">
                      <AlertDescription>
                        Total column spans are less than 12. There will be empty space in the row.
                      </AlertDescription>
                    </Alert>
                  )}

                  <div className="text-muted-foreground text-xs">
                    <div className="mb-1 font-medium">Layout Summary ({currentBreakpoint}):</div>
                    <div>Total columns: {element.children?.length || 0}</div>
                    <div>Total spans: {totalSpans} / 12</div>
                    <div>
                      Status:{' '}
                      {isOverflowing
                        ? 'Overflowing'
                        : hasEmptySpace
                          ? 'Has empty space'
                          : element.children?.length
                            ? 'Balanced'
                            : 'Empty'}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="columns" className="space-y-4">
                  {element.children && element.children.length > 0 ? (
                    element.children.map((child, index) => {
                      const childConfig = columnConfig.children[child.id] || {}
                      const breakpointConfig = childConfig[currentBreakpoint] || { span: 1, order: 0 }
                      const span = breakpointConfig.span || 1
                      const order = breakpointConfig.order || 0

                      return (
                        <div key={child.id} className="border-border space-y-2 border-b pb-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">
                              {child.label || `Column ${index + 1}`} ({span}/12)
                            </Label>
                            <div className="text-muted-foreground text-xs">ID: {child.id}</div>
                          </div>

                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`span-${child.id}`} className="text-xs">
                                Span
                              </Label>
                              <span className="text-xs font-medium">{span}/12</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Slider
                                id={`span-${child.id}`}
                                min={1}
                                max={12}
                                step={1}
                                value={[span]}
                                onValueChange={value => updateChildSpan(child.id, value[0] as number)}
                              />
                            </div>
                            <div className="mt-1 flex gap-1">
                              {[1, 2, 3, 4, 6, 12].map(value => (
                                <Button
                                  key={value}
                                  size="sm"
                                  variant={span === value ? 'default' : 'outline'}
                                  className="h-6 flex-1 px-2 text-xs"
                                  onClick={() => updateChildSpan(child.id, value)}
                                >
                                  {value}
                                </Button>
                              ))}
                            </div>
                          </div>

                          {columnConfig.allowReorder && (
                            <div className="mt-2 space-y-1">
                              <div className="flex items-center justify-between">
                                <Label htmlFor={`order-${child.id}`} className="text-xs">
                                  Order ({currentBreakpoint})
                                </Label>
                                <span className="text-xs font-medium">{order}</span>
                              </div>
                              <Input
                                id={`order-${child.id}`}
                                type="number"
                                min={0}
                                value={order}
                                onChange={e => updateChildOrder(child.id, Number.parseInt(e.target.value) || 0)}
                                className="h-8"
                              />
                            </div>
                          )}
                        </div>
                      )
                    })
                  ) : (
                    <div className="text-muted-foreground py-4 text-center">
                      <p>No columns added yet.</p>
                      <p className="mt-1 text-xs">Drag Box components into this Column to create columns.</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
    </>
  )
}
