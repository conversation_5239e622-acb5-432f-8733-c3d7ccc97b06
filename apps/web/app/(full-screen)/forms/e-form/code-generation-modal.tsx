'use client'

import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Download, FileText, FormInput, Info, Table } from 'lucide-react'
import { useState } from 'react'

import { DetailCodeOptionsModal } from './detail-code-options-modal'
import { FormCodeOptionsModal } from './form-code-options-modal'
import { TableCodeOptionsModal } from './table-code-options-modal'

interface CodeGenerationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onGenerate: (type: CodeGenerationType, options: CodeGenerationOptions) => void
}

export type CodeGenerationType = 'form' | 'detail' | 'table'

export interface CodeGenerationOptions {
  moduleName: string
  componentName: string
  framework: 'shadcn' | 'mui' | 'antd'
  modelName?: string
  prefixPageFolder?: string
  // Form specific options
  formType?: 'create' | 'edit' | 'both'
  validation?: boolean
  apiIntegration?: boolean
  // Detail specific options
  layout?: 'card' | 'list' | 'grid'
  showActions?: boolean
  // Table specific options
  pagination?: boolean
  sorting?: boolean
  filtering?: boolean
  // Action options
  deleteAction?: boolean
  editAction?: boolean
  viewAction?: boolean
  createAction?: boolean
  exportAction?: boolean
  importAction?: boolean
  printAction?: boolean
  copyAction?: boolean
  shareAction?: boolean
}

interface GenerationOption {
  type: CodeGenerationType
  title: string
  description: string
  icon: React.ReactNode
  examples: string[]
  status: 'available'
}

export function CodeGenerationModal({ open, onOpenChange, onGenerate }: CodeGenerationModalProps) {
  const [selectedType, setSelectedType] = useState<CodeGenerationType | null>(null)
  const [showOptionsModal, setShowOptionsModal] = useState(false)

  const generationOptions: GenerationOption[] = [
    {
      type: 'table',
      title: 'Bảng dữ liệu',
      description: 'Tạo component bảng hiển thị danh sách dữ liệu với các tính năng phân trang, sắp xếp',
      icon: <Table className="h-6 w-6" />,
      examples: ['Danh sách user', 'Bảng sản phẩm', 'Danh sách đơn hàng'],
      status: 'available',
    },
    {
      type: 'form',
      title: 'Form tạo/cập nhật',
      description: 'Tạo component form để nhập liệu, tạo mới hoặc cập nhật dữ liệu',
      icon: <FormInput className="h-6 w-6" />,
      examples: ['Form tạo user', 'Form cập nhật profile', 'Form đăng ký'],
      status: 'available',
    },
    {
      type: 'detail',
      title: 'Màn hình chi tiết',
      description: 'Tạo component hiển thị thông tin chi tiết của một đối tượng',
      icon: <FileText className="h-6 w-6" />,
      examples: ['Chi tiết user', 'Thông tin sản phẩm', 'Profile detail'],
      status: 'available',
    },
  ]

  const handleSelectType = (type: CodeGenerationType) => {
    setSelectedType(type)
    setShowOptionsModal(true)
  }

  const handleOptionsConfirm = (options: CodeGenerationOptions) => {
    if (selectedType) {
      onGenerate(selectedType, options)
      handleClose()
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    setSelectedType(null)
    setShowOptionsModal(false)
  }

  const handleCloseOptionsModal = () => {
    setShowOptionsModal(false)
    setSelectedType(null)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Chọn kiểu generate code
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Generation Type Selection */}
            <div>
              <h3 className="mb-4 text-lg font-semibold">Chọn kiểu component bạn muốn tạo:</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {generationOptions.map(option => (
                  <Card
                    key={option.type}
                    className="hover:border-primary/50 cursor-pointer transition-all hover:shadow-md"
                    onClick={() => handleSelectType(option.type)}
                  >
                    <CardHeader className="pb-3 text-center">
                      <div className="mb-2 flex justify-center">
                        <div className="bg-primary/10 text-primary rounded-full p-3">{option.icon}</div>
                      </div>
                      <CardTitle className="text-base">{option.title}</CardTitle>
                      <Badge variant="secondary" className="mx-auto w-fit text-xs">
                        {option.status === 'available' ? 'Có sẵn' : 'Đang phát triển'}
                      </Badge>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="mb-3 text-center">{option.description}</CardDescription>
                      <div className="space-y-1">
                        <div className="text-muted-foreground flex items-center gap-1 text-xs">
                          <Info className="h-3 w-3" />
                          <span>Ví dụ:</span>
                        </div>
                        {option.examples.map((example, index) => (
                          <div key={index} className="text-muted-foreground pl-4 text-xs">
                            • {example}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 border-t pt-4">
              <Button variant="outline" onClick={handleClose}>
                Hủy
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Options Modals */}
      {selectedType === 'form' && (
        <FormCodeOptionsModal
          open={showOptionsModal}
          onOpenChange={handleCloseOptionsModal}
          onConfirm={handleOptionsConfirm}
        />
      )}
      {selectedType === 'detail' && (
        <DetailCodeOptionsModal
          open={showOptionsModal}
          onOpenChange={handleCloseOptionsModal}
          onConfirm={handleOptionsConfirm}
        />
      )}
      {selectedType === 'table' && (
        <TableCodeOptionsModal
          open={showOptionsModal}
          onOpenChange={handleCloseOptionsModal}
          onConfirm={handleOptionsConfirm}
        />
      )}
    </>
  )
}
