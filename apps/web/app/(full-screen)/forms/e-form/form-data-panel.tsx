'use client'

import { formGeneratorService } from '@/lib/json-form/form-generator-service'
import { type ApiEndpoint, type ApiSchema } from '@/lib/json-form/json-form-api-service'
import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, ChevronUp, Copy, Database, Globe, RefreshCw } from 'lucide-react'
import { useState } from 'react'

import { ApiImportModal } from './api-import-modal'
import { EndpointUsageModal, type UsageType } from './endpoint-usage-modal'
import { useFormBuilder } from './form-builder'

export function FormDataPanel() {
  const t = useTranslations('e-form')
  const { formData, setFormData, elements, setElements } = useFormBuilder()
  const [isExpanded, setIsExpanded] = useState(false)
  const [exampleData, setExampleData] = useState('')
  const [isApiModalOpen, setIsApiModalOpen] = useState(false)
  const [isUsageModalOpen, setIsUsageModalOpen] = useState(false)
  const [selectedEndpoint, setSelectedEndpoint] = useState<ApiEndpoint | null>(null)
  const [selectedSchema, setSelectedSchema] = useState<ApiSchema | null>(null)

  // Generate example form data based on current form elements
  const generateExampleData = () => {
    const example: Record<string, Any> = {}

    const processElements = (elements: Any[]) => {
      elements.forEach(element => {
        if (
          element.name &&
          [
            'input',
            'select',
            'textarea',
            'checkbox',
            'radio',
            'date-picker',
            'file-upload',
            'rating',
            'slider',
          ].includes(element.type)
        ) {
          switch (element.type) {
            case 'input':
              example[element.name] = element.placeholder || t('sampleTextInput')
              break
            case 'select':
            case 'radio':
              example[element.name] = element.options?.[0] || `${t('option')} 1`
              break
            case 'textarea':
              example[element.name] = t('sampleTextareaContent')
              break
            case 'checkbox':
              example[element.name] = true
              break
            case 'date-picker':
              example[element.name] = new Date().toISOString().split('T')[0]
              break
            case 'file-upload':
              example[element.name] = t('sampleFile')
              break
            case 'rating':
              example[element.name] = element.customAttributes?.max ? Math.ceil(element.customAttributes.max / 2) : 3
              break

            case 'slider': {
              const min = element.customAttributes?.min || 0
              const max = element.customAttributes?.max || 100
              example[element.name] = Math.floor((min + max) / 2)
              break
            }
            default:
              example[element.name] = t('sampleValue')
          }
        }

        if (element.children) {
          processElements(element.children)
        }
      })
    }

    processElements(elements)
    setExampleData(JSON.stringify(example, null, 2))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const applyExampleData = () => {
    try {
      const parsed = JSON.parse(exampleData)
      setFormData(parsed)
    } catch (error) {
      console.error('Invalid JSON in example data:', error)
      alert(t('invalidJsonInExampleData'))
    }
  }

  const clearFormData = () => {
    setFormData({})
  }

  const handleApiImported = (api: ApiSchema) => {
    // Có thể thêm logic để xử lý API đã import
    // Ví dụ: hiển thị thông báo, refresh danh sách API, etc.
    console.log('API imported:', api)
  }

  const handleSelectEndpoint = (endpoint: ApiEndpoint, schema: ApiSchema) => {
    // Lưu endpoint và schema được chọn
    setSelectedEndpoint(endpoint)
    setSelectedSchema(schema)

    // Mở modal chọn kiểu màn hình
    setIsUsageModalOpen(true)

    // Đóng API modal
    setIsApiModalOpen(false)
  }

  const handleSelectUsageType = (endpoint: ApiEndpoint, schema: ApiSchema, usageType: UsageType, dataPath?: string) => {
    try {
      // Generate form từ endpoint
      const generatedForm = formGeneratorService.generateForm(endpoint, schema, usageType, dataPath)

      console.log('Generated form:', generatedForm)

      // Import generated form vào form builder
      setElements(generatedForm.elements)

      // Clear form data hiện tại
      setFormData({})

      // Hiển thị thông báo thành công
      // alert(`Đã tạo thành công ${usageType === 'detail' ? 'màn hình chi tiết' : usageType === 'form' ? 'form' : 'bảng'} từ API endpoint!`)

      // Reset selected values
      setSelectedEndpoint(null)
      setSelectedSchema(null)
    } catch (error) {
      console.error('Error generating form:', error)
      alert(t('errorGeneratingFormFromApi'))
    }
  }

  const currentFormDataJson = JSON.stringify(formData, null, 2)
  const hasFormData = Object.keys(formData).length > 0

  return (
    <div className="bg-card border-t">
      {/* Panel Header */}
      <div
        className="hover:bg-accent/50 flex cursor-pointer items-center justify-between p-3 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Database className="h-4 w-4" />
          <span className="text-sm font-medium">{t('formData')}</span>
          {hasFormData && (
            <Badge variant="secondary" className="text-xs">
              {Object.keys(formData).length} {t('fields')}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={e => {
              e.stopPropagation()
              generateExampleData()
            }}
            className="h-7 px-2"
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            {t('generate')}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={e => {
              e.stopPropagation()
              setIsApiModalOpen(true)
            }}
            className="h-7 px-2"
          >
            <Globe className="mr-1 h-3 w-3" />
            {t('apis')}
          </Button>
          {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
        </div>
      </div>

      {/* Panel Content */}
      {isExpanded && (
        <div className="border-t">
          <div className="grid grid-cols-2 gap-4 p-4">
            {/* Example Data */}
            <Card className="shadow-none">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-sm">
                  <span>{t('exampleFormData')}</span>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(exampleData)}
                      className="h-6 px-2"
                      disabled={!exampleData}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={applyExampleData}
                      className="h-6 px-2 text-xs"
                      disabled={!exampleData}
                    >
                      {t('apply')}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <Textarea
                  value={exampleData}
                  onChange={e => setExampleData(e.target.value)}
                  placeholder={t('clickGenerateToCreateExampleData')}
                  className="resize-none font-mono text-xs"
                  rows={12}
                />
                <p className="text-muted-foreground mt-2 text-xs">{t('editJsonAndApply')}</p>
              </CardContent>
            </Card>

            {/* Current Form Data */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-sm">
                  <span>{t('currentFormData')}</span>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(currentFormDataJson)}
                      className="h-6 px-2"
                      disabled={!hasFormData}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={clearFormData}
                      className="h-6 px-2 text-xs"
                      disabled={!hasFormData}
                    >
                      {t('clear')}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <Textarea
                  value={currentFormDataJson}
                  readOnly
                  placeholder={t('formDataWillAppear')}
                  className="bg-muted/50 resize-none font-mono text-xs"
                  rows={12}
                />
                <p className="text-muted-foreground mt-2 text-xs">
                  {t('showsCurrentFormDataInRealtime')}{' '}
                  {hasFormData ? `${Object.keys(formData).length} ${t('fieldsHaveData')}` : t('noDataEnteredYet')}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="bg-muted/20 border-t p-3">
            <div className="text-muted-foreground flex items-center justify-between text-xs">
              <span>{t('tipUseExampleData')}</span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={generateExampleData} className="h-7 text-xs">
                  <RefreshCw className="mr-1 h-3 w-3" />
                  {t('regenerateExample')}
                </Button>
                {hasFormData && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const dataStr = JSON.stringify(formData, null, 2)
                      const blob = new Blob([dataStr], { type: 'application/json' })
                      const url = URL.createObjectURL(blob)
                      const a = document.createElement('a')
                      a.href = url
                      a.download = 'form-data.json'
                      a.click()
                      URL.revokeObjectURL(url)
                    }}
                    className="h-7 text-xs"
                  >
                    {t('exportJson')}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* API Import Modal */}
      <ApiImportModal
        open={isApiModalOpen}
        onOpenChange={setIsApiModalOpen}
        onApiImported={handleApiImported}
        onSelectEndpoint={handleSelectEndpoint}
      />

      {/* Endpoint Usage Modal */}
      <EndpointUsageModal
        open={isUsageModalOpen}
        onOpenChange={setIsUsageModalOpen}
        endpoint={selectedEndpoint}
        schema={selectedSchema}
        onSelectUsageType={handleSelectUsageType}
      />
    </div>
  )
}
