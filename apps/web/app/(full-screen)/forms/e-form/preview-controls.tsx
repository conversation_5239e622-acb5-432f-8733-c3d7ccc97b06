'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@workspace/ui/components/resizable'
import { ScrollArea } from '@workspace/ui/components/scroll-area'
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@workspace/ui/components/sheet'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { Code, Eye } from 'lucide-react'

import { useFormBuilder } from './form-builder'

interface PreviewControlsProps {
  isPreviewMode: boolean
  onTogglePreview: () => void
}

export function PreviewControls({ isPreviewMode, onTogglePreview }: PreviewControlsProps) {
  const t = useTranslations('e-form')
  const { formElements, formData } = useFormBuilder()

  return (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={onTogglePreview} className="gap-1">
        <Eye className="h-4 w-4" />
        {isPreviewMode ? t('exitPreview') : t('preview')}
      </Button>

      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1">
            <Code className="h-4 w-4" />
            {t('viewJson')}
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-full sm:max-w-none">
          <SheetHeader className="border-b pb-4">
            <SheetTitle>
              {t('viewJson')}: {t('formData')}
            </SheetTitle>
          </SheetHeader>
          <ResizablePanelGroup direction="horizontal" className="h-[calc(100vh-4rem)]">
            <ResizablePanel defaultSize={66}>
              <div className="h-full p-4">
                <h3 className="mb-2 text-sm font-medium">{t('formStructure')}</h3>
                <ScrollArea className="h-[calc(100%-4rem)] rounded-md border p-4">
                  <pre className="text-sm">{JSON.stringify(formElements, null, 2)}</pre>
                </ScrollArea>
              </div>
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel defaultSize={34}>
              <div className="h-full p-4">
                <h3 className="mb-2 text-sm font-medium">{t('formData')}</h3>
                <ScrollArea className="h-[calc(100%-4rem)] rounded-md border p-4">
                  <pre className="text-sm">{JSON.stringify(formData, null, 2)}</pre>
                </ScrollArea>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </SheetContent>
      </Sheet>
    </div>
  )
}
