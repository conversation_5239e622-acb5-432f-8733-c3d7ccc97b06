'use client'

import { But<PERSON> } from '@workspace/ui/components/button'
import { Code2, Download, Upload } from 'lucide-react'
import type React from 'react'
import { useRef } from 'react'

interface ExportImportControlsProps {
  onExport: () => void
  onImport: (jsonString: string) => void
  onDownloadReactCode: () => void
}

export function ExportImportControls({ onExport, onImport, onDownloadReactCode }: ExportImportControlsProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImport = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]

    if (file) {
      const reader = new FileReader()

      reader.onload = e => {
        const content = e.target?.result as string
        onImport(content)
      }
      reader.readAsText(file)
    }

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Button onClick={onExport} variant="outline" size="sm" className="gap-2" title="Export to JSON">
        <Download className="h-4 w-4" />
        {/* Export */}
      </Button>
      <Button onClick={handleImport} variant="outline" size="sm" className="gap-2" title="Import from JSON">
        <Upload className="h-4 w-4" />
        {/* Import */}
      </Button>
      <Button onClick={onDownloadReactCode} variant="outline" size="sm" className="gap-2" title="Download React Code">
        <Code2 className="h-4 w-4" />
        {/* React Code */}
      </Button>
      <input ref={fileInputRef} type="file" accept=".json" onChange={handleFileChange} className="hidden" />
    </div>
  )
}
