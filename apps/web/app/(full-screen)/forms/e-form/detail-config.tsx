'use client'

import { Any } from '@/lib/types'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { ArrowDown, ArrowUp, GripVertical, Plus, Trash2 } from 'lucide-react'
import React, { useState } from 'react'

import { FormElement } from './form-builder'

interface DetailRow {
  id: string
  name: string
  value: string
  type: 'text' | 'datetime' | 'date' | 'number' | 'boolean' | 'object' | 'array'
}

interface DetailConfigProps {
  element: FormElement
  onUpdate: (updates: Partial<FormElement>) => void
}

const DATA_TYPE_OPTIONS = [
  { value: 'text', label: 'Văn bản' },
  { value: 'number', label: 'Số' },
  { value: 'date', label: 'Ng<PERSON><PERSON>' },
  { value: 'datetime', label: 'Ngày giờ' },
  { value: 'boolean', label: 'True/False' },
  { value: 'object', label: 'Object' },
  { value: 'array', label: 'Array' },
]

export function DetailConfig({ element, onUpdate }: DetailConfigProps) {
  const [rows, setRows] = useState<DetailRow[]>(element.metadata?.rows || [])

  const generateId = () => `row_${Math.random().toString(36).substring(2, 7)}`

  const addRow = () => {
    const newRow: DetailRow = {
      id: generateId(),
      name: `Trường ${rows.length + 1}`,
      value: 'Giá trị mẫu',
      type: 'text',
    }

    const newRows = [...rows, newRow]
    setRows(newRows)
    updateDetailMetadata(newRows)
  }

  const removeRow = (rowId: string) => {
    const newRows = rows.filter(row => row.id !== rowId)
    setRows(newRows)
    updateDetailMetadata(newRows)
  }

  const updateRow = (rowId: string, updates: Partial<DetailRow>) => {
    const newRows = rows.map(row => (row.id === rowId ? { ...row, ...updates } : row))
    setRows(newRows)
    updateDetailMetadata(newRows)
  }

  const moveRow = (rowId: string, direction: 'up' | 'down') => {
    const index = rows.findIndex(row => row.id === rowId)

    if (index === -1) return

    const newIndex = direction === 'up' ? index - 1 : index + 1

    if (newIndex < 0 || newIndex >= rows.length) return

    const newRows = [...rows]
    ;[newRows[index], newRows[newIndex] as DetailRow] = [newRows[newIndex] as DetailRow, newRows[index] as DetailRow]

    setRows(newRows)
    updateDetailMetadata(newRows)
  }

  const updateDetailMetadata = (newRows: DetailRow[]) => {
    const metadata = {
      ...element.metadata,
      rows: newRows,
      // Set detail table specific settings
      columns: [
        {
          id: 'col_name',
          label: 'Tên trường',
          key: 'name',
          dataType: 'text',
          sortable: false,
          filterable: false,
          width: '33%',
        },
        {
          id: 'col_value',
          label: 'Thông tin',
          key: 'value',
          dataType: 'text',
          sortable: false,
          filterable: false,
          width: '67%',
        },
      ],
    }

    onUpdate({ metadata })
  }

  const updateDetailSetting = (key: string, value: Any) => {
    const metadata = {
      ...element.metadata,
      [key]: value,
    }

    onUpdate({ metadata })
  }

  return (
    <div className="space-y-6">
      {/* Detail Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Cài đặt hiển thị</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="showHeader"
                checked={element.metadata?.showHeader !== false}
                onCheckedChange={checked => updateDetailSetting('showHeader', checked)}
              />
              <Label htmlFor="showHeader" className="text-sm">
                Hiện header
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="striped"
                checked={element.metadata?.striped !== false}
                onCheckedChange={checked => updateDetailSetting('striped', checked)}
              />
              <Label htmlFor="striped" className="text-sm">
                Dòng xen kẽ
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="bordered"
                checked={element.metadata?.bordered !== false}
                onCheckedChange={checked => updateDetailSetting('bordered', checked)}
              />
              <Label htmlFor="bordered" className="text-sm">
                Viền
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="hover"
                checked={element.metadata?.hover !== false}
                onCheckedChange={checked => updateDetailSetting('hover', checked)}
              />
              <Label htmlFor="hover" className="text-sm">
                Hover
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rows Configuration */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-sm">Cấu hình dữ liệu ({rows.length})</CardTitle>
          <Button onClick={addRow} size="sm" className="h-8">
            <Plus className="mr-1 h-4 w-4" />
            Thêm trường
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {rows.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <p>Chưa có trường dữ liệu nào</p>
              <p className="text-sm">Nhấn &quot;Thêm trường&quot; để bắt đầu</p>
            </div>
          ) : (
            rows.map((row, index) => (
              <Card key={row.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GripVertical className="text-muted-foreground h-4 w-4 cursor-move" />
                      <Badge variant="default">{DATA_TYPE_OPTIONS.find(opt => opt.value === row.type)?.label}</Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="sm" onClick={() => moveRow(row.id, 'up')} disabled={index === 0}>
                        <ArrowUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveRow(row.id, 'down')}
                        disabled={index === rows.length - 1}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => removeRow(row.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs">Tên trường</Label>
                      <Input
                        value={row.name}
                        onChange={e => updateRow(row.id, { name: e.target.value })}
                        placeholder="Nhập tên trường..."
                        className="h-8"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs">Giá trị mẫu</Label>
                      <Input
                        value={row.value}
                        onChange={e => updateRow(row.id, { value: e.target.value })}
                        placeholder="Nhập giá trị mẫu..."
                        className="h-8"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs">Loại dữ liệu</Label>
                      <Select value={row.type} onValueChange={(value: Any) => updateRow(row.id, { type: value })}>
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {DATA_TYPE_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </CardContent>
      </Card>

      {/* Endpoint Info */}
      {element.metadata?.endpoint && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Thông tin API</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-xs">
              <span className="font-medium">URL:</span>
              <code className="bg-muted ml-2 rounded px-1 py-0.5 text-xs">{element.metadata.endpoint.url}</code>
            </div>
            <div className="text-xs">
              <span className="font-medium">Method:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {element.metadata.endpoint.method}
              </Badge>
            </div>
            {element.metadata.endpoint.summary && (
              <div className="text-xs">
                <span className="font-medium">Mô tả:</span>
                <span className="text-muted-foreground ml-2">{element.metadata.endpoint.summary}</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
