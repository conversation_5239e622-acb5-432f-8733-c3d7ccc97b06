'use client'

import { Any } from '@/lib/types'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, Palette } from 'lucide-react'
import { useState } from 'react'

import { type Theme, useFormBuilder } from './form-builder'

export function ThemeManager() {
  const t = useTranslations('e-form')
  const { currentTheme, setCurrentTheme } = useFormBuilder()
  const [isOpen, setIsOpen] = useState(false)

  const updateThemeProperty = (category: keyof Theme, property: string, value: string, nestedProperty?: string) => {
    const newTheme: Any = { ...currentTheme }

    if (nestedProperty) {
      newTheme[category][nestedProperty][property] = value
    } else {
      newTheme[category][property] = value
    }

    setCurrentTheme(newTheme)
  }

  return (
    <Card className="shadow-none">
      <CardHeader className="hover:bg-accent/50 cursor-pointer transition-colors" onClick={() => setIsOpen(!isOpen)}>
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            {t('themeSettings')}
          </div>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </CardTitle>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-6 px-3">
          <div>
            <h3 className="mb-3 text-sm font-medium">{t('colors')}</h3>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(currentTheme.colors).map(([key, value]) => (
                <div key={key}>
                  <label className="text-xs font-medium capitalize">{key}</label>
                  <div className="flex gap-2">
                    <div className="h-6 w-6 rounded border" style={{ backgroundColor: value }} aria-hidden="true"></div>
                    <Input
                      type="text"
                      value={value}
                      onChange={e => updateThemeProperty('colors', key, e.target.value)}
                      className="text-xs"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="mb-3 text-sm font-medium">{t('typography')}</h3>
            <div className="space-y-3">
              {Object.entries(currentTheme.fonts).map(([key, value]) => (
                <div key={key}>
                  <label className="text-xs font-medium capitalize">{key}</label>
                  <Input
                    type="text"
                    value={value}
                    onChange={e => updateThemeProperty('fonts', key, e.target.value)}
                    className="text-xs"
                  />
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="mb-3 text-sm font-medium">{t('spacing')}</h3>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(currentTheme.spacing).map(([key, value]) => (
                <div key={key}>
                  <label className="text-xs font-medium capitalize">{key}</label>
                  <Input
                    type="text"
                    value={value}
                    onChange={e => updateThemeProperty('spacing', key, e.target.value)}
                    className="text-xs"
                  />
                </div>
              ))}
            </div>
          </div>

          <Button size="sm" variant="outline" className="w-full">
            {t('applyTheme')}
          </Button>
        </CardContent>
      )}
    </Card>
  )
}
