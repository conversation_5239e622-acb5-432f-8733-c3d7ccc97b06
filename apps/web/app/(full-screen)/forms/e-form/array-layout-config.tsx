'use client'

import { Badge } from '@workspace/ui/components/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@workspace/ui/components/collapsible'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Slider } from '@workspace/ui/components/slider'
import { Switch } from '@workspace/ui/components/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { ChevronDown, Grid, LayoutTemplate, List, Monitor, Plus, Smartphone, Table, Tablet } from 'lucide-react'
import type React from 'react'
import { useState } from 'react'

import { type ArrayConfig, type Breakpoint, type FormElement, useFormBuilder } from './form-builder'

interface ArrayLayoutConfigProps {
  element: FormElement
  onUpdateElement: (updates: Partial<FormElement>) => void
}

export function ArrayLayoutConfig({ element, onUpdateElement }: ArrayLayoutConfigProps) {
  const { currentBreakpoint } = useFormBuilder()
  const [isOpen, setIsOpen] = useState(true)

  // Initialize array config if it doesn't exist
  const arrayConfig = element.arrayConfig || {
    displayMode: {
      desktop: 'table',
      tablet: 'table',
      mobile: 'cards',
    },
    addView: 'inline',
    minItems: 0,
    maxItems: 10,
    allowAdd: true,
    allowRemove: true,
    allowReorder: true,
    showMoveButtons: true,
    cardColumns: {
      desktop: 2,
      tablet: 2,
      mobile: 1,
    },
    tableHeaders: true,
    addButtonText: 'Add Item',
    removeButtonText: 'Remove',
  }

  const updateArrayConfig = (updates: Partial<ArrayConfig>) => {
    onUpdateElement({
      arrayConfig: {
        ...arrayConfig,
        ...updates,
      },
    })
  }

  const updateDisplayMode = (breakpoint: Breakpoint, mode: 'table' | 'cards') => {
    updateArrayConfig({
      displayMode: {
        ...arrayConfig.displayMode,
        [breakpoint]: mode,
      },
    })
  }

  const updateCardColumns = (breakpoint: Breakpoint, columns: number) => {
    updateArrayConfig({
      cardColumns: {
        ...arrayConfig.cardColumns,
        [breakpoint]: columns,
      },
    })
  }

  const DisplayModeSelector = ({ breakpoint, icon }: { breakpoint: Breakpoint; icon: React.ReactNode }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        {icon}
        <span className="text-sm font-medium capitalize">{breakpoint}</span>
        <Badge variant="secondary" className="text-xs">
          {arrayConfig.displayMode[breakpoint]}
        </Badge>
      </div>

      <div>
        <Label className="text-xs">Display Mode</Label>
        <Select
          value={arrayConfig.displayMode[breakpoint]}
          onValueChange={(value: 'table' | 'cards') => updateDisplayMode(breakpoint, value)}
        >
          <SelectTrigger className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="table">
              <div className="flex items-center gap-2">
                <Table className="h-4 w-4" />
                Table View
              </div>
            </SelectItem>
            <SelectItem value="cards">
              <div className="flex items-center gap-2">
                <Grid className="h-4 w-4" />
                Card View
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {arrayConfig.displayMode[breakpoint] === 'cards' && (
        <div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Columns per Row</Label>
            <span className="text-xs font-medium">{arrayConfig.cardColumns[breakpoint]}</span>
          </div>
          <Slider
            min={1}
            max={4}
            step={1}
            value={[arrayConfig.cardColumns[breakpoint]]}
            onValueChange={value => updateCardColumns(breakpoint, value[0] as number)}
            className="mt-1"
          />
        </div>
      )}
    </div>
  )

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <Card className="shadow-none">
        <CollapsibleTrigger className="w-full text-left">
          <CardHeader className="py-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-sm">
                <List className="h-4 w-4" />
                <span>Array Configuration</span>
                <Badge variant="outline" className="text-xs">
                  {arrayConfig.minItems}-{arrayConfig.maxItems} items
                </Badge>
              </CardTitle>
              <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="space-y-4 px-3 pt-0">
            <Tabs defaultValue="display" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="display">Display</TabsTrigger>
                <TabsTrigger value="limits">Limits</TabsTrigger>
                <TabsTrigger value="behavior">Behavior</TabsTrigger>
              </TabsList>

              <TabsContent value="display" className="space-y-4">
                <div className="space-y-6">
                  <DisplayModeSelector breakpoint="desktop" icon={<Monitor className="h-4 w-4" />} />
                  <DisplayModeSelector breakpoint="tablet" icon={<Tablet className="h-4 w-4" />} />
                  <DisplayModeSelector breakpoint="mobile" icon={<Smartphone className="h-4 w-4" />} />
                </div>

                <div className="border-t pt-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="tableHeaders"
                      checked={arrayConfig.tableHeaders}
                      onCheckedChange={checked => updateArrayConfig({ tableHeaders: checked })}
                    />
                    <Label htmlFor="tableHeaders" className="text-sm">
                      Show table headers
                    </Label>
                  </div>
                  <p className="text-muted-foreground mt-1 text-xs">
                    Display column headers in table view using field labels
                  </p>
                </div>

                <div className="border-t pt-4">
                  <Label className="mb-2 block text-sm">Add Item View</Label>
                  <Select
                    value={arrayConfig.addView}
                    onValueChange={(value: 'inline' | 'modal') => updateArrayConfig({ addView: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="inline">
                        <div className="flex items-center gap-2">
                          <LayoutTemplate className="h-4 w-4" />
                          Inline (Direct in Table/Cards)
                        </div>
                      </SelectItem>
                      <SelectItem value="modal">
                        <div className="flex items-center gap-2">
                          <Plus className="h-4 w-4" />
                          Modal Dialog
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-muted-foreground mt-1 text-xs">
                    {arrayConfig.addView === 'inline'
                      ? 'Add items directly in the table or card view'
                      : 'Open a modal dialog to add new items'}
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="limits" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="minItems" className="text-sm">
                      Minimum Items
                    </Label>
                    <Input
                      id="minItems"
                      type="number"
                      min={0}
                      value={arrayConfig.minItems}
                      onChange={e => updateArrayConfig({ minItems: Number(e.target.value) || 0 })}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxItems" className="text-sm">
                      Maximum Items
                    </Label>
                    <Input
                      id="maxItems"
                      type="number"
                      min={1}
                      value={arrayConfig.maxItems}
                      onChange={e => updateArrayConfig({ maxItems: Number(e.target.value) || 10 })}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div className="text-muted-foreground text-xs">
                  <div className="mb-1 font-medium">Current Configuration:</div>
                  <div>
                    Range: {arrayConfig.minItems} to {arrayConfig.maxItems} items
                  </div>
                  <div>
                    Validation:{' '}
                    {arrayConfig.minItems > 0 ? `At least ${arrayConfig.minItems} required` : 'No minimum required'}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="behavior" className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="allowAdd"
                      checked={arrayConfig.allowAdd}
                      onCheckedChange={checked => updateArrayConfig({ allowAdd: checked })}
                    />
                    <Label htmlFor="allowAdd" className="text-sm">
                      Allow adding items
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="allowRemove"
                      checked={arrayConfig.allowRemove}
                      onCheckedChange={checked => updateArrayConfig({ allowRemove: checked })}
                    />
                    <Label htmlFor="allowRemove" className="text-sm">
                      Allow removing items
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="allowReorder"
                      checked={arrayConfig.allowReorder}
                      onCheckedChange={checked => updateArrayConfig({ allowReorder: checked })}
                    />
                    <Label htmlFor="allowReorder" className="text-sm">
                      Allow reordering items
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showMoveButtons"
                      checked={arrayConfig.showMoveButtons}
                      onCheckedChange={checked => updateArrayConfig({ showMoveButtons: checked })}
                    />
                    <Label htmlFor="showMoveButtons" className="text-sm">
                      Show move up/down buttons
                    </Label>
                  </div>
                </div>

                <div className="space-y-3 border-t pt-4">
                  <div>
                    <Label htmlFor="addButtonText" className="text-sm">
                      Add Button Text
                    </Label>
                    <Input
                      id="addButtonText"
                      value={arrayConfig.addButtonText}
                      onChange={e => updateArrayConfig({ addButtonText: e.target.value })}
                      className="mt-1"
                      placeholder="Add Item"
                    />
                  </div>

                  <div>
                    <Label htmlFor="removeButtonText" className="text-sm">
                      Remove Button Text
                    </Label>
                    <Input
                      id="removeButtonText"
                      value={arrayConfig.removeButtonText}
                      onChange={e => updateArrayConfig({ removeButtonText: e.target.value })}
                      className="mt-1"
                      placeholder="Remove"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Preview Section */}
            <div className="border-t pt-4">
              <Label className="mb-3 block text-sm font-medium">Display Preview ({currentBreakpoint})</Label>
              <div className="bg-muted/30 rounded-md p-3">
                <div className="text-muted-foreground mb-2 text-xs">
                  Mode: {arrayConfig.displayMode[currentBreakpoint]}
                  {arrayConfig.displayMode[currentBreakpoint] === 'cards' &&
                    ` • ${arrayConfig.cardColumns[currentBreakpoint]} columns`}
                </div>
                <div className="space-y-2">
                  {arrayConfig.displayMode[currentBreakpoint] === 'table' ? (
                    <div className="rounded border">
                      {arrayConfig.tableHeaders && (
                        <div className="bg-muted/50 border-b px-2 py-1 text-xs font-medium">
                          {element.children?.map(child => child.label).join(' | ') || 'Field Headers'}
                        </div>
                      )}
                      <div className="px-2 py-1 text-xs">Sample row data...</div>
                    </div>
                  ) : (
                    <div
                      className="grid gap-2"
                      style={{
                        gridTemplateColumns: `repeat(${arrayConfig.cardColumns[currentBreakpoint]}, 1fr)`,
                      }}
                    >
                      <div className="rounded border p-2 text-xs">Sample card...</div>
                      {arrayConfig.cardColumns[currentBreakpoint] > 1 && (
                        <div className="rounded border p-2 text-xs">Sample card...</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  )
}
