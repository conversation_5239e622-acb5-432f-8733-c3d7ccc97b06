'use client'

import { useDraggable } from '@dnd-kit/core'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@workspace/ui/components/collapsible'
import { Input } from '@workspace/ui/components/input'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, Code, FormInput, Grid3X3, MousePointer, Search } from 'lucide-react'
import type React from 'react'
import { useState } from 'react'

import type { ElementType } from './form-builder'
import { iconMap } from './form-builder'

interface ComponentCategory {
  id: string
  name: string
  icon: React.ReactNode
  components: Array<{
    type: ElementType
    label: string
    icon: React.ReactNode
    description: string
    isNew?: boolean
    isPro?: boolean
  }>
}

interface DraggableComponentProps {
  type: ElementType
  label: string
  icon: React.ReactNode
  description: string
  isNew?: boolean
  isPro?: boolean
}

function DraggableComponent({ type, label, icon, description, isNew, isPro }: DraggableComponentProps) {
  const t = useTranslations('e-form')
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `palette-${type}`,
    data: { type },
  })

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`group relative cursor-grab active:cursor-grabbing ${isDragging ? 'opacity-50' : ''}`}
    >
      <Button
        variant="ghost"
        className="hover:bg-accent/50 h-auto w-full justify-start gap-3 p-3 transition-colors"
        title={description}
      >
        <div className="flex flex-1 items-center gap-3">
          <div className="bg-primary/10 text-primary flex h-8 w-8 flex-shrink-0 items-center justify-center rounded">
            {icon}
          </div>
          <div className="flex-1 text-left">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">{label}</span>
              {isNew && (
                <Badge variant="secondary" className="text-xs">
                  {t('new')}
                </Badge>
              )}
              {isPro && (
                <Badge variant="outline" className="text-xs">
                  {t('pro')}
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground mt-1 text-xs">{description}</p>
          </div>
        </div>
      </Button>
    </div>
  )
}

export function ComponentPalette() {
  const t = useTranslations('e-form')
  const [searchQuery, setSearchQuery] = useState('')
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    layout: true,
    html: true,
    form: true,
  })

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }))
  }

  const categories: ComponentCategory[] = [
    {
      id: 'layout',
      name: t('layout'),
      icon: <Grid3X3 className="h-4 w-4" />,
      components: [
        {
          type: 'grid',
          label: t('boxGrid'),
          icon: iconMap.grid,
          description: t('autoLayoutGridContainer'),
        },
        {
          type: 'column',
          label: t('column'),
          icon: iconMap.column,
          description: t('responsiveColumnLayout'),
          isNew: true,
        },
        {
          type: 'container',
          label: t('container'),
          icon: iconMap['container'],
          description: t('flexibleGridContainerWithResponsiveColumns'),
          isNew: true,
        },
      ],
    },
    {
      id: 'html',
      name: t('htmlElements'),
      icon: <Code className="h-4 w-4" />,
      components: [
        { type: 'h1', label: t('heading1'), icon: iconMap['h1'], description: t('mainHeading') },
        { type: 'h2', label: t('heading2'), icon: iconMap['h2'], description: t('sectionHeading') },
        { type: 'h3', label: t('heading3'), icon: iconMap['h3'], description: t('subsectionHeading') },
        { type: 'paragraph', label: t('paragraph'), icon: iconMap['paragraph'], description: t('paragraphContent') },
        { type: 'span', label: t('span'), icon: iconMap['span'], description: t('spanContent') },
        { type: 'divider', label: t('divider'), icon: iconMap['divider'], description: t('visualSeparator') },
        { type: 'image', label: t('image'), icon: iconMap['image'], description: t('imageDisplay') },
        { type: 'table', label: t('table'), icon: iconMap['table'], description: t('tableDisplay') },
      ],
    },
    {
      id: 'form',
      name: t('formElements'),
      icon: <FormInput className="h-4 w-4" />,
      components: [
        {
          type: 'input',
          label: t('inputField'),
          icon: iconMap['input'],
          description: t('textInputWithValidation'),
        },
        {
          type: 'select',
          label: t('select'),
          icon: iconMap['select'],
          description: t('dropdownSelection'),
        },
        {
          type: 'select-api',
          label: t('apiSelect'),
          icon: iconMap['select-api'],
          description: t('dynamicOptionsFromApi'),
          isNew: true,
        },
        {
          type: 'textarea',
          label: t('textarea'),
          icon: iconMap['textarea'],
          description: t('multiLineTextInput'),
        },
        {
          type: 'checkbox',
          label: t('checkbox'),
          icon: iconMap['checkbox'],
          description: t('booleanSelection'),
        },
        {
          type: 'radio',
          label: t('radioGroup'),
          icon: iconMap['radio'],
          description: t('singleChoiceSelection'),
        },
        {
          type: 'date-picker',
          label: t('datePicker'),
          icon: iconMap['date-picker'],
          description: t('dateSelection'),
          isNew: true,
        },
        {
          type: 'file-upload',
          label: t('fileUpload'),
          icon: iconMap['file-upload'],
          description: t('fileSelection'),
          isNew: true,
        },
        {
          type: 'rating',
          label: t('rating'),
          icon: iconMap['rating'],
          description: t('starRatingInput'),
          isPro: true,
        },
        {
          type: 'slider',
          label: t('slider'),
          icon: iconMap['slider'],
          description: t('rangeInput'),
          isPro: true,
        },
        {
          type: 'array',
          label: t('array'),
          icon: iconMap['array'],
          description: t('arrayInput'),
          isNew: true,
        },
        {
          type: 'button',
          label: t('button'),
          icon: <MousePointer className="h-4 w-4" />,
          description: t('actionButton'),
        },
      ],
    },
  ]

  // Filter components based on search query
  const filteredCategories = categories
    .map(category => ({
      ...category,
      components: category.components.filter(
        component =>
          component.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          component.description.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    }))
    .filter(category => category.components.length > 0)

  return (
    <div className="flex h-full flex-col">
      {/* Search Box */}
      <div className="px-4 py-3">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
          <Input
            placeholder={t('searchComponents')}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-auto">
        <div className="space-y-4 px-3 pb-4">
          {/* Show message if no results */}
          {searchQuery && filteredCategories.length === 0 && (
            <div className="text-muted-foreground py-8 text-center">
              <Search className="mx-auto mb-2 h-8 w-8 opacity-50" />
              <p className="text-sm">{t('noComponentsFound')}</p>
              <p className="text-xs">{t('tryDifferentSearchTerm')}</p>
            </div>
          )}

          {/* Component Categories */}
          {filteredCategories.map(category => (
            <Card key={category.id} className="overflow-hidden">
              <Collapsible open={openSections[category.id]} onOpenChange={() => toggleSection(category.id)}>
                <CollapsibleTrigger className="w-full text-left">
                  <CardHeader className="hover:bg-accent/50 cursor-pointer px-2 py-3 transition-colors">
                    <CardTitle className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        {category.icon}
                        <span>{category.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {category.components.length}
                        </Badge>
                      </div>
                      <ChevronDown
                        className={`h-4 w-4 transition-transform ${openSections[category.id] ? 'rotate-180' : ''}`}
                      />
                    </CardTitle>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="space-y-1 px-2 pt-0">
                    {category.components.map(component => (
                      <DraggableComponent
                        key={component.type}
                        type={component.type}
                        label={component.label}
                        icon={component.icon}
                        description={component.description}
                        isNew={component.isNew}
                        isPro={component.isPro}
                      />
                    ))}
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
