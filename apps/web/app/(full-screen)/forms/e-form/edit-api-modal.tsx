'use client'

import { type ApiSchema, apiService, type ApiType } from '@/lib/json-form/json-form-api-service'
import { Any } from '@/lib/types'
import { Alert, AlertDescription } from '@workspace/ui/components/alert'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { Textarea } from '@workspace/ui/components/textarea'
import { AlertCircle, Clipboard, Download, Edit3, Save, Upload } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

interface EditApiModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  apiToEdit: ApiSchema | null
  onApiUpdated?: (api: ApiSchema) => void
}

export function EditApiModal({ open, onOpenChange, apiToEdit, onApiUpdated }: EditApiModalProps) {
  const [apiName, setApiName] = useState('')
  const [apiDescription, setApiDescription] = useState('')
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [apiType, setApiType] = useState<ApiType>('swagger')
  const [apiUrl, setApiUrl] = useState('')
  const [jsonContent, setJsonContent] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingFromUrl, setIsLoadingFromUrl] = useState(false)
  const [activeTab, setActiveTab] = useState('paste')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize form when modal opens
  useEffect(() => {
    if (open && apiToEdit) {
      setApiName(apiToEdit.name)
      setApiDescription(apiToEdit.description || '')
      setApiType(apiToEdit.type)
      setApiUrl(apiToEdit.url || '')
      setJsonContent(
        typeof apiToEdit.schema === 'string' ? apiToEdit.schema : JSON.stringify(apiToEdit.schema, null, 2)
      )
      setError('')
    }
  }, [open, apiToEdit])

  const resetForm = () => {
    setApiName('')
    setApiDescription('')
    setApiType('swagger')
    setApiUrl('')
    setJsonContent('')
    setError('')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]

    if (!file) return

    if (!file.type.includes('json') && !file.name.endsWith('.json')) {
      setError('Vui lòng chọn file JSON')

      return
    }

    const reader = new FileReader()

    reader.onload = e => {
      try {
        const content = e.target?.result as string
        const parsed = JSON.parse(content)
        setJsonContent(JSON.stringify(parsed, null, 2))
        setError('')

        // Auto-detect API type
        const detectedType = detectApiType(parsed)

        if (detectedType && detectedType !== apiToEdit?.type) {
          setError(
            `⚠️ Cảnh báo: File này có vẻ là ${detectedType === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'} nhưng API hiện tại là ${
              apiToEdit?.type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'
            }. Bạn có chắc muốn tiếp tục?`
          )
        }

        // Auto-fill name and description from schema info
        if (detectedType === 'swagger' && parsed.info?.title && !apiName) {
          setApiName(parsed.info.title)
        }

        if (detectedType === 'swagger' && parsed.info?.description) {
          // Always update description from schema unless user has manually changed it
          if (!apiDescription || apiDescription === (apiToEdit?.description || '')) {
            setApiDescription(parsed.info.description)
          }
        }
      } catch (err) {
        setError('File JSON không hợp lệ')
        console.error('Error parsing JSON file:', err)
      }
    }
    reader.readAsText(file)
  }

  const handleFetchFromUrl = async () => {
    if (!apiUrl.trim()) {
      setError('Vui lòng nhập URL API')

      return
    }

    try {
      setIsLoadingFromUrl(true)
      setError('')

      // Validate URL format
      let urlToFetch = apiUrl.trim()

      if (!/^https?:\/\//i.test(urlToFetch)) {
        urlToFetch = 'https://' + urlToFetch
      }

      const { schema, type } = await apiService.fetchApiSchemaFromUrl(urlToFetch)

      // Warn if type mismatch
      if (type !== apiToEdit?.type) {
        setError(
          `⚠️ Cảnh báo: URL này trả về ${type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'} nhưng API hiện tại là ${
            apiToEdit?.type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'
          }. Bạn có chắc muốn tiếp tục?`
        )
      }

      setJsonContent(typeof schema === 'string' ? schema : JSON.stringify(schema, null, 2))

      // Auto-fill name and description from schema info
      if (type === 'swagger' && typeof schema === 'object') {
        if (schema.info?.title && !apiName) {
          setApiName(schema.info.title)
        }

        if (schema.info?.description) {
          // Always update description from schema unless user has manually changed it
          if (!apiDescription || apiDescription === (apiToEdit?.description || '')) {
            setApiDescription(schema.info.description)
          }
        }
      }

      // Update URL to the validated one
      setApiUrl(urlToFetch)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải API từ URL')
    } finally {
      setIsLoadingFromUrl(false)
    }
  }

  const detectApiType = (content: Any): ApiType | null => {
    if (typeof content === 'object') {
      // Check for Swagger/OpenAPI
      if (content.openapi || content.swagger || content.paths) {
        return 'swagger'
      }

      // Check for GraphQL
      if (content.data?.__schema || content.__schema) {
        return 'graphql'
      }
    } else if (typeof content === 'string') {
      // GraphQL SDL
      if (
        content.includes('type Query') ||
        content.includes('type Mutation') ||
        content.includes('type Subscription')
      ) {
        return 'graphql'
      }
    }

    return null
  }

  const validateApiSchema = (content: string, type: ApiType) => {
    if (type === 'swagger') {
      try {
        const parsed = JSON.parse(content)

        // Kiểm tra cấu trúc cơ bản của Swagger/OpenAPI
        if (!parsed.openapi && !parsed.swagger) {
          throw new Error('Không phải là file Swagger/OpenAPI hợp lệ')
        }

        if (!parsed.paths) {
          throw new Error(`File Swagger phải có phần 'paths'`)
        }

        return parsed
      } catch (err) {
        throw new Error('Swagger/OpenAPI phải là JSON hợp lệ', { cause: err })
      }
    } else if (type === 'graphql') {
      // Kiểm tra xem có phải là JSON không
      const trimmedContent = content.trim()

      if (trimmedContent.startsWith('{')) {
        // Có thể là JSON - thử parse
        try {
          const parsed = JSON.parse(content)

          if (parsed.data?.__schema) {
            // Introspection query result
            if (!parsed.data.__schema.types) {
              throw new Error('GraphQL Introspection result không hợp lệ')
            }

            return parsed
          } else if (parsed.__schema) {
            // Direct schema object
            if (!parsed.__schema.types) {
              throw new Error('GraphQL Schema object không hợp lệ')
            }

            return parsed
          } else {
            throw new Error('GraphQL JSON phải có cấu trúc Introspection hợp lệ')
          }
        } catch (err) {
          throw new Error('GraphQL JSON không hợp lệ', { cause: err })
        }
      } else {
        // SDL format - đây là string raw, không phải JSON
        if (
          !content.includes('type Query') &&
          !content.includes('type Mutation') &&
          !content.includes('type Subscription')
        ) {
          throw new Error('GraphQL SDL phải có ít nhất một type Query, Mutation hoặc Subscription')
        }

        return content // Return raw SDL string
      }
    }

    throw new Error('Loại API không được hỗ trợ')
  }

  const handleSave = async () => {
    if (!apiToEdit) return

    if (!apiName.trim()) {
      setError('Vui lòng nhập tên API')

      return
    }

    if (!jsonContent.trim()) {
      setError('Vui lòng nhập nội dung API schema')

      return
    }

    try {
      setIsLoading(true)
      setError('')

      // Validate content with current API type (not detected type)
      const schema = validateApiSchema(jsonContent, apiToEdit.type)
      const endpoints = apiService.parseApiToEndpoints(schema, apiToEdit.type)

      // Auto-extract description from schema if user hasn't manually set it
      let finalDescription = apiDescription.trim() || undefined

      // If description is empty or same as original, try to extract from schema
      if (!finalDescription || finalDescription === (apiToEdit.description || '')) {
        if (apiToEdit.type === 'swagger' && typeof schema === 'object' && schema.info?.description) {
          finalDescription = schema.info.description
        }
      }

      const updatedApi: ApiSchema = {
        ...apiToEdit,
        name: apiName.trim(),
        description: finalDescription,
        schema,
        endpoints,
        updatedAt: new Date(),
        url: apiUrl.trim() || apiToEdit.url, // Keep existing URL if not changed
      }

      await apiService.updateApiSchema(updatedApi)

      onApiUpdated?.(updatedApi)
      onOpenChange(false)
      resetForm()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi cập nhật API')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const files = Array.from(e.dataTransfer.files)
    const jsonFile = files.find(file => file.type.includes('json') || file.name.endsWith('.json'))

    if (jsonFile) {
      const reader = new FileReader()

      reader.onload = event => {
        try {
          const content = event.target?.result as string
          const parsed = JSON.parse(content)
          setJsonContent(JSON.stringify(parsed, null, 2))
          setError('')

          // Auto-detect API type
          const detectedType = detectApiType(parsed)

          if (detectedType && detectedType !== apiToEdit?.type) {
            setError(
              `⚠️ Cảnh báo: File này có vẻ là ${detectedType === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'} nhưng API hiện tại là ${
                apiToEdit?.type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL'
              }. Bạn có chắc muốn tiếp tục?`
            )
          }

          // Auto-fill name and description from schema info
          if (detectedType === 'swagger' && parsed.info?.title && !apiName) {
            setApiName(parsed.info.title)
          }

          if (detectedType === 'swagger' && parsed.info?.description) {
            // Always update description from schema unless user has manually changed it
            if (!apiDescription || apiDescription === (apiToEdit?.description || '')) {
              setApiDescription(parsed.info.description)
            }
          }
        } catch (err) {
          console.error('Error parsing JSON file:', err)
          setError('File JSON không hợp lệ')
        }
      }
      reader.readAsText(jsonFile)
    } else {
      setError('Vui lòng thả file JSON')
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] max-w-[95vw] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Chỉnh sửa API: {apiToEdit?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="api-name">Tên API *</Label>
              <Input
                id="api-name"
                value={apiName}
                onChange={e => setApiName(e.target.value)}
                placeholder="Nhập tên API..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-type">Loại API (không thể thay đổi)</Label>
              <Select value={apiToEdit?.type} disabled>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="swagger">Swagger/OpenAPI</SelectItem>
                  <SelectItem value="graphql">GraphQL</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-description">Mô tả</Label>
              <Input
                id="api-description"
                value={apiDescription}
                onChange={e => setApiDescription(e.target.value)}
                placeholder="Nhập mô tả API..."
              />
            </div>
          </div>

          {/* URL Field */}
          <div className="space-y-2">
            <Label htmlFor="api-url">URL (tùy chọn)</Label>
            <div className="flex gap-2">
              <Input
                id="api-url"
                value={apiUrl}
                onChange={e => setApiUrl(e.target.value)}
                placeholder="https://api.example.com/swagger.json"
                onKeyDown={e => e.key === 'Enter' && !isLoadingFromUrl && handleFetchFromUrl()}
              />
              <Button
                onClick={handleFetchFromUrl}
                disabled={isLoadingFromUrl || !apiUrl.trim()}
                variant="outline"
                size="sm"
              >
                <Download className="h-4 w-4" />
                {isLoadingFromUrl ? 'Đang tải...' : 'Tải từ URL'}
              </Button>
            </div>
          </div>

          {/* Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="paste" className="flex items-center gap-2">
                <Clipboard className="h-4 w-4" />
                Paste Content
              </TabsTrigger>
              <TabsTrigger value="file" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Upload File
              </TabsTrigger>
            </TabsList>

            <TabsContent value="paste" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    Paste {apiToEdit?.type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL Schema'} Content
                  </CardTitle>
                  <CardDescription>
                    Dán nội dung{' '}
                    {apiToEdit?.type === 'swagger'
                      ? 'Swagger/OpenAPI JSON'
                      : 'GraphQL Schema (SDL hoặc Introspection JSON)'}{' '}
                    vào đây
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={jsonContent}
                    onChange={e => {
                      setJsonContent(e.target.value)

                      // Clear type mismatch errors when user manually edits
                      if (error.includes('⚠️ Cảnh báo')) {
                        setError('')
                      }
                    }}
                    placeholder={
                      apiToEdit?.type === 'swagger'
                        ? 'Dán Swagger/OpenAPI JSON vào đây...'
                        : 'Dán GraphQL Schema vào đây...'
                    }
                    className="font-mono text-xs"
                    rows={12}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="file" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    Upload {apiToEdit?.type === 'swagger' ? 'Swagger/OpenAPI' : 'GraphQL Schema'} File
                  </CardTitle>
                  <CardDescription>
                    Chọn file JSON chứa{' '}
                    {apiToEdit?.type === 'swagger'
                      ? 'Swagger hoặc OpenAPI schema'
                      : 'GraphQL Schema (SDL hoặc Introspection result)'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    className="border-muted-foreground/25 hover:border-muted-foreground/50 cursor-pointer space-y-4 rounded-lg border-2 border-dashed p-8 text-center transition-colors"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="text-muted-foreground mx-auto h-12 w-12" />
                    <div>
                      <p className="text-sm font-medium">Thả file JSON vào đây hoặc click để chọn</p>
                      <p className="text-muted-foreground mt-1 text-xs">
                        {apiToEdit?.type === 'swagger'
                          ? 'Hỗ trợ Swagger 2.0 và OpenAPI 3.0+'
                          : 'Hỗ trợ GraphQL Schema (SDL hoặc Introspection)'}
                      </p>
                    </div>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json,application/json"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Preview */}
          {jsonContent && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted rounded-md p-3">
                  <pre className="max-h-32 overflow-x-auto text-xs">
                    {jsonContent.slice(0, 500)}
                    {jsonContent.length > 500 && '...'}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant={error.includes('⚠️') ? 'default' : 'destructive'}>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 border-t pt-4">
            <Button
              variant="outline"
              onClick={() => {
                onOpenChange(false)
                resetForm()
              }}
            >
              Hủy
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !apiName.trim() || !jsonContent.trim()}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isLoading ? 'Đang lưu...' : 'Cập nhật'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
