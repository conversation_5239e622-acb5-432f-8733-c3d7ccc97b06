'use client'

import { <PERSON><PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { ArrowRight, Database, Table } from 'lucide-react'
import { useState } from 'react'

import { type CodeGenerationOptions } from './code-generation-modal'

interface TableCodeOptionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (options: CodeGenerationOptions) => void
}

export function TableCodeOptionsModal({ open, onOpenChange, onConfirm }: TableCodeOptionsModalProps) {
  const [moduleName, setModuleName] = useState('user-management')
  const [componentName, setComponentName] = useState('UserTable')
  const [framework, setFramework] = useState<'shadcn' | 'mui' | 'antd'>('shadcn')
  const [pagination, setPagination] = useState(true)
  const [sorting, setSorting] = useState(true)
  const [deleteAction, setDeleteAction] = useState(true)
  const [editAction, setEditAction] = useState(true)
  const [viewAction, setViewAction] = useState(true)
  const [createAction, setCreateAction] = useState(false)
  const [exportAction, setExportAction] = useState(false)
  const [importAction, setImportAction] = useState(false)
  const [printAction, setPrintAction] = useState(false)
  const [copyAction, setCopyAction] = useState(false)
  const [shareAction, setShareAction] = useState(false)
  const [filtering, setFiltering] = useState(true)
  const [modelName, setModelName] = useState('user')
  const [prefixPageFolder, setPrefixPageFolder] = useState('(admin)')

  const handleConfirm = () => {
    if (!moduleName.trim() || !componentName.trim()) {
      alert('Vui lòng nhập đầy đủ tên module và component')

      return
    }

    const options: CodeGenerationOptions = {
      moduleName: moduleName.trim(),
      modelName: modelName.trim(),
      componentName: componentName.trim(),
      prefixPageFolder: prefixPageFolder.trim(),
      framework,
      pagination,
      sorting,
      filtering,
      // Additional action options
      deleteAction,
      editAction,
      viewAction,
      createAction,
      exportAction,
      importAction,
      printAction,
      copyAction,
      shareAction,
    }

    onConfirm(options)
    onOpenChange(false)
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="flex max-h-[90vh] max-w-2xl flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Table className="h-5 w-5" />
            Cấu hình Table Component
          </DialogTitle>
        </DialogHeader>
        <div className="h-[80vh] flex-1 overflow-y-auto">
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Database className="h-4 w-4" />
                  Thông tin cơ bản
                </CardTitle>
                <CardDescription>Cấu hình tên module và component sẽ được tạo</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="moduleName">Tên module</Label>
                    <Input
                      id="moduleName"
                      value={moduleName}
                      onChange={e => setModuleName(e.target.value)}
                      placeholder="vd: user-management"
                    />
                    <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="componentName">Tên component</Label>
                    <Input
                      id="componentName"
                      value={componentName}
                      onChange={e => setComponentName(e.target.value)}
                      placeholder="vd: UserTable"
                    />
                    <p className="text-muted-foreground text-xs">Sử dụng PascalCase</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="modelName">Tên model</Label>
                    <Input
                      id="modelName"
                      value={modelName}
                      onChange={e => setModelName(e.target.value)}
                      placeholder="vd: user"
                    />
                    <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="prefixPageFolder">Tên thư mục (prefix)</Label>
                    <Input
                      id="prefixPageFolder"
                      value={prefixPageFolder}
                      onChange={e => setPrefixPageFolder(e.target.value)}
                      placeholder="vd: (admin)"
                    />
                    <p className="text-muted-foreground text-xs">Sử dụng kebab-case (dấu gạch ngang)</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="framework">UI Framework</Label>
                  <Select value={framework} onValueChange={(value: 'shadcn' | 'mui' | 'antd') => setFramework(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn UI framework" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shadcn">ShadCN UI (Khuyến nghị)</SelectItem>
                      <SelectItem value="mui">Material UI</SelectItem>
                      <SelectItem value="antd">Ant Design</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Table Features */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tính năng bảng</CardTitle>
                <CardDescription>Chọn các tính năng bạn muốn bao gồm trong bảng</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="pagination"
                      checked={pagination}
                      onCheckedChange={checked => setPagination(!!checked)}
                    />
                    <div className="space-y-1">
                      <Label htmlFor="pagination" className="text-sm font-medium">
                        Phân trang (Pagination)
                      </Label>
                      <p className="text-muted-foreground text-xs">
                        Tự động chia dữ liệu thành các trang với điều hướng
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox id="sorting" checked={sorting} onCheckedChange={checked => setSorting(!!checked)} />
                    <div className="space-y-1">
                      <Label htmlFor="sorting" className="text-sm font-medium">
                        Sắp xếp (Sorting)
                      </Label>
                      <p className="text-muted-foreground text-xs">Cho phép sắp xếp dữ liệu theo các cột</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox id="filtering" checked={filtering} onCheckedChange={checked => setFiltering(!!checked)} />
                    <div className="space-y-1">
                      <Label htmlFor="filtering" className="text-sm font-medium">
                        Lọc dữ liệu (Filtering)
                      </Label>
                      <p className="text-muted-foreground text-xs">Thêm các bộ lọc để tìm kiếm và lọc dữ liệu</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="deleteAction"
                        checked={deleteAction}
                        onCheckedChange={checked => setDeleteAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="deleteAction" className="text-sm font-medium">
                          Xóa dữ liệu (Delete Action)
                        </Label>
                        <p className="text-muted-foreground text-xs">Thêm chức năng xóa dữ liệu</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="editAction"
                        checked={editAction}
                        onCheckedChange={checked => setEditAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="editAction" className="text-sm font-medium">
                          Sửa dữ liệu (Edit Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="viewAction"
                        checked={viewAction}
                        onCheckedChange={checked => setViewAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="viewAction" className="text-sm font-medium">
                          Xem dữ liệu (View Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="createAction"
                        checked={createAction}
                        onCheckedChange={checked => setCreateAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="createAction" className="text-sm font-medium">
                          Tạo dữ liệu (Create Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="exportAction"
                        checked={exportAction}
                        onCheckedChange={checked => setExportAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="exportAction" className="text-sm font-medium">
                          Xuất dữ liệu (Export Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="importAction"
                        checked={importAction}
                        onCheckedChange={checked => setImportAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="importAction" className="text-sm font-medium">
                          Nhập dữ liệu (Import Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="printAction"
                        checked={printAction}
                        onCheckedChange={checked => setPrintAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="printAction" className="text-sm font-medium">
                          In dữ liệu (Print Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="copyAction"
                        checked={copyAction}
                        onCheckedChange={checked => setCopyAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="copyAction" className="text-sm font-medium">
                          Sao chép dữ liệu (Copy Action)
                        </Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="shareAction"
                        checked={shareAction}
                        onCheckedChange={checked => setShareAction(!!checked)}
                      />
                      <div className="space-y-1">
                        <Label htmlFor="shareAction" className="text-sm font-medium">
                          Chia sẻ dữ liệu (Share Action)
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card className="bg-muted/20">
              <CardHeader>
                <CardTitle className="text-base">Xem trước</CardTitle>
                <CardDescription>Component sẽ được tạo với các tính năng sau:</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                    <span>
                      Hiển thị dữ liệu dạng bảng với{' '}
                      {framework === 'shadcn' ? 'ShadCN Table' : framework === 'mui' ? 'Material Table' : 'Ant Table'}
                    </span>
                  </div>
                  {pagination && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                      <span>Phân trang tự động</span>
                    </div>
                  )}
                  {sorting && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-purple-500" />
                      <span>Sắp xếp theo cột</span>
                    </div>
                  )}
                  {filtering && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-orange-500" />
                      <span>Tìm kiếm và lọc dữ liệu</span>
                    </div>
                  )}
                  {deleteAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-red-500" />
                      <span>Xóa dữ liệu</span>
                    </div>
                  )}
                  {editAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-yellow-500" />
                      <span>Sửa dữ liệu</span>
                    </div>
                  )}
                  {viewAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-green-500" />
                      <span>Xem dữ liệu</span>
                    </div>
                  )}
                  {createAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                      <span>Tạo dữ liệu</span>
                    </div>
                  )}
                  {exportAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-purple-500" />
                      <span>Xuất dữ liệu</span>
                    </div>
                  )}
                  {importAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-orange-500" />
                      <span>Nhập dữ liệu</span>
                    </div>
                  )}
                  {printAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-red-500" />
                      <span>In dữ liệu</span>
                    </div>
                  )}
                  {copyAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-green-500" />
                      <span>Sao chép dữ liệu</span>
                    </div>
                  )}
                  {shareAction && (
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                      <span>Chia sẻ dữ liệu</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
          </div>
        </div>
        <DialogFooter>
          <div className="flex w-full justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button onClick={handleConfirm} className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              Tạo Table Code
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
