'use client'

import { type ApiEndpoint, type ApiSchema, apiService, type ApiType } from '@/lib/json-form/json-form-api-service'
import { Alert, AlertDescription } from '@workspace/ui/components/alert'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { Textarea } from '@workspace/ui/components/textarea'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { AlertCircle, Clipboard, Download, FileText, Link, Plus, Save, Upload } from 'lucide-react'
import { useRef, useState } from 'react'

import { ApiManager } from './api-manager'

interface ApiImportModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onApiImported?: (api: ApiSchema) => void
  onSelectEndpoint?: (endpoint: ApiEndpoint, schema: ApiSchema) => void
}

export function ApiImportModal({ open, onOpenChange, onApiImported, onSelectEndpoint }: ApiImportModalProps) {
  const t = useTranslations('e-form')
  const [apiName, setApiName] = useState('')
  const [apiDescription, setApiDescription] = useState('')
  const [apiType, setApiType] = useState<ApiType>('swagger')
  const [jsonContent, setJsonContent] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('manage')
  const [apiUrl, setApiUrl] = useState('')
  const [isLoadingFromUrl, setIsLoadingFromUrl] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const resetForm = () => {
    setApiName('')
    setApiDescription('')
    setApiType('swagger')
    setJsonContent('')
    setApiUrl('')
    setError('')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]

    if (!file) return

    if (!file.type.includes('json') && !file.name.endsWith('.json')) {
      setError(t('pleaseSelectJsonFile'))

      return
    }

    const reader = new FileReader()

    reader.onload = e => {
      try {
        const content = e.target?.result as string
        const parsed = JSON.parse(content)
        setJsonContent(JSON.stringify(parsed, null, 2))
        setError('')

        // Auto-fill name from schema info
        if (apiType === 'swagger' && parsed.info?.title) {
          setApiName(parsed.info.title)
        }

        if (apiType === 'swagger' && parsed.info?.description) {
          setApiDescription(parsed.info.description)
        }

        if (apiType === 'graphql' && parsed.data?.__schema?.description) {
          setApiDescription(parsed.data.__schema.description)
        }
      } catch {
        setError(t('invalidJsonFile'))
      }
    }
    reader.readAsText(file)
  }

  const handleFetchFromUrl = async () => {
    if (!apiUrl.trim()) {
      setError(t('pleaseEnterApiUrl'))

      return
    }

    try {
      setIsLoadingFromUrl(true)
      setError('')

      // Validate URL format
      let urlToFetch = apiUrl.trim()

      if (!/^https?:\/\//i.test(urlToFetch)) {
        urlToFetch = 'https://' + urlToFetch
      }

      const { schema, type } = await apiService.fetchApiSchemaFromUrl(urlToFetch)

      // Auto-fill form
      setApiType(type)
      setJsonContent(typeof schema === 'string' ? schema : JSON.stringify(schema, null, 2))

      // Auto-fill name from schema info
      if (type === 'swagger' && typeof schema === 'object') {
        if (schema.info?.title) {
          setApiName(schema.info.title)
        }

        if (schema.info?.description) {
          setApiDescription(schema.info.description)
        }
      }

      // Update URL to the validated one
      setApiUrl(urlToFetch)
    } catch (err) {
      setError(err instanceof Error ? err.message : t('errorLoadingApiFromUrl'))
    } finally {
      setIsLoadingFromUrl(false)
    }
  }

  const validateApiSchema = (content: string, type: ApiType) => {
    if (type === 'swagger') {
      try {
        const parsed = JSON.parse(content)

        // Kiểm tra cấu trúc cơ bản của Swagger/OpenAPI
        if (!parsed.openapi && !parsed.swagger) {
          throw new Error(t('notValidSwaggerOpenApi'))
        }

        if (!parsed.paths) {
          throw new Error(t('swaggerMustHavePaths'))
        }

        return parsed
      } catch (err) {
        throw new Error(t('swaggerMustBeValidJson'), { cause: err })
      }
    } else if (type === 'graphql') {
      // Kiểm tra xem có phải là JSON không
      const trimmedContent = content.trim()

      if (trimmedContent.startsWith('{')) {
        // Có thể là JSON - thử parse
        try {
          const parsed = JSON.parse(content)

          if (parsed.data?.__schema) {
            // Introspection query result
            if (!parsed.data.__schema.types) {
              throw new Error(t('invalidGraphqlIntrospection'))
            }

            return parsed
          } else if (parsed.__schema) {
            // Direct schema object
            if (!parsed.__schema.types) {
              throw new Error(t('invalidGraphqlSchema'))
            }

            return parsed
          } else {
            throw new Error(t('graphqlJsonMustHaveValidStructure'))
          }
        } catch (err) {
          throw new Error(t('invalidGraphqlJson'), { cause: err })
        }
      } else {
        // SDL format - đây là string raw, không phải JSON
        if (
          !content.includes('type Query') &&
          !content.includes('type Mutation') &&
          !content.includes('type Subscription')
        ) {
          throw new Error(t('graphqlSdlMustHaveType'))
        }

        return content // Return raw SDL string
      }
    }

    throw new Error(t('apiTypeNotSupported'))
  }

  const handleSave = async () => {
    if (!apiName.trim()) {
      setError(t('pleaseEnterApiName'))

      return
    }

    if (!jsonContent.trim()) {
      setError(t('pleaseEnterOrUploadSwaggerJson'))

      return
    }

    try {
      setIsLoading(true)
      setError('')

      const schema = validateApiSchema(jsonContent, apiType)
      const endpoints = apiService.parseApiToEndpoints(schema, apiType)

      const apiSchema: ApiSchema = {
        id: `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: apiName.trim(),
        description: apiDescription.trim() || undefined,
        type: apiType,
        schema,
        endpoints,
        createdAt: new Date(),
        status: 1,
        url: apiUrl.trim() || undefined,
      }

      await apiService.saveApiSchema(apiSchema)

      onApiImported?.(apiSchema)
      setActiveTab('manage') // Switch to manage tab after importing
      resetForm()
    } catch (err) {
      setError(err instanceof Error ? err.message : t('errorSavingApi'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const files = Array.from(e.dataTransfer.files)
    const jsonFile = files.find(file => file.type.includes('json') || file.name.endsWith('.json'))

    if (jsonFile) {
      const reader = new FileReader()

      reader.onload = event => {
        try {
          const content = event.target?.result as string
          const parsed = JSON.parse(content)
          setJsonContent(JSON.stringify(parsed, null, 2))
          setError('')

          // Auto-fill name from schema info
          if (apiType === 'swagger' && parsed.info?.title) {
            setApiName(parsed.info.title)
          }

          if (apiType === 'swagger' && parsed.info?.description) {
            setApiDescription(parsed.info.description)
          }

          if (apiType === 'graphql' && parsed.data?.__schema?.description) {
            setApiDescription(parsed.data.__schema.description)
          }
        } catch (err) {
          console.error('Error parsing JSON file:', err)
          setError(t('invalidJsonFile'))
        }
      }
      reader.readAsText(jsonFile)
    } else {
      setError(t('pleaseDropJsonFile'))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] min-w-[95vw] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('importApiSchema')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="api-name">{t('apiName')} *</Label>
              <Input
                id="api-name"
                value={apiName}
                onChange={e => setApiName(e.target.value)}
                placeholder={t('enterApiName')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-type">{t('apiType')} *</Label>
              <Select value={apiType} onValueChange={(value: ApiType) => setApiType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder={t('selectApiType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="swagger">{t('swaggerOpenApi')}</SelectItem>
                  <SelectItem value="graphql">{t('graphql')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-description">{t('description')}</Label>
              <Input
                id="api-description"
                value={apiDescription}
                onChange={e => setApiDescription(e.target.value)}
                placeholder={t('enterApiDescription')}
              />
            </div>
          </div>

          {/* Main Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="manage" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                {t('manageApi')}
              </TabsTrigger>
              <TabsTrigger value="url" className="flex items-center gap-2">
                <Link className="h-4 w-4" />
                {t('importFromUrl')}
              </TabsTrigger>
              <TabsTrigger value="file" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                {t('uploadFile')}
              </TabsTrigger>
              <TabsTrigger value="paste" className="flex items-center gap-2">
                <Clipboard className="h-4 w-4" />
                {t('pasteJson')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="manage" className="space-y-4">
              <ApiManager onSelectEndpoint={onSelectEndpoint} />
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">{t('importFromUrl')}</CardTitle>
                  <CardDescription>{t('enterPathToSwaggerOrGraphql')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={apiUrl}
                      onChange={e => setApiUrl(e.target.value)}
                      placeholder="https://api.example.com/swagger.json hoặc https://api.example.com/graphql"
                      onKeyDown={e => e.key === 'Enter' && !isLoadingFromUrl && handleFetchFromUrl()}
                    />
                    <Button
                      onClick={handleFetchFromUrl}
                      disabled={isLoadingFromUrl || !apiUrl.trim()}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      {isLoadingFromUrl ? t('loading') : t('download')}
                    </Button>
                  </div>

                  <div className="text-muted-foreground space-y-1 text-xs">
                    <p>• Swagger/OpenAPI: https://petstore.swagger.io/v2/swagger.json</p>
                    <p>• GraphQL SDL: https://api.github.com/graphql/schema</p>
                    <p>• GraphQL Introspection: https://api.example.com/graphql?query=introspection</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="file" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    {apiType === 'swagger' ? t('uploadSwaggerFile') : t('uploadGraphqlFile')}
                  </CardTitle>
                  <CardDescription>
                    {apiType === 'swagger' ? t('selectSwaggerJsonFile') : t('selectGraphqlJsonFile')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    className="border-muted-foreground/25 hover:border-muted-foreground/50 cursor-pointer space-y-4 rounded-lg border-2 border-dashed p-8 text-center transition-colors"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="text-muted-foreground mx-auto h-12 w-12" />
                    <div>
                      <p className="text-sm font-medium">{t('dropJsonOrClick')}</p>
                      <p className="text-muted-foreground mt-1 text-xs">
                        {apiType === 'swagger' ? t('supportSwagger') : t('supportGraphql')}
                      </p>
                    </div>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json,application/json"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="paste" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    {apiType === 'swagger' ? t('pasteSwaggerContent') : t('pasteGraphqlContent')}
                  </CardTitle>
                  <CardDescription>
                    {apiType === 'swagger' ? t('pasteSwaggerJsonHere') : t('pasteGraphqlSchemaHere')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={jsonContent}
                    onChange={e => setJsonContent(e.target.value)}
                    placeholder={
                      apiType === 'swagger' ? t('pasteSwaggerJsonPlaceholder') : t('pasteGraphqlSchemaPlaceholder')
                    }
                    className="font-mono text-xs"
                    rows={12}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Preview */}
          {jsonContent && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">{t('preview')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted rounded-md p-3">
                  <pre className="max-h-32 overflow-x-auto text-xs">
                    {jsonContent.slice(0, 500)}
                    {jsonContent.length > 500 && '...'}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          {(activeTab === 'file' || activeTab === 'paste' || activeTab === 'url') && (
            <div className="flex justify-end gap-2 border-t pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setActiveTab('manage')
                  resetForm()
                }}
              >
                {t('cancel')}
              </Button>
              <Button
                onClick={handleSave}
                disabled={isLoading || !apiName.trim() || !jsonContent.trim()}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isLoading ? t('saving') : t('saveApi')}
              </Button>
            </div>
          )}

          {activeTab === 'manage' && (
            <div className="flex justify-end gap-2 border-t pt-4">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                {t('close')}
              </Button>
              <Button onClick={() => setActiveTab('url')} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {t('addNewApi')}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
