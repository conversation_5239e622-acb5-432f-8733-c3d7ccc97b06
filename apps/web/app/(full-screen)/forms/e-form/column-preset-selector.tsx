'use client'

import { Card, CardContent } from '@workspace/ui/components/card'
import type React from 'react'

export interface ColumnPreset {
  id: string
  name: string
  description: string
  spans: number[]
  icon: React.ReactNode
}

interface ColumnPresetSelectorProps {
  onSelect: (preset: ColumnPreset) => void
}

export function ColumnPresetSelector({ onSelect }: ColumnPresetSelectorProps) {
  const presets: ColumnPreset[] = [
    {
      id: 'full',
      name: 'Full Width',
      description: '1 column (span 12)',
      spans: [12],
      icon: (
        <div className="bg-primary/20 flex h-6 w-full items-center justify-center rounded">
          <span className="text-primary/70 text-xs">1/1</span>
        </div>
      ),
    },
    {
      id: 'half-half',
      name: 'Two Equal',
      description: '2 columns (span 6 each)',
      spans: [6, 6],
      icon: (
        <div className="flex h-6 w-full gap-1">
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/2</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/2</span>
          </div>
        </div>
      ),
    },
    {
      id: 'thirds',
      name: 'Three Equal',
      description: '3 columns (span 4 each)',
      spans: [4, 4, 4],
      icon: (
        <div className="flex h-6 w-full gap-1">
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/3</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/3</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/3</span>
          </div>
        </div>
      ),
    },
    {
      id: 'quarters',
      name: 'Four Equal',
      description: '4 columns (span 3 each)',
      spans: [3, 3, 3, 3],
      icon: (
        <div className="flex h-6 w-full gap-1">
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/4</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/4</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/4</span>
          </div>
          <div className="bg-primary/20 flex flex-1 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/4</span>
          </div>
        </div>
      ),
    },
    {
      id: 'one-third-two-thirds',
      name: '1/3 + 2/3',
      description: '2 columns (span 4 + 8)',
      spans: [4, 8],
      icon: (
        <div className="flex h-6 w-full gap-1">
          <div className="bg-primary/20 flex w-1/3 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/3</span>
          </div>
          <div className="bg-primary/20 flex w-2/3 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">2/3</span>
          </div>
        </div>
      ),
    },
    {
      id: 'two-thirds-one-third',
      name: '2/3 + 1/3',
      description: '2 columns (span 8 + 4)',
      spans: [8, 4],
      icon: (
        <div className="flex h-6 w-full gap-1">
          <div className="bg-primary/20 flex w-2/3 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">2/3</span>
          </div>
          <div className="bg-primary/20 flex w-1/3 items-center justify-center rounded">
            <span className="text-primary/70 text-xs">1/3</span>
          </div>
        </div>
      ),
    },
  ]

  return (
    <div className="grid grid-cols-2 gap-2">
      {presets.map(preset => (
        <Card
          key={preset.id}
          className="hover:border-primary/50 cursor-pointer transition-colors"
          onClick={() => onSelect(preset)}
        >
          <CardContent className="p-3">
            <div className="mb-2">{preset.icon}</div>
            <div className="text-xs font-medium">{preset.name}</div>
            <div className="text-muted-foreground text-xs">{preset.description}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
