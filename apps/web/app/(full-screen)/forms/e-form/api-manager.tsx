'use client'

import { type ApiEndpoint, type ApiSchema, apiService, type ApiType } from '@/lib/json-form/json-form-api-service'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { ScrollArea } from '@workspace/ui/components/scroll-area'
import { Separator } from '@workspace/ui/components/separator'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import {
  ArrowRight,
  Calendar,
  ChevronDown,
  ChevronUp,
  Edit,
  Eye,
  Globe,
  Link,
  RefreshCw,
  Search,
  Trash2,
} from 'lucide-react'
import { useEffect, useState } from 'react'

import { EditApiModal } from './edit-api-modal'

interface ApiManagerProps {
  onSelectEndpoint?: (endpoint: ApiEndpoint, schema: ApiSchema) => void
}

export function ApiManager({ onSelectEndpoint }: ApiManagerProps) {
  const t = useTranslations('e-form')
  const [apis, setApis] = useState<ApiSchema[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedApi, setSelectedApi] = useState<ApiSchema | null>(null)
  const [expandedEndpoints, setExpandedEndpoints] = useState<Set<string>>(new Set())
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [apiToDelete, setApiToDelete] = useState<ApiSchema | null>(null)
  const [syncDialogOpen, setSyncDialogOpen] = useState(false)
  const [apiToSync, setApiToSync] = useState<ApiSchema | null>(null)
  const [isSyncing, setIsSyncing] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [apiToEdit, setApiToEdit] = useState<ApiSchema | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [endpointSearchQuery, setEndpointSearchQuery] = useState('')
  const [debouncedEndpointSearch, setDebouncedEndpointSearch] = useState('')

  useEffect(() => {
    loadApis()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Debounce endpoint search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedEndpointSearch(endpointSearchQuery)
    }, 100)

    return () => clearTimeout(timer)
  }, [endpointSearchQuery])

  const loadApis = async () => {
    try {
      setIsLoading(true)
      const result = await apiService.searchApiSchemas(searchQuery, 1, 100)
      setApis(result.apis)
    } catch (error) {
      console.error('Error loading APIs:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = async () => {
    await loadApis()
  }

  const handleDeleteApi = async () => {
    if (!apiToDelete) return

    try {
      await apiService.deleteApiSchemaPermanently(apiToDelete.id)
      await loadApis()
      setDeleteDialogOpen(false)
      setApiToDelete(null)

      if (selectedApi?.id === apiToDelete.id) {
        setSelectedApi(null)
      }
    } catch (error) {
      console.error('Error deleting API:', error)
    }
  }

  const handleSyncApi = async () => {
    if (!apiToSync) return

    try {
      setIsSyncing(true)
      const updatedApi = await apiService.syncApiSchemaFromUrl(apiToSync.id)

      // Update the local state
      await loadApis()

      // If this was the selected API, update it
      if (selectedApi?.id === apiToSync.id) {
        setSelectedApi(updatedApi)
      }

      setSyncDialogOpen(false)
      setApiToSync(null)
    } catch (error) {
      console.error('Error syncing API:', error)
      // You might want to show an error message to the user here
    } finally {
      setIsSyncing(false)
    }
  }

  const handleApiUpdated = async (updatedApi: ApiSchema) => {
    // Refresh the API list
    await loadApis()

    // If this was the selected API, update it
    if (selectedApi?.id === updatedApi.id) {
      setSelectedApi(updatedApi)
    }
  }

  const toggleEndpointExpansion = (endpointKey: string) => {
    const newExpanded = new Set(expandedEndpoints)

    if (newExpanded.has(endpointKey)) {
      newExpanded.delete(endpointKey)
    } else {
      newExpanded.add(endpointKey)
    }
    setExpandedEndpoints(newExpanded)
  }

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'POST':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'PUT':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PATCH':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'WS':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200' // WebSocket for GraphQL subscriptions
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getApiTypeBadge = (type: ApiType) => {
    switch (type) {
      case 'swagger':
        return (
          <Badge variant="outline" className="border-blue-200 bg-blue-50 text-xs text-blue-700">
            Swagger
          </Badge>
        )
      case 'graphql':
        return (
          <Badge variant="outline" className="border-pink-200 bg-pink-50 text-xs text-pink-700">
            GraphQL
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="text-xs">
            {type}
          </Badge>
        )
    }
  }

  const getFilteredEndpoints = (endpoints: ApiEndpoint[]) => {
    if (!debouncedEndpointSearch.trim()) {
      return endpoints
    }

    /*
    🎯 HIERARCHICAL SEARCH cho endpoint: "/requester-biz/api/v1/document-sets"

    TIER 1 - Traditional Search (ưu tiên cao):
    - "doc set" ✓ (document + sets)
    - "set doc" ✓ (sets + document)
    - "req biz" ✓ (requester + business)
    - "document sets" ✓ (exact phrase)

    TIER 2 - OpenShift Substring (chỉ khi TIER 1 không có kết quả):
    - "docset" ✓ (document-sets)
    - "reqbiz" ✓ (requester-biz)
    - "docu" ✓ (substring in document)

    TIER 3 - Fuzzy Matching (chỉ khi TIER 1+2 đều thất bại):
    - "dcmt" ✓ (subsequence: d-o-c-u-m-e-n-t)
    - "rqstr" ✓ (subsequence: r-e-q-u-e-s-t-e-r)

    ✅ Ưu điểm: Kết quả chính xác, không quá rộng, fallback thông minh
    */

    const searchTerm = debouncedEndpointSearch.toLowerCase().trim()
    const searchWords = searchTerm.split(/\s+/).filter(word => word.length > 0)

    // Mapping cho các từ viết tắt phổ biến
    const abbreviationMap: Record<string, string[]> = {
      doc: ['document', 'documentation'],
      docs: ['documents', 'documentation'],
      set: ['sets', 'setting', 'setup'],
      sets: ['set', 'setting', 'setup'],
      api: ['application', 'interface'],
      req: ['request', 'requester'],
      biz: ['business', 'bizness'],
    }

    // Helper function để kiểm tra subsequence matching (như K8s fuzzy search)
    const isSubsequence = (needle: string, haystack: string): boolean => {
      if (needle.length === 0) return true

      if (needle.length > haystack.length) return false

      let needleIndex = 0

      for (let i = 0; i < haystack.length && needleIndex < needle.length; i++) {
        if (haystack[i] === needle[needleIndex]) {
          needleIndex++
        }
      }

      return needleIndex === needle.length
    }

    // 1. TIER 1: Traditional search (exact + word-based + abbreviations)
    let filteredResults = endpoints.filter(endpoint => {
      // Normalize path để tách thành các từ
      const normalizedPath = endpoint.path
        .replace(/[/\-_]/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .replace(/([a-zA-Z])(\d)|(\d)([a-zA-Z])/g, '$1$3 $2$4')
        .toLowerCase()

      // Tạo chuỗi tìm kiếm từ tất cả thông tin của endpoint
      const baseContent = [
        endpoint.path,
        normalizedPath,
        endpoint.description || '',
        endpoint.summary || '',
        endpoint.method,
      ]

      // Thêm các variations từ abbreviation map
      const expandedContent = [...baseContent]
      Object.entries(abbreviationMap).forEach(([abbr, expansions]) => {
        expansions.forEach(expansion => {
          if (normalizedPath.includes(abbr)) {
            expandedContent.push(normalizedPath.replace(new RegExp(abbr, 'g'), expansion))
          }

          if (normalizedPath.includes(expansion)) {
            expandedContent.push(normalizedPath.replace(new RegExp(expansion, 'g'), abbr))
          }
        })
      })

      const searchableContent = expandedContent.join(' ').toLowerCase()

      // Tier 1: Traditional word-based search
      const hasAllWords = searchWords.every(word => {
        // Kiểm tra trực tiếp trong searchable content
        if (searchableContent.includes(word)) return true

        // Kiểm tra qua abbreviation map
        const expansions = abbreviationMap[word] || []

        return expansions.some(expansion => searchableContent.includes(expansion))
      })

      // Tier 1: Exact phrase matching
      const hasExactPhrase = searchableContent.includes(searchTerm)

      return hasAllWords || hasExactPhrase
    })

    // 2. TIER 2: Nếu không có kết quả, dùng OpenShift-style substring search
    if (filteredResults.length === 0) {
      filteredResults = endpoints.filter(endpoint => {
        const normalizedPath = endpoint.path
          .replace(/[/\-_]/g, ' ')
          .replace(/([a-z])([A-Z])/g, '$1 $2')
          .replace(/([a-zA-Z])(\d)|(\d)([a-zA-Z])/g, '$1$3 $2$4')
          .toLowerCase()

        // Substring matching cho individual words
        const hasWordSubstring = searchWords.every(word => {
          const originalPath = endpoint.path.toLowerCase()
          const compactPath = normalizedPath.replace(/\s+/g, '')

          return originalPath.includes(word) || compactPath.includes(word)
        })

        // Substring matching cho entire query
        const queryWithoutSpaces = searchTerm.replace(/\s+/g, '')

        if (queryWithoutSpaces.length >= 3) {
          const originalPath = endpoint.path.toLowerCase()
          const compactPath = normalizedPath.replace(/\s+/g, '')
          const compactDescription = (endpoint.description || '').toLowerCase().replace(/\s+/g, '')

          const hasQuerySubstring =
            originalPath.includes(queryWithoutSpaces) ||
            compactPath.includes(queryWithoutSpaces) ||
            compactDescription.includes(queryWithoutSpaces)

          if (hasQuerySubstring) return true
        }

        return hasWordSubstring
      })
    }

    // 3. TIER 3: Nếu vẫn không có kết quả, dùng fuzzy matching
    if (filteredResults.length === 0 && searchWords.length === 1) {
      const query = searchWords[0]

      if (query && query.length >= 3) {
        filteredResults = endpoints.filter(endpoint => {
          const normalizedPath = endpoint.path
            .replace(/[/\-_]/g, ' ')
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .replace(/([a-zA-Z])(\d)|(\d)([a-zA-Z])/g, '$1$3 $2$4')
            .toLowerCase()

          // Fuzzy matching (subsequence)
          const originalPath = endpoint.path.toLowerCase().replace(/[/\-_]/g, '')

          if (isSubsequence(query, originalPath)) return true

          const words = normalizedPath.split(' ').filter(w => w.length > 0)

          return words.some(word => isSubsequence(query, word))
        })
      }
    }

    return filteredResults
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="grid h-[600px] grid-cols-1 gap-4 lg:grid-cols-3">
      {/* API List */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          <h3 className="font-semibold">{t('apiSchemas')}</h3>
        </div>

        <div className="flex gap-2">
          <Input
            placeholder={t('searchApi')}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleSearch()}
            className="flex-1"
          />
          <Button onClick={handleSearch} size="sm">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        <ScrollArea className="h-[500px]">
          <div className="space-y-2">
            {isLoading ? (
              <div className="text-muted-foreground py-4 text-center">{t('loading')}</div>
            ) : apis.length === 0 ? (
              <div className="text-muted-foreground py-4 text-center">{t('noApiYet')}</div>
            ) : (
              apis.map(api => (
                <Card
                  key={api.id}
                  className={`cursor-pointer transition-colors ${selectedApi?.id === api.id ? 'ring-primary ring-2' : 'hover:bg-accent'}`}
                  onClick={() => setSelectedApi(api)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-sm">{api.name}</CardTitle>
                        {api.description && (
                          <CardDescription className="mt-1 text-xs">
                            <div dangerouslySetInnerHTML={{ __html: api.description }} />
                          </CardDescription>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={e => {
                            e.stopPropagation()
                            setApiToEdit(api)
                            setEditDialogOpen(true)
                          }}
                          className="text-muted-foreground h-6 w-6 p-0 hover:text-green-600"
                          title={t('editApi')}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        {api.url && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={e => {
                              e.stopPropagation()
                              setApiToSync(api)
                              setSyncDialogOpen(true)
                            }}
                            className="text-muted-foreground h-6 w-6 p-0 hover:text-blue-600"
                            title={t('syncFromUrl')}
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={e => {
                            e.stopPropagation()
                            setApiToDelete(api)
                            setDeleteDialogOpen(true)
                          }}
                          className="text-muted-foreground hover:text-destructive h-6 w-6 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-muted-foreground flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(api.createdAt)}
                        </div>
                        {getApiTypeBadge(api.type)}
                      </div>
                      <div className="flex items-center gap-2">
                        {api.url && (
                          <Badge variant="outline" className="border-blue-200 bg-blue-50 text-xs text-blue-700">
                            <Link className="mr-1 h-3 w-3" />
                            URL
                          </Badge>
                        )}
                        <Badge variant="secondary" className="text-xs">
                          {api.endpoints.length} {t('endpoints')}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* API Details */}
      <div className="lg:col-span-2">
        {selectedApi ? (
          <div className="space-y-4">
            <div>
              <h3 className="flex items-center gap-2 font-semibold">
                <Eye className="h-5 w-5" />
                {selectedApi.name}
              </h3>
              {/* {selectedApi.description && <p className="text-sm text-muted-foreground mt-1" dangerouslySetInnerHTML={{ __html: selectedApi.description }} />} */}
            </div>

            <Separator />

            <div>
              <div className="flex items-center justify-between gap-10">
                <h4 className="mb-3 font-medium">
                  {t('endpoints')} ({getFilteredEndpoints(selectedApi.endpoints).length}
                  {debouncedEndpointSearch.trim() ? ` / ${selectedApi.endpoints.length}` : ''})
                </h4>

                {/* Endpoint Search Input */}
                <div className="relative mb-4 flex-1">
                  <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                  <Input
                    placeholder={t('searchEndpointOrDescription')}
                    value={endpointSearchQuery}
                    onChange={e => setEndpointSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <ScrollArea className="h-[450px]">
                <div className="space-y-2">
                  {getFilteredEndpoints(selectedApi.endpoints).length === 0 && debouncedEndpointSearch.trim() ? (
                    <div className="text-muted-foreground py-8 text-center">
                      <Search className="mx-auto mb-2 h-8 w-8 opacity-50" />
                      <p>
                        {t('noEndpointFound')} &quot;{debouncedEndpointSearch}&quot;
                      </p>
                    </div>
                  ) : (
                    getFilteredEndpoints(selectedApi.endpoints).map((endpoint, index) => {
                      const endpointKey = `${endpoint.method}-${endpoint.path}-${index}`
                      const isExpanded = expandedEndpoints.has(endpointKey)

                      return (
                        <Card key={endpointKey} className="overflow-hidden">
                          <CardHeader
                            className="hover:bg-accent/50 cursor-pointer pb-2"
                            onClick={() => toggleEndpointExpansion(endpointKey)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Badge
                                  variant="outline"
                                  className={`font-mono text-xs ${getMethodColor(endpoint.method)}`}
                                >
                                  {endpoint.method}
                                </Badge>
                                <code className="font-mono text-sm">{endpoint.path}</code>
                              </div>
                              <div className="flex items-center gap-2">
                                {onSelectEndpoint && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={e => {
                                      e.stopPropagation()
                                      onSelectEndpoint(endpoint, selectedApi)
                                    }}
                                    className="h-6 px-2 text-xs"
                                  >
                                    <ArrowRight className="mr-1 h-3 w-3" />
                                    {t('use')}
                                  </Button>
                                )}
                                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                              </div>
                            </div>
                            {endpoint.summary && (
                              <p className="text-muted-foreground mt-1 text-xs">{endpoint.summary}</p>
                            )}
                          </CardHeader>

                          {isExpanded && (
                            <CardContent className="pt-0">
                              {endpoint.description && (
                                <div className="mb-3">
                                  <h5 className="mb-1 text-xs font-medium">{t('descriptionLabel')}</h5>
                                  <p className="text-muted-foreground text-xs">{endpoint.description}</p>
                                </div>
                              )}

                              {/* GraphQL specific fields */}
                              {(endpoint.query || endpoint.mutation || endpoint.subscription) && (
                                <div className="mb-3">
                                  <h5 className="mb-1 text-xs font-medium">{t('graphqlOperation')}</h5>
                                  <div className="bg-muted rounded p-2 text-xs">
                                    {endpoint.query && (
                                      <div className="mb-2">
                                        <span className="font-medium text-green-700">{t('query')}</span>{' '}
                                        <code className="rounded bg-green-50 px-1 py-0.5 text-green-800">
                                          {endpoint.query}
                                          {endpoint.parameters && endpoint.parameters.length > 0 && (
                                            <>
                                              (
                                              {endpoint.parameters?.map((param, idx) => (
                                                <span key={idx}>
                                                  {param.name}: {param.type?.name || 'Unknown'}
                                                  {param.type?.kind === 'NON_NULL' ? '!' : ''}
                                                  {idx < (endpoint.parameters?.length || 0) - 1 ? ', ' : ''}
                                                </span>
                                              ))}
                                              )
                                            </>
                                          )}
                                        </code>
                                      </div>
                                    )}
                                    {endpoint.mutation && (
                                      <div className="mb-2">
                                        <span className="font-medium text-blue-700">{t('mutation')}</span>{' '}
                                        <code className="rounded bg-blue-50 px-1 py-0.5 text-blue-800">
                                          {endpoint.mutation}
                                          {endpoint.parameters && endpoint.parameters.length > 0 && (
                                            <>
                                              (
                                              {endpoint.parameters?.map((param, idx) => (
                                                <span key={idx}>
                                                  {param.name}: {param.type?.name || 'Unknown'}
                                                  {param.type?.kind === 'NON_NULL' ? '!' : ''}
                                                  {idx < (endpoint.parameters?.length || 0) - 1 ? ', ' : ''}
                                                </span>
                                              ))}
                                              )
                                            </>
                                          )}
                                        </code>
                                      </div>
                                    )}
                                    {endpoint.subscription && (
                                      <div className="mb-2">
                                        <span className="font-medium text-purple-700">{t('subscription')}</span>{' '}
                                        <code className="rounded bg-purple-50 px-1 py-0.5 text-purple-800">
                                          {endpoint.subscription}
                                          {endpoint.parameters && endpoint.parameters.length > 0 && (
                                            <>
                                              (
                                              {endpoint.parameters?.map((param, idx) => (
                                                <span key={idx}>
                                                  {param.name}: {param.type?.name || 'Unknown'}
                                                  {param.type?.kind === 'NON_NULL' ? '!' : ''}
                                                  {idx < (endpoint.parameters?.length || 0) - 1 ? ', ' : ''}
                                                </span>
                                              ))}
                                              )
                                            </>
                                          )}
                                        </code>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}

                              {endpoint.parameters && endpoint.parameters.length > 0 && (
                                <div className="mb-3">
                                  <h5 className="mb-1 text-xs font-medium">
                                    {selectedApi?.type === 'graphql' ? t('arguments') : t('parameters')}
                                  </h5>
                                  <div className="bg-muted rounded p-2 text-xs">
                                    <pre className="whitespace-pre-wrap">
                                      {JSON.stringify(endpoint.parameters, null, 2)}
                                    </pre>
                                  </div>
                                </div>
                              )}

                              {endpoint.requestBody && (
                                <div className="mb-3">
                                  <h5 className="mb-1 text-xs font-medium">{t('requestBody')}</h5>
                                  <div className="bg-muted rounded p-2 text-xs">
                                    <pre className="whitespace-pre-wrap">
                                      {JSON.stringify(endpoint.requestBody, null, 2)}
                                    </pre>
                                  </div>
                                </div>
                              )}

                              {endpoint.variables && (
                                <div className="mb-3">
                                  <h5 className="mb-1 text-xs font-medium">{t('variables')}</h5>
                                  <div className="bg-muted rounded p-2 text-xs">
                                    <pre className="whitespace-pre-wrap">
                                      {JSON.stringify(endpoint.variables, null, 2)}
                                    </pre>
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          )}
                        </Card>
                      )
                    })
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        ) : (
          <div className="flex h-full items-center justify-center text-center">
            <div className="space-y-2">
              <Globe className="text-muted-foreground mx-auto h-12 w-12" />
              <p className="text-muted-foreground">{t('selectApiFromList')}</p>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('confirmDeleteApi')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('confirmDeleteApiMessage', { name: apiToDelete?.name || '' })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteApi} className="bg-destructive text-destructive-foreground">
              {t('delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Sync Confirmation Dialog */}
      <AlertDialog open={syncDialogOpen} onOpenChange={setSyncDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('syncApiFromUrl')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('confirmSyncApiMessage', { name: apiToSync?.name || '' })}
              <br />
              <span className="text-muted-foreground mt-2 block text-xs">
                {t('url')} {apiToSync?.url}
              </span>
              <br />
              <span className="text-xs text-yellow-600">{t('syncWarning')}</span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSyncing}>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSyncApi}
              disabled={isSyncing}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              {isSyncing ? (
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  {t('syncing')}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  {t('syncNow')}
                </div>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit API Modal */}
      <EditApiModal
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        apiToEdit={apiToEdit}
        onApiUpdated={handleApiUpdated}
      />
    </div>
  )
}
