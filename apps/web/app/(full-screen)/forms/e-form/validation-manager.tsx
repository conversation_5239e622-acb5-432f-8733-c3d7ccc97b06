'use client'

import { Badge } from '@workspace/ui/components/badge'
import { But<PERSON> } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Input } from '@workspace/ui/components/input'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ChevronDown, Plus, ShieldCheck, Trash2 } from 'lucide-react'
import { useState } from 'react'

import type { FormElement, ValidationRule } from './form-builder'

interface ValidationManagerProps {
  element: FormElement
  onUpdateValidation: (rules: ValidationRule[]) => void
}

export function ValidationManager({ element, onUpdateValidation }: ValidationManagerProps) {
  const t = useTranslations('e-form')
  const [isOpen, setIsOpen] = useState(false)

  // Only show validation for form elements
  const isFormElement = ['input', 'select', 'textarea', 'checkbox', 'radio', 'date-picker', 'file-upload'].includes(
    element.type
  )

  if (!isFormElement) return null

  const validationRules = element.validationRules || []

  const addRule = () => {
    onUpdateValidation([...validationRules, { type: 'required', message: t('thisFieldIsRequiredValidation') }])
  }

  const updateRule = (index: number, updates: Partial<ValidationRule>) => {
    const newRules = [...validationRules]
    newRules[index] = { ...(newRules[index] || {}), ...updates } as ValidationRule
    onUpdateValidation(newRules)
  }

  const removeRule = (index: number) => {
    const newRules = validationRules.filter((_, i) => i !== index)
    onUpdateValidation(newRules)
  }

  return (
    <Card className="shadow-none">
      <CardHeader className="hover:bg-accent/50 cursor-pointer transition-colors" onClick={() => setIsOpen(!isOpen)}>
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <ShieldCheck className="h-4 w-4" />
            {t('validationRules')}
            {validationRules.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {validationRules.length}
              </Badge>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </CardTitle>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-4 px-3">
          {validationRules.length === 0 ? (
            <div className="text-muted-foreground py-4 text-center">
              <p className="text-sm">{t('noValidationRulesDefined')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {validationRules.map((rule, index) => (
                <div key={index} className="space-y-3 rounded-md border p-3">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">
                      {t('rule')} {index + 1}
                    </div>
                    <Button size="sm" variant="ghost" onClick={() => removeRule(index)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs font-medium">{t('type')}</label>
                      <select
                        className="w-full rounded border p-2 text-sm"
                        value={rule.type}
                        onChange={e => updateRule(index, { type: e.target.value as ValidationRule['type'] })}
                      >
                        <option value="required">{t('required')}</option>
                        <option value="email">{t('email')}</option>
                        <option value="regex">{t('regexPattern')}</option>
                        <option value="min">{t('minValueLength')}</option>
                        <option value="max">{t('maxValueLength')}</option>
                        <option value="custom">{t('custom')}</option>
                      </select>
                    </div>

                    {['regex', 'min', 'max', 'custom'].includes(rule.type) && (
                      <div>
                        <label className="text-xs font-medium">{t('value')}</label>
                        <Input
                          className="text-sm"
                          value={rule.value || ''}
                          onChange={e => updateRule(index, { value: e.target.value })}
                          placeholder={
                            rule.type === 'regex'
                              ? t('regexPlaceholder')
                              : rule.type === 'custom'
                                ? t('customValidationPlaceholder')
                                : t('valuePlaceholder')
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="text-xs font-medium">{t('errorMessage')}</label>
                    <Input
                      className="text-sm"
                      value={rule.message}
                      onChange={e => updateRule(index, { message: e.target.value })}
                      placeholder={t('errorMessagePlaceholder')}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          <Button size="sm" variant="outline" onClick={addRule} className="w-full">
            <Plus className="mr-2 h-4 w-4" />
            {t('addValidationRule')}
          </Button>
        </CardContent>
      )}
    </Card>
  )
}
