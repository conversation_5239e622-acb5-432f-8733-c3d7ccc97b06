{"e-form.templates": "Templates", "e-form.templateName": "Template Name", "e-form.description": "Description", "e-form.elementsWillBeSavedInThisTemplate": "Elements will be saved in this template.", "e-form.pleaseSelectAtLeastOneElementToSaveAsTemplate": "Please select at least one element to save as template.", "e-form.saving": "Saving...", "e-form.saveTemplate": "Save Template", "e-form.selectAnElementToEditItsProperties": "Select an element to edit its properties", "e-form.basicProperties": "Basic Properties", "e-form.name": "Name", "e-form.placeholder": "Placeholder", "e-form.imageUrl": "Image URL", "e-form.buttonType": "Button Type", "e-form.selectButtonType": "Select Button Type", "e-form.button": "<PERSON><PERSON>", "e-form.submit": "Submit", "e-form.reset": "Reset", "e-form.options": "Options", "e-form.appearance": "Appearance", "e-form.width": "<PERSON><PERSON><PERSON>", "e-form.height": "Height", "e-form.backgroundColor": "Background Color", "e-form.textColor": "Text Color", "e-form.fontSize": "Font Size", "e-form.fontWeight": "Font Weight", "e-form.border": "Border", "e-form.borderRadius": "Border Radius", "e-form.spacingAndLayout": "Spacing and Layout", "e-form.margin": "<PERSON><PERSON>", "e-form.padding": "Padding", "e-form.display": "Display", "e-form.flexDirection": "Flex Direction", "e-form.justifyContent": "Justify Content", "e-form.alignItems": "Align Items", "e-form.gap": "Gap", "e-form.layout": "Layout", "e-form.layoutDirection": "Layout Direction", "e-form.labelAlign": "Label Align", "e-form.vertical": "Vertical", "e-form.horizontal": "Horizontal", "e-form.left": "Left", "e-form.right": "Right", "e-form.selectAlignment": "Select Alignment", "e-form.selectLayout": "Select Layout", "e-form.addOption": "Add Option", "e-form.label": "<PERSON><PERSON>ã<PERSON>", "e-form.value": "Value", "e-form.required": "Required", "e-form.cssClass": "CSS Class", "e-form.columns": "Columns", "e-form.layoutConfiguration": "Layout Configuration", "e-form.quickSetup": "Quick Setup", "e-form.desktopToTabletToMobile": "Desktop → Tablet → Mobile", "e-form.autoAdjusts": "Auto-adjusts", "e-form.customResponsiveSettings": "Custom Responsive Settings", "e-form.configureEachBreakpointIndividually": "Configure each breakpoint individually", "e-form.layoutPreview": "Layout Preview", "e-form.desktop": "Desktop", "e-form.tablet": "Tablet", "e-form.mobile": "Mobile", "e-form.conditionalLogic": "Conditional Logic", "e-form.active": "Active", "e-form.visual": "Visual", "e-form.code": "Code", "e-form.showThisElementWhen": "Show this element when:", "e-form.field": "Field", "e-form.selectField": "Select field...", "e-form.operator": "Operator", "e-form.enterValue": "Enter value...", "e-form.applyCondition": "Apply Condition", "e-form.jsonataExpression": "JSONata Expression", "e-form.enterJsonataExpression": "Enter JSONata expression...", "e-form.examples": "Examples:", "e-form.showWhenCheckboxIsChecked": "Show when checkbox \"agree\" is checked", "e-form.showWhenAgeIsGreaterThan": "Show when age is greater than 18", "e-form.showWhenEmailContains": "Show when email contains @", "e-form.currentCondition": "Current condition:", "e-form.clear": "Clear", "e-form.themeSettings": "Theme Settings", "e-form.colors": "Colors", "e-form.typography": "Typography", "e-form.spacing": "Spacing", "e-form.applyTheme": "Apply Theme", "e-form.dropComponentsHere": "Drop components here", "e-form.unknownElementType": "Unknown element type", "e-form.createResponsiveLayoutsWithGrid": "Create responsive layouts with grid", "e-form.pickADate": "Pick a date", "e-form.clickToUploadOrDragAndDrop": "Click to upload or drag and drop", "e-form.svgPngJpgGifMax2Mb": "SVG, PNG, JPG or GIF (max. 2MB)", "e-form.paragraphText": "Paragraph text", "e-form.spanText": "Span text", "e-form.heading1": "Heading 1", "e-form.heading2": "Heading 2", "e-form.heading3": "Heading 3", "e-form.image": "Image", "e-form.createResponsiveColumnLayouts": "Create responsive column layouts", "e-form.selectAnOption": "Select an option", "e-form.loadingOptions": "Loading options...", "e-form.noOptionsAvailable": "No options available", "e-form.notConfigured": "Not configured", "e-form.dragComponentsFromTheLeftPanelToStartBuildingYourForm": "Drag components from the left panel to start building your form", "e-form.general": "General", "e-form.style": "Style", "e-form.treeLayout": "Tree Layout", "e-form.boxGrid": "Box Grid", "e-form.autoLayoutGridContainer": "Auto-layout grid container", "e-form.responsiveColumnLayout": "Responsive column layout", "e-form.flexibleGridContainerWithResponsiveColumns": "Flexible grid container with responsive columns", "e-form.mainHeading": "Main heading", "e-form.sectionHeading": "Section heading", "e-form.subsectionHeading": "Subsection heading", "e-form.paragraphContent": "Paragraph content", "e-form.spanContent": "Span content", "e-form.visualSeparator": "Visual separator", "e-form.imageDisplay": "Image display", "e-form.tableDisplay": "Table display", "e-form.inputField": "Input Field", "e-form.textInputWithValidation": "Text input with validation", "e-form.select": "Select", "e-form.dropdownSelection": "Dropdown selection", "e-form.apiSelect": "API Select", "e-form.dynamicOptionsFromApi": "Dynamic options from API", "e-form.textarea": "Textarea", "e-form.multiLineTextInput": "Multi-line text input", "e-form.checkbox": "Checkbox", "e-form.booleanSelection": "Boolean selection", "e-form.radioGroup": "Radio Group", "e-form.singleChoiceSelection": "Single choice selection", "e-form.datePicker": "Date Picker", "e-form.dateSelection": "Date selection", "e-form.fileUpload": "File Upload", "e-form.fileSelection": "File selection", "e-form.rating": "Rating", "e-form.starRatingInput": "Star rating input", "e-form.slider": "Slide<PERSON>", "e-form.rangeInput": "Range input", "e-form.array": "Array", "e-form.arrayInput": "Array input", "e-form.actionButton": "Action button", "e-form.searchComponents": "Search components...", "e-form.noComponentsFound": "No components found", "e-form.tryDifferentSearchTerm": "Try a different search term", "e-form.new": "New", "e-form.pro": "Pro", "e-form.htmlElements": "HTML Elements", "e-form.formElements": "Form Elements", "e-form.column": "<PERSON><PERSON><PERSON>", "e-form.container": "Container", "e-form.divider": "Divider", "e-form.table": "Table", "e-form.paragraph": "Paragraph", "e-form.span": "Span", "e-form.htmlElementConfiguration": "HTML Element Configuration", "e-form.hiddenDesktop": "Hidden Desktop", "e-form.hiddenTablet": "Hidden Tablet", "e-form.hiddenMobile": "Hidden Mobile", "e-form.content": "Content", "e-form.styling": "Styl<PERSON>", "e-form.visibility": "Visibility", "e-form.fontConfiguration": "Font Configuration", "e-form.light": "Light", "e-form.normal": "Normal", "e-form.medium": "Medium", "e-form.semiBold": "Semi Bold", "e-form.bold": "Bold", "e-form.extraBold": "Extra Bold", "e-form.noBackground": "No background", "e-form.responsiveVisibility": "Responsive Visibility", "e-form.controlWhichDevicesThisElementAppearsOn": "Control which devices this element appears on. Useful for hiding content on mobile or showing desktop-only sections.", "e-form.showOnDesktopDevices": "Show on desktop devices", "e-form.showOnTabletDevices": "Show on tablet devices", "e-form.showOnMobileDevices": "Show on mobile devices", "e-form.visibilitySummary": "Visibility Summary:", "e-form.hiddenOnAllDevices": "Hidden on all devices", "e-form.preview": "Preview:", "e-form.htmlContent": "HTML content", "e-form.enterContentPlaceholder": "Enter {type} content...", "e-form.customSizePlaceholder": "Custom size (e.g., 20px)", "e-form.customWeightPlaceholder": "Custom weight (e.g., 500)", "e-form.eFormBuilder": "E-Form Builder", "e-form.columnLayout": "Column Layout", "e-form.atLeastOneItemIsRequired": "At least one item is required", "e-form.thisFieldIsRequired": "This field is required", "e-form.option": "Option", "e-form.noOptionsFound": "No options found", "e-form.thisIsAParagraphOfText": "This is a paragraph of text.", "e-form.htmlContentDefault": "<h3>HTML Content</h3><p>Edit this content in the properties panel.</p>", "e-form.enterLongText": "Enter long text...", "e-form.clickMe": "Click Me", "e-form.radioButton": "Radio Button", "e-form.actions": "Actions", "e-form.detail": "Detail", "e-form.id": "ID", "e-form.sampleData": "Sample data", "e-form.createdDate": "Created date", "e-form.fieldName": "Field name", "e-form.information": "Information", "e-form.addItem": "Add Item", "e-form.remove": "Remove", "e-form.enterText": "Enter text...", "e-form.invalidJsonFormat": "Invalid JSON format. Please check your file.", "e-form.errorGeneratingCode": "An error occurred while generating code", "e-form.formStructure": "Form Structure", "e-form.formData": "Form Data", "e-form.exitPreview": "Exit Preview", "e-form.viewJson": "View JSON", "e-form.fields": "fields", "e-form.generate": "Generate", "e-form.apis": "APIs", "e-form.exampleFormData": "Example Form Data", "e-form.apply": "Apply", "e-form.clickGenerateToCreateExampleData": "Click 'Generate' to create example data based on your form fields", "e-form.editJsonAndApply": "Edit this JSON and click \"Apply\" to populate the form with example data.", "e-form.currentFormData": "Current Form Data", "e-form.formDataWillAppear": "Form data will appear here as you interact with the form", "e-form.showsCurrentFormDataInRealtime": "This shows the current form data in real-time.", "e-form.fieldsHaveData": "field(s) have data.", "e-form.noDataEnteredYet": "No data entered yet.", "e-form.tipUseExampleData": "Tip: Use the example data to quickly test your form, or copy the current data for debugging.", "e-form.regenerateExample": "Regenerate Example", "e-form.exportJson": "Export JSON", "e-form.sampleTextInput": "Sample text input", "e-form.sampleTextareaContent": "Sample textarea content with multiple lines of text.", "e-form.sampleFile": "sample-file.pdf", "e-form.sampleValue": "Sample value", "e-form.invalidJsonInExampleData": "Invalid JSON format in example data", "e-form.errorGeneratingFormFromApi": "An error occurred while generating form from API endpoint", "e-form.pleaseSelectJsonFile": "Please select a JSON file", "e-form.invalidJsonFile": "Invalid JSON file", "e-form.pleaseEnterApiUrl": "Please enter API URL", "e-form.errorLoadingApiFromUrl": "An error occurred while loading API from URL", "e-form.notValidSwaggerOpenApi": "Not a valid Swagger/OpenAPI file", "e-form.swaggerMustHavePaths": "Swagger file must have paths section", "e-form.swaggerMustBeValidJson": "Swagger/OpenAPI must be valid JSON", "e-form.invalidGraphqlIntrospection": "Invalid GraphQL Introspection result", "e-form.invalidGraphqlSchema": "Invalid GraphQL Schema object", "e-form.graphqlJsonMustHaveValidStructure": "GraphQL JSON must have valid Introspection structure", "e-form.invalidGraphqlJson": "Invalid GraphQL JSON", "e-form.graphqlSdlMustHaveType": "GraphQL SDL must have at least one type Query, Mutation, or Subscription", "e-form.apiTypeNotSupported": "API type not supported", "e-form.pleaseEnterApiName": "Please enter API name", "e-form.pleaseEnterOrUploadSwaggerJson": "Please enter or upload Swagger JSON file", "e-form.errorSavingApi": "An error occurred while saving API", "e-form.pleaseDropJsonFile": "Please drop a JSON file", "e-form.importApiSchema": "Import API Schema", "e-form.apiName": "API Name", "e-form.enterApiName": "Enter API name...", "e-form.apiType": "API Type", "e-form.selectApiType": "Select API type", "e-form.swaggerOpenApi": "Swagger/OpenAPI", "e-form.graphql": "GraphQL", "e-form.enterApiDescription": "Enter API description...", "e-form.manageApi": "Manage API", "e-form.importFromUrl": "Import from URL", "e-form.uploadFile": "Upload File", "e-form.pasteJson": "Paste JSON", "e-form.enterPathToSwaggerOrGraphql": "Enter path to Swagger JSON or GraphQL Schema to automatically download", "e-form.loading": "Loading...", "e-form.download": "Download", "e-form.uploadSwaggerFile": "Upload Swagger/OpenAPI File", "e-form.uploadGraphqlFile": "Upload GraphQL Schema File", "e-form.selectSwaggerJsonFile": "Select JSON file containing Swagger or OpenAPI schema", "e-form.selectGraphqlJsonFile": "Select JSON file containing GraphQL Schema (SDL or Introspection result)", "e-form.dropJsonOrClick": "Drop JSON file here or click to select", "e-form.supportSwagger": "Supports Swagger 2.0 and OpenAPI 3.0+", "e-form.supportGraphql": "Supports GraphQL Schema (SDL or Introspection)", "e-form.pasteSwaggerContent": "Paste Swagger/OpenAPI Content", "e-form.pasteGraphqlContent": "Paste GraphQL Schema Content", "e-form.pasteSwaggerJsonHere": "Paste Swagger/OpenAPI JSON content here", "e-form.pasteGraphqlSchemaHere": "Paste GraphQL Schema (SDL or Introspection JSON) here", "e-form.pasteSwaggerJsonPlaceholder": "Paste Swagger/OpenAPI JSON here...", "e-form.pasteGraphqlSchemaPlaceholder": "Paste GraphQL Schema here...", "e-form.cancel": "Cancel", "e-form.saveApi": "Save API", "e-form.close": "Close", "e-form.addNewApi": "Add New API", "e-form.apiSchemas": "API Schemas", "e-form.searchApi": "Search API...", "e-form.noApiYet": "No API yet", "e-form.editApi": "Edit API", "e-form.syncFromUrl": "Sync from URL", "e-form.endpoints": "endpoints", "e-form.searchEndpointOrDescription": "Search endpoint or description...", "e-form.noEndpointFound": "No endpoint found matching", "e-form.use": "Use", "e-form.descriptionLabel": "Description:", "e-form.graphqlOperation": "GraphQL Operation:", "e-form.query": "Query:", "e-form.mutation": "Mutation:", "e-form.subscription": "Subscription:", "e-form.arguments": "Arguments:", "e-form.parameters": "Parameters:", "e-form.requestBody": "Request Body:", "e-form.variables": "Variables:", "e-form.selectApiFromList": "Select an API from the list to view details", "e-form.confirmDeleteApi": "Confirm Delete API", "e-form.confirmDeleteApiMessage": "Are you sure you want to delete API \"{name}\"? This action cannot be undone.", "e-form.delete": "Delete", "e-form.syncApiFromUrl": "Sync API from URL", "e-form.confirmSyncApiMessage": "Do you want to update API \"{name}\" from URL?", "e-form.url": "URL:", "e-form.syncWarning": "Note: This operation will overwrite the current schema and endpoints.", "e-form.syncing": "Syncing...", "e-form.syncNow": "Sync now", "e-form.thisFieldIsRequiredValidation": "This field is required", "e-form.validationRules": "Validation Rules", "e-form.noValidationRulesDefined": "No validation rules defined.", "e-form.rule": "Rule", "e-form.type": "Type", "e-form.email": "Email", "e-form.regexPattern": "Regex Pattern", "e-form.minValueLength": "Min Value/Length", "e-form.maxValueLength": "Max Value/Length", "e-form.custom": "Custom", "e-form.regexPlaceholder": "^[A-Za-z0-9]+$", "e-form.customValidationPlaceholder": "Custom validation", "e-form.valuePlaceholder": "Value", "e-form.errorMessage": "Error Message", "e-form.errorMessagePlaceholder": "Error message", "e-form.addValidationRule": "Add Validation Rule"}