{"e-form.templates": "Mẫu biểu mẫu", "e-form.templateName": "Tên mẫu biểu mẫu", "e-form.description": "<PERSON><PERSON>", "e-form.elementsWillBeSavedInThisTemplate": "<PERSON><PERSON><PERSON> phần tử sẽ được lưu vào mẫu này.", "e-form.pleaseSelectAtLeastOneElementToSaveAsTemplate": "<PERSON><PERSON> lòng chọn ít nhất một phần tử để lưu thành mẫu.", "e-form.saving": "<PERSON><PERSON> l<PERSON>...", "e-form.saveTemplate": "Lưu mẫu biểu mẫu", "e-form.selectAnElementToEditItsProperties": "<PERSON><PERSON><PERSON> một phần tử để chỉnh sửa thuộc tính", "e-form.basicProperties": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> c<PERSON> bản", "e-form.name": "<PERSON><PERSON><PERSON>", "e-form.placeholder": "Placeholder", "e-form.imageUrl": "URL hình ảnh", "e-form.buttonType": "<PERSON><PERSON><PERSON> n<PERSON>", "e-form.selectButtonType": "<PERSON><PERSON><PERSON> lo<PERSON> n<PERSON>", "e-form.button": "<PERSON><PERSON><PERSON>", "e-form.submit": "<PERSON><PERSON><PERSON>", "e-form.reset": "<PERSON><PERSON><PERSON>", "e-form.options": "<PERSON><PERSON><PERSON>", "e-form.appearance": "<PERSON><PERSON><PERSON>", "e-form.width": "<PERSON><PERSON><PERSON> r<PERSON>", "e-form.height": "<PERSON><PERSON><PERSON> cao", "e-form.backgroundColor": "<PERSON><PERSON><PERSON>", "e-form.textColor": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "e-form.fontSize": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "e-form.fontWeight": "<PERSON><PERSON> đậm", "e-form.border": "<PERSON><PERSON><PERSON><PERSON>", "e-form.borderRadius": "<PERSON><PERSON> k<PERSON> v<PERSON>n", "e-form.spacingAndLayout": "K<PERSON><PERSON><PERSON> cách và bố cục", "e-form.margin": "<PERSON><PERSON>", "e-form.padding": "Padding", "e-form.display": "<PERSON><PERSON><PERSON> thị", "e-form.flexDirection": "Hướng flex", "e-form.justifyContent": "<PERSON><PERSON><PERSON> chỉnh theo chiều ngang", "e-form.alignItems": "<PERSON><PERSON><PERSON> chỉnh theo chiều dọc", "e-form.gap": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "e-form.layout": "Bố cục", "e-form.layoutDirection": "<PERSON><PERSON><PERSON><PERSON> bố cục", "e-form.labelAlign": "<PERSON>ăn chỉnh nhãn", "e-form.vertical": "Thẳng đứng", "e-form.horizontal": "Nằm ngang", "e-form.left": "Trái", "e-form.right": "<PERSON><PERSON><PERSON>", "e-form.selectAlignment": "<PERSON><PERSON><PERSON> căn chỉnh", "e-form.selectLayout": "<PERSON><PERSON><PERSON> b<PERSON> cục", "e-form.addOption": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "e-form.label": "<PERSON><PERSON>ã<PERSON>", "e-form.value": "<PERSON><PERSON><PERSON> trị", "e-form.required": "<PERSON><PERSON><PERSON> b<PERSON>", "e-form.cssClass": "CSS Class", "e-form.columns": "<PERSON><PERSON><PERSON>", "e-form.layoutConfiguration": "Bố cục", "e-form.quickSetup": "Cài đặt nhanh", "e-form.desktopToTabletToMobile": "Desktop → Tablet → Mobile", "e-form.autoAdjusts": "Tự động điều chỉnh", "e-form.customResponsiveSettings": "<PERSON>ài đặt phản hồi tùy chỉnh", "e-form.configureEachBreakpointIndividually": "<PERSON><PERSON><PERSON> đặt từng điểm phản hồi", "e-form.layoutPreview": "<PERSON><PERSON> b<PERSON> cục", "e-form.desktop": "Desktop", "e-form.tablet": "Tablet", "e-form.mobile": "Mobile", "e-form.conditionalLogic": "Logic điều kiện", "e-form.active": "<PERSON><PERSON><PERSON> đ<PERSON>", "e-form.visual": "Visual", "e-form.code": "Code", "e-form.showThisElementWhen": "<PERSON><PERSON><PERSON> thị phần tử này khi:", "e-form.field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e-form.selectField": "<PERSON><PERSON><PERSON> trường...", "e-form.operator": "<PERSON><PERSON> tử", "e-form.enterValue": "<PERSON><PERSON><PERSON><PERSON> giá trị...", "e-form.applyCondition": "<PERSON><PERSON> dụng điều kiện", "e-form.jsonataExpression": "JSONata Expression", "e-form.enterJsonataExpression": "Nhập JSONata expression...", "e-form.examples": "<PERSON><PERSON> dụ:", "e-form.showWhenCheckboxIsChecked": "Hiển thị khi checkbox \"agree\" <PERSON><PERSON><PERSON><PERSON> ch<PERSON>n", "e-form.showWhenAgeIsGreaterThan": "<PERSON><PERSON><PERSON> thị khi tuổi lớn hơn 18", "e-form.showWhenEmailContains": "Hiển thị khi email chứa @", "e-form.currentCondition": "<PERSON><PERSON><PERSON><PERSON> kiện hiện tại:", "e-form.clear": "Xóa", "e-form.themeSettings": "<PERSON>ài đặt giao di<PERSON>n", "e-form.colors": "<PERSON><PERSON><PERSON>", "e-form.typography": "Phông chữ", "e-form.spacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "e-form.applyTheme": "<PERSON><PERSON> d<PERSON> giao <PERSON>", "e-form.dropComponentsHere": "<PERSON><PERSON><PERSON> c<PERSON>c thành phần vào đây", "e-form.unknownElementType": "<PERSON><PERSON><PERSON> thành phần không xác định", "e-form.createResponsiveLayoutsWithGrid": "<PERSON><PERSON><PERSON> b<PERSON> cục phản hồi với grid", "e-form.pickADate": "<PERSON><PERSON><PERSON>", "e-form.clickToUploadOrDragAndDrop": "<PERSON><PERSON><PERSON><PERSON> để tải lên hoặc kéo và thả", "e-form.svgPngJpgGifMax2Mb": "SVG, PNG, JPG hoặc GIF (tối đa 2MB)", "e-form.paragraphText": "<PERSON><PERSON><PERSON> bản đ<PERSON>n", "e-form.spanText": "<PERSON><PERSON><PERSON> bản span", "e-form.heading1": "Tiêu đề 1", "e-form.heading2": "Tiêu đề 2", "e-form.heading3": "Tiêu đề 3", "e-form.image": "<PERSON><PERSON><PERSON>", "e-form.createResponsiveColumnLayouts": "<PERSON><PERSON><PERSON> b<PERSON> cục cột ph<PERSON>n hồi", "e-form.selectAnOption": "<PERSON><PERSON><PERSON> một tùy chọn", "e-form.loadingOptions": "<PERSON><PERSON> tải tùy chọn...", "e-form.noOptionsAvailable": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn nào", "e-form.notConfigured": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> cấu hình", "e-form.dragComponentsFromTheLeftPanelToStartBuildingYourForm": "<PERSON><PERSON><PERSON> c<PERSON>c thành phần từ bảng bên trái để bắt đầu xây dựng biểu mẫu", "e-form.general": "<PERSON><PERSON><PERSON> quan", "e-form.style": "<PERSON><PERSON><PERSON>", "e-form.treeLayout": "Cây", "e-form.boxGrid": "Lưới", "e-form.autoLayoutGridContainer": "Container <PERSON><PERSON><PERSON><PERSON> tự động", "e-form.responsiveColumnLayout": "<PERSON><PERSON> cục cột ph<PERSON>n hồi", "e-form.flexibleGridContainerWithResponsiveColumns": "Container <PERSON><PERSON><PERSON><PERSON> linh ho<PERSON>t với các cột phản hồi", "e-form.mainHeading": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> ch<PERSON>h", "e-form.sectionHeading": "<PERSON><PERSON><PERSON><PERSON> đề phần", "e-form.subsectionHeading": "<PERSON><PERSON><PERSON><PERSON> đề phần phụ", "e-form.paragraphContent": "<PERSON><PERSON><PERSON> dung đoạn văn", "e-form.spanContent": "Nội dung span", "e-form.visualSeparator": "Đường phân cách", "e-form.imageDisplay": "<PERSON><PERSON><PERSON> thị h<PERSON>nh <PERSON>nh", "e-form.tableDisplay": "<PERSON><PERSON><PERSON> thị bảng", "e-form.inputField": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> li<PERSON>", "e-form.textInputWithValidation": "<PERSON><PERSON><PERSON><PERSON> văn bản với xác thực", "e-form.select": "<PERSON><PERSON><PERSON>", "e-form.dropdownSelection": "<PERSON><PERSON><PERSON> ch<PERSON>n thả xuống", "e-form.apiSelect": "API Select", "e-form.dynamicOptionsFromApi": "<PERSON><PERSON><PERSON> chọn động từ <PERSON>", "e-form.textarea": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "e-form.multiLineTextInput": "<PERSON><PERSON><PERSON><PERSON> văn bản nhi<PERSON>u dòng", "e-form.checkbox": "<PERSON><PERSON><PERSON>", "e-form.booleanSelection": "<PERSON><PERSON><PERSON> ch<PERSON>n boolean", "e-form.radioGroup": "Nhóm radio", "e-form.singleChoiceSelection": "<PERSON><PERSON><PERSON> ch<PERSON>n đ<PERSON>n", "e-form.datePicker": "<PERSON><PERSON><PERSON>", "e-form.dateSelection": "<PERSON><PERSON><PERSON> ng<PERSON>g", "e-form.fileUpload": "<PERSON><PERSON><PERSON> lên", "e-form.fileSelection": "<PERSON><PERSON><PERSON>", "e-form.rating": "Đánh giá", "e-form.starRatingInput": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> giá sao", "e-form.slider": "<PERSON><PERSON>", "e-form.rangeInput": "<PERSON><PERSON><PERSON><PERSON>", "e-form.array": "<PERSON><PERSON><PERSON>", "e-form.arrayInput": "<PERSON><PERSON><PERSON><PERSON>", "e-form.actionButton": "<PERSON><PERSON><PERSON> hành động", "e-form.searchComponents": "<PERSON><PERSON><PERSON> kiếm thành phần...", "e-form.noComponentsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thành phần nào", "e-form.tryDifferentSearchTerm": "Thử từ khóa tìm kiếm khác", "e-form.new": "<PERSON><PERSON><PERSON>", "e-form.pro": "Pro", "e-form.htmlElements": "Phần tử HTML", "e-form.formElements": "Phần tử biểu mẫu", "e-form.column": "<PERSON><PERSON><PERSON>", "e-form.container": "Container", "e-form.divider": "<PERSON><PERSON> c<PERSON>", "e-form.table": "<PERSON><PERSON><PERSON>", "e-form.paragraph": "<PERSON><PERSON><PERSON><PERSON> văn", "e-form.span": "Span", "e-form.htmlElementConfiguration": "<PERSON><PERSON><PERSON> hình phần tử HTML", "e-form.hiddenDesktop": "Ẩn Desktop", "e-form.hiddenTablet": "Ẩn Tablet", "e-form.hiddenMobile": "Ẩn Mobile", "e-form.content": "<PERSON><PERSON>i dung", "e-form.styling": "<PERSON><PERSON><PERSON> d<PERSON>ng", "e-form.visibility": "<PERSON><PERSON><PERSON> thị", "e-form.fontConfiguration": "<PERSON><PERSON><PERSON> hình phông chữ", "e-form.light": "Nhẹ", "e-form.normal": "<PERSON><PERSON><PERSON>", "e-form.medium": "<PERSON>rung bình", "e-form.semiBold": "<PERSON><PERSON><PERSON> đ<PERSON>m", "e-form.bold": "Đậm", "e-form.extraBold": "<PERSON><PERSON><PERSON> đ<PERSON>", "e-form.noBackground": "<PERSON><PERSON><PERSON><PERSON> có n<PERSON>n", "e-form.responsiveVisibility": "<PERSON><PERSON><PERSON> thị theo thiết bị", "e-form.controlWhichDevicesThisElementAppearsOn": "<PERSON><PERSON><PERSON> so<PERSON>t thiết bị nào hiển thị phần tử này. Hữu ích để ẩn nội dung trên mobile hoặc hiển thị các phần chỉ dành cho desktop.", "e-form.showOnDesktopDevices": "<PERSON><PERSON><PERSON> thị trên thiết bị <PERSON>", "e-form.showOnTabletDevices": "<PERSON><PERSON><PERSON> thị trên thiết bị tablet", "e-form.showOnMobileDevices": "<PERSON><PERSON><PERSON> thị trên thiết bị mobile", "e-form.visibilitySummary": "<PERSON><PERSON><PERSON> tắt hiển thị:", "e-form.hiddenOnAllDevices": "Ẩn trên tất cả thiết bị", "e-form.preview": "<PERSON><PERSON> tr<PERSON>:", "e-form.htmlContent": "Nội dung HTML", "e-form.enterContentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nội dung {type}...", "e-form.customSizePlaceholder": "<PERSON><PERSON><PERSON> th<PERSON> tùy chỉnh (vd: 20px)", "e-form.customWeightPlaceholder": "<PERSON><PERSON> đậm tùy chỉnh (vd: 500)", "e-form.eFormBuilder": "Bi<PERSON>u mẫu điện tử", "e-form.columnLayout": "<PERSON><PERSON> cục c<PERSON>t", "e-form.atLeastOneItemIsRequired": "<PERSON><PERSON> nh<PERSON>t một mục là bắt buộc", "e-form.thisFieldIsRequired": "Trư<PERSON>ng này là bắ<PERSON> buộc", "e-form.option": "<PERSON><PERSON><PERSON>", "e-form.noOptionsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tùy chọn nào", "e-form.thisIsAParagraphOfText": "<PERSON><PERSON><PERSON> là một đoạn văn bản.", "e-form.htmlContentDefault": "<h3>Nội dung HTML</h3><p>Chỉnh sửa nội dung này trong bảng thuộc t<PERSON>h.</p>", "e-form.enterLongText": "<PERSON><PERSON><PERSON><PERSON> văn bản dài...", "e-form.clickMe": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o tôi", "e-form.radioButton": "Nút radio", "e-form.actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "e-form.detail": "<PERSON> ti<PERSON>", "e-form.id": "ID", "e-form.sampleData": "<PERSON><PERSON> liệu mẫu", "e-form.createdDate": "<PERSON><PERSON><PERSON>", "e-form.fieldName": "<PERSON><PERSON><PERSON> tr<PERSON>", "e-form.information": "Thông tin", "e-form.addItem": "<PERSON><PERSON><PERSON><PERSON>", "e-form.remove": "Xóa", "e-form.enterText": "<PERSON><PERSON><PERSON><PERSON> văn bản...", "e-form.invalidJsonFormat": "<PERSON>ịnh dạng JSON không hợp lệ. <PERSON><PERSON> lòng kiểm tra tệp của bạn.", "e-form.errorGeneratingCode": "Đã xảy ra lỗi khi generate code", "e-form.formStructure": "<PERSON><PERSON>u trúc biểu mẫu", "e-form.formData": "<PERSON><PERSON> liệu biểu mẫu", "e-form.exitPreview": "<PERSON><PERSON><PERSON><PERSON> xem tr<PERSON>", "e-form.viewJson": "Xem J<PERSON>", "e-form.fields": "tr<PERSON><PERSON><PERSON>", "e-form.generate": "Tạo", "e-form.apis": "APIs", "e-form.exampleFormData": "<PERSON><PERSON> liệu biểu mẫu mẫu", "e-form.apply": "<PERSON><PERSON>", "e-form.clickGenerateToCreateExampleData": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' để tạo dữ liệu mẫu dựa trên các trường biểu mẫu của bạn", "e-form.editJsonAndApply": "Chỉnh sửa JSON này và nhấp \"<PERSON><PERSON> dụng\" để điền dữ liệu mẫu vào biểu mẫu.", "e-form.currentFormData": "<PERSON><PERSON> liệu biểu mẫu hiện tại", "e-form.formDataWillAppear": "Dữ liệu biểu mẫu sẽ xuất hiện ở đây khi bạn tương tác với biểu mẫu", "e-form.showsCurrentFormDataInRealtime": "Hiển thị dữ liệu biểu mẫu hiện tại theo thời gian thực.", "e-form.fieldsHaveData": "trư<PERSON><PERSON> có dữ liệu.", "e-form.noDataEnteredYet": "<PERSON><PERSON><PERSON> nh<PERSON>p dữ liệu.", "e-form.tipUseExampleData": "Mẹo: <PERSON><PERSON> dụng dữ liệu mẫu để kiểm tra nhanh biểu mẫu của bạn hoặc sao chép dữ liệu hiện tại để gỡ lỗi.", "e-form.regenerateExample": "Tạo lại mẫu", "e-form.exportJson": "Xuất JSON", "e-form.sampleTextInput": "<PERSON><PERSON><PERSON> bản nh<PERSON>p mẫu", "e-form.sampleTextareaContent": "Nội dung textarea mẫu với nhiều dòng văn bản.", "e-form.sampleFile": "tep-mau.pdf", "e-form.sampleValue": "<PERSON><PERSON><PERSON> trị mẫu", "e-form.invalidJsonInExampleData": "Định dạng JSON không hợp lệ trong dữ liệu mẫu", "e-form.errorGeneratingFormFromApi": "Có lỗi xảy ra khi tạo form từ API endpoint", "e-form.pleaseSelectJsonFile": "<PERSON>ui lòng chọn file JSON", "e-form.invalidJsonFile": "File JSON không hợp lệ", "e-form.pleaseEnterApiUrl": "<PERSON><PERSON> lòng nhập URL API", "e-form.errorLoadingApiFromUrl": "Có lỗi xảy ra khi tải API từ URL", "e-form.notValidSwaggerOpenApi": "Không phải là file Swagger/OpenAPI hợp lệ", "e-form.swaggerMustHavePaths": "File Swagger ph<PERSON>i có phần paths", "e-form.swaggerMustBeValidJson": "Swagger/OpenAPI phải là JSON hợp lệ", "e-form.invalidGraphqlIntrospection": "GraphQL Introspection result kh<PERSON>ng hợp lệ", "e-form.invalidGraphqlSchema": "GraphQL Schema object không hợp lệ", "e-form.graphqlJsonMustHaveValidStructure": "GraphQL JSON phải có cấu trúc Introspection hợp lệ", "e-form.invalidGraphqlJson": "GraphQL JSON không hợp lệ", "e-form.graphqlSdlMustHaveType": "GraphQL SDL phải có ít nhất một type Query, Mutation hoặc Subscription", "e-form.apiTypeNotSupported": "Loại API không được hỗ trợ", "e-form.pleaseEnterApiName": "<PERSON><PERSON> lòng nhập tên <PERSON>", "e-form.pleaseEnterOrUploadSwaggerJson": "<PERSON><PERSON> lòng nhập hoặc tải lên file Swagger JSON", "e-form.errorSavingApi": "Có lỗi xảy ra khi lưu API", "e-form.pleaseDropJsonFile": "<PERSON><PERSON> lòng thả file JSON", "e-form.importApiSchema": "Import API Schema", "e-form.apiName": "Tên API", "e-form.enterApiName": "Nhập tên API...", "e-form.apiType": "Loại API", "e-form.selectApiType": "Chọn loại API", "e-form.swaggerOpenApi": "Swagger/OpenAPI", "e-form.graphql": "GraphQL", "e-form.enterApiDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả API...", "e-form.manageApi": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "e-form.importFromUrl": "Import từ URL", "e-form.uploadFile": "Upload File", "e-form.pasteJson": "Paste JSON", "e-form.enterPathToSwaggerOrGraphql": "<PERSON><PERSON><PERSON><PERSON> đường dẫn tới Swagger JSON hoặc GraphQL Schema để tự động tải về", "e-form.loading": "<PERSON><PERSON> tả<PERSON>...", "e-form.download": "<PERSON><PERSON><PERSON> về", "e-form.uploadSwaggerFile": "Upload Swagger/OpenAPI File", "e-form.uploadGraphqlFile": "Upload GraphQL Schema File", "e-form.selectSwaggerJsonFile": "Chọn file JSON chứa Swagger hoặc OpenAPI schema", "e-form.selectGraphqlJsonFile": "Chọn file JSON chứa GraphQL Schema (SDL hoặc Introspection result)", "e-form.dropJsonOrClick": "Thả file JSON vào đây hoặc click để chọn", "e-form.supportSwagger": "Hỗ trợ Swagger 2.0 và OpenAPI 3.0+", "e-form.supportGraphql": "Hỗ trợ GraphQL Schema (SDL hoặc Introspection)", "e-form.pasteSwaggerContent": "Paste Swagger/OpenAPI Content", "e-form.pasteGraphqlContent": "Paste GraphQL Schema Content", "e-form.pasteSwaggerJsonHere": "<PERSON><PERSON> nội dung Swagger/OpenAPI JSON vào đây", "e-form.pasteGraphqlSchemaHere": "Dán GraphQL Schema (SDL hoặc Introspection JSON) vào đây", "e-form.pasteSwaggerJsonPlaceholder": "<PERSON><PERSON>/OpenAPI JSON vào đây...", "e-form.pasteGraphqlSchemaPlaceholder": "Dán GraphQL Schema vào đây...", "e-form.cancel": "<PERSON><PERSON><PERSON>", "e-form.saveApi": "Lưu API", "e-form.close": "Đ<PERSON><PERSON>", "e-form.addNewApi": "Thêm API mới", "e-form.apiSchemas": "API Schemas", "e-form.searchApi": "Tìm kiếm API...", "e-form.noApiYet": "Chưa có API nào", "e-form.editApi": "Chỉnh sửa API", "e-form.syncFromUrl": "Sync từ URL", "e-form.endpoints": "endpoints", "e-form.searchEndpointOrDescription": "<PERSON><PERSON><PERSON> kiếm endpoint hoặc mô tả...", "e-form.noEndpointFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy endpoint n<PERSON>o phù hợp với", "e-form.use": "Sử dụng", "e-form.descriptionLabel": "<PERSON><PERSON> tả:", "e-form.graphqlOperation": "GraphQL Operation:", "e-form.query": "Query:", "e-form.mutation": "Mutation:", "e-form.subscription": "Subscription:", "e-form.arguments": "Arguments:", "e-form.parameters": "Parameters:", "e-form.requestBody": "Request Body:", "e-form.variables": "Variables:", "e-form.selectApiFromList": "<PERSON><PERSON><PERSON> một API từ danh sách để xem chi tiết", "e-form.confirmDeleteApi": "Xác n<PERSON>n xóa API", "e-form.confirmDeleteApiMessage": "Bạn có chắc chắn muốn xóa API \"{name}\"? Hành động này không thể hoàn tác.", "e-form.delete": "Xóa", "e-form.syncApiFromUrl": "Sync API từ URL", "e-form.confirmSyncApiMessage": "Bạn có muốn cập nhật API \"{name}\" từ URL không?", "e-form.url": "URL:", "e-form.syncWarning": "L<PERSON>u ý: <PERSON><PERSON> t<PERSON> nà<PERSON> sẽ ghi đè lên schema và endpoints hiện tại.", "e-form.syncing": "Đang sync...", "e-form.syncNow": "Sync ngay", "e-form.thisFieldIsRequiredValidation": "Trư<PERSON>ng này là bắ<PERSON> buộc", "e-form.validationRules": "<PERSON><PERSON> tắc x<PERSON>c thực", "e-form.noValidationRulesDefined": "<PERSON><PERSON><PERSON> có quy tắc xác thực nào.", "e-form.rule": "<PERSON><PERSON>", "e-form.type": "<PERSON><PERSON><PERSON>", "e-form.email": "Email", "e-form.regexPattern": "Mẫu Regex", "e-form.minValueLength": "<PERSON><PERSON><PERSON> trị/<PERSON><PERSON> dài tối thiểu", "e-form.maxValueLength": "<PERSON><PERSON><PERSON> trị/<PERSON><PERSON> dài tối đa", "e-form.custom": "<PERSON><PERSON><PERSON> chỉnh", "e-form.regexPlaceholder": "^[A-Za-z0-9]+$", "e-form.customValidationPlaceholder": "<PERSON><PERSON><PERSON> thực tùy chỉnh", "e-form.valuePlaceholder": "<PERSON><PERSON><PERSON> trị", "e-form.errorMessage": "<PERSON>h<PERSON>ng báo lỗi", "e-form.errorMessagePlaceholder": "<PERSON>h<PERSON>ng báo lỗi", "e-form.addValidationRule": "<PERSON>hê<PERSON> quy tắc xác thực"}