'use client'

import { FormBuilder } from '@/app/(full-screen)/forms/e-form/form-builder'
import { useEffect, useState } from 'react'

import './e-form.css'

function SafeHydrate({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)
  useEffect(() => setMounted(true), [])

  if (!mounted) {
    return null
  }

  return <>{children}</>
}

export default function FormsPage() {
  return (
    <div className="e-form-container">
      <SafeHydrate>
        <FormBuilder />
      </SafeHydrate>
    </div>
  )
}
