'use client'

import { useDeleteProcess } from '@/lib/hooks'
import { Process, PROCESS_IS_DEFAULT, PROCESS_STATUS, processesApi } from '@/lib/services/processes-api'
import { AnyRecord } from '@/lib/types'
import { useQueryClient } from '@tanstack/react-query'
import { type ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { toast } from '@workspace/ui/components/toast'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { AdminPageContent, AdminPageLayout, Button, DataTable } from '@workspace/ui/mi'
import { AddButton } from '@workspace/ui/mi/add-button'
import { TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { Edit, PlusIcon, Trash2 } from 'lucide-react'
import { redirect, useRouter } from 'next/navigation'
import { useState } from 'react'

// Component for text with tooltip
const TextWithTooltip = ({ text, maxLength = 30 }: { text: string; maxLength?: number }) => {
  if (!text) return <span>-</span>

  if (text.length <= maxLength) {
    return <span>{text}</span>
  }

  return <span title={text}>{text.substring(0, maxLength)}...</span>
}

export function ProcessesListPage() {
  const t = useTranslations('processes')
  const queryClient = useQueryClient()
  const router = useRouter()

  // State for dialog management
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedProcess, setSelectedProcess] = useState<Process | null>(null)

  const deleteMutation = useDeleteProcess()

  // Function to delete a process
  const handleDeleteProcess = async () => {
    if (!selectedProcess) return

    try {
      await deleteMutation.mutateAsync(selectedProcess.id)
      toast.success(t('messages.delete.success'))
      queryClient.invalidateQueries({
        queryKey: ['processes'],
      })
      setIsDeleteDialogOpen(false)
      setSelectedProcess(null)
    } catch (error) {
      console.error('Error deleting process:', error)
      toast.error(t('messages.delete.error'))
    }
  }

  // Table columns
  const columns: ColumnDef<Process>[] = [
    {
      accessorKey: 'code',
      header: t('table.columns.code'),
      meta: {
        title: t('table.columns.code'),
      },
      cell: ({ getValue }) => (
        <div className="font-medium">
          <TextWithTooltip text={getValue() as string} maxLength={20} />
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: t('table.columns.name'),
      meta: {
        title: t('table.columns.name'),
      },
      cell: ({ getValue }) => (
        <div>
          <TextWithTooltip text={getValue() as string} maxLength={40} />
        </div>
      ),
    },
    {
      accessorKey: 'orgUnit',
      header: t('table.columns.orgUnit'),
      meta: {
        title: t('table.columns.orgUnit'),
      },
      cell: ({ row }) => {
        const orgUnit = row.original.orgUnit
        const orgUnitName = orgUnit?.name || row.original.orgUnitId || '-'

        return (
          <div className="text-muted-foreground text-sm">
            <TextWithTooltip text={orgUnitName} maxLength={25} />
          </div>
        )
      },
    },
    {
      accessorKey: 'status',
      header: t('table.columns.status'),
      meta: {
        title: t('table.columns.status'),
        filter: {
          type: 'select',
          // position: 'advanced',
          placeholder: 'Chọn trạng thái',
          options: PROCESS_STATUS.map(status => ({
            value: status.value.toString(),
            label: status.label,
          })),
        },
      },
      cell: ({ getValue }) => {
        const status = getValue() as number

        return <Badge variant={status === 1 ? 'default' : 'secondary'}>{t(`status.${status}`)}</Badge>
      },
    },
    {
      accessorKey: 'isDefault',
      header: t('table.columns.isDefault'),
      meta: {
        title: t('table.columns.isDefault'),
        filter: {
          type: 'select',
          // position: 'advanced',
          placeholder: 'Chọn mặc định',
          options: PROCESS_IS_DEFAULT.map(isDefault => ({
            value: isDefault.value.toString(),
            label: isDefault.label,
          })),
        },
      },
      cell: ({ getValue }) => {
        const isDefault = getValue() as number

        return <Badge variant={isDefault === 1 ? 'default' : 'outline'}>{isDefault === 1 ? 'Có' : 'Không'}</Badge>
      },
    },
    {
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      header: t('table.columns.actions'),
      meta: {
        title: t('table.columns.actions'),
      },
      cell: ({ row }) => {
        const process = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                router.push(`/processes/edit?id=${process.id}`)
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedProcess(process)
                setIsDeleteDialogOpen(true)
              }}
            >
              <Trash2 className="text-destructive h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  // Use enhanced table with built-in search functionality
  const { table } = useEnhancedTable({
    columns,
    pageName: 'processes',
    queryFn: async state => {
      // Build filter object from state.filter
      const filterObj: AnyRecord = {}

      state.filter.forEach(filter => {
        if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
          // Handle different filter value types
          if (typeof filter.value === 'string') {
            // Direct string value
            filterObj[filter.id] = parseInt(filter.value)
          } else if (typeof filter.value === 'number') {
            // Direct number value
            filterObj[filter.id] = filter.value
          } else if (Array.isArray(filter.value) && filter.value.length > 0) {
            // Array of values (multi-select)
            const firstValue = filter.value[0]

            if (typeof firstValue === 'string') {
              filterObj[filter.id] = parseInt(firstValue)
            } else if (typeof firstValue === 'object' && 'value' in firstValue) {
              filterObj[filter.id] = parseInt(firstValue.value as string)
            }
          } else if (typeof filter.value === 'object' && 'value' in filter.value) {
            // Combobox object { value, label }
            filterObj[filter.id] = parseInt(filter.value.value as string)
          }
        }
      })

      const params = {
        page: state.pagination.pageIndex + 1,
        pageSize: state.pagination.pageSize,
        orderBy: state.sorting[0]?.id || 'name',
        orderDir: (state.sorting[0]?.desc ? 'DESC' : 'ASC') as 'ASC' | 'DESC',
        keyword: state.keyword || undefined,
        filter: filterObj,
      }

      console.log('Final API params:', params)

      const response = await processesApi.getList(params)

      return {
        items: response.data || [],
        totalPages: response.pagination?.totalPages || 0,
        totalItems: response.pagination?.total || 0,
      }
    },
  })

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Trang chủ', href: '/dashboard' },
        { label: t('title'), href: '/processes' },
      ]}
    >
      <AdminPageContent
        title={t('title')}
        subtitle={t('description')}
        actions={[
          <AddButton
            tooltip={t('actions.create')}
            key="create"
            icon={<PlusIcon className="h-4 w-4" />}
            onClick={() => redirect('/processes/create')}
          />,
        ]}
      >
        <DataTable table={table} />
      </AdminPageContent>

      {/* Delete Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('messages.deleteConfirm')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('messages.deleteWarning')}
              {selectedProcess && (
                <div className="mt-2 font-medium">
                  {selectedProcess.name} ({selectedProcess.code})
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setIsDeleteDialogOpen(false)
                setSelectedProcess(null)
              }}
            >
              {t('form.buttons.cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProcess}
              className="bg-destructive hover:bg-destructive/90 text-white"
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? 'Đang xóa...' : t('actions.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminPageLayout>
  )
}
