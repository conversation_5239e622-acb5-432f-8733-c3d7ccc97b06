'use client'

import { caseAdvanceActionLabels } from '@/constants/case-advance'
import { getMyWorksStats, MyWorksStatsResponse } from '@/lib/services/workflow-api'
import { IHanhDongHoSo, RHanhDongHoSo } from '@/lib/workflow/shared'
import { useUserStoreActions } from '@/stores'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { cn } from '@workspace/ui/lib/utils'
import { AdminPageContent, AdminPageLayout, Button } from '@workspace/ui/mi'
import { EyeIcon } from 'lucide-react'
import Link from 'next/link'

export default function Pm01DashboardPage() {
  const t = useTranslations()
  const { hasPermission, permissions } = useUserStoreActions()
  console.log(permissions, 'permissions')
  const { data, isLoading } = useQuery<MyWorksStatsResponse>({
    queryKey: ['my-works-stats'],
    staleTime: 0,
    queryFn: getMyWorksStats,
  })

  // Tạo danh sách tất cả các actions từ RHanhDongHoSo
  const excludedActions = new Set<IHanhDongHoSo>([
    RHanhDongHoSo.TU_CHOI,
    RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG,
    RHanhDongHoSo.YEU_CAU_CD_NOP_BO_SUNG,
  ])

  const allActions = (Object.keys(RHanhDongHoSo) as IHanhDongHoSo[])
    .filter(action => action !== RHanhDongHoSo.YEU_CAU_THAM_MUU)
    .filter(action => !excludedActions.has(action))

  // Tạo link từ action
  const createLinkFromAction = (action: IHanhDongHoSo) => {
    const linkPath = action.toLowerCase().replaceAll('_', '-')

    return `/case-advance/my-works/${linkPath}`
  }

  return (
    <AdminPageLayout dashboardHref="/pm01/dashboard" breadcrumb={[]}>
      <AdminPageContent
        title={t('dashboard.pm01.page.title')}
        subtitle={t('dashboard.pm01.page.subtitle')}
        actions={
          [
            //   <Button key="create" onClick={() => router.push('/processes/create')} size="sm">
            //     <Plus className="h-4 w-4" />
            //     {t('actions.create')}
            //   </Button>,
          ]
        }
      >
        {/* Loading state */}
        {isLoading && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-3">
                  <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                  <div className="h-3 w-1/2 rounded bg-gray-200"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 w-1/3 rounded bg-gray-200"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Work stats cards */}
        {!isLoading && data && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {allActions
              .filter(action => hasPermission(`PM01_WORKFLOW_${action}`))
              .map((action, index) => {
                const count = data[action] || 0
                const label = caseAdvanceActionLabels[action] || action
                const link = createLinkFromAction(action)

                let color = 'border-l-blue-500'

                if (index % 6 === 1) {
                  color = 'border-l-emerald-500'
                } else if (index % 6 === 2) {
                  color = 'border-l-orange-500'
                } else if (index % 6 === 3) {
                  color = 'border-l-sky-500'
                } else if (index % 6 === 4) {
                  color = 'border-l-purple-800'
                } else if (index % 6 === 5) {
                  color = 'border-l-indigo-500'
                }

                return (
                  <Card key={action} className={cn('border-l-4', color)}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{label}</CardTitle>
                      <Button asChild variant="outline" size="sm" className="shadow-none">
                        <Link href={link}>
                          <EyeIcon /> Xem
                        </Link>
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-gray-700">{count}</div>
                      <p className="text-muted-foreground text-xs">đang chờ xử lý</p>
                    </CardContent>
                  </Card>
                )
              })}
          </div>
        )}

        {/* Error state */}
        {!isLoading && !data && (
          <Card className="py-8 text-center">
            <CardContent>
              <p className="text-muted-foreground">Không thể tải dữ liệu công việc. Vui lòng thử lại sau.</p>
            </CardContent>
          </Card>
        )}
      </AdminPageContent>
    </AdminPageLayout>
  )
}
