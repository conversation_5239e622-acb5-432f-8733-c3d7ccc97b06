import { EnumCaseAdvancedStatus, mapCaseAdvancedStatusText } from '@/lib/types'

/**
 * Helper function to get status text by status value
 * @param status - The status value
 * @returns The corresponding status text
 */
export function getCaseAdvancedStatusText(status: EnumCaseAdvancedStatus): string {
  return mapCaseAdvancedStatusText[status] || 'Không xác định'
}

/**
 * Helper function to get all status options for dropdowns
 * @returns Array of status options with value and label
 */
export function getCaseAdvancedStatusOptions() {
  return Object.entries(mapCaseAdvancedStatusText).map(([value, label]) => ({
    value: parseInt(value),
    label,
  }))
}
