'use client'

import { useCaseAdvanceDetail, useCategoryDetailQuery } from '@/lib/hooks'
import { GENDER_LABELS } from '@/lib/types'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip'
import { AdminPageLayout, Button, Card, CardContent, CardFooter, CardHeader, CardTitle } from '@workspace/ui/mi'
import { ArrowLeft, ChevronDown, Edit, Eye, FileText, MoreHorizontal } from 'lucide-react'
import { useParams, useRouter } from 'next/navigation'
import { useState } from 'react'

import { EditCaseForm } from '../edit-case-form'

// Hook for getting category name by ID
const useCategoryName = (categoryId?: string) => {
  const { data: category } = useCategoryDetailQuery(categoryId || '', !!categoryId)

  return category?.name
}

// Component for displaying field information
const FieldDisplay = ({
  label,
  value,
  className = '',
}: {
  label: string
  value: string | number | null | undefined
  className?: string
}) => {
  const displayValue = value || 'Chưa cập nhật'
  const isLongText = String(displayValue).length > 30
  const isEmptyValue = !value

  return (
    <div className={`flex items-start justify-between border-b border-gray-100 py-3 last:border-b-0 ${className}`}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="min-w-0 flex-1 cursor-help pr-4 text-sm text-gray-600">{label}</span>
          </TooltipTrigger>
          {label.length > 25 && (
            <TooltipContent>
              <p>{label}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span
              className={`min-w-0 flex-1 text-right text-sm ${
                isEmptyValue ? 'text-gray-400 italic' : 'font-medium text-gray-900'
              } ${isLongText ? 'cursor-help truncate' : ''}`}
            >
              {displayValue}
            </span>
          </TooltipTrigger>
          {isLongText && (
            <TooltipContent>
              <p className="max-w-xs">{displayValue}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}

// Component for section headers
const SectionHeader = ({ title }: { title: string }) => (
  <div className="px-4 py-3 text-base font-bold text-blue-600">{title}</div>
)

// Component for expandable section content
const SectionContent = ({
  children,
  expandedContent,
  isExpanded,
  onToggle,
}: {
  children: React.ReactNode
  expandedContent?: React.ReactNode
  isExpanded: boolean
  onToggle: () => void
}) => (
  <div className="bg-white px-4 pb-4">
    <div className="space-y-0">{children}</div>
    {expandedContent && isExpanded && <div className="mt-0 space-y-0 border-t border-gray-100">{expandedContent}</div>}
    {expandedContent && (
      <div className="flex justify-center border-t border-gray-100 py-3">
        <button
          onClick={onToggle}
          className="flex items-center gap-1 text-sm text-gray-900 transition-colors hover:text-gray-700"
        >
          Xem thêm
          <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
        </button>
      </div>
    )}
  </div>
)

export default function CaseAdvanceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [isEditMode, setIsEditMode] = useState(false)
  const [expandedSections, setExpandedSections] = useState<{
    caseInfo: boolean
    requestInfo: boolean
    personInfo: boolean
  }>({
    caseInfo: false,
    requestInfo: false,
    personInfo: false,
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const caseId = params.id as string

  const { data: caseDetail, isLoading, error } = useCaseAdvanceDetail(caseId, !!caseId)

  // Get category names for various fields
  const formsOfAssistanceName = useCategoryName(caseDetail?.formsOfAssistanceId)
  const legalFieldName = useCategoryName(caseDetail?.legalFieldId)
  const objectLegalFieldName = useCategoryName(caseDetail?.objectLegalFieldId)
  const guardianPublishedPlaceName = useCategoryName(caseDetail?.guardianPublishedPlace)
  const guardianProvinceName = useCategoryName(caseDetail?.guardianProvince)
  const guardianWardName = useCategoryName(caseDetail?.guardianWard)
  const rpEthnicName = useCategoryName(caseDetail?.rpEthnic)
  const rpProvinceName = useCategoryName(caseDetail?.rpProvince)
  const rpWardName = useCategoryName(caseDetail?.rpWard)

  if (isLoading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Danh sách vụ việc', href: '/case-advance' },
          { label: 'Chi tiết vụ việc', href: `/case-advance/${caseId}` },
        ]}
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground text-sm">Đang tải...</div>
        </div>
      </AdminPageLayout>
    )
  }

  if (error || !caseDetail) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Danh sách vụ việc', href: '/case-advance' },
          { label: 'Chi tiết vụ việc', href: `/case-advance/${caseId}` },
        ]}
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-red-500">Không thể tải thông tin vụ việc. Vui lòng thử lại.</div>
        </div>
      </AdminPageLayout>
    )
  }

  if (isEditMode) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Danh sách vụ việc', href: '/case-advance' },
          { label: 'Chi tiết vụ việc', href: `/case-advance/${caseId}` },
          { label: 'Chỉnh sửa', href: `/case-advance/${caseId}/edit` },
        ]}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Chỉnh sửa vụ việc: {caseDetail.code}
              </CardTitle>
              <Button variant="outline" onClick={() => setIsEditMode(false)}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại xem
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <EditCaseForm
              initialData={caseDetail}
              onSuccess={() => {
                setIsEditMode(false)
                // Refresh data
                window.location.reload()
              }}
              onCancel={() => setIsEditMode(false)}
            />
          </CardContent>
        </Card>
      </AdminPageLayout>
    )
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Danh sách vụ việc', href: '/case-advance' },
        { label: 'Chi tiết vụ việc', href: `/case-advance/${caseId}` },
      ]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <button onClick={() => router.back()} className="hover:text-primary transition-colors">
            <ArrowLeft className="h-5 w-5" />
          </button>
          <span className="text-lg font-semibold text-gray-900">Thông tin chi tiết vụ việc</span>
        </div>

        {/* Main Card containing all information */}
        <Card className="flex flex-col overflow-hidden rounded-lg p-0 shadow-sm">
          {/* Case Header Info */}
          <div className="border-b border-gray-200 bg-gray-50 p-4">
            <div className="flex items-start justify-between">
              <div>
                <div className="mb-1 text-lg font-semibold text-gray-900">
                  Mã vụ việc: {caseDetail.code || 'DDNTT.03.2025'}
                </div>
                <div className="text-sm text-gray-600">
                  Hình thức trợ giúp: {formsOfAssistanceName || 'Tư vấn pháp luật'}
                </div>
                {caseDetail.isOldLegalAidCase === 1 && (
                  <div className="mt-1 flex items-center gap-2">
                    <input type="checkbox" checked={true} readOnly className="rounded" />
                    <span className="text-sm text-gray-600">Là hồ sơ cũ</span>
                  </div>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => router.push(`/case-advance/${caseId}/edit`)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Chỉnh sửa
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* Left Column */}
              <div className="space-y-6">
                {/* Thông tin vụ việc TGPL */}
                <div>
                  <SectionHeader title="Thông tin vụ việc TGPL" />
                  <SectionContent
                    isExpanded={expandedSections.caseInfo}
                    onToggle={() => toggleSection('caseInfo')}
                    expandedContent={
                      <>
                        <FieldDisplay label="Người thực hiện TGPL" value={caseDetail.performer} />
                        <FieldDisplay label="Địa điểm thực hiện" value={caseDetail.placeOfDisposal} />
                      </>
                    }
                  >
                    <FieldDisplay label="Tên vụ việc" value={caseDetail.name} />
                    <FieldDisplay
                      label="Ngày thu lý"
                      value={
                        caseDetail.entryDate ? new Date(caseDetail.entryDate).toLocaleDateString('vi-VN') : undefined
                      }
                    />
                    <FieldDisplay label="Lĩnh vực" value={legalFieldName} />
                    <FieldDisplay label="Nguồn yêu cầu" value={caseDetail.rpRequestSource} />
                  </SectionContent>
                </div>

                {/* Nội dung yêu cầu TGPL */}
                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Nội dung yêu cầu TGPL</div>
                  <div className="px-4 pb-4">
                    <div className={`text-sm ${caseDetail.contentRequest ? 'text-gray-900' : 'text-gray-400 italic'}`}>
                      {caseDetail.contentRequest || 'Chưa cập nhật'}
                    </div>
                  </div>
                </div>

                {/* Nội dung tư vấn pháp luật */}
                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Nội dung tư vấn pháp luật</div>
                  <div className="px-4 pb-4">
                    <div className={`text-sm ${caseDetail.content ? 'text-gray-900' : 'text-gray-400 italic'}`}>
                      {caseDetail.content || 'Chưa cập nhật'}
                    </div>
                  </div>
                </div>

                {/* Tài liệu kèm theo đơn */}
                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Tài liệu kèm theo đơn</div>
                  <div className="px-4 pb-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={caseDetail.attachForRequestPerson}
                          readOnly
                          className="rounded"
                        />
                        <span className="text-sm text-gray-900">
                          Giấy tờ chứng minh người thuộc diện trợ giúp pháp lý
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" checked={caseDetail.attachForLegalCase} readOnly className="rounded" />
                        <span className="text-sm text-gray-900">
                          Các giấy tờ, tài liệu có liên quan đến vụ việc trợ giúp pháp lý
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" checked={caseDetail.isNotFullProfile} readOnly className="rounded" />
                        <span className="text-sm text-gray-900">Chưa đầy đủ hồ sơ</span>
                      </div>
                      {caseDetail.documentAttachName && (
                        <div className="mt-3 flex items-center gap-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-blue-600">{caseDetail.documentAttachName}</span>
                          <button className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800">
                            <Eye className="h-4 w-4" />
                            Xem
                          </button>
                        </div>
                      )}
                      {!caseDetail.documentAttachName && (
                        <div className="mt-3 text-sm text-gray-400 italic">Chưa có tài liệu đính kèm</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Ghi chú */}
                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Ghi chú</div>
                  <div className="px-4 pb-4">
                    <div className={`text-sm ${caseDetail.note ? 'text-gray-900' : 'text-gray-400 italic'}`}>
                      {caseDetail.note || 'Chưa cập nhật'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Thông tin người yêu cầu TGPL */}
                <div>
                  <SectionHeader title="Thông tin người yêu cầu TGPL" />
                  <SectionContent
                    isExpanded={expandedSections.requestInfo}
                    onToggle={() => toggleSection('requestInfo')}
                    expandedContent={
                      <>
                        <FieldDisplay label="Nơi cấp" value={guardianPublishedPlaceName} />
                        <FieldDisplay label="Địa chỉ" value={caseDetail.guardianAddress} />
                        <FieldDisplay label="Tỉnh/TP" value={guardianProvinceName} />
                        <FieldDisplay label="Phường/Xã" value={guardianWardName} />
                      </>
                    }
                  >
                    <FieldDisplay label="Họ tên người yêu cầu" value={caseDetail.guardianName} />
                    <FieldDisplay label="Số CMND/CCCD" value={caseDetail.guardianCardNumber} />
                    <FieldDisplay
                      label="Ngày cấp CMND"
                      value={
                        caseDetail.guardianPublishedDate
                          ? new Date(caseDetail.guardianPublishedDate).toLocaleDateString('vi-VN')
                          : undefined
                      }
                    />
                    <FieldDisplay label="Điện thoại" value={caseDetail.guardianPhone} />
                    <FieldDisplay label="Mối quan hệ với người được TGPL" value={caseDetail.relationRp} />
                  </SectionContent>
                </div>

                {/* Thông tin người được TGPL */}
                <div>
                  <SectionHeader title="Thông tin người được TGPL" />
                  <SectionContent
                    isExpanded={expandedSections.personInfo}
                    onToggle={() => toggleSection('personInfo')}
                    expandedContent={
                      <>
                        <FieldDisplay
                          label="Ngày sinh"
                          value={
                            caseDetail.rpBirthDate
                              ? new Date(caseDetail.rpBirthDate).toLocaleDateString('vi-VN')
                              : undefined
                          }
                        />
                        <FieldDisplay label="Dân tộc" value={rpEthnicName} />
                        <FieldDisplay label="Địa chỉ" value={caseDetail.rpAddress} />
                        <FieldDisplay label="Tỉnh/Thành phố" value={rpProvinceName} />
                        <FieldDisplay label="Phường/Xã" value={rpWardName} />
                        <FieldDisplay label="Nghề nghiệp" value={caseDetail.rpJob} />
                        <FieldDisplay label="Email" value={caseDetail.rpEmail} />
                      </>
                    }
                  >
                    <FieldDisplay label="Họ tên" value={caseDetail.rpName || 'Nguyễn Văn Võ Song Toàn'} />
                    <FieldDisplay
                      label="Giới tính"
                      value={GENDER_LABELS[caseDetail.rpSex as keyof typeof GENDER_LABELS] || 'Nam'}
                    />
                    <FieldDisplay label="Thuộc diện người" value={objectLegalFieldName} />
                    <FieldDisplay label="Số căn cước" value={caseDetail.rpCardNumber} />
                  </SectionContent>
                </div>
              </div>
            </div>
          </CardContent>

          {/* Footer with creation date */}
          <CardFooter className="bg-gray-100 py-6 text-left text-sm text-gray-700">
            Ngày cập nhật:{' '}
            {caseDetail.updatedAt
              ? new Date(caseDetail.updatedAt).toLocaleDateString('vi-VN')
              : caseDetail.modifyDate
                ? new Date(caseDetail.modifyDate).toLocaleDateString('vi-VN')
                : 'Chưa cập nhật'}
          </CardFooter>
        </Card>
      </div>
    </AdminPageLayout>
  )
}
