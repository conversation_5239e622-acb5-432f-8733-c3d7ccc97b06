'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { Loader2 } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

// Base schema cho tất cả workflow forms
const baseWorkflowFormSchema = z.object({
  content: z.string().min(1, 'Nội dung là bắt buộc'),
  note: z.string().optional(),
})

export type BaseWorkflowFormValues = z.infer<typeof baseWorkflowFormSchema>

interface BaseWorkflowFormProps {
  title: string
  contentLabel?: string
  contentPlaceholder?: string
  noteLabel?: string
  notePlaceholder?: string
  submitLabel?: string
  onSubmit: (values: BaseWorkflowFormValues) => Promise<void>
  onCancel: () => void
  initialData?: Partial<BaseWorkflowFormValues>
}

export function BaseWorkflowForm({
  contentLabel = 'Nội dung',
  contentPlaceholder = 'Nhập nội dung...',
  noteLabel = 'Ghi chú',
  notePlaceholder = 'Nhập ghi chú (tùy chọn)...',
  submitLabel = 'Xác nhận',
  onSubmit,
  onCancel,
  initialData,
}: BaseWorkflowFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<BaseWorkflowFormValues>({
    resolver: zodResolver(baseWorkflowFormSchema),
    defaultValues: {
      content: initialData?.content || '',
      note: initialData?.note || '',
    },
  })

  const handleSubmit = async (values: BaseWorkflowFormValues) => {
    setIsSubmitting(true)

    try {
      await onSubmit(values)
    } catch (error) {
      console.error('Error submitting workflow form:', error)
      toast.error('Có lỗi xảy ra khi thực hiện thao tác')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{contentLabel}</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder={contentPlaceholder} rows={4} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{noteLabel}</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder={notePlaceholder} rows={3} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Hủy
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {submitLabel}
          </Button>
        </div>
      </form>
    </Form>
  )
}
