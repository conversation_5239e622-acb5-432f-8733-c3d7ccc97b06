'use client'

import { RenderColumn } from '@/components/render-column'
import { RenderCopyText } from '@/components/render-copy-text'
import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderText } from '@/components/render-text'
import { MODEL_STATUS } from '@/constants'
import { caseAdvanceActionLabels } from '@/constants/case-advance'
import { getMyWorksByAction, postNhanTask, postXyLyTask } from '@/lib/services/workflow-api'
import { WorkflowHistory, WorkflowHistoryStatus, WorkflowHistoryStatusLabel } from '@/lib/types'
import { IHanhDongHoSo, LHanhDongHoSo, RHanhDongHoSo } from '@/lib/workflow/shared'
import { ColumnDef } from '@tanstack/react-table'
import { Button } from '@workspace/ui/components/button'
import { Dialog, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@workspace/ui/components/dialog'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { toast } from '@workspace/ui/lib/toast'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { BackToPageButton } from '@workspace/ui/mi/back-to-page-button'
import { RenderUser } from '@workspace/ui/mi/render-user'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import {
  ArrowDownToLine,
  Check,
  ClipboardListIcon,
  ClipboardPenIcon,
  ClockIcon,
  CornerDownRightIcon,
  Edit,
  InfoIcon,
  MessageCircleMore,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams, useRouter } from 'next/navigation'
import { Fragment, useState } from 'react'

import {
  BaseWorkflowFormValues,
  DongTgplForm,
  DongYTiepNhanForm,
  DuyetKetQuaTgplForm,
  DuyetPhanCongTgplForm,
  DuyetThamDinhTgplForm,
  NhapKetQuaTgplForm,
  NhapKqThamDinhTgplChatLuongForm,
  NhapKqThamDinhTgplThoiGianForm,
  NhapKqThamMuuForm,
  NopLuuTruTgplForm,
  PhanCongThamDinhTgplForm,
  YeuCauDuyetPhanCongTgplForm,
  YeuCauThamMuuForm,
} from '../../workflow-forms'

export default function Pm01DashboardPage() {
  const router = useRouter()
  const params = useParams()
  const t = useTranslations()
  const action = (params.action as string).toUpperCase().replaceAll('-', '_') as IHanhDongHoSo

  // Dialog states
  const [selectedItem, setSelectedItem] = useState<WorkflowHistory | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [dialogAction, setDialogAction] = useState<IHanhDongHoSo | null>(null)

  // Handle opening dialog with selected item
  const handleOpenDialog = (item: WorkflowHistory, action?: IHanhDongHoSo) => {
    setDialogAction(action || null)
    setSelectedItem(item)
    setIsDialogOpen(true)
  }

  // Handle form submission
  const handleSubmit = async (values: BaseWorkflowFormValues) => {
    try {
      if (!selectedItem) {
        return
      }

      await postXyLyTask(
        selectedItem.caseAdvancedId,
        selectedItem.id,
        (dialogAction || (params.action as string)).toLowerCase().replaceAll('_', '-'),
        values
      )
      // TODO: check response by use hook use-workflow-api
      toast.success('Thực hiện thao tác thành công')
      setIsDialogOpen(false)
      setSelectedItem(null)
      setDialogAction(null)
      table.options.meta?.reload?.()
    } catch (error) {
      console.error('Error submitting workflow action:', error)
      toast.error('Có lỗi xảy ra khi thực hiện thao tác')
    }
  }

  const columns: ColumnDef<WorkflowHistory>[] = [
    selectColumn(t),
    {
      id: 'caseAdvanced.code',
      accessorKey: 'caseAdvanced.code',
      enableHiding: true,
      meta: {
        showToggle: false,
        title: 'Mã vụ việc',
        filter: {
          type: 'text',
          // position: 'advanced',
          placeholder: 'Tìm kiếm mã vụ việc',
          debounceMs: 200,
        },
      },
    },
    {
      accessorKey: 'caseAdvanced.name',
      meta: {
        title: 'Vụ việc',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderInteractiveLink onClick={() => router.push(`/case-advance/${row.original.caseAdvancedId}`)}>
              {row.original.caseAdvanced?.name}
            </RenderInteractiveLink>
            <RenderCopyText text={row.original.caseAdvanced?.code} prefix="Mã" />
          </RenderColumn>
        )
      },
    },
    {
      accessorKey: 'assignedTo.name',
      meta: {
        title: 'Cán bộ xử lý',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderUser user={row.original.assignedTo} defaultValue="Chưa gán cán bộ" />
            <RenderDateTime datetime={row.original.assignedAt} showTime={true} inline={true} prefix="Lúc" />
            <RenderDateTime datetime={row.original.startedAt} showTime={true} inline={true} prefix="Bắt đầu" />
            <RenderDateTime datetime={row.original.completedAt} showTime={true} inline={true} prefix="Hoàn thành" />
            <RenderDateTime datetime={row.original.cancelledAt} showTime={true} inline={true} prefix="Từ chối" />
          </RenderColumn>
        )
      },
    },
    {
      accessorKey: 'status',
      meta: {
        title: 'Trạng thái',
        filter: {
          type: 'select',
          placeholder: 'Chọn trạng thái',
          options: MODEL_STATUS.map(status => ({
            label: t(status.label),
            value: status.value.toString(),
            icon: status.icon,
          })),
        },
      },
      cell: ({ getValue }) => {
        return (
          <RenderColumn>
            <RenderText
              text={WorkflowHistoryStatusLabel[getValue() as WorkflowHistoryStatus]}
              icon={<InfoIcon size={16} />}
              prefix="Trạng thái"
            />
          </RenderColumn>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: t('field.createdAt.label'),
        filter: {
          type: 'date-range',
        },
      },
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('createdAt')} showTime={showTime} />
      },
    },
    {
      accessorKey: 'updatedAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: t('field.updatedAt.label'),
        filter: {
          position: 'advanced',
          type: 'date-range',
        },
      },
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('updatedAt')} showTime={showTime} />
      },
    },
    {
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const rowData = row.original
        const actions = rowData.actions.filter(
          (action: string) => action !== RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG && action !== RHanhDongHoSo.TU_CHOI
        )

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            {rowData.status === 'PENDING' && !rowData.assignedTo?.id && (
              <Button
                title="Nhận xử lý"
                size="sm"
                onClick={async () => {
                  await postNhanTask(rowData.caseAdvancedId, rowData.id)
                  // TODO: check response by use hook use-workflow-api
                  toast.success('Nhận xử lý thành công')
                  table.options.meta?.reload?.()
                }}
              >
                <CornerDownRightIcon className="h-4 w-4" /> Nhận xử lý
              </Button>
            )}
            {rowData.assignedTo?.id && (
              <>
                {actions.map((action: string) => {
                  return (
                    <Fragment key={action}>
                      {action === RHanhDongHoSo.DONG_Y_TIEP_NHAN && (
                        <Button title="Tiếp nhận" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Check className="h-4 w-4" /> Tiếp nhận
                        </Button>
                      )}
                      {action === RHanhDongHoSo.YEU_CAU_THAM_MUU && (
                        <Button
                          title="Yêu cầu tham mưu"
                          size="sm"
                          onClick={() => handleOpenDialog(rowData, RHanhDongHoSo.YEU_CAU_THAM_MUU)}
                        >
                          <MessageCircleMore className="h-4 w-4" /> Tham mưu
                        </Button>
                      )}
                      {action === RHanhDongHoSo.YEU_CAU_DUYET_PHAN_CONG_TGPL && (
                        <Button title="Yêu cầu duyệt phân công" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Edit className="h-4 w-4" /> Phân công
                        </Button>
                      )}
                      {action === RHanhDongHoSo.NHAP_KQ_THAM_MUU && (
                        <Button
                          title="Nhập kết quả tham mưu"
                          size="sm"
                          onClick={() => handleOpenDialog(rowData, RHanhDongHoSo.NHAP_KQ_THAM_MUU)}
                        >
                          <Edit className="h-4 w-4" /> Nhập KQ
                        </Button>
                      )}
                      {action === RHanhDongHoSo.DUYET_PHAN_CONG_TGPL && (
                        <Button title="Duyệt phân công TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Check className="h-4 w-4" /> Duyệt
                        </Button>
                      )}
                      {action === RHanhDongHoSo.NHAP_KET_QUA_TGPL && (
                        <Button title="Nhập kết quả TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <ClipboardPenIcon className="h-4 w-4" /> Nhập KQ
                        </Button>
                      )}
                      {action === RHanhDongHoSo.DUYET_KET_QUA_TGPL && (
                        <Button title="Duyệt kết quả TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Check className="h-4 w-4" /> Duyệt KQ
                        </Button>
                      )}
                      {action === RHanhDongHoSo.PHAN_CONG_THAM_DINH_TGPL && (
                        <Button title="Phân công thẩm định TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <ClipboardListIcon className="h-4 w-4" /> Phân công TD
                        </Button>
                      )}
                      {action === RHanhDongHoSo.NOP_LUU_TRU_TGPL && (
                        <Button title="Nộp lưu trữ TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <ArrowDownToLine className="h-4 w-4" /> Lưu trữ
                        </Button>
                      )}
                      {action === RHanhDongHoSo.DUYET_THAM_DINH_TGPL && (
                        <Button title="Duyệt thẩm định TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Check className="h-4 w-4" /> Duyệt TĐ
                        </Button>
                      )}
                      {action === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN && (
                        <Button title="Nhập KQ thẩm định thời gian" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <ClockIcon className="h-4 w-4" /> KQ TG
                        </Button>
                      )}
                      {action === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG && (
                        <Button
                          title="Nhập KQ thẩm định chất lượng"
                          size="sm"
                          onClick={() => handleOpenDialog(rowData)}
                        >
                          <Edit className="h-4 w-4" /> KQ CL
                        </Button>
                      )}
                      {action === RHanhDongHoSo.DONG_TGPL && (
                        <Button title="Đóng TGPL" size="sm" onClick={() => handleOpenDialog(rowData)}>
                          <Check className="h-4 w-4" /> Đóng
                        </Button>
                      )}
                    </Fragment>
                  )
                })}
              </>
            )}
          </div>
        )
      },
    },
  ]

  const { table } = useEnhancedTable<WorkflowHistory>({
    columns,
    pageName: 'users',
    keyObject: {
      action,
    },
    queryFn: async state => {
      generateQueryParams(state, {
        baseParams: {},
      })
      const data = await getMyWorksByAction(action)
      const items = data

      return {
        items,
        totalPages: 1,
        totalItems: items.length,
      }
    },
    initialState: {
      // filter: [{ id: 'status', value: 'ACTIVE' }],
      columnVisibility: {
        'caseAdvanced.code': false,
      },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['users'],
  })

  return (
    <AdminPageLayout
      dashboardHref="/dashboard/pm01"
      breadcrumb={[
        {
          label: LHanhDongHoSo[action],
          href: `/case-advance/my-works/${action}`,
        },
      ]}
    >
      <AdminPageContent
        title={`Công việc: ${caseAdvanceActionLabels[action] || action}`}
        actions={[
          <BackToPageButton key="back" onClick={() => router.push('/case-advance')}>
            Về danh sách
          </BackToPageButton>,
        ]}
      >
        <DataTable table={table} />

        {/* Workflow dialogs */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>
                {selectedItem && `${LHanhDongHoSo[action]} - ${selectedItem.caseAdvanced?.code}`}
              </DialogTitle>
            </DialogHeader>

            {action === RHanhDongHoSo.DONG_Y_TIEP_NHAN && (
              <DongYTiepNhanForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.YEU_CAU_THAM_MUU && (
              <YeuCauThamMuuForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.YEU_CAU_DUYET_PHAN_CONG_TGPL && (
              <YeuCauDuyetPhanCongTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.NHAP_KQ_THAM_MUU && (
              <NhapKqThamMuuForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.DUYET_PHAN_CONG_TGPL && (
              <DuyetPhanCongTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.NHAP_KET_QUA_TGPL && (
              <NhapKetQuaTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.DUYET_KET_QUA_TGPL && (
              <DuyetKetQuaTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.PHAN_CONG_THAM_DINH_TGPL && (
              <PhanCongThamDinhTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.NOP_LUU_TRU_TGPL && (
              <NopLuuTruTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.DUYET_THAM_DINH_TGPL && (
              <DuyetThamDinhTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN && (
              <NhapKqThamDinhTgplThoiGianForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG && (
              <NhapKqThamDinhTgplChatLuongForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}

            {action === RHanhDongHoSo.DONG_TGPL && (
              <DongTgplForm onSubmit={handleSubmit} onCancel={() => setIsDialogOpen(false)} />
            )}
          </DialogContent>
        </Dialog>
      </AdminPageContent>
    </AdminPageLayout>
  )
}
