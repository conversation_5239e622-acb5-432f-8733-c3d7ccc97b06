'use client'

import { CATEGORY_TYPE_VALUES } from '@/app/(pm2)/categories/category-types'
import { RenderAutoText } from '@/components/render-auto-text'
import { RenderColumn } from '@/components/render-column'
import { RenderCopyText } from '@/components/render-copy-text'
import { RenderFullName } from '@/components/render-fullname'
import { RenderGender } from '@/components/render-gender'
import { RenderIdentificationId } from '@/components/render-identification-id'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderText } from '@/components/render-text'
import { caseAdvanceApi } from '@/lib/services/case-advance-api'
import { categoryApi } from '@/lib/services/category-api'
import { organizationUnitsApi } from '@/lib/services/organization-units-api'
import { postBatDauXuLy } from '@/lib/services/workflow-api'
import {
  AnyRecord,
  CaseAdvanced,
  WorkflowHistory,
  WorkflowHistoryStatus,
  WorkflowHistoryStatusLabel,
} from '@/lib/types'
import { EnumCaseAdvancedStatus, GENDER, GENDER_LABELS } from '@/lib/types'
import { RHanhDongHoSo } from '@/lib/workflow/shared'
import { type ColumnDef, createColumnHelper } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Checkbox } from '@workspace/ui/components/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'
import { toast } from '@workspace/ui/components/toast'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip'
import { useExportExcel } from '@workspace/ui/hooks/use-export-excel'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { cn } from '@workspace/ui/lib/utils'
import { AdminPageContent, AdminPageLayout, Button, DataTable, RenderDateTime } from '@workspace/ui/mi'
import { AddButton } from '@workspace/ui/mi/add-button'
import { ExportButton } from '@workspace/ui/mi/export-button'
import { RenderDate } from '@workspace/ui/mi/render-date'
import { RenderUser } from '@workspace/ui/mi/render-user'
import {
  ActivityIcon,
  CheckCircle,
  Edit,
  Eye,
  InfoIcon,
  MoreHorizontal,
  PlayIcon,
  PlusIcon,
  Trash2,
  UserCircleIcon,
} from 'lucide-react'
import Link from 'next/link'
import { redirect, useRouter } from 'next/navigation'
import { Fragment, useState } from 'react'

import CaseCompletionDialog from './components/case-completion-dialog'
import { QuaTrinhXuLy } from './components/qua-trinh-xu-ly'
import VerificationDialog from './components/workflow-subtask'
import SubtaskRecommendationDialog from './components/workflow-subtask-recommendation'
import { getCaseAdvancedStatusOptions, getCaseAdvancedStatusText } from './fn'

const getColorVariant = (status: EnumCaseAdvancedStatus) => {
  switch (status) {
    case EnumCaseAdvancedStatus.DANG_XU_LY:
      return 'warning'
    case EnumCaseAdvancedStatus.HOAN_THANH:
      return 'success'
    case EnumCaseAdvancedStatus.TU_CHOI:
      return 'destructive'
    default:
      return 'secondary'
  }
}

const columnHelper = createColumnHelper<CaseAdvanced>()

// Component for text with tooltip
const TextWithTooltip = ({ text, maxLength = 30 }: { text: string; maxLength?: number }) => {
  if (!text) return <span>-</span>

  const shouldTruncate = text.length > maxLength
  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text

  if (!shouldTruncate) {
    return <span>{text}</span>
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-help">{displayText}</span>
        </TooltipTrigger>
        <TooltipContent className="max-w-xs">
          <p>{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export function CaseAdvanceListPage() {
  const router = useRouter()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedCaseId, setSelectedCaseId] = useState<string | null>(null)
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [isCreateVerificationDialogOpen, setIsCreateVerificationDialogOpen] = useState(false)
  const [isCreateRecommendationDialogOpen, setIsCreateRecommendationDialogOpen] = useState(false)
  const [isCompletionDialogOpen, setIsCompletionDialogOpen] = useState(false)
  const [caseInfo, setCaseInfo] = useState<CaseAdvanced | null>(null)
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false)

  // Excel export hook
  const { exportToExcel } = useExportExcel()
  const [isExporting, setIsExporting] = useState(false)

  // Handler to navigate to edit page
  const handleEditCase = (caseId: string) => {
    router.push(`/case-advance/${caseId}/edit`)
  }

  const handlePrepareDeleteCase = (caseId: string) => {
    setSelectedCaseId(caseId)
    setIsDeleteDialogOpen(true)
  }

  const handlePrepareTransferCase = (caseId: string) => {
    setSelectedCaseId(caseId)
    setIsTransferDialogOpen(true)
  }

  // Handle export to Excel
  const handleExportExcel = async () => {
    try {
      setIsExporting(true)

      // Get all data without pagination - will use current table filters
      const allData = await caseAdvanceApi.getListAll({
        orderBy: 'createdAt',
        orderDir: 'DESC',
      })

      // Transform data for Excel export
      const excelData = allData.data.map((item: CaseAdvanced, index: number) => ({
        STT: index + 1,
        'Mã vụ việc': item.code || '',
        'Tên vụ việc': item.name || '',
        'Người được TGPL': item.rpName || '',
        'Hình thức trợ giúp': item.formsOfAssistance || '',
        'Lĩnh vực': item.legalField || '',
        'Ngày thụ lý': item.entryDate ? new Date(item.entryDate).toLocaleDateString('vi-VN') : '',
        'Tình trạng': item.status ? getCaseAdvancedStatusText(item.status) : '',
        'Cán bộ thực hiện': item.performer || '',
        'Ghi chú': item.note || '',
      }))

      exportToExcel(excelData, {
        fileName: `danh-sach-vu-viec-${new Date().toISOString().split('T')[0]}`,
        sheetName: 'Danh sách vụ việc',
      })

      toast.success('Xuất Excel thành công!')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Có lỗi xảy ra khi xuất Excel')
    } finally {
      setIsExporting(false)
    }
  }

  const handleDeleteSelectedCase = async () => {
    if (!selectedCaseId) {
      toast.error('Không tìm thấy ID vụ việc để xóa')

      return
    }

    await caseAdvanceApi.delete(selectedCaseId)
  }

  const handleTransferCase = async () => {
    if (!selectedCaseId) {
      toast.error('Không tìm thấy ID vụ việc để chuyển xử lý')

      return
    }

    try {
      await postBatDauXuLy(selectedCaseId)
      toast.success('Chuyển xử lý thành công')
    } catch (error) {
      console.error('Error transferring case:', error)
      toast.error('Lỗi, vụ việc đã được bắt đầu xử lý trước đó')
    } finally {
      setIsTransferDialogOpen(false)
      setSelectedCaseId(null)
      table.options.meta?.reload?.()
    }
  }

  // Helper functions for row selection
  const toggleRowSelection = (caseId: string) => {
    const newSelected = new Set(selectedRows)

    if (newSelected.has(caseId)) {
      newSelected.delete(caseId)
    } else {
      newSelected.add(caseId)
    }
    setSelectedRows(newSelected)
  }

  const toggleAllRows = (cases: CaseAdvanced[]) => {
    if (selectedRows.size === cases.length) {
      setSelectedRows(new Set())
    } else {
      setSelectedRows(new Set(cases.map((c: CaseAdvanced) => c.id).filter(Boolean) as string[]))
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns: ColumnDef<CaseAdvanced, any>[] = [
    // STT Column
    columnHelper.display({
      id: 'stt',
      header: 'STT',
      cell: ({ row }) => <div className="text-center text-sm text-gray-600">{row.index + 1}</div>,
      size: 50,
    }),

    // Actions Column (dấu ba chấm)
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const caseId = row.original.id

        if (!caseId) {
          return null
        }

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled={false}>
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Mở menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem asChild>
                <Link href={`/case-advance/${caseId}`} className="flex items-center">
                  <Eye className="h-4 w-4" />
                  Xem chi tiết
                </Link>
              </DropdownMenuItem>
              {!row.original.currentWorkflowId && (
                <DropdownMenuItem
                  onClick={() => {
                    handlePrepareTransferCase(caseId)
                  }}
                >
                  <PlayIcon className="h-4 w-4" />
                  Chuyển xử lý
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => {
                  setCaseInfo(row.original)
                  setIsCreateVerificationDialogOpen(true)
                }}
              >
                <PlusIcon className="h-4 w-4" />
                Đề nghị xác minh
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setCaseInfo(row.original)
                  setIsCreateRecommendationDialogOpen(true)
                }}
              >
                <PlusIcon className="h-4 w-4" />
                Kiến nghị
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setCaseInfo(row.original)
                  setIsCompletionDialogOpen(true)
                }}
              >
                <CheckCircle className="h-4 w-4" />
                Kết thúc vụ việc
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleEditCase(caseId)
                }}
              >
                <Edit className="h-4 w-4" />
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handlePrepareDeleteCase(caseId)
                }}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4" />
                Xóa vụ việc
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
      size: 50,
    }),

    // Checkbox Column
    columnHelper.display({
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={selectedRows.size === table.getRowModel().rows.length && table.getRowModel().rows.length > 0}
          onCheckedChange={() => toggleAllRows(table.getRowModel().rows.map(row => row.original))}
          aria-label="Chọn tất cả"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedRows.has(row.original.id || '')}
          onCheckedChange={() => toggleRowSelection(row.original.id || '')}
          aria-label={`Chọn dòng ${row.index + 1}`}
        />
      ),
      size: 50,
    }),

    columnHelper.accessor('code', {
      header: 'Mã vụ việc',
      enableHiding: true,
      meta: {
        title: 'Mã vụ việc',
        showToggle: false,
        // filter: {
        //   type: 'text',
        //   position: 'advanced',
        //   label: 'Mã vụ việc',
        //   placeholder: 'Tìm kiếm mã vụ việc',
        //   debounceMs: 300,
        // },
      },
    }),

    columnHelper.accessor('name', {
      header: 'Vụ việc',
      meta: {
        title: 'Vụ việc',
        // filter: {
        //   type: 'text',
        //   placeholder: 'Tìm kiếm vụ việc',
        //   debounceMs: 300,
        //   position: 'advanced',
        //   label: 'Tên vụ việc',
        // },
      },
      cell: ({ getValue, row }) => {
        return (
          <RenderColumn>
            <RenderInteractiveLink onClick={() => router.push(`/case-advance/${row.original.id}`)}>
              {getValue()}
            </RenderInteractiveLink>
            <RenderCopyText text={row.original.code} prefix="Mã" />
            <RenderText text={row.original.orgUnit.name} prefix="Đơn vị" />
            <RenderText text={row.original.formsOfAssistance} prefix="Hình thức trợ giúp" />
            <RenderAutoText text={row.original.legalField} textBehavior="wrap" maxLines={2} prefix="Lĩnh vực" />
            <RenderAutoText text={row.original.objectLegalField} textBehavior="wrap" maxLines={2} prefix="Diện" />
          </RenderColumn>
        )
      },
      // cell: ({ getValue, row }) => (
      //   <div>
      //     <Link
      //       href={`/case-advance/${row.original.id}`}
      //       className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
      //     >
      //       {getValue() || <span className="text-gray-400 italic">Chưa cập nhật</span>}
      //     </Link>
      //   </div>
      // ),
    }),

    columnHelper.accessor('orgUnitId', {
      enableHiding: true,
      meta: {
        showToggle: false,
        title: 'Đơn vị',
        filter: {
          type: 'combobox',
          position: 'advanced',
          placeholder: 'Chọn đơn vị',
          apiRequest: async (keyword?: string) => {
            const response = await organizationUnitsApi.getList({
              keyword,
              pageSize: 50,
            })
            const mappedData =
              response.items
                ?.map((item: { id?: string; name?: string }) => {
                  const value = item.id || String(item.name)

                  return {
                    label: item.name || 'Không có tên',
                    value: value,
                  }
                })
                .filter((item: { value?: string; label?: string }) => item.value && item.label) || []

            return mappedData
          },
        },
      },
    }),

    columnHelper.accessor('status', {
      header: 'Tình trạng',
      meta: {
        title: 'Tình trạng',
        filter: {
          type: 'select',
          // position: 'advanced',
          placeholder: 'Chọn tình trạng',
          options: getCaseAdvancedStatusOptions().map(option => ({
            label: option.label,
            value: option.value.toString(),
          })),
        },
      },
      cell: ({ getValue, row }) => {
        const status = getValue()
        const statusText = getCaseAdvancedStatusText(status as EnumCaseAdvancedStatus)
        const colorVariant = getColorVariant(status)

        const workflowHistoriesData = (row.original?.currentWorkflow?.workflowHistoriesData ||
          []) as unknown as WorkflowHistory[]

        const doingWorkflowHistory = workflowHistoriesData.filter(
          history => history.status !== WorkflowHistoryStatus.COMPLETED
        )

        return (
          <RenderColumn className="gap-1">
            <Badge
              variant={colorVariant}
              className={cn('px-3', colorVariant === 'secondary' ? 'bg-yellow-500 py-[5px] text-gray-800' : 'py-1')}
            >
              <span>{statusText}</span>
            </Badge>
            {doingWorkflowHistory.map(history => {
              const actions = history.actions.filter(
                action =>
                  action !== RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG &&
                  action !== RHanhDongHoSo.TU_CHOI &&
                  action !== RHanhDongHoSo.YEU_CAU_CD_NOP_BO_SUNG
              )

              return (
                <Fragment key={history.id}>
                  <RenderText
                    text={
                      actions.length === 1 ? (
                        <Link
                          className="text-blue-600 hover:text-blue-800 hover:underline"
                          href={`/case-advance/my-works/${actions[0]?.toLowerCase().replaceAll('_', '-')}`}
                        >
                          {history.stepName}
                        </Link>
                      ) : (
                        history.stepName
                      )
                    }
                    icon={<ActivityIcon size={16} />}
                    prefix="Bước"
                  />
                  <RenderText
                    text={WorkflowHistoryStatusLabel[history.status]}
                    icon={<InfoIcon size={16} />}
                    prefix="Trạng thái"
                  />
                  {history.assignedTo && (
                    <RenderText text={history.assignedTo.name} icon={<UserCircleIcon size={16} />} prefix="Cán bộ" />
                  )}
                </Fragment>
              )
            })}
            <QuaTrinhXuLy histories={workflowHistoriesData as unknown as WorkflowHistory[]} />
          </RenderColumn>
        )
      },
      size: 260,
      minSize: 260,
    }),

    columnHelper.accessor('rpName', {
      header: 'Người được trợ giúp',
      meta: {
        title: 'Người được trợ giúp',
        filter: {
          type: 'text',
          position: 'advanced',
          placeholder: 'Tìm kiếm tên người được trợ giúp',
          debounceMs: 300,
        },
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderFullName fullName={row.original.rpName} />
            <RenderIdentificationId identificationId={row.original.rpCardNumber} />
            <RenderGender gender={row.original.rpSex} />
          </RenderColumn>
        )
      },
      size: 120,
      minSize: 100,
    }),

    columnHelper.accessor('rpCardNumber', {
      meta: {
        showToggle: false,
        title: 'Số căn cước',
        filter: {
          type: 'text',
          position: 'advanced',
          placeholder: 'Nhập số căn cước',
          debounceMs: 300,
        },
      },
      cell: ({ getValue }) => {
        return <RenderIdentificationId identificationId={getValue()} />
      },
      size: 140,
      minSize: 120,
      enableHiding: true,
    }),

    columnHelper.accessor('rpSex', {
      header: 'Giới tính',
      meta: {
        showToggle: false,
        title: 'Giới tính',
        filter: {
          type: 'combobox',
          position: 'advanced',
          placeholder: 'Chọn giới tính',
          options: Object.entries(GENDER).map(([, value]) => ({
            label: GENDER_LABELS[value as keyof typeof GENDER_LABELS],
            value: value.toString(),
          })),
        },
      },
      cell: ({ getValue }) => {
        return <RenderGender gender={getValue()} />
      },
      enableHiding: true,
    }),

    columnHelper.accessor('objectLegalField', {
      enableHiding: true,
      meta: {
        showToggle: false,
        title: 'Thông tin trợ giúp',
        filter: {
          label: 'Thuộc diện trợ giúp',
          type: 'combobox',
          position: 'advanced',
          placeholder: 'Chọn diện trợ giúp',
          apiRequest: async (keyword?: string) => {
            const response = (await categoryApi.getCategories({
              keyword,
              filter: { type: CATEGORY_TYPE_VALUES.DOI_TUONG },
              pageSize: 50,
            })) as { items?: Array<{ id?: string; _id?: string; categoryId?: string; code?: string; name?: string }> }
            const mappedData =
              response.items
                ?.map((item: { id?: string; _id?: string; categoryId?: string; code?: string; name?: string }) => {
                  const value = item.id || item._id || item.categoryId || item.code || String(item.name)

                  return {
                    label: item.name || 'Không có tên',
                    value: value,
                  }
                })
                .filter((item: { value?: string; label?: string }) => item.value && item.label) || []

            return mappedData
          },
        },
      },
      cell: ({ getValue }) => {
        const text = getValue()

        return <RenderAutoText text={text} maxLines={2} defaultValue="Chưa cập nhật" />
      },
    }),

    columnHelper.accessor('formsOfAssistance', {
      id: 'formsOfAssistance',
      enableHiding: true,
      meta: {
        showToggle: false,
        title: 'Hình thức trợ giúp',
        filter: {
          type: 'combobox',
          position: 'advanced',
          placeholder: 'Chọn hình thức trợ giúp',
          apiRequest: async (keyword?: string) => {
            const response = (await categoryApi.getCategories({
              keyword,
              filter: {
                type: `${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP}`,
              },
              pageSize: 50,
            })) as { items?: Array<{ id?: string; _id?: string; categoryId?: string; code?: string; name?: string }> }
            const mappedData =
              response.items
                ?.map((item: { id?: string; _id?: string; categoryId?: string; code?: string; name?: string }) => {
                  const value = item.id || item._id || item.categoryId || item.code || String(item.name)

                  return {
                    label: item.name || 'Không có tên',
                    value: value,
                  }
                })
                .filter((item: { value?: string; label?: string }) => item.value && item.label) || []

            return mappedData
          },
        },
      },
    }),

    columnHelper.accessor('legalField', {
      enableHiding: true,
      meta: {
        showToggle: false,
        title: 'Lĩnh vực trợ giúp',
        filter: {
          type: 'combobox',
          position: 'advanced',
          placeholder: 'Chọn lĩnh vực trợ giúp',
          apiRequest: async (keyword?: string) => {
            const response = (await categoryApi.getCategories({
              keyword,
              filter: {
                type: `${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP}`,
              },
              pageSize: 50,
            })) as { items?: Array<{ id?: string; _id?: string; categoryId?: string; code?: string; name?: string }> }
            const mappedData =
              response.items
                ?.map((item: { id?: string; _id?: string; categoryId?: string; code?: string; name?: string }) => {
                  const value = item.id || item._id || item.categoryId || item.code || String(item.name)

                  return {
                    label: item.name || 'Không có tên',
                    value: value,
                  }
                })
                .filter((item: { value?: string; label?: string }) => item.value && item.label) || []

            return mappedData
          },
        },
      },
      cell: ({ getValue }) => {
        const text = getValue()

        if (!text) {
          return <span className="text-gray-400 italic">Chưa cập nhật</span>
        }

        return <TextWithTooltip text={text} maxLength={20} />
      },
      size: 130,
      minSize: 100,
    }),

    columnHelper.accessor('entryDate', {
      meta: {
        title: 'Ngày yêu cầu/hẹn',
        filter: {
          type: 'date-range',
          position: 'advanced',
        },
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderDate datetime={row.original.entryDate} prefix="Lúc" />
            <RenderDate datetime={row.original.appointmentDate} prefix="Ngày hẹn" />
          </RenderColumn>
        )
      },
    }),

    columnHelper.accessor('appointmentDate', {
      meta: {
        showToggle: false,
        title: 'Ngày hẹn',
      },
      enableHiding: true,
    }),

    columnHelper.accessor('createdBy.name', {
      meta: {
        title: 'Người tạo',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderUser user={row.original.createdBy} />
            <RenderDateTime
              datetime={row.original.createdAt}
              showTime={table.options.meta?.getShowTime()}
              // prefix="Ngày tạo"
            />
          </RenderColumn>
        )
      },
    }),

    columnHelper.accessor('createdAt', {
      meta: {
        showToggle: false,
        title: 'Ngày tạo',
      },
      enableHiding: true,
    }),

    columnHelper.accessor('updatedBy.name', {
      header: 'Người cập nhật',
      meta: {
        title: 'Người cập nhật',
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderUser user={row.original.updatedBy} />
            <RenderDateTime
              datetime={row.original.updatedAt}
              showTime={table.options.meta?.getShowTime()}
              // prefix="Ngày cập nhật"
            />
          </RenderColumn>
        )
      },
    }),

    columnHelper.accessor('updatedAt', {
      meta: {
        showToggle: false,
        title: 'Ngày cập nhật',
      },
      enableHiding: true,
    }),
  ]

  const { table } = useEnhancedTable<CaseAdvanced>({
    columns,
    pageName: 'case-advance',
    keyObject: {},
    queryFn: async state => {
      // Build filter object from state.filter (following processes pattern)
      const filterObj: AnyRecord = {}

      state.filter.forEach(filter => {
        if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
          // Map field names to API parameter names
          let apiFieldName = filter.id

          if (filter.id === 'legalField') {
            apiFieldName = 'legalFieldId'
          } else if (filter.id === 'formsOfAssistance') {
            apiFieldName = 'formsOfAssistanceId'
          } else if (filter.id === 'objectLegalField') {
            apiFieldName = 'objectLegalFieldId'
          }

          // Handle different filter value types
          if (typeof filter.value === 'string') {
            // Direct string value
            filterObj[apiFieldName] = filter.value
          } else if (typeof filter.value === 'number') {
            // Direct number value
            filterObj[apiFieldName] = filter.value
          } else if (Array.isArray(filter.value) && filter.value.length > 0) {
            // Array of values (multi-select)
            const firstValue = filter.value[0]

            if (typeof firstValue === 'string') {
              filterObj[apiFieldName] = firstValue
            } else if (typeof firstValue === 'object' && 'value' in firstValue) {
              filterObj[apiFieldName] = firstValue.value as string
            }
          } else if (typeof filter.value === 'object' && 'value' in filter.value) {
            // Combobox object { value, label }
            filterObj[apiFieldName] = filter.value.value as string
          } else if (typeof filter.value === 'object' && 'from' in filter.value && 'to' in filter.value) {
            // Date range object { from: Date, to: Date }
            const dateRange = filter.value as { from?: Date; to?: Date }

            if (dateRange.from) {
              // Format date to Vietnam timezone (UTC+7)
              filterObj[`${apiFieldName}From`] = dateRange.from.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
            }

            if (dateRange.to) {
              // Format date to Vietnam timezone (UTC+7)
              filterObj[`${apiFieldName}To`] = dateRange.to.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
            }
          }
        }
      })

      const params = {
        page: state.pagination.pageIndex + 1,
        pageSize: state.pagination.pageSize,
        orderBy: state.sorting[0]?.id || 'createdAt',
        orderDir: (state.sorting[0]?.desc ? 'DESC' : 'ASC') as 'ASC' | 'DESC',
        keyword: state.keyword || undefined,
        filter: filterObj,
      }

      console.log('Final API params:', params)

      const response = await caseAdvanceApi.getList(params)

      return {
        items: response.data || [],
        totalPages: response.pagination?.totalPages || 0,
        totalItems: response.pagination?.total || 0,
      }
    },
    initialState: {
      columnVisibility: {
        rpCardNumber: false,
        rpSex: false,
        updatedAt: false,
        appointmentDate: false,
        createdAt: false,
        formsOfAssistance: false,
        code: false,
        orgUnitId: false,
        objectLegalField: false,
        legalField: false,
      },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['case-advance'],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: 'Danh sách vụ việc',
            href: '/case-advance',
          },
        ]}
      >
        <AdminPageContent
          title="Danh sách vụ việc trợ giúp pháp lý"
          subtitle="Tổng hợp và quản lý các vụ việc trợ giúp pháp lý đã được ghi nhận"
          actions={[
            <ExportButton
              key="export"
              variant="outline"
              isExporting={isExporting}
              onClick={handleExportExcel}
              disabled={isExporting}
            />,

            <AddButton
              tooltip="Thêm mới vụ việc"
              key="create"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={() => redirect('/case-advance/create')}
            />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>

        {/* <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Danh sách vụ việc trợ giúp pháp lý</h1>
              <p className="mt-1 text-sm text-gray-600">
                Tổng hợp và quản lý các vụ việc trợ giúp pháp lý đã được ghi nhận
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleExportExcel} disabled={isExporting}>
                <Download className="h-4 w-4" />
                {isExporting ? 'Đang xuất...' : 'Xuất Excel'}
              </Button>
              <Button asChild className="bg-black hover:bg-gray-800">
                <Link href="/case-advance/create">
                  <Plus className="h-4 w-4" />
                  Thêm mới vụ việc
                </Link>
              </Button>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Chuyển xử lý
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Cập nhật phân công thực hiện TGPL
            </Button>
            <Button variant="outline" className="flex items-center gap-2 text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4" />
              Xóa
            </Button>
          </div>
          <DataTable table={table} />
        </div> */}
      </AdminPageLayout>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa vụ việc này không? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSelectedCase} className="bg-red-600 hover:bg-red-700">
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <VerificationDialog
        open={isCreateVerificationDialogOpen}
        onOpenChange={setIsCreateVerificationDialogOpen}
        caseInfo={caseInfo ?? undefined}
      />

      <SubtaskRecommendationDialog
        open={isCreateRecommendationDialogOpen}
        onOpenChange={setIsCreateRecommendationDialogOpen}
        caseInfo={caseInfo ?? undefined}
      />

      <CaseCompletionDialog
        open={isCompletionDialogOpen}
        onOpenChange={setIsCompletionDialogOpen}
        caseInfo={caseInfo ?? undefined}
      />

      <AlertDialog open={isTransferDialogOpen} onOpenChange={setIsTransferDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận chuyển xử lý</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn chuyển xử lý vụ việc này không?</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleTransferCase}>Chuyển xử lý</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
