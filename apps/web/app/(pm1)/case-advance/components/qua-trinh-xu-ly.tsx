'use client'

import { WorkflowHistory } from '@/lib/types'
import { But<PERSON> } from '@workspace/ui/components/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@workspace/ui/components/dialog'
import { cn } from '@workspace/ui/lib/utils'
import { Calendar, CheckCircle, Clock, ListOrderedIcon, User } from 'lucide-react'
import { useState } from 'react'

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case 'PENDING':
      return <Clock className="h-5 w-5 text-yellow-500" />
    case 'WAITING_RESPONSE':
      return <Clock className="h-5 w-5 text-blue-500" />
    case 'WAITING_SUBTASK':
      return <Clock className="h-5 w-5 text-purple-500" />
    default:
      return <Clock className="h-5 w-5 text-gray-500" />
  }
}

// const getStatusColor = (status: string) => {
//   switch (status) {
//     case 'COMPLETED':
//       return 'bg-green-50 border-green-200'
//     case 'PENDING':
//       return 'bg-yellow-50 border-yellow-200'
//     case 'WAITING_RESPONSE':
//       return 'bg-blue-50 border-blue-200'
//     case 'WAITING_SUBTASK':
//       return 'bg-purple-50 border-purple-200'
//     default:
//       return 'bg-gray-50 border-gray-200'
//   }
// }

const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'border-green-200'
    case 'PENDING':
      return 'border-yellow-200'
    case 'WAITING_RESPONSE':
      return 'border-blue-200'
    case 'WAITING_SUBTASK':
      return 'border-purple-200'
    default:
      return 'border-gray-200'
  }
}

const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'bg-green-200'
    case 'PENDING':
      return 'bg-yellow-200'
    case 'WAITING_RESPONSE':
      return 'bg-blue-200'
    case 'WAITING_SUBTASK':
      return 'bg-purple-200'
    default:
      return 'bg-gray-200'
  }
}

const formatDateTime = (dateString: string | null | undefined) => {
  if (!dateString) {
    return 'Chưa có'
  }

  return new Date(dateString).toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

export const QuaTrinhXuLy = ({ histories }: { histories: WorkflowHistory[] }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <span>
          <Button variant="outline" size="sm" className="h-7 gap-2 hover:bg-blue-500 hover:text-white">
            <ListOrderedIcon className="h-4 w-4" /> Quá trình xử lý
          </Button>
        </span>
      </DialogTrigger>
      <DialogContent className="flex h-auto max-h-[90vh] flex-col p-0 sm:max-w-[760px]">
        <div className="flex-shrink-0 border-b border-gray-200 p-4 sm:p-6 lg:px-10 lg:pt-4 lg:pb-4">
          <DialogHeader>
            <DialogTitle className="pr-6 text-lg font-semibold text-gray-900 sm:text-xl">
              Quá trình xử lý hồ sơ
            </DialogTitle>
          </DialogHeader>
        </div>

        <div className="min-h-0 flex-1 space-y-6 overflow-y-auto px-4 py-4 sm:px-6 lg:px-10">
          {histories.map((history, index) => (
            <div key={history.id} className="relative">
              {/* Timeline line */}
              {index < histories.length - 1 && <div className="absolute top-12 left-6 h-full w-0.5 bg-gray-200" />}

              <div className="flex gap-4">
                {/* Status icon */}
                <div className="relative z-10 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full border-2 border-gray-200 bg-white">
                  {getStatusIcon(history.status)}
                </div>

                {/* Content */}
                <div className="min-w-0 flex-1">
                  <div className={cn('space-y-3 rounded-lg border p-4', getStatusColor(history.status))}>
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">{history.stepName}</h3>
                      <span
                        className={cn(
                          'rounded-full border px-3 py-1 text-xs font-medium',
                          getStatusBadgeColor(history.status)
                        )}
                      >
                        {history.status === 'COMPLETED'
                          ? 'Hoàn thành'
                          : history.status === 'PENDING'
                            ? 'Chờ xử lý'
                            : history.status === 'WAITING_RESPONSE'
                              ? 'Chờ phản hồi'
                              : history.status === 'WAITING_SUBTASK'
                                ? 'Chờ subtask'
                                : history.status}
                      </span>
                    </div>

                    {/* Assigned to */}
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4" />
                      <span className="font-medium">Người xử lý:</span>
                      <span>{history.assignedTo?.name || 'Chưa phân công'}</span>
                    </div>

                    {/* Selected action */}
                    {/* {history.selectedAction && (
                      <div className="flex items-center gap-2 text-sm">
                        <ArrowRight className="h-4 w-4" />
                        <span className="font-medium">Hành động:</span>
                        <span className="rounded bg-white/50 px-2 py-1 text-xs">{history.selectedAction}</span>
                      </div>
                    )} */}

                    {/* Content */}
                    {history.content?.content && (
                      <div className="text-sm">
                        <span className="font-medium">Nội dung:</span>
                        <p className="mt-1 rounded bg-white/50 p-2 text-xs">{history.content.content}</p>
                      </div>
                    )}

                    {/* Note */}
                    {history.content?.note && (
                      <div className="text-sm">
                        <span className="font-medium">Ghi chú:</span>
                        <p className="mt-1 rounded bg-white/50 p-2 text-xs">{history.content.note}</p>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="grid grid-cols-1 gap-3 text-xs md:grid-cols-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-3 w-3" />
                        <span className="font-medium">Giao việc:</span>
                        <span>{formatDateTime(history.assignedAt)}</span>
                      </div>
                      {history.startedAt && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3" />
                          <span className="font-medium">Bắt đầu:</span>
                          <span>{formatDateTime(history.startedAt)}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Calendar className="h-3 w-3" />
                        <span className="font-medium">Hoàn thành:</span>
                        <span>{formatDateTime(history.completedAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}
