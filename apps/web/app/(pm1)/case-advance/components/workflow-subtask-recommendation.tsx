// SubtaskRecommendationDialog.tsx
import { useCreateWorkflowSubtaskMutation, useOrganizationUnitsQuery, type User, useUsersQuery } from '@/lib/hooks'
import { CaseAdvanced, OrganizationUnit } from '@/lib/types'
import { WorkflowSubTaskType } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { DatePickerInput } from '@workspace/ui/components/date-picker-input'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  soKienNghi: z.string().min(1, 'Bắt buộc'),
  ngayKienNghi: z.string().min(1, 'Bắt buộc'),
  orgUnitId: z.string().min(1, 'Bắt buộc'),
  noiDungKienNghi: z.string().min(1, 'Bắt buộc'),
  thoiGianThucHien: z.string().optional(),
  diaDiem: z.string().optional(),
  canBoThucHien: z.string().min(1, 'Bắt buộc'),
  ghiChu: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface SubtaskRecommendationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseInfo?: CaseAdvanced & {
    currentWorkflowId?: string
    currentProcessId?: string
  }
  isDisabled?: boolean
  onSubmit?: (values: FormValues) => void
}

function Required() {
  return <span className="ml-1 text-red-500">*</span>
}

export default function SubtaskRecommendationDialog({
  open,
  onOpenChange,
  caseInfo,
  isDisabled = false,
  onSubmit: externalSubmit,
}: SubtaskRecommendationDialogProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      soKienNghi: '',
      ngayKienNghi: '',
      orgUnitId: '',
      noiDungKienNghi: '',
      thoiGianThucHien: '',
      diaDiem: '',
      canBoThucHien: '',
      ghiChu: '',
    },
  })

  function handleSubmit(values: FormValues) {
    // call external handler if provided
    if (externalSubmit) {
      externalSubmit(values)
    } else {
      // Create recommendation via subtask API (same endpoint, different type)
      if (!caseInfo?.id || !caseInfo?.currentWorkflowId || !caseInfo?.currentProcessId || !caseInfo?.orgUnitId) {
        console.error('Missing required case info for creating recommendation')

        return
      }

      if (!values.canBoThucHien) {
        console.error('Missing assigned user for creating recommendation')

        return
      }

      const recommendationData = {
        soKienNghi: values.soKienNghi,
        ngayKienNghi: values.ngayKienNghi,
        noiDungKienNghi: values.noiDungKienNghi,
        thoiGianThucHien: values.thoiGianThucHien,
        diaDiem: values.diaDiem,
        ghiChu: values.ghiChu,
      }

      const payload = {
        assignedToId: values.canBoThucHien as string, // Cán bộ thực hiện -> assignedToId (đã validate ở trên)
        caseAdvancedId: caseInfo.id,
        data: JSON.stringify(recommendationData),
        fromOrgUnitId: String(caseInfo.orgUnitId),
        processId: caseInfo.currentProcessId,
        toOrgUnitId: values.orgUnitId, // Cơ quan kiến nghị -> toOrgUnitId
        type: WorkflowSubTaskType.KIEN_NGHI,
        workflowId: caseInfo.currentWorkflowId,
      }

      createRecommendationMutation.mutate(payload)
    }

    onOpenChange(false)
    form.reset()
  }

  React.useEffect(() => {
    if (!open) {
      form.reset()
    }
  }, [open, form])

  const getOrganizationUnitMutation = useOrganizationUnitsQuery({
    page: 1,
    pageSize: 50,
    filter: {},
  })

  const [organizationUnit, setOrganizationUnit] = useState<OrganizationUnit[]>([])
  const [users, setUsers] = useState<User[]>([])

  const getUsersQuery = useUsersQuery({
    page: 1,
    pageSize: 50,
    orderBy: 'createdAt',
    orderDir: 'DESC',
    filter: {},
  })

  const createRecommendationMutation = useCreateWorkflowSubtaskMutation()

  const getOptionOrganizationUnit = async () => {
    const response = await getOrganizationUnitMutation.refetch()

    if (response.data) {
      setOrganizationUnit(response.data.items || [])
    }
  }

  const getOptionUsers = async () => {
    const response = await getUsersQuery.refetch()

    if (response.data) {
      setUsers(response.data.items || [])
    }
  }

  useEffect(() => {
    if (open) {
      getOptionOrganizationUnit()
      getOptionUsers()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-4xl sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Thêm mới kiến nghị</DialogTitle>
        </DialogHeader>

        {/* Thông tin vụ việc (header card) */}
        <div className="bg-muted mt-3 rounded-md p-4 text-sm">
          <div className="flex flex-col justify-between gap-4 sm:flex-row">
            <div>
              <p>
                <span className="font-medium">Tên vụ việc: </span>
                {caseInfo?.name ?? 'Cướp tài sản'}
              </p>
              <p className="mt-1">
                <span className="font-medium">Người được trợ giúp: </span>
                {caseInfo?.rpName ?? 'Khuất Văn Tú Tiến'}
              </p>
            </div>

            <div className="text-sm">
              <p>
                <span className="font-medium">CCCD/DDCN: </span>
                {caseInfo?.rpCardNumber ?? '001123456789'}
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="mt-6 space-y-6">
            {/* Row 1: Số kiến nghị | Ngày kiến nghị | Cơ quan kiến nghị */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <FormField
                control={form.control}
                name="soKienNghi"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Số kiến nghị <Required />
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập số" {...field} disabled={isDisabled} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ngayKienNghi"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Ngày kiến nghị <Required />
                    </FormLabel>
                    <FormControl>
                      <DatePickerInput
                        placeholder="dd/mm/yyyy"
                        disabled={isDisabled}
                        {...field}
                        value={field.value || ''}
                        disallowFuture={true}
                        onValidate={(ok, msg) => {
                          if (ok) form.clearErrors('ngayKienNghi')
                          else
                            form.setError('ngayKienNghi', {
                              type: 'manual',
                              message: msg || 'Ngày không hợp lệ',
                            })
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="orgUnitId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Cơ quan kiến nghị <Required />
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value} disabled={isDisabled}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Chọn" />
                        </SelectTrigger>
                        <SelectContent>
                          {organizationUnit.length > 0 ? (
                            organizationUnit.map(item => (
                              <SelectItem key={item.id} value={item.id}>
                                {item.name}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="text-muted-foreground px-2 py-1 text-sm">Không có dữ liệu</div>
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Nội dung kiến nghị (textarea full width) */}
            <FormField
              control={form.control}
              name="noiDungKienNghi"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Nội dung kiến nghị <Required />
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung"
                      {...field}
                      className="min-h-[120px] resize-none"
                      disabled={isDisabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Row 2: Thời gian thực hiện | Địa điểm | Cán bộ thực hiện */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <FormField
                control={form.control}
                name="thoiGianThucHien"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thời gian thực hiện (giờ)</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập số giờ" {...field} disabled={isDisabled} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="diaDiem"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa điểm</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập địa điểm" {...field} disabled={isDisabled} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="canBoThucHien"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cán bộ thực hiện</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value} disabled={isDisabled}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Chọn cán bộ" />
                        </SelectTrigger>
                        <SelectContent>
                          {users.length > 0 ? (
                            users.map(user => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.name}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="text-muted-foreground px-2 py-1 text-sm">Không có dữ liệu</div>
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Ghi chú */}
            <FormField
              control={form.control}
              name="ghiChu"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ghi chú</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập ghi chú"
                      {...field}
                      className="min-h-[80px] resize-none"
                      disabled={isDisabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Footer buttons */}
            <DialogFooter className="flex justify-end gap-2 pt-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button type="submit" disabled={createRecommendationMutation.isPending}>
                {createRecommendationMutation.isPending ? 'Đang gửi...' : 'Gửi kiến nghị'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
