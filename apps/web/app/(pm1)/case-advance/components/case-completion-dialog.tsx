'use client'

import { CaseAdvanced } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { DatePicker } from '@workspace/ui/components/date-picker'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/components/toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/mi'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  ngayKetThuc: z.string().min(1, '<PERSON><PERSON><PERSON> kết thúc là bắt buộc'),
  trangThaiVuViec<PERSON>etThuc: z.string().min(1, 'Trạng thái vụ việc kết thúc là bắt buộc'),
  thoiGianThucHien: z.string().min(1, 'Thời gian thực hiện là bắt buộc'),
  ghiChu: z.string().optional(),
  documentAttachName: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface CaseCompletionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseInfo?: CaseAdvanced & {
    currentWorkflowId?: string
    currentProcessId?: string
  }
  isDisabled?: boolean
  onSubmit?: (values: FormValues) => void
}

function Required() {
  return <span className="ml-1 text-red-500">*</span>
}

// Status options for case completion
const CASE_COMPLETION_STATUS = [
  { value: 'HOAN_THANH', label: 'Hoàn thành' },
  { value: 'KHONG_CO_CO_SO_PHAP_LY', label: 'Không có cơ sở pháp lý' },
  { value: 'KHONG_THUOC_DIEN_TRO_GIUP', label: 'Không thuộc diện trợ giúp' },
  { value: 'NGUOI_DUOC_TRO_GIUP_TU_CHOI', label: 'Người được trợ giúp từ chối' },
  { value: 'KHAC', label: 'Khác' },
]

export default function CaseCompletionDialog({
  open,
  onOpenChange,
  caseInfo,
  isDisabled = false,
  onSubmit: externalSubmit,
}: CaseCompletionDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    setSelectedFiles(files)

    // Set document attach name (comma-separated file names)
    if (files && files.length > 0) {
      const fileNames = Array.from(files)
        .map(file => file.name)
        .join(', ')
      form.setValue('documentAttachName', fileNames)
    } else {
      form.setValue('documentAttachName', '')
    }
  }

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ngayKetThuc: '',
      trangThaiVuViecKetThuc: '',
      thoiGianThucHien: '',
      ghiChu: '',
      documentAttachName: '',
    },
  })

  function handleSubmit(values: FormValues) {
    // call external handler if provided
    if (externalSubmit) {
      externalSubmit(values)

      return
    }

    // Create case completion via subtask API
    if (!caseInfo?.id) {
      console.error('Missing case ID for case completion')

      return
    }

    // Check if we have workflow info, if not use fallback values
    const workflowId = caseInfo.currentWorkflowId
    const processId = caseInfo.currentProcessId
    const orgUnitId = caseInfo.orgUnitId

    if (!processId || !workflowId || !orgUnitId) {
      toast.error('Thiếu thông tin workflow/process để kết thúc vụ việc')

      return
    }

    onOpenChange(false)
    form.reset()
  }

  React.useEffect(() => {
    if (!open) {
      form.reset({
        ngayKetThuc: '',
        trangThaiVuViecKetThuc: '',
        thoiGianThucHien: '',
        ghiChu: '',
        documentAttachName: '',
      })
      setSelectedFiles(null)
    }
  }, [open, form])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-4xl sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Kết thúc vụ việc</DialogTitle>
        </DialogHeader>

        {/* Thông tin vụ việc (header card) */}
        <div className="bg-muted mt-3 rounded-md p-4 text-sm">
          <div className="flex flex-col justify-between gap-4 sm:flex-row">
            <div>
              <p>
                <span className="font-medium">Tên vụ việc: </span>
                {caseInfo?.name ?? 'Cướp tài sản'}
              </p>
              <p className="mt-1">
                <span className="font-medium">Người được trợ giúp: </span>
                {caseInfo?.rpName ?? 'Khuất Văn Tú Tiến'}
              </p>
            </div>

            <div className="text-sm">
              <p>
                <span className="font-medium">CCCD/DDCN: </span>
                {caseInfo?.rpCardNumber ?? '001123456789'}
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Thông tin kết thúc vụ việc */}
            <div>
              <h3 className="mb-4 text-lg font-semibold text-blue-600">Thông tin kết thúc vụ việc</h3>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Ngày kết thúc */}
                <FormField
                  control={form.control}
                  name="ngayKetThuc"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày kết thúc
                        <Required />
                      </FormLabel>
                      <FormControl>
                        <DatePicker
                          placeholder="Chọn ngày"
                          value={field.value}
                          onChange={date => {
                            field.onChange(date)
                            form.trigger('ngayKetThuc')
                          }}
                          disabled={isDisabled}
                          className={form.formState.errors.ngayKetThuc ? 'border-destructive ring-destructive/20' : ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Trạng thái vụ việc kết thúc */}
                <FormField
                  control={form.control}
                  name="trangThaiVuViecKetThuc"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Trạng thái vụ việc kết thúc
                        <Required />
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={value => {
                          field.onChange(value)
                          form.trigger('trangThaiVuViecKetThuc')
                        }}
                        disabled={isDisabled}
                      >
                        <FormControl>
                          <SelectTrigger
                            className={`w-full ${
                              form.formState.errors.trangThaiVuViecKetThuc
                                ? 'border-destructive ring-destructive/20'
                                : ''
                            }`}
                          >
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CASE_COMPLETION_STATUS.map(status => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Thời gian thực hiện (giờ) */}
                <FormField
                  control={form.control}
                  name="thoiGianThucHien"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Thời gian thực hiện (giờ)
                        <Required />
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nhập số"
                          type="number"
                          min="0"
                          step="0.5"
                          {...field}
                          disabled={isDisabled}
                          className={
                            form.formState.errors.thoiGianThucHien ? 'border-destructive ring-destructive/20' : ''
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Ghi chú */}
            <div>
              <h3 className="mb-4 text-lg font-semibold text-blue-600">Ghi chú</h3>

              <FormField
                control={form.control}
                name="ghiChu"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nhập ghi chú</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nhập ghi chú"
                        className="min-h-[100px] resize-none"
                        {...field}
                        disabled={isDisabled}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <Tabs defaultValue="upload" className="w-full">
                {/* Nút toggle */}
                <TabsList>
                  <TabsTrigger value="upload">Đính kèm tài liệu</TabsTrigger>
                  <TabsTrigger value="scan">Scan tài liệu</TabsTrigger>
                </TabsList>

                {/* Nội dung tab Upload */}
                <TabsContent value="upload" className="mt-4">
                  <label className="mb-2 block text-sm font-medium">Tệp đính kèm</label>
                  <div className="flex items-center justify-between rounded-lg border border-dashed border-gray-300 p-4">
                    <div className="flex items-center gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled={isDisabled}
                        onClick={() => document.getElementById('file-input')?.click()}
                      >
                        Chọn tệp
                      </Button>
                      <span className="text-muted-foreground text-sm">
                        {selectedFiles && selectedFiles.length > 0
                          ? Array.from(selectedFiles)
                              .map(file => file.name)
                              .join(', ')
                          : 'Không có tệp nào được chọn'}
                      </span>
                    </div>
                  </div>
                  <input
                    id="file-input"
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                  />
                  <p className="mt-1 text-xs text-gray-500">(Loại file: .pdf, .doc, .docx)</p>
                </TabsContent>

                {/* Nội dung tab Scan */}
                <TabsContent value="scan" className="mt-4">
                  <label className="mb-2 block text-sm font-medium">Tên tài liệu</label>
                  <Input placeholder="Nhập tên" className="w-full" />
                  <Button type="button" className="mt-3">
                    📄 Scan
                  </Button>
                </TabsContent>
              </Tabs>
            </div>

            {/* Action buttons */}
            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isDisabled}>
                Hủy
              </Button>
              <Button type="submit" disabled={isDisabled}>
                Lưu
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
