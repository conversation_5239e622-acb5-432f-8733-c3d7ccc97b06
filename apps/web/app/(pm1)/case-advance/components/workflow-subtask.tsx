// VerificationDialog.tsx
import {
  useCreateWorkflowSubtaskMutation,
  useDeleteWorkflowSubtaskMutation,
  useOrganizationUnitsQuery,
  type User,
  useUsersQuery,
  useWorkflowSubtasksQuery,
} from '@/lib/hooks'
import { CaseAdvanced, WorkflowSubTask } from '@/lib/types'
import { WorkflowSubTaskType } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { DatePickerInput } from '@workspace/ui/components/date-picker-input'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table'
import { Textarea } from '@workspace/ui/components/textarea'
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Edit,
  MoreHorizontal,
  Send,
  Trash2,
} from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  soXacMinh: z.string().min(1, 'Bắt buộc'),
  ngayXacMinh: z.string().min(1, 'Bắt buộc'),
  orgUnitId: z.string().min(1, 'Bắt buộc'),
  noiDung: z.string().min(1, 'Bắt buộc'),
  ngayDeNghiPhanHoi: z.string().min(1, 'Bắt buộc'),
  soVanBanTraLoi: z.string().optional(),
  ngayVanBanTraLoi: z.string().min(1, 'Bắt buộc'),
  ketQuaXacMinh: z.string().min(1, 'Bắt buộc'),
  canBoThucHien: z.string().min(1, 'Bắt buộc'),
  ghiChu: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface VerificationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseInfo?: CaseAdvanced & {
    currentWorkflowId?: string
    currentProcessId?: string
  }
  isDisabled?: boolean
  onSubmit?: (values: FormValues) => void
}

function Required() {
  return <span className="ml-1 text-red-500">*</span>
}

export default function VerificationDialog({
  open,
  onOpenChange,
  caseInfo,
  isDisabled = false,
  onSubmit: externalSubmit,
}: VerificationDialogProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      soXacMinh: '',
      ngayXacMinh: '',
      orgUnitId: '',
      noiDung: '',
      ngayDeNghiPhanHoi: '',
      soVanBanTraLoi: '',
      ngayVanBanTraLoi: '',
      ketQuaXacMinh: '',
      ghiChu: '',
    },
  })

  function handleSubmit(values: FormValues) {
    // call external handler if provided
    if (externalSubmit) {
      externalSubmit(values)
    } else {
      // Create subtask via API
      if (!caseInfo?.id || !caseInfo?.currentWorkflowId || !caseInfo?.currentProcessId || !caseInfo?.orgUnitId) {
        console.error('Missing required case info for creating subtask')

        return
      }

      const subtaskData = {
        soXacMinh: values.soXacMinh,
        ngayXacMinh: values.ngayXacMinh,
        noiDung: values.noiDung,
        ngayDeNghiPhanHoi: values.ngayDeNghiPhanHoi,
        soVanBanTraLoi: values.soVanBanTraLoi,
        ngayVanBanTraLoi: values.ngayVanBanTraLoi,
        ketQuaXacMinh: values.ketQuaXacMinh,
        ghiChu: values.ghiChu,
      }

      const payload = {
        assignedToId: values.canBoThucHien, // Cán bộ thực hiện -> assignedToId
        caseAdvancedId: caseInfo.id,
        data: JSON.stringify(subtaskData),
        fromOrgUnitId: String(caseInfo.orgUnitId),
        processId: caseInfo.currentProcessId,
        toOrgUnitId: values.orgUnitId, // Cơ quan xác minh -> toOrgUnitId
        type: WorkflowSubTaskType.PHOI_HOP_XAC_MINH,
        workflowId: caseInfo.currentWorkflowId,
      }

      createSubtaskMutation.mutate(payload, {
        onSuccess: () => {
          // Refetch subtasks after successful creation
          refetchSubtasks()
        },
      })
    }

    onOpenChange(false)
    form.reset()
  }

  React.useEffect(() => {
    if (!open) {
      form.reset()
    }
  }, [open, form])

  const getOrganizationUnitMutation = useOrganizationUnitsQuery({
    page: 1,
    pageSize: 50,
    filter: {},
  })

  const [organizationUnit, setOrganizationUnit] = useState<Array<{ id: string; name: string }>>([])
  const [users, setUsers] = useState<User[]>([])

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const getUsersQuery = useUsersQuery({
    page: 1,
    pageSize: 50,
    orderBy: 'createdAt',
    orderDir: 'DESC',
    filter: {},
  })

  const createSubtaskMutation = useCreateWorkflowSubtaskMutation()
  const deleteSubtaskMutation = useDeleteWorkflowSubtaskMutation()

  // Get existing subtasks for this case
  const queryParams = {
    filter: {
      caseAdvancedId: caseInfo?.id,
      type: WorkflowSubTaskType.PHOI_HOP_XAC_MINH,
    },
  }

  const { data: subtasksResponse, refetch: refetchSubtasks } = useWorkflowSubtasksQuery(queryParams)

  // Handle different response structures
  const allSubtasks: WorkflowSubTask[] = React.useMemo(() => {
    if (!subtasksResponse) return []

    // If response is already an array
    if (Array.isArray(subtasksResponse)) {
      return subtasksResponse as unknown as WorkflowSubTask[]
    }

    // If response has items property
    if (subtasksResponse && typeof subtasksResponse === 'object' && 'items' in subtasksResponse) {
      return ((subtasksResponse as { items?: WorkflowSubTask[] }).items || []) as WorkflowSubTask[]
    }

    // If response has data property
    if (subtasksResponse && typeof subtasksResponse === 'object' && 'data' in subtasksResponse) {
      return ((subtasksResponse as { data?: WorkflowSubTask[] }).data || []) as WorkflowSubTask[]
    }

    return []
  }, [subtasksResponse])

  // Pagination calculations
  const totalItems = allSubtasks.length
  const totalPages = Math.ceil(totalItems / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const existingSubtasks = allSubtasks.slice(startIndex, endIndex)

  // Pagination handlers
  const goToFirstPage = () => setCurrentPage(1)
  const goToLastPage = () => setCurrentPage(totalPages)
  const goToPreviousPage = () => setCurrentPage(prev => Math.max(1, prev - 1))
  const goToNextPage = () => setCurrentPage(prev => Math.min(totalPages, prev + 1))

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // Reset to first page when changing page size
  }

  // Handle send verification request
  const handleSendVerificationRequest = (subtaskId: string) => {
    // TODO: Implement send verification request API call
    console.log('Sending verification request for subtask:', subtaskId)
  }

  // Handle cancel verification
  const handleCancelVerification = (subtaskId: string) => {
    deleteSubtaskMutation.mutate(subtaskId, {
      onSuccess: () => {
        refetchSubtasks()
      },
    })
  }

  // Handle edit subtask
  const handleEditSubtask = (subtaskId: string) => {
    // TODO: Implement edit subtask functionality
    console.log('Editing subtask:', subtaskId)
  }

  const getOptionOrganizationUnit = useCallback(async () => {
    const response = await getOrganizationUnitMutation.refetch()

    if (response.data) {
      setOrganizationUnit(response.data.items || [])
    }
  }, [getOrganizationUnitMutation])

  const getOptionUsers = useCallback(async () => {
    const response = await getUsersQuery.refetch()

    if (response.data) {
      setUsers(response.data.items || [])
    }
  }, [getUsersQuery])

  useEffect(() => {
    if (open) {
      getOptionOrganizationUnit()
      getOptionUsers()
      refetchSubtasks()
    }
  }, [open, getOptionOrganizationUnit, getOptionUsers, refetchSubtasks])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[90vh] w-full max-w-4xl flex-col sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Thêm mới đề nghị phối hợp xác minh</DialogTitle>
        </DialogHeader>

        {/* Thông tin vụ việc (header card) */}
        <div className="bg-muted mt-3 rounded-md p-4 text-sm">
          <div className="flex flex-col justify-between gap-4 sm:flex-row">
            <div>
              <p>
                <span className="font-medium">Tên vụ việc: </span>
                {caseInfo?.name ?? 'Cướp tài sản'}
              </p>
              <p className="mt-1">
                <span className="font-medium">Người được trợ giúp: </span>
                {caseInfo?.rpName ?? 'Khuất Văn Tú Tiến'}
              </p>
            </div>

            <div className="text-sm">
              <p>
                <span className="font-medium">CCCD/DDCN: </span>
                {caseInfo?.rpCardNumber ?? '001123456789'}
              </p>
            </div>
          </div>
        </div>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto">
          {/* Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="mt-6 space-y-6">
              {/* Row 1: Số xác minh | Ngày xác minh | Cơ quan xác minh */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <FormField
                  control={form.control}
                  name="soXacMinh"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Số xác minh <Required />
                      </FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Nhập số" {...field} disabled={isDisabled} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ngayXacMinh"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày xác minh <Required />
                      </FormLabel>
                      <FormControl>
                        <DatePickerInput
                          placeholder="dd/mm/yyyy"
                          disabled={isDisabled}
                          {...field}
                          value={field.value || ''}
                          disallowFuture={true}
                          onValidate={(ok, msg) => {
                            if (ok) form.clearErrors('ngayXacMinh')
                            else
                              form.setError('ngayXacMinh', {
                                type: 'manual',
                                message: msg || 'Ngày không hợp lệ',
                              })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="orgUnitId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Cơ quan xác minh <Required />
                      </FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value} disabled={isDisabled}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                          <SelectContent>
                            {organizationUnit.length > 0 ? (
                              organizationUnit.map(item => (
                                <SelectItem key={item.id} value={item.id}>
                                  {item.name}
                                </SelectItem>
                              ))
                            ) : (
                              <div className="text-muted-foreground px-2 py-1 text-sm">Không có dữ liệu</div>
                            )}
                            {/* <SelectItem value="ngan">Nguyễn Thu Ngân</SelectItem>
                          <SelectItem value="an">Nguyễn Văn An</SelectItem>
                          <SelectItem value="hieu">Trần Thị Hiếu</SelectItem> */}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Nội dung đề nghị xác minh (textarea full width) */}
              <FormField
                control={form.control}
                name="noiDung"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nội dung đề nghị xác minh <Required />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nhập nội dung"
                        {...field}
                        className="min-h-[120px] resize-none"
                        disabled={isDisabled}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Row 2: Ngày đề nghị phản hồi | Số văn bản trả lời | Ngày văn bản trả lời */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <FormField
                  control={form.control}
                  name="ngayDeNghiPhanHoi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày đề nghị phản hồi <Required />
                      </FormLabel>
                      <FormControl>
                        <DatePickerInput
                          placeholder="dd/mm/yyyy"
                          disabled={isDisabled}
                          {...field}
                          value={field.value || ''}
                          disallowFuture={false}
                          onValidate={(ok, msg) => {
                            if (ok) form.clearErrors('ngayDeNghiPhanHoi')
                            else
                              form.setError('ngayDeNghiPhanHoi', {
                                type: 'manual',
                                message: msg || 'Ngày không hợp lệ',
                              })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="soVanBanTraLoi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số văn bản trả lời</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Nhập số" {...field} disabled={isDisabled} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ngayVanBanTraLoi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày văn bản trả lời <Required />
                      </FormLabel>
                      <FormControl>
                        <DatePickerInput
                          placeholder="dd/mm/yyyy"
                          disabled={isDisabled}
                          {...field}
                          value={field.value || ''}
                          disallowFuture={false}
                          onValidate={(ok, msg) => {
                            if (ok) form.clearErrors('ngayVanBanTraLoi')
                            else
                              form.setError('ngayVanBanTraLoi', {
                                type: 'manual',
                                message: msg || 'Ngày không hợp lệ',
                              })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Row 3: Kết quả xác minh | Cán bộ thực hiện */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="ketQuaXacMinh"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Kết quả xác minh <Required />
                      </FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Nhập kết quả" {...field} disabled={isDisabled} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="canBoThucHien"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Cán bộ thực hiện <Required />
                      </FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value} disabled={isDisabled}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn cán bộ" />
                          </SelectTrigger>
                          <SelectContent>
                            {users.length > 0 ? (
                              users.map(user => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.name}
                                </SelectItem>
                              ))
                            ) : (
                              <div className="text-muted-foreground px-2 py-1 text-sm">Không có dữ liệu</div>
                            )}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Ghi chú */}
              <FormField
                control={form.control}
                name="ghiChu"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ghi chú</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nhập ghi chú"
                        {...field}
                        className="min-h-[80px] resize-none"
                        disabled={isDisabled}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Footer buttons */}
              <DialogFooter className="flex justify-end gap-2 pt-2">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Hủy
                </Button>
                <Button type="submit" disabled={createSubtaskMutation.isPending}>
                  {createSubtaskMutation.isPending ? 'Đang gửi...' : 'Gửi yêu cầu'}
                </Button>
              </DialogFooter>
            </form>
          </Form>

          {/* Bảng kê yêu cầu xác minh */}
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-blue-600">Bảng kê yêu cầu xác minh</h3>
            </div>

            <div className="overflow-x-auto rounded-md border">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">STT</TableHead>
                    <TableHead className="w-32">Thao tác</TableHead>
                    <TableHead>Trạng thái</TableHead>
                    <TableHead>Số xác minh</TableHead>
                    <TableHead>Ngày xác minh</TableHead>
                    <TableHead>Đơn vị xác minh</TableHead>
                    <TableHead>Nội dung đề nghị</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {existingSubtasks && existingSubtasks.length > 0 ? (
                    existingSubtasks.map((subtask: WorkflowSubTask, index: number) => {
                      let parsedData: Record<string, unknown> = {}

                      try {
                        parsedData = JSON.parse(subtask.data || '{}')
                      } catch (e) {
                        console.error('Error parsing subtask data:', e)
                      }

                      // Find organization name
                      const orgUnit = organizationUnit.find(org => org.id === subtask.toOrgUnitId)

                      return (
                        <TableRow key={subtask.id || index}>
                          <TableCell>#{index + 1}</TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => subtask.id && handleEditSubtask(subtask.id)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Sửa
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => subtask.id && handleSendVerificationRequest(subtask.id)}
                                >
                                  <Send className="mr-2 h-4 w-4" />
                                  Gửi yêu cầu xác minh
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => subtask.id && handleCancelVerification(subtask.id)}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Hủy yêu cầu xác minh
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-red-500 text-white">Chưa gửi xác minh</Badge>
                          </TableCell>
                          <TableCell>{String(parsedData.soXacMinh || '-')}</TableCell>
                          <TableCell>{String(parsedData.ngayXacMinh || '-')}</TableCell>
                          <TableCell>{orgUnit?.name || subtask.toOrgUnitId || '-'}</TableCell>
                          <TableCell className="max-w-xs truncate">{String(parsedData.noiDung || '-')}</TableCell>
                        </TableRow>
                      )
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-muted-foreground py-8 text-center">
                        Chưa có yêu cầu xác minh nào
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Số dòng trên trang:</span>
                <Select value={pageSize.toString()} onValueChange={value => handlePageSizeChange(parseInt(value))}>
                  <SelectTrigger className="h-8 w-16">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">
                  Trang {currentPage} trên {totalPages || 1}
                </span>

                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToFirstPage}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToLastPage}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
