'use client'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/mi'
import { X } from 'lucide-react'
import { useEffect, useState } from 'react'

// Type definitions
interface CitizenRelative {
  relationship: string
  fullName: string
  birthDate: string
  relationshipType: string
  nationality: string
  idNumber: string
}

interface CitizenData {
  id: string
  fullName: string
  birthDate: string
  gender: string
  bloodType: string
  birthPlace: string
  idNumber: string
  socialSecurityNumber: string
  nationality: string
  ethnicity: string
  religion: string
  hometown: string
  permanentAddress: string
  currentAddress: string
  maritalStatus: string
  relatives?: CitizenRelative[]
}

interface CitizenLookupSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSelectCitizen?: (citizenData: CitizenData) => void
}

function CitizenLookupSidebar({ isOpen, onClose, onSelectCitizen }: CitizenLookupSidebarProps) {
  const [formData, setFormData] = useState({
    fullName: '',
    birthDate: '',
    gender: '',
    idNumber: '',
    birthPlace: '',
    fatherName: '',
    motherName: '',
  })

  const [searchResults, setSearchResults] = useState<CitizenData[]>([])
  const [isSearching, setIsSearching] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSearch = async () => {
    setIsSearching(true)

    try {
      // TODO: Implement actual API call to CSDLQG
      // Mock search results for now
      await new Promise(resolve => setTimeout(resolve, 1000))

      const mockResults = [
        {
          id: '1',
          // Thông tin cá nhân
          fullName: 'Nguyễn Văn Chung',
          birthDate: '06/10/1992',
          gender: 'Nam',
          bloodType: 'Nhóm O',
          birthPlace: 'Phường Hà Đông, Thành phố Hà Nội',
          idNumber: '001123456789',
          socialSecurityNumber: '09715-008546',
          nationality: 'Việt Nam',
          ethnicity: 'Mông (HMông)',
          religion: 'Chưa cập nhật',
          hometown: 'Phường Hà Đông, Thành phố Hà Nội',
          permanentAddress: 'Phường Hà Đông, Thành phố Hà Nội',
          currentAddress: 'Phường Hà Đông, Thành phố Hà Nội',
          maritalStatus: 'Đã kết hôn',

          // Thông tin người thân
          relatives: [
            {
              relationship: 'Người thứ nhất',
              fullName: 'Nguyễn Thị Mười Hoa',
              birthDate: '16/11/1972',
              relationshipType: 'Mẹ',
              nationality: 'Việt Nam',
              idNumber: '001123456789',
            },
            {
              relationship: 'Người thứ hai',
              fullName: 'Nguyễn Đình Bắc',
              birthDate: '23/10/1968',
              relationshipType: 'Bố',
              nationality: 'Việt Nam',
              idNumber: '001123456789',
            },
            {
              relationship: 'Người thứ ba',
              fullName: 'Nguyễn Mai Hồng',
              birthDate: '13/12/1998',
              relationshipType: 'Vợ',
              nationality: 'Việt Nam',
              idNumber: '001123456789',
            },
          ],
        },
      ]

      setSearchResults(mockResults)
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleReset = () => {
    setFormData({
      fullName: '',
      birthDate: '',
      gender: '',
      idNumber: '',
      birthPlace: '',
      fatherName: '',
      motherName: '',
    })
    setSearchResults([])
  }

  const handleSelectResult = (result: CitizenData) => {
    onSelectCitizen?.(result)
    onClose()
  }

  const handleOverlayClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onClose()
  }

  // Prevent body scroll when sidebar is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 z-[9998] bg-black/50" onClick={handleOverlayClick} />

      {/* Sidebar */}
      <div
        className="fixed top-0 right-0 z-[9999] h-full w-[600px] overflow-y-auto bg-white shadow-xl"
        onClick={e => e.stopPropagation()}
      >
        <div className="p-6">
          {/* Header */}
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-lg font-semibold">Tra cứu thông tin từ CSDL Quốc Gia về dân cư</h2>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Search Form */}
          <div className="mb-6 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Họ tên <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.fullName}
                  onChange={e => handleInputChange('fullName', e.target.value)}
                  placeholder="Nguyễn Văn Chung"
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Ngày sinh</label>
                <Input
                  type="date"
                  value={formData.birthDate}
                  onChange={e => handleInputChange('birthDate', e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">
                Số CMND / CCCD / DDCN <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.idNumber}
                onChange={e => handleInputChange('idNumber', e.target.value)}
                placeholder="Nhập số"
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label className="mb-2 block text-sm font-medium">Giới tính</label>
                <Select value={formData.gender} onValueChange={value => handleInputChange('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Nam" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Nam">Nam</SelectItem>
                    <SelectItem value="Nữ">Nữ</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Nơi đăng ký khai sinh</label>
                <Select value={formData.birthPlace} onValueChange={value => handleInputChange('birthPlace', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hanoi">Hà Nội</SelectItem>
                    <SelectItem value="hcm">TP.HCM</SelectItem>
                    <SelectItem value="danang">Đà Nẵng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium">Họ tên cha</label>
                <Input
                  value={formData.fatherName}
                  onChange={e => handleInputChange('fatherName', e.target.value)}
                  placeholder="Nhập họ tên"
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Họ tên mẹ</label>
                <Input
                  value={formData.motherName}
                  onChange={e => handleInputChange('motherName', e.target.value)}
                  placeholder="Nhập họ tên"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button onClick={handleSearch} disabled={isSearching} className="flex items-center gap-2">
                🔍 {isSearching ? 'Đang tra cứu...' : 'Tra cứu'}
              </Button>
              <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
                🔄 Nhập mới
              </Button>
            </div>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="border-t pt-6">
              {searchResults.map(result => (
                <div key={result.id} className="space-y-6">
                  {/* Header */}
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-blue-600">
                      Thông tin công dân &quot;{result.fullName}&quot; trong CSDL Quốc Gia về dân cư
                    </h3>
                  </div>

                  {/* Thông tin cá nhân */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold text-blue-600 uppercase">THÔNG TIN CÁ NHÂN</h4>

                    <div className="grid grid-cols-2 gap-x-8 gap-y-3 text-sm">
                      <div className="flex">
                        <span className="w-20 text-gray-600">Họ tên</span>
                        <span className="font-medium">{result.fullName}</span>
                      </div>
                      <div className="flex">
                        <span className="w-20 text-gray-600">Ngày sinh</span>
                        <span>{result.birthDate}</span>
                      </div>

                      <div className="flex">
                        <span className="w-20 text-gray-600">Giới tính</span>
                        <span>{result.gender}</span>
                      </div>
                      <div className="flex">
                        <span className="w-20 text-gray-600">Nhóm máu</span>
                        <span>{result.bloodType}</span>
                      </div>

                      <div className="col-span-2 flex">
                        <span className="w-20 text-gray-600">Nơi ĐKKS</span>
                        <span>{result.birthPlace}</span>
                      </div>

                      <div className="flex">
                        <span className="w-20 text-gray-600">CCCD/ĐDCN</span>
                        <span>{result.idNumber}</span>
                      </div>
                      <div className="flex">
                        <span className="w-20 text-gray-600">Số SHK</span>
                        <span>{result.socialSecurityNumber}</span>
                      </div>

                      <div className="flex">
                        <span className="w-20 text-gray-600">Quốc tịch</span>
                        <span>{result.nationality}</span>
                      </div>
                      <div className="flex">
                        <span className="w-20 text-gray-600">Dân tộc</span>
                        <span>{result.ethnicity}</span>
                      </div>

                      <div className="flex">
                        <span className="w-20 text-gray-600">Tôn giáo</span>
                        <span className="text-gray-500 italic">{result.religion}</span>
                      </div>

                      <div className="col-span-2 flex">
                        <span className="w-20 text-gray-600">Quê quán</span>
                        <span>{result.hometown}</span>
                      </div>

                      <div className="col-span-2 flex">
                        <span className="w-20 text-gray-600">Nơi thường trú</span>
                        <span>{result.permanentAddress}</span>
                      </div>

                      <div className="col-span-2 flex">
                        <span className="w-20 text-gray-600">Nơi ở hiện tại</span>
                        <span>{result.currentAddress}</span>
                      </div>

                      <div className="flex">
                        <span className="w-20 text-gray-600">Tình trạng hôn nhân</span>
                        <span>{result.maritalStatus}</span>
                      </div>
                    </div>
                  </div>

                  {/* Thông tin người thân */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold text-blue-600 uppercase">THÔNG TIN NGƯỜI THÂN</h4>

                    {result.relatives?.map((relative, index) => (
                      <div key={index} className="border-l-2 border-gray-200 pl-4">
                        <h5 className="mb-2 text-sm font-medium">{relative.relationship}</h5>
                        <div className="grid grid-cols-2 gap-x-8 gap-y-2 text-sm">
                          <div className="flex">
                            <span className="w-24 text-gray-600">Họ tên</span>
                            <span className="font-medium">{relative.fullName}</span>
                          </div>
                          <div className="flex">
                            <span className="w-24 text-gray-600">Ngày sinh</span>
                            <span>{relative.birthDate}</span>
                          </div>

                          <div className="flex">
                            <span className="w-24 text-gray-600">Mối quan hệ</span>
                            <span>{relative.relationshipType}</span>
                          </div>
                          <div className="flex">
                            <span className="w-24 text-gray-600">Quốc tịch</span>
                            <span>{relative.nationality}</span>
                          </div>

                          <div className="col-span-2 flex">
                            <span className="w-24 text-gray-600">CCCD/ĐDCN</span>
                            <span>{relative.idNumber}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <div className="border-t pt-4">
                    <button
                      onClick={() => handleSelectResult(result)}
                      className="w-full rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700"
                    >
                      Chọn thông tin này để điền vào form
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {isSearching && (
            <div className="border-t pt-6">
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
                  <p className="text-gray-600">Đang tra cứu thông tin...</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
