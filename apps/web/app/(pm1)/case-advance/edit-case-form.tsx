'use client'

import { CATEGORY_TYPE_VALUES } from '@/app/(pm2)/categories/category-types'
import { useAllCategoriesQuery, useCategoriesQuery, useUpdateCaseAdvance } from '@/lib/hooks'
import { Category } from '@/lib/services/types'
import { Any, AnyRecord } from '@/lib/types'
import { GENDER } from '@/lib/types'
import { generateCaseCode } from '@/lib/utils/case-code-generator'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Button,
  Card,
  CardContent,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@workspace/ui/mi'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

const formSchema = z.object({
  code: z.string().optional(),
  formsOfAssistanceId: z.string().optional(),
  isOldLegalAidCase: z.boolean().optional(),
  isNotFullProfile: z.boolean().optional(),
  rpName: z.string().optional(),
  rpBirthDate: z
    .string()
    .optional()
    .refine(
      date => {
        if (!date) return true // Optional field
        const selectedDate = new Date(date)
        const today = new Date()
        today.setHours(23, 59, 59, 999) // End of today

        return selectedDate <= today
      },
      {
        message: 'Ngày sinh không được là ngày trong tương lai',
      }
    ),
  rpSex: z.number().optional(),
  rpCardNumber: z.string().optional(),
  rpPublishedDate: z
    .string()
    .optional()
    .refine(
      date => {
        if (!date) return true // Optional field
        const selectedDate = new Date(date)
        const today = new Date()
        today.setHours(23, 59, 59, 999) // End of today

        return selectedDate <= today
      },
      {
        message: 'Ngày cấp không được là ngày trong tương lai',
      }
    ),
  rpPublishedPlace: z.string().optional(),
  rpEthnic: z.string().optional(),
  rpPhone: z.string().optional(),
  rpJob: z.string().optional(),
  rpProvince: z.string().optional(),
  rpDistrict: z.string().optional(),
  rpWard: z.string().optional(),
  rpAddress: z.string().optional(),
  objectLegalFieldId: z.string().optional(),
  guardianName: z.string().optional(),
  relationRp: z.string().optional(),
  guardianPhone: z.string().optional(),
  guardianCardNumber: z.string().optional(),
  guardianPublishedDate: z
    .string()
    .optional()
    .refine(
      date => {
        if (!date) return true // Optional field
        const selectedDate = new Date(date)
        const today = new Date()
        today.setHours(23, 59, 59, 999) // End of today

        return selectedDate <= today
      },
      {
        message: 'Ngày cấp không được là ngày trong tương lai',
      }
    ),
  guardianPublishedPlace: z.string().optional(),
  guardianProvince: z.string().optional(),
  guardianDistrict: z.string().optional(),
  guardianWard: z.string().optional(),
  guardianAddress: z.string().optional(),
  name: z.string().optional(),
  entryDate: z.string().optional(),
  legalFieldId: z.string().optional(),
  rpRequestSource: z.string().optional(),
  performer: z.string().optional(),
  placeOfDisposal: z.string().optional(),
  fromStage: z.string().optional(),
  toStage: z.string().optional(),
  contentRequest: z.string().optional(),
  assignedOfficer: z.string().optional(),
  coordinatorName: z.string().optional(),
  coordinatorId: z.string().optional(),
  appointmentDate: z
    .string()
    .optional()
    .refine(
      date => {
        if (!date) return true // Optional field
        const selectedDate = new Date(date)
        const today = new Date()
        today.setHours(0, 0, 0, 0) // Start of today

        return selectedDate >= today
      },
      {
        message: 'Ngày hẹn phải là ngày trong tương lai',
      }
    ),
  appointmentPlace: z.string().optional(),
  appointmentContent: z.string().optional(),
  attachForRequestPerson: z.boolean().optional(),
  attachForLegalCase: z.boolean().optional(),
  isNotFullDocument: z.boolean().optional(),
  documentAttachName: z.string().optional(),
  note: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

interface EditCaseFormProps {
  initialData?: Any
  onSuccess?: () => void
  onCancel?: () => void
}

interface FormRef {
  handleSubmit: () => void
  reset: () => void
}

// Helper function to format date for input
const formatDateForInput = (dateValue: string): string => {
  if (!dateValue) return ''

  try {
    const date = new Date(dateValue)

    if (isNaN(date.getTime())) return ''

    return date.toISOString().split('T')[0] || ''
  } catch {
    return ''
  }
}

export const EditCaseForm = forwardRef<FormRef, EditCaseFormProps>(({ initialData, onSuccess, onCancel }, ref) => {
  const updateMutation = useUpdateCaseAdvance()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: initialData?.code || '',
      formsOfAssistanceId: initialData?.formsOfAssistanceId || '',
      isOldLegalAidCase: initialData?.isOldLegalAidCase ? true : false,
      rpName: initialData?.rpName || '',
      rpSex: initialData?.rpSex || undefined,
      rpCardNumber: initialData?.rpCardNumber || '',
      rpBirthDate: formatDateForInput(initialData?.rpBirthDate),
      rpPublishedDate: formatDateForInput(initialData?.rpPublishedDate),
      rpPublishedPlace: initialData?.rpPublishedPlace || '',
      rpEthnic: initialData?.rpEthnic || '',
      rpPhone: initialData?.rpPhone || '',
      rpJob: initialData?.rpJob || '',
      rpProvince: initialData?.rpProvince || '',
      rpDistrict: initialData?.rpDistrict || '',
      rpWard: initialData?.rpWard || '',
      rpAddress: initialData?.rpAddress || '',
      objectLegalFieldId: initialData?.objectLegalFieldId || '',
      guardianName: initialData?.guardianName || '',
      relationRp: initialData?.relationRp || '',
      guardianPhone: initialData?.guardianPhone || '',
      guardianCardNumber: initialData?.guardianCardNumber || '',
      guardianPublishedDate: formatDateForInput(initialData?.guardianPublishedDate),
      guardianPublishedPlace: initialData?.guardianPublishedPlace || '',
      guardianProvince: initialData?.guardianProvince || '',
      guardianDistrict: initialData?.guardianDistrict || '',
      guardianWard: initialData?.guardianWard || '',
      guardianAddress: initialData?.guardianAddress || '',
      name: initialData?.name || '',
      entryDate: formatDateForInput(initialData?.entryDate),
      legalFieldId: initialData?.legalFieldId || '',
      rpRequestSource: initialData?.rpRequestSource || '',
      performer: initialData?.performer || '',
      placeOfDisposal: initialData?.placeOfDisposal || '',
      fromStage: initialData?.fromStage || '',
      toStage: initialData?.toStage || '',
      contentRequest: initialData?.contentRequest || '',
      assignedOfficer: initialData?.assignedOfficer || '',
      coordinatorName: initialData?.coordinatorName || '',
      coordinatorId: initialData?.coordinatorId || '',
      appointmentDate: formatDateForInput(initialData?.appointmentDate),
      appointmentPlace: initialData?.appointmentPlace || '',
      appointmentContent: initialData?.appointmentContent || '',
      attachForRequestPerson: initialData?.attachForRequestPerson ? true : false,
      attachForLegalCase: initialData?.attachForLegalCase ? true : false,
      isNotFullProfile: initialData?.isNotFullProfile ? true : false,
      documentAttachName: initialData?.documentAttachName || '',
      note: initialData?.note || '',
    },
  })

  // State for tracking selected province for dependent dropdowns
  const [selectedRpProvince, setSelectedRpProvince] = useState(initialData?.rpProvince || '')
  const [selectedGuardianProvince, setSelectedGuardianProvince] = useState(initialData?.guardianProvince || '')

  // State for storing filtered options
  const [rpWardOptions, setRpWardOptions] = useState<Category[]>([])
  const [guardianWardOptions, setGuardianWardOptions] = useState<Category[]>([])

  // State for file attachment
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null)

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    setSelectedFiles(files)

    // Set document attach name (comma-separated file names)
    if (files && files.length > 0) {
      const fileNames = Array.from(files)
        .map(file => file.name)
        .join(', ')
      form.setValue('documentAttachName', fileNames)
    } else {
      form.setValue('documentAttachName', '')
    }
  }

  useImperativeHandle(ref, () => ({
    handleSubmit: () => {
      form.handleSubmit(onSubmit)()
    },
    reset: () => {
      form.reset()
    },
  }))

  // Fetch categories for dropdowns - same pattern as case-advance-form.tsx
  const getCategoryMutation = useAllCategoriesQuery(
    `${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP},${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.NOI_CAP},${CATEGORY_TYPE_VALUES.DAN_TOC_CHI_TIET},${CATEGORY_TYPE_VALUES.TINH_TP},${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP},${CATEGORY_TYPE_VALUES.DOI_TUONG}`
  )

  // Queries for dependent dropdowns (Ward based on Province)
  const getRpWardQuery = useCategoriesQuery({
    page: 1,
    pageSize: 1000,
    filter: {
      type: CATEGORY_TYPE_VALUES.PHUONG_XA,
      parentId: selectedRpProvince || undefined,
    },
  })

  const getGuardianWardQuery = useCategoriesQuery({
    page: 1,
    pageSize: 1000,
    filter: {
      type: CATEGORY_TYPE_VALUES.PHUONG_XA,
      parentId: selectedGuardianProvince || undefined,
    },
  })

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const hinhThucTroGiup =
    getCategoryMutation.data?.filter(
      item =>
        item.type === CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP ||
        item.type === CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET
    ) || []

  const linhVucTroGiupChiTiet =
    getCategoryMutation.data?.filter(
      item =>
        item.type === CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP_CHI_TIET ||
        item.type === CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP
    ) || []

  const doiTuong = getCategoryMutation.data?.filter(item => item.type === CATEGORY_TYPE_VALUES.DOI_TUONG) || []

  const noiCap = getCategoryMutation.data?.filter(item => item.type === CATEGORY_TYPE_VALUES.NOI_CAP) || []

  const danTocChiTiet =
    getCategoryMutation.data?.filter(item => item.type === CATEGORY_TYPE_VALUES.DAN_TOC_CHI_TIET) || []

  const tinhTP = getCategoryMutation.data?.filter(item => item.type === CATEGORY_TYPE_VALUES.TINH_TP) || []

  // Functions to fetch dependent options
  const fetchRpWardOptions = async () => {
    const response = await getRpWardQuery.refetch()

    if (response.data) {
      // Handle both old and new API response structures
      const items = response.data.items || response.data.data || []
      setRpWardOptions(items)
    }
  }

  const fetchGuardianWardOptions = async () => {
    const response = await getGuardianWardQuery.refetch()

    if (response.data) {
      // Handle both old and new API response structures
      const items = response.data.items || response.data.data || []
      setGuardianWardOptions(items)
    }
  }

  // Auto-generate case code when form of assistance changes
  const handleFormsOfAssistanceChange = useCallback(
    (value: string) => {
      const selectedCategory = hinhThucTroGiup.find(item => item.categoryId === value)

      if (selectedCategory) {
        const generatedCode = generateCaseCode(selectedCategory)
        form.setValue('code', generatedCode)
      }
    },
    [hinhThucTroGiup, form]
  )

  // Effects to fetch dependent options when parent changes
  useEffect(() => {
    if (selectedRpProvince) {
      fetchRpWardOptions()
      // Reset ward when province changes
      form.setValue('rpWard', '')
    } else {
      setRpWardOptions([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRpProvince])

  useEffect(() => {
    if (selectedGuardianProvince) {
      fetchGuardianWardOptions()
      // Reset ward when province changes
      form.setValue('guardianWard', '')
    } else {
      setGuardianWardOptions([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedGuardianProvince])

  const onSubmit = async (data: FormData) => {
    console.log('EditCaseForm onSubmit called with data:', data)

    try {
      // Create API payload with proper type conversions
      const processedData: AnyRecord = {
        ...data,
        // Convert booleans to numbers for API
        attachForRequestPerson: data.attachForRequestPerson ? 1 : 0,
        attachForLegalCase: data.attachForLegalCase ? 1 : 0,
        isOldLegalAidCase: data.isOldLegalAidCase ? 1 : 0,
        isNotFullProfile: data.isNotFullProfile ? 1 : 0,
        ...(data.formsOfAssistanceId && {
          formsOfAssistance: hinhThucTroGiup.find(item => item.categoryId === data.formsOfAssistanceId)?.name || '',
        }),
        ...(data.legalFieldId && {
          legalField: linhVucTroGiupChiTiet.find(item => item.categoryId === data.legalFieldId)?.name || '',
        }),
        ...(data.objectLegalFieldId && {
          objectLegalField: doiTuong.find(item => item.categoryId === data.objectLegalFieldId)?.name || '',
        }),
      }

      // Remove empty fields
      Object.keys(processedData).forEach(key => {
        if (processedData[key] === undefined || processedData[key] === null || processedData[key] === '') {
          delete processedData[key]
        }
      })

      console.log('Calling updateMutation with:', {
        id: initialData?.id,
        data: processedData,
      })

      await updateMutation.mutateAsync({
        id: initialData?.id,
        data: processedData,
      })

      console.log('Update successful, calling onSuccess')
      onSuccess?.()
    } catch (error) {
      console.error('Error updating case advance:', error)
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={e => {
          console.log('Form onSubmit event triggered')
          form.handleSubmit(onSubmit)(e)
        }}
        className="space-y-6"
      >
        <Card>
          <CardContent className="space-y-4 pt-6">
            <div className="text-muted-foreground mb-4 text-sm">Hãy chọn &quot;Hình thức trợ giúp&quot; trước tiên</div>
            <div className="rounded-lg bg-gray-50 p-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="formsOfAssistanceId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Hình thức trợ giúp <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={value => {
                          field.onChange(value)
                          handleFormsOfAssistanceChange(value)
                        }}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {hinhThucTroGiup.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Mã vụ việc <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Sẽ tự động tạo khi chọn hình thức trợ giúp" readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="isOldLegalAidCase"
                render={({ field }) => (
                  <FormItem className="mt-4 flex flex-row items-start space-y-0 space-x-3">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Là hồ sơ cũ</FormLabel>
                  </FormItem>
                )}
              />
            </div>

            {/* Thông tin người được TGPL */}
            <div className="space-y-4 pt-6">
              <div className="flex items-center justify-between">
                <h3 className="text-primary text-lg font-semibold">Thông tin người được TGPL</h3>
                <button type="button" className="text-primary hover:text-primary/80 text-sm">
                  🔍 Tra cứu thông tin từ CSDLQG về dân cư
                </button>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="rpName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Họ tên người được trợ giúp <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập họ tên" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpBirthDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày sinh <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          placeholder="Chọn ngày"
                          max={new Date().toISOString().split('T')[0]}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpSex"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Giới tính <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select value={field.value?.toString()} onValueChange={value => field.onChange(parseInt(value))}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={GENDER.MALE.toString()}>Nam</SelectItem>
                          <SelectItem value={GENDER.FEMALE.toString()}>Nữ</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="rpCardNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Số căn cước <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số căn cước" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpPublishedDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày cấp <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} type="date" max={new Date().toISOString().split('T')[0]} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpPublishedPlace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Nơi cấp <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {noiCap.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="rpEthnic"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dân tộc</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {danTocChiTiet.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số điện thoại</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số điện thoại" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpJob"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nghề nghiệp</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập nghề nghiệp" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="rpProvince"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tỉnh/Thành phố <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={value => {
                          field.onChange(value)
                          setSelectedRpProvince(value)
                        }}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tinhTP.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpWard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Phường/Xã <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {rpWardOptions?.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          )) || []}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Địa chỉ</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập địa chỉ" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="objectLegalFieldId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Thuộc diện trợ giúp <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Chọn" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {doiTuong.map(item => (
                          <SelectItem key={item.categoryId} value={item.categoryId}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Thông tin người yêu cầu TGPL */}
            <div className="space-y-4 pt-6">
              <div className="flex items-center justify-between">
                <h3 className="text-primary text-lg font-semibold">Thông tin người yêu cầu TGPL</h3>
                <button type="button" className="text-primary hover:text-primary/80 text-sm">
                  🔍 Tra cứu thông tin từ CSDLQG về dân cư
                </button>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="guardianName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Họ tên người yêu cầu</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập họ tên" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="relationRp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mối quan hệ với người được TGPL</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập mối quan hệ" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="guardianPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số điện thoại</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số điện thoại" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="guardianCardNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số căn cước</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập số căn cước" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="guardianPublishedDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ngày cấp</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" max={new Date().toISOString().split('T')[0]} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="guardianPublishedPlace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nơi cấp</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {noiCap.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="guardianProvince"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tỉnh/Thành phố</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={value => {
                          field.onChange(value)
                          setSelectedGuardianProvince(value)
                        }}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tinhTP.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="guardianWard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phường/Xã</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {guardianWardOptions?.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          )) || []}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="guardianAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Địa chỉ</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập địa chỉ" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Thông tin vụ việc TGPL */}
            <div className="space-y-4 pt-6">
              <h3 className="text-primary text-lg font-semibold">Thông tin vụ việc TGPL</h3>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Tên vụ việc <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nhập tên vụ việc" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="entryDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Ngày thụ lý <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="legalFieldId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Lĩnh vực <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {linhVucTroGiupChiTiet.map(item => (
                            <SelectItem key={item.categoryId} value={item.categoryId}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rpRequestSource"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nguồn yêu cầu</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="direct">Trực tiếp</SelectItem>
                          <SelectItem value="online">Trực tuyến</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="performer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Người thực hiện</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập tên" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="placeOfDisposal"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Địa điểm thực hiện</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="office">Văn phòng</SelectItem>
                          <SelectItem value="home">Tại nhà</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Giai đoạn bảo vệ */}
            <div className="space-y-4 pt-6">
              <h3 className="text-primary text-lg font-semibold">Giai đoạn bảo vệ</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="fromStage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Từ giai đoạn <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="stage1">Giai đoạn 1</SelectItem>
                          <SelectItem value="stage2">Giai đoạn 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="toStage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Đến giai đoạn</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="stage1">Giai đoạn 1</SelectItem>
                          <SelectItem value="stage2">Giai đoạn 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Nội dung yêu cầu TGPL */}
            <div className="space-y-4 pt-6">
              <h3 className="text-lg font-semibold">
                Nội dung yêu cầu TGPL <span className="text-red-500">*</span>
              </h3>
              <FormField
                control={form.control}
                name="contentRequest"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea {...field} placeholder="Nhập nội dung" className="min-h-[100px]" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tài liệu kèm theo đơn */}
            <div className="space-y-4 pt-6">
              <h3 className="text-lg font-semibold">Tài liệu kèm theo đơn</h3>
              <FormField
                control={form.control}
                name="attachForRequestPerson"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Giấy tờ chứng minh người thuộc diện trợ giúp pháp lý</FormLabel>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="attachForLegalCase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Các giấy tờ, tài liệu có liên quan đến vụ việc trợ giúp pháp lý</FormLabel>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isNotFullProfile"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Chưa đầy đủ hồ sơ</FormLabel>
                  </FormItem>
                )}
              />

              <div className="space-y-3">
                <label className="text-sm font-medium">Tệp đính kèm</label>
                <div className="flex items-center justify-between rounded-lg border border-dashed border-gray-300 p-4">
                  <div className="flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      onClick={() => document.getElementById('file-input')?.click()}
                    >
                      Chọn tệp
                    </Button>
                    <span className="text-muted-foreground text-sm">
                      {selectedFiles && selectedFiles.length > 0
                        ? Array.from(selectedFiles)
                            .map(file => file.name)
                            .join(', ')
                        : form.watch('documentAttachName') || 'Không có tệp nào được chọn'}
                    </span>
                  </div>
                </div>
                <input
                  id="file-input"
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                />
              </div>
            </div>

            {/* Ghi chú */}
            <div className="space-y-4 pt-6">
              <h3 className="text-primary text-lg font-semibold">Ghi chú</h3>
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea {...field} placeholder="Nhập ghi chú" className="min-h-[100px]" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Thông tin phân công thực hiện TGPL */}
            <div className="space-y-4 pt-6">
              <h3 className="text-primary text-lg font-semibold">Thông tin phân công thực hiện TGPL</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="performer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cán bộ thực hiện</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="officer1">Cán bộ 1</SelectItem>
                          <SelectItem value="officer2">Cán bộ 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="coordinatorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cán bộ phối hợp</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={value => {
                          field.onChange(value)
                          // Set coordinatorName based on selected ID
                          const coordinatorOptions = [
                            { id: 'coordinator1', name: 'Cán bộ phối hợp 1' },
                            { id: 'coordinator2', name: 'Cán bộ phối hợp 2' },
                          ]
                          const selectedCoordinator = coordinatorOptions.find(coord => coord.id === value)

                          if (selectedCoordinator) {
                            form.setValue('coordinatorName', selectedCoordinator.name)
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="coordinator1">Cán bộ phối hợp 1</SelectItem>
                          <SelectItem value="coordinator2">Cán bộ phối hợp 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Thông tin phiếu hẹn */}
            <div className="space-y-4 pt-6">
              <h3 className="text-primary text-lg font-semibold">Thông tin phiếu hẹn</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="appointmentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ngày hẹn</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" min={new Date().toISOString().split('T')[0]} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="appointmentPlace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Địa điểm hẹn</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập địa chỉ" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="appointmentContent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Giấy tờ cần mang theo</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nhập nội dung" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4 border-t pt-6">
              <Button type="button" variant="outline" onClick={onCancel} disabled={updateMutation.isPending}>
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={updateMutation.isPending}
                className="min-w-[120px]"
                onClick={async e => {
                  console.log('Submit button clicked')
                  console.log('Form errors:', form.formState.errors)
                  console.log('Form values:', form.getValues())
                  console.log('Form is valid:', form.formState.isValid)

                  // Force trigger validation and submit
                  console.log('Force triggering form validation and submit')
                  e.preventDefault()

                  // Trigger validation first
                  const isValid = await form.trigger()
                  console.log('After trigger validation, isValid:', isValid)
                  console.log('Updated form errors:', form.formState.errors)

                  if (isValid) {
                    console.log('Form is valid, calling onSubmit directly')
                    // Use handleSubmit to ensure proper data processing
                    await form.handleSubmit(onSubmit)()
                  } else {
                    console.log('Form validation failed, not submitting')
                  }
                }}
              >
                {updateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  )
})

EditCaseForm.displayName = 'EditCaseForm'
