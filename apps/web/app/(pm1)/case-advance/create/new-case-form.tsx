'use client'

import { CATEGORY_TYPE_VALUES } from '@/app/(pm2)/categories/category-types'
import { useAllCategoriesQuery, useCreateCaseAdvance } from '@/lib/hooks'
import { Any, GENDER } from '@/lib/types'
import { generateCaseCode } from '@/lib/utils/case-code-generator'
import { zodResolver } from '@hookform/resolvers/zod'
import { DatePickerInput } from '@workspace/ui/components/date-picker-input'
import {
  Button,
  Card,
  CardContent,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@workspace/ui/mi'
import { Combobox } from '@workspace/ui/mi/combobox'
import { useTranslations } from 'next-intl'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { type CitizenData, CitizenLookup } from '../../../../components/citizen-lookup'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const createFormSchema = (_t: unknown) => {
  // Fallback messages in case translation fails
  const messages = {
    codeRequired: 'Mã vụ việc là bắt buộc',
    formsOfAssistanceRequired: 'Hình thức trợ giúp là bắt buộc',
    rpNameRequired: 'Họ tên người được trợ giúp là bắt buộc',
    rpBirthDateRequired: 'Ngày sinh là bắt buộc',
    rpBirthDateFuture: 'Ngày sinh không được là ngày trong tương lai',
    rpSexRequired: 'Giới tính là bắt buộc',
    rpCardNumberRequired: 'Số căn cước là bắt buộc',
    rpPublishedDateRequired: 'Ngày cấp là bắt buộc',
    rpPublishedDateFuture: 'Ngày cấp không được là ngày trong tương lai',
    rpPublishedPlaceRequired: 'Nơi cấp là bắt buộc',
    rpProvinceRequired: 'Tỉnh/Thành phố là bắt buộc',
    objectLegalFieldRequired: 'Thuộc diện trợ giúp là bắt buộc',
    caseNameRequired: 'Tên vụ việc là bắt buộc',
    entryDateRequired: 'Ngày thụ lý là bắt buộc',
    legalFieldRequired: 'Lĩnh vực là bắt buộc',
    fromStageRequired: 'Từ giai đoạn là bắt buộc',
    contentRequestRequired: 'Nội dung yêu cầu TGPL là bắt buộc',
    guardianPublishedDateFuture: 'Ngày cấp không được là ngày trong tương lai',
    appointmentDateFuture: 'Ngày hẹn phải là ngày trong tương lai',
  }

  return z.object({
    code: z.string().min(1, messages.codeRequired),
    formsOfAssistanceId: z.string().min(1, messages.formsOfAssistanceRequired),
    isOldLegalAidCase: z.boolean().optional(),
    isNotFullProfile: z.boolean().optional(),
    rpName: z.string().min(1, messages.rpNameRequired),
    rpBirthDate: z
      .string()
      .min(1, messages.rpBirthDateRequired)
      .refine(
        date => {
          if (!date) return false // Required field
          const selectedDate = new Date(date)
          const today = new Date()
          today.setHours(23, 59, 59, 999) // End of today

          return selectedDate <= today
        },
        {
          message: messages.rpBirthDateFuture,
        }
      ),
    rpSex: z.number({ error: messages.rpSexRequired }).min(1, messages.rpSexRequired),
    rpCardNumber: z.string().min(1, messages.rpCardNumberRequired),
    rpPublishedDate: z
      .string()
      .min(1, messages.rpPublishedDateRequired)
      .refine(
        date => {
          if (!date) return false // Required field
          const selectedDate = new Date(date)
          const today = new Date()
          today.setHours(23, 59, 59, 999) // End of today

          return selectedDate <= today
        },
        {
          message: messages.rpPublishedDateFuture,
        }
      ),
    rpPublishedPlace: z.string().min(1, messages.rpPublishedPlaceRequired),
    rpEthnic: z.string().optional(),
    rpPhone: z.string().optional(),
    rpJob: z.string().optional(),
    rpProvince: z.string().min(1, messages.rpProvinceRequired),
    rpDistrict: z.string().optional(),
    rpWard: z.string().optional(),
    rpAddress: z.string().optional(),
    objectLegalFieldId: z.string().min(1, messages.objectLegalFieldRequired),
    guardianName: z.string().optional(),
    relationRp: z.string().optional(),
    guardianPhone: z.string().optional(),
    guardianCardNumber: z.string().optional(),
    guardianPublishedDate: z
      .string()
      .optional()
      .refine(
        date => {
          if (!date) return true // Optional field
          const selectedDate = new Date(date)
          const today = new Date()
          today.setHours(23, 59, 59, 999) // End of today

          return selectedDate <= today
        },
        {
          message: messages.guardianPublishedDateFuture,
        }
      ),
    guardianPublishedPlace: z.string().optional(),
    guardianProvince: z.string().optional(),
    guardianDistrict: z.string().optional(),
    guardianWard: z.string().optional(),
    guardianAddress: z.string().optional(),
    name: z.string().min(1, messages.caseNameRequired),
    entryDate: z.string().min(1, messages.entryDateRequired),
    legalFieldId: z.string().min(1, messages.legalFieldRequired),
    rpRequestSource: z.string().optional(),
    performer: z.string().optional(),
    placeOfDisposal: z.string().optional(),
    fromStage: z.string().min(1, messages.fromStageRequired),
    toStage: z.string().optional(),
    contentRequest: z.string().min(1, messages.contentRequestRequired),
    assignedOfficer: z.string().optional(),
    coordinatorName: z.string().optional(),
    coordinatorId: z.string().optional(),
    appointmentDate: z
      .string()
      .optional()
      .refine(
        date => {
          if (!date) return true // Optional field
          const selectedDate = new Date(date)
          const today = new Date()
          today.setHours(0, 0, 0, 0) // Start of today

          return selectedDate >= today
        },
        {
          message: messages.appointmentDateFuture,
        }
      ),
    appointmentPlace: z.string().optional(),
    appointmentContent: z.string().optional(),
    attachForRequestPerson: z.boolean().optional(),
    attachForLegalCase: z.boolean().optional(),
    documentAttachName: z.string().optional(),
    note: z.string().optional(),
  })
}

interface NewCaseFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

// Helper function to create API request for categories
const createCategoryApiRequest = (categoryType: string, parentId?: string) => {
  return async (keyword?: string) => {
    if (!categoryType || categoryType.trim() === '') {
      return []
    }

    try {
      const response = await fetch(
        '/ac-apis/categories?' +
          new URLSearchParams({
            page: '1',
            pageSize: '50',
            filter: JSON.stringify({
              type: categoryType,
              ...(parentId && { parentId }),
            }),
            ...(keyword && { keyword }),
          })
      )

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      const items = data.items || data.data || []

      return items.map((item: { categoryId: string; name: string }) => ({
        value: item.categoryId,
        label: item.name,
      }))
    } catch (error) {
      console.error('Error fetching categories:', error)

      return []
    }
  }
}

interface FormRef {
  handleSubmit: () => void
  reset: () => void
}

export const NewCaseForm = forwardRef<FormRef, NewCaseFormProps>(({ onSuccess }, ref) => {
  const _t = useTranslations('case-advance')
  const createMutation = useCreateCaseAdvance()

  const formSchema = createFormSchema(_t)
  type FormData = z.infer<typeof formSchema>

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: {
      code: '',
      formsOfAssistanceId: '',
      isOldLegalAidCase: false,
      isNotFullProfile: false,
      rpName: '',
      rpBirthDate: '',
      rpSex: undefined,
      rpCardNumber: '',
      rpPublishedDate: '',
      rpPublishedPlace: '',
      rpEthnic: '',
      rpPhone: '',
      rpJob: '',
      rpProvince: '',
      rpDistrict: '',
      rpWard: '',
      rpAddress: '',
      objectLegalFieldId: '',
      guardianName: '',
      guardianPhone: '',
      guardianCardNumber: '',
      guardianPublishedDate: '',
      guardianPublishedPlace: '',
      guardianProvince: '',
      guardianDistrict: '',
      guardianWard: '',
      guardianAddress: '',
      relationRp: '',
      name: '',
      entryDate: '',
      legalFieldId: '',
      rpRequestSource: '',
      performer: '',
      placeOfDisposal: '',
      fromStage: '',
      toStage: '',
      contentRequest: '',
      assignedOfficer: '',
      coordinatorName: '',
      coordinatorId: '',
      appointmentDate: '',
      appointmentPlace: '',
      appointmentContent: '',
      attachForRequestPerson: false,
      attachForLegalCase: false,
      documentAttachName: '',
      note: '',
    },
  })

  // State for tracking selected province for dependent dropdowns
  const [selectedRpProvince, setSelectedRpProvince] = useState('')
  const [selectedGuardianProvince, setSelectedGuardianProvince] = useState('')

  // Keep state for tracking selected provinces for dependent ward dropdowns
  // (Ward comboboxes will use parentId to filter automatically)

  // State for file attachment
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null)

  // State for citizen lookup sidebar
  const [isCitizenLookupOpen, setIsCitizenLookupOpen] = useState(false)
  const [currentLookupTarget, setCurrentLookupTarget] = useState<'rp' | 'guardian' | null>(null)

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    setSelectedFiles(files)

    // Set document attach name (comma-separated file names)
    if (files && files.length > 0) {
      const fileNames = Array.from(files)
        .map(file => file.name)
        .join(', ')
      form.setValue('documentAttachName', fileNames)
    } else {
      form.setValue('documentAttachName', '')
    }
  }

  // Handle citizen lookup
  const handleOpenCitizenLookup = (target: 'rp' | 'guardian') => {
    setCurrentLookupTarget(target)
    setIsCitizenLookupOpen(true)
  }

  const handleCloseCitizenLookup = () => {
    setIsCitizenLookupOpen(false)
    setCurrentLookupTarget(null)
  }

  const handleSelectCitizen = (citizenData: CitizenData) => {
    if (currentLookupTarget === 'rp') {
      // Fill RP (Người được TGPL) fields
      form.setValue('rpName', citizenData.fullName || '')
      form.setValue(
        'rpBirthDate',
        (citizenData.birthDate ? new Date(citizenData.birthDate).toISOString().split('T')[0] : '') as string
      )
      form.setValue('rpSex', citizenData.gender === 'Nam' ? 1 : citizenData.gender === 'Nữ' ? 2 : 0)
      form.setValue('rpCardNumber', citizenData.idNumber || '')
    } else if (currentLookupTarget === 'guardian') {
      // Fill Guardian (Người yêu cầu TGPL) fields
      form.setValue('guardianName', citizenData.fullName || '')
      form.setValue('guardianCardNumber', citizenData.idNumber || '')
    }

    handleCloseCitizenLookup()
  }

  useImperativeHandle(ref, () => ({
    handleSubmit: async () => {
      const isValid = await form.trigger() // Manually trigger validation

      if (isValid) {
        form.handleSubmit(onSubmit)()
      }
    },
    reset: () => {
      form.reset()
    },
  }))

  // Fetch categories for form of assistance to generate case code
  const getCategoryMutation = useAllCategoriesQuery(
    `${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP},${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET}`
  )

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const hinhThucTroGiup =
    getCategoryMutation.data?.filter(
      item =>
        item.type === CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP ||
        item.type === CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET
    ) || []

  // Auto-generate case code when form of assistance changes
  const handleFormsOfAssistanceChange = useCallback(
    (value: string) => {
      const selectedCategory = hinhThucTroGiup.find(item => item.categoryId === value)

      if (selectedCategory) {
        const generatedCode = generateCaseCode(selectedCategory)
        form.setValue('code', generatedCode)
      }
    },
    [hinhThucTroGiup, form]
  )

  // Effects to reset ward when province changes
  useEffect(() => {
    if (selectedRpProvince) {
      // Reset ward when province changes
      form.setValue('rpWard', '')
    }
  }, [selectedRpProvince, form])

  useEffect(() => {
    if (selectedGuardianProvince) {
      // Reset ward when province changes
      form.setValue('guardianWard', '')
    }
  }, [selectedGuardianProvince, form])

  const onSubmit = async (data: FormData) => {
    try {
      const processedData = {
        ...data,
        attachForRequestPerson: data.attachForRequestPerson,
        attachForLegalCase: data.attachForLegalCase,
        isOldLegalAidCase: data.isOldLegalAidCase,
        isNotFullProfile: data.isNotFullProfile,
        ...(data.formsOfAssistanceId && {
          formsOfAssistance: hinhThucTroGiup.find(item => item.categoryId === data.formsOfAssistanceId)?.name || '',
        }),
        // Note: legalField and objectLegalField names will be resolved on server side
      }

      // Remove empty fields
      Object.keys(processedData).forEach(key => {
        const keyName = key as keyof typeof processedData

        if (processedData[keyName] === undefined || processedData[keyName] === null || processedData[keyName] === '') {
          delete processedData[keyName]
        }
      })

      await createMutation.mutateAsync(processedData as Any)
      onSuccess?.()
    } catch (error) {
      console.error('Error creating case advance:', error)
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6" noValidate>
          <Card className="h-[calc(100vh-200px)]">
            <CardContent className="h-full space-y-4 overflow-y-auto pt-6">
              <div className="text-muted-foreground mb-4 text-sm">
                Hãy chọn &ldquo;Hình thức trợ giúp&rdquo; trước tiên
              </div>
              <div className="rounded-lg bg-gray-50 p-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="formsOfAssistanceId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Hình thức trợ giúp <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            key={`${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP},${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET}`}
                            placeholder="Chọn hình thức trợ giúp"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                              handleFormsOfAssistanceChange(stringValue)
                              form.trigger('formsOfAssistanceId') // Manually trigger validation
                            }}
                            apiRequest={createCategoryApiRequest(
                              `${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP},${CATEGORY_TYPE_VALUES.HINH_THUC_TRO_GIUP_CHI_TIET}`
                            )}
                            keywordName="hình thức trợ giúp"
                            hasError={!!form.formState.errors.formsOfAssistanceId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mã vụ việc <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Sẽ tự động tạo khi chọn hình thức trợ giúp" readOnly />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isOldLegalAidCase"
                  render={({ field }) => (
                    <FormItem className="mt-4 flex flex-row items-start space-y-0 space-x-3">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Là hồ sơ cũ</FormLabel>
                    </FormItem>
                  )}
                />
              </div>

              {/* Thông tin người được TGPL */}
              <div className="space-y-4 pt-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-primary text-lg font-semibold">Thông tin người được TGPL</h3>
                  <button
                    type="button"
                    className="text-primary hover:text-primary/80 text-sm"
                    onClick={() => handleOpenCitizenLookup('rp')}
                  >
                    🔍 Tra cứu thông tin từ CSDLQG về dân cư
                  </button>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="rpName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Họ tên người được trợ giúp <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Nhập họ tên"
                            className={form.formState.errors.rpName ? 'border-destructive ring-destructive/20' : ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpBirthDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày sinh <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            value={field.value}
                            onChange={value => {
                              field.onChange(value)
                              form.trigger('rpBirthDate') // Manually trigger validation
                            }}
                            placeholder="dd/mm/yyyy"
                            disallowFuture={true}
                            className={
                              form.formState.errors.rpBirthDate ? 'border-destructive ring-destructive/20' : ''
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpSex"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Giới tính <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select
                          value={field.value ? field.value.toString() : ''}
                          onValueChange={value => {
                            field.onChange(parseInt(value))
                            form.trigger('rpSex') // Manually trigger validation
                          }}
                        >
                          <FormControl>
                            <SelectTrigger
                              className={`w-full ${form.formState.errors.rpSex ? 'border-destructive ring-destructive/20' : ''}`}
                            >
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value={GENDER.MALE.toString()}>Nam</SelectItem>
                            <SelectItem value={GENDER.FEMALE.toString()}>Nữ</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="rpCardNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Số căn cước <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Nhập số căn cước"
                            className={
                              form.formState.errors.rpCardNumber ? 'border-destructive ring-destructive/20' : ''
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpPublishedDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày cấp <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="dd/mm/yyyy"
                            disallowFuture={true}
                            className={
                              form.formState.errors.rpPublishedDate ? 'border-destructive ring-destructive/20' : ''
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpPublishedPlace"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nơi cấp <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            key={CATEGORY_TYPE_VALUES.NOI_CAP}
                            placeholder="Chọn nơi cấp"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.NOI_CAP)}
                            keywordName="nơi cấp"
                            hasError={!!form.formState.errors.rpPublishedPlace}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="rpEthnic"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dân tộc</FormLabel>
                        <FormControl>
                          <Combobox
                            key={CATEGORY_TYPE_VALUES.DAN_TOC_CHI_TIET}
                            placeholder="Chọn dân tộc"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.DAN_TOC_CHI_TIET)}
                            keywordName="dân tộc"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập số điện thoại" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpJob"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nghề nghiệp</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập nghề nghiệp" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="rpProvince"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tỉnh/Thành phố <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            key={CATEGORY_TYPE_VALUES.TINH_TP}
                            placeholder="Chọn tỉnh/thành phố"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                              setSelectedRpProvince(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.TINH_TP)}
                            keywordName="tỉnh/thành phố"
                            hasError={!!form.formState.errors.rpProvince}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpWard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phường/Xã</FormLabel>
                        <FormControl>
                          <Combobox
                            key={`${CATEGORY_TYPE_VALUES.PHUONG_XA}-${selectedRpProvince || 'no-parent'}`}
                            placeholder="Chọn phường/xã"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.PHUONG_XA, selectedRpProvince)}
                            keywordName="phường/xã"
                            buttonTriggerProps={{
                              disabled: !selectedRpProvince,
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa chỉ</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập địa chỉ" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="objectLegalFieldId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Thuộc diện trợ giúp <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          key={CATEGORY_TYPE_VALUES.DOI_TUONG}
                          placeholder="Chọn đối tượng"
                          value={field.value}
                          onChange={value => {
                            const stringValue =
                              typeof value === 'string'
                                ? value
                                : typeof value === 'object' && value && 'value' in value
                                  ? value.value
                                  : ''
                            field.onChange(stringValue)
                          }}
                          apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.DOI_TUONG)}
                          keywordName="đối tượng"
                          hasError={!!form.formState.errors.objectLegalFieldId}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Thông tin người yêu cầu TGPL */}
              <div className="space-y-4 pt-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-primary text-lg font-semibold">Thông tin người yêu cầu TGPL</h3>
                  <button
                    type="button"
                    className="text-primary hover:text-primary/80 text-sm"
                    onClick={() => handleOpenCitizenLookup('guardian')}
                  >
                    🔍 Tra cứu thông tin từ CSDLQG về dân cư
                  </button>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="guardianName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Họ tên người yêu cầu</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập họ tên" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="relationRp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mối quan hệ với người được TGPL</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập mối quan hệ" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="guardianPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập số điện thoại" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="guardianCardNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số căn cước</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập số căn cước" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="guardianPublishedDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ngày cấp</FormLabel>
                        <FormControl>
                          <DatePickerInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="dd/mm/yyyy"
                            disallowFuture={true}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="guardianPublishedPlace"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nơi cấp</FormLabel>
                        <FormControl>
                          <Combobox
                            key={CATEGORY_TYPE_VALUES.NOI_CAP}
                            placeholder="Chọn nơi cấp"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.NOI_CAP)}
                            keywordName="nơi cấp"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="guardianProvince"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tỉnh/Thành phố</FormLabel>
                        <FormControl>
                          <Combobox
                            key={CATEGORY_TYPE_VALUES.TINH_TP}
                            placeholder="Chọn tỉnh/thành phố"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                              setSelectedGuardianProvince(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(CATEGORY_TYPE_VALUES.TINH_TP)}
                            keywordName="tỉnh/thành phố"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="guardianWard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phường/Xã</FormLabel>
                        <FormControl>
                          <Combobox
                            key={`${CATEGORY_TYPE_VALUES.PHUONG_XA}-${selectedGuardianProvince || 'no-parent'}`}
                            placeholder="Chọn phường/xã"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(
                              CATEGORY_TYPE_VALUES.PHUONG_XA,
                              selectedGuardianProvince
                            )}
                            keywordName="phường/xã"
                            buttonTriggerProps={{
                              disabled: !selectedGuardianProvince,
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="guardianAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa chỉ</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập địa chỉ" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Thông tin vụ việc TGPL */}
              <div className="space-y-4 pt-6">
                <h3 className="text-primary text-lg font-semibold">Thông tin vụ việc TGPL</h3>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tên vụ việc <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Nhập tên vụ việc"
                          className={form.formState.errors.name ? 'border-destructive ring-destructive/20' : ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="entryDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày thụ lý <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="dd/mm/yyyy"
                            className={form.formState.errors.entryDate ? 'border-destructive ring-destructive/20' : ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="legalFieldId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lĩnh vực <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            key={`${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP}`}
                            placeholder="Chọn lĩnh vực"
                            value={field.value}
                            onChange={value => {
                              const stringValue =
                                typeof value === 'string'
                                  ? value
                                  : typeof value === 'object' && value && 'value' in value
                                    ? value.value
                                    : ''
                              field.onChange(stringValue)
                            }}
                            apiRequest={createCategoryApiRequest(
                              `${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP_CHI_TIET},${CATEGORY_TYPE_VALUES.LINH_VUC_TRO_GIUP}`
                            )}
                            keywordName="lĩnh vực"
                            hasError={!!form.formState.errors.legalFieldId}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rpRequestSource"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nguồn yêu cầu</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="direct">Trực tiếp</SelectItem>
                            <SelectItem value="online">Trực tuyến</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="performer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Người thực hiện</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập tên" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="placeOfDisposal"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa điểm thực hiện</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="office">Văn phòng</SelectItem>
                            <SelectItem value="home">Tại nhà</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Giai đoạn bảo vệ */}
              <div className="space-y-4 pt-6">
                <h3 className="text-primary text-lg font-semibold">Giai đoạn bảo vệ</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="fromStage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Từ giai đoạn <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={value => {
                            field.onChange(value)
                            form.trigger('fromStage') // Manually trigger validation
                          }}
                        >
                          <FormControl>
                            <SelectTrigger
                              className={`w-full ${form.formState.errors.fromStage ? 'border-destructive ring-destructive/20' : ''}`}
                            >
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="stage1">Giai đoạn 1</SelectItem>
                            <SelectItem value="stage2">Giai đoạn 2</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="toStage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Đến giai đoạn</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="stage1">Giai đoạn 1</SelectItem>
                            <SelectItem value="stage2">Giai đoạn 2</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Nội dung yêu cầu TGPL */}
              <div className="space-y-4 pt-6">
                <h3 className="text-lg font-semibold">
                  Nội dung yêu cầu TGPL <span className="text-red-500">*</span>
                </h3>
                <FormField
                  control={form.control}
                  name="contentRequest"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Nhập nội dung"
                          className={`min-h-[100px] ${form.formState.errors.contentRequest ? 'border-destructive ring-destructive/20' : ''}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Tài liệu kèm theo đơn */}
              <div className="space-y-4 pt-6">
                <h3 className="text-lg font-semibold">Tài liệu kèm theo đơn</h3>
                <FormField
                  control={form.control}
                  name="attachForRequestPerson"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Giấy tờ chứng minh người thuộc diện trợ giúp pháp lý</FormLabel>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="attachForLegalCase"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Các giấy tờ, tài liệu có liên quan đến vụ việc trợ giúp pháp lý</FormLabel>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="isNotFullProfile"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Chưa đầy đủ hồ sơ</FormLabel>
                    </FormItem>
                  )}
                />

                <div className="space-y-3">
                  <label className="text-sm font-medium">Tệp đính kèm</label>
                  <div className="flex items-center justify-between rounded-lg border border-dashed border-gray-300 p-4">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        type="button"
                        onClick={() => document.getElementById('file-input')?.click()}
                      >
                        Chọn tệp
                      </Button>
                      <span className="text-muted-foreground text-sm">
                        {selectedFiles && selectedFiles.length > 0
                          ? Array.from(selectedFiles)
                              .map(file => file.name)
                              .join(', ')
                          : 'Không có tệp nào được chọn'}
                      </span>
                    </div>
                  </div>
                  <input
                    id="file-input"
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                  />
                </div>
              </div>

              {/* Ghi chú */}
              <div className="space-y-4 pt-6">
                <h3 className="text-primary text-lg font-semibold">Ghi chú</h3>
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea {...field} placeholder="Nhập ghi chú" className="min-h-[100px]" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Thông tin phân công thực hiện TGPL */}
              <div className="space-y-4 pt-6">
                <h3 className="text-primary text-lg font-semibold">Thông tin phân công thực hiện TGPL</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="performer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cán bộ thực hiện</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="officer1">Cán bộ 1</SelectItem>
                            <SelectItem value="officer2">Cán bộ 2</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="coordinatorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cán bộ phối hợp</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={value => {
                            field.onChange(value)
                            // Set coordinatorName based on selected ID
                            const coordinatorOptions = [
                              {
                                id: 'coordinator1',
                                name: 'Cán bộ phối hợp 1',
                              },
                              {
                                id: 'coordinator2',
                                name: 'Cán bộ phối hợp 2',
                              },
                            ]
                            const selectedCoordinator = coordinatorOptions.find(coord => coord.id === value)

                            if (selectedCoordinator) {
                              form.setValue('coordinatorName', selectedCoordinator.name)
                            }
                          }}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="coordinator1">Cán bộ phối hợp 1</SelectItem>
                            <SelectItem value="coordinator2">Cán bộ phối hợp 2</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Thông tin phiếu hẹn */}
              <div className="space-y-4 pt-6">
                <h3 className="text-primary text-lg font-semibold">Thông tin phiếu hẹn</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="appointmentDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ngày hẹn</FormLabel>
                        <FormControl>
                          <DatePickerInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="dd/mm/yyyy"
                            disallowPast={true}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="appointmentPlace"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa điểm hẹn</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập địa chỉ" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="appointmentContent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Giấy tờ cần mang theo</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nhập nội dung" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>

      {/* Citizen Lookup Sidebar */}
      <CitizenLookup
        variant="sidebar"
        isOpen={isCitizenLookupOpen}
        onClose={handleCloseCitizenLookup}
        onSelectCitizen={handleSelectCitizen}
        headerTitle="Tra cứu thông tin từ CSDL Quốc Gia về dân cư"
      />
    </>
  )
})

NewCaseForm.displayName = 'NewCaseForm'
