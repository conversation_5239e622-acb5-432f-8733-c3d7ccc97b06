'use client'

import { useCategoryDetailQuery, userCaseSimpleDetail } from '@/lib/hooks'
import { categoryService } from '@/services/categoryService'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip'
import { AdminPageLayout, Card, CardContent, CardFooter } from '@workspace/ui/mi'
import { ArrowLeft } from 'lucide-react'
import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { getCaseSimpleStatusText } from '../../types'

const FieldDisplay = ({
  label,
  value,
  className = '',
}: {
  label: string
  value: string | number | null | undefined
  className?: string
}) => {
  const displayValue = value || 'Chưa cập nhật'
  const isLongText = String(displayValue).length > 30
  const isEmptyValue = !value

  return (
    <div className={`flex items-start justify-between border-b border-gray-100 py-3 last:border-b-0 ${className}`}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="min-w-0 flex-1 cursor-help pr-4 text-sm text-gray-600">{label}</span>
          </TooltipTrigger>
          {label.length > 25 && (
            <TooltipContent>
              <p>{label}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span
              className={`min-w-0 flex-1 text-right text-sm ${
                isEmptyValue ? 'text-gray-400 italic' : 'font-medium text-gray-900'
              } ${isLongText ? 'cursor-help truncate' : ''}`}
            >
              {displayValue}
            </span>
          </TooltipTrigger>
          {isLongText && (
            <TooltipContent>
              <p className="max-w-xs">{displayValue}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}

type IRmInfo = {
  rpName: string
  rpSex: string
  rpBirthDate: string
  rpCardNumber: string
  entryDate: string
  rpEthnic: string
  rpPhone: string
  rpEmail: string
  rpPublishedPlace: string
  objectLegalFieldId: string
  rpAddress: string
  rpWard: string
  rpProvince: string
}

const rmInfoKeys: Array<keyof IRmInfo> = [
  'rpName',
  'rpSex',
  'rpBirthDate',
  'rpCardNumber',
  'entryDate',
  'rpEthnic',
  'rpPhone',
  'rpEmail',
  'rpPublishedPlace',
  'objectLegalFieldId',
  'rpAddress',
  'rpWard',
  'rpProvince',
]
type ICaseSimpleInfo = {
  createdAt: string
  createdBy: string
  content: string
  contentRequest: string
  orgUnit: string
}

const caseSimpleInfoKeys: Array<keyof ICaseSimpleInfo> = [
  'createdAt',
  'createdBy',
  'content',
  'contentRequest',
  'orgUnit',
]

export default function CaseSimpleDetailPage() {
  const params = useParams()
  const router = useRouter()

  const caseSimpleId = params.id as string

  const [loading, setLoading] = useState(false)
  const [rmInfo, setRmInfo] = useState<IRmInfo | undefined>(undefined)
  const [caseSimleInfo, setCaseSimpleInfo] = useState<ICaseSimpleInfo | undefined>(undefined)

  const {
    data: caseSimpleDetail,
    isLoading,
    error,
  } = userCaseSimpleDetail({ id: caseSimpleId, enable: !!caseSimpleId })

  useEffect(() => {
    if (!caseSimpleDetail) return

    let mounted = true

    const fetchCategories = async () => {
      setLoading(true)

      try {
        await handleGetCategory()
      } catch (err) {
        console.error('handleGetCategory error', err)
      } finally {
        if (mounted) setLoading(false)
      }
    }

    fetchCategories()

    return () => {
      mounted = false
    }
  }, [caseSimpleDetail])

  const handleGetCategory = async () => {
    try {
      const filter = 'TP, DTK, GT, NC, DTG'

      const categoryResponse = await categoryService.getCategories(filter)

      if (!categoryResponse) return
      // Normalize response to an array before using array methods (handle different shapes)
      const categories: any[] = Array.isArray(categoryResponse)
        ? categoryResponse
        : categoryResponse && Array.isArray((categoryResponse as any).data)
          ? (categoryResponse as any).data
          : []

      const rmInfoData = rmInfoKeys.reduce<IRmInfo>((acc, key) => {
        if (
          key === 'rpName' ||
          key === 'rpPhone' ||
          key === 'rpEmail' ||
          key === 'rpCardNumber' ||
          key === 'rpBirthDate' ||
          key === 'entryDate' ||
          key === 'rpAddress'
        ) {
          acc[key] = caseSimpleDetail?.[key] || undefined
        } else if (key === 'rpSex') {
          const genderCategory = categories.find(cat => cat.type === 'GT' && cat.keyword === caseSimpleDetail?.rpSex)
          acc[key] = genderCategory ? genderCategory.name : caseSimpleDetail?.rpSex || undefined
        } else {
          const category = categories.find(cat => cat.categoryId === caseSimpleDetail?.[key])
          acc[key] = (category && category.name) || undefined
        }

        return acc
      }, {} as IRmInfo)

      const caseSimpleInfoData = caseSimpleInfoKeys.reduce<ICaseSimpleInfo>((acc, key) => {
        if (key === 'createdAt') {
          acc[key] = caseSimpleDetail?.[key] || undefined
        } else if (key === 'createdBy' || key === 'orgUnit') {
          acc[key] = caseSimpleDetail?.[key].name || undefined
        } else {
          acc[key] = caseSimpleDetail?.[key] || undefined
        }

        return acc
      }, {} as ICaseSimpleInfo)
      setCaseSimpleInfo(caseSimpleInfoData)
      setRmInfo(rmInfoData)
    } catch (error) {
      console.log('Get category error', error)
    }
  }

  if (isLoading || loading) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Danh sách việc', href: '/case-simple' },
          { label: 'Chi tiết việc', href: `/case-simple/${caseSimpleId}` },
        ]}
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground text-sm">Đang tải...</div>
        </div>
      </AdminPageLayout>
    )
  }

  if (error || !caseSimpleDetail) {
    return (
      <AdminPageLayout
        breadcrumb={[
          { label: 'Danh sách vụ việc', href: '/case-simple' },
          { label: 'Chi tiết vụ việc', href: `/case-simple/${caseSimpleId}` },
        ]}
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-red-500">Không thể tải thông tin vụ việc. Vui lòng thử lại.</div>
        </div>
      </AdminPageLayout>
    )
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        { label: 'Danh sách việc', href: '/case-simple' },
        { label: 'Chi tiết việc', href: `/case-simple/${caseSimpleId}` },
      ]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <button onClick={() => router.back()} className="hover:text-primary transition-colors">
            <ArrowLeft className="h-5 w-5" />
          </button>
          <span className="text-lg font-semibold text-gray-900">Thông tin chi tiết việc TGPL</span>
        </div>

        {/* Main Card containing all information */}
        <Card className="flex flex-col overflow-hidden rounded-lg p-0 shadow-sm">
          {/* Case Header Info */}
          <div className="border-b border-gray-200 bg-gray-50 p-4">
            <div className="flex items-start justify-between">
              <div>
                <div className="mb-1 text-lg leading-normal font-semibold">
                  {caseSimpleDetail?.caseSimpleName || ''}
                </div>
                <div className="text-sm text-gray-600">
                  Trạng thái:{' '}
                  {caseSimpleDetail?.caseSimpleStatus ? getCaseSimpleStatusText(caseSimpleDetail.caseSimpleStatus) : ''}
                </div>
              </div>
            </div>
          </div>

          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* Left Column */}
              <div className="space-y-6">
                <div>
                  <div className="px-4 py-3 text-base font-bold text-blue-600">Thông tin người được TGPL</div>
                  <FieldDisplay label="Họ và tên" value={rmInfo?.rpName} />
                  <FieldDisplay label="Giới tính" value={rmInfo?.rpSex} />
                  <FieldDisplay
                    label="Ngày sinh"
                    value={rmInfo?.rpBirthDate ? new Date(rmInfo?.rpBirthDate).toLocaleDateString('vi-VN') : undefined}
                  />
                  <FieldDisplay label="Số căn cước" value={rmInfo?.rpCardNumber} />
                  <FieldDisplay
                    label="Ngày cấp"
                    value={rmInfo?.entryDate ? new Date(rmInfo?.entryDate).toLocaleDateString('vi-VN') : undefined}
                  />
                  <FieldDisplay label="Nơi cấp" value={rmInfo?.rpPublishedPlace} />
                  <FieldDisplay label="Dân tộc" value={rmInfo?.rpEthnic} />
                  <FieldDisplay label="Thuộc diện trợ giúp" value={rmInfo?.objectLegalFieldId} />
                  <FieldDisplay label="Số diện thoại" value={rmInfo?.rpPhone} />
                  <FieldDisplay
                    label="Đia chỉ"
                    value={`${rmInfo?.rpAddress}, ${rmInfo?.rpWard || ''}, ${rmInfo?.rpProvince}`}
                  />
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                <div>
                  <div className="px-4 py-3 text-base font-bold text-blue-600">Thông tin xử lý việc TGPL</div>
                  <FieldDisplay
                    label="Ngày tiếp nhận"
                    value={
                      caseSimleInfo?.createdAt
                        ? new Date(caseSimleInfo?.createdAt).toLocaleDateString('vi-VN')
                        : undefined
                    }
                  />
                  <FieldDisplay label="Đơn vị tiếp nhận" value={caseSimleInfo?.orgUnit} />
                  <FieldDisplay label="Người tiếp nhận" value={caseSimleInfo?.createdBy} />
                </div>
                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Nội dung hỏi</div>
                  <div className="px-4 pb-4">
                    <div className={`text-sm ${caseSimleInfo?.content ? 'text-gray-900' : 'text-gray-400 italic'}`}>
                      {caseSimleInfo?.content || 'Chưa cập nhật'}
                    </div>
                  </div>
                </div>

                <div className="rounded-lg bg-gray-50">
                  <div className="px-4 py-3 text-base font-bold text-gray-900">Nội dung trả lời</div>
                  <div className="px-4 pb-4">
                    <div
                      className={`text-sm ${caseSimleInfo?.contentRequest ? 'text-gray-900' : 'text-gray-400 italic'}`}
                    >
                      {caseSimleInfo?.contentRequest || 'Chưa cập nhật'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          {/* Footer with creation date */}
          <CardFooter className="bg-gray-100 py-6 text-left text-sm text-gray-700">
            Ngày cập nhật:{' '}
            {caseSimpleDetail.updatedAt
              ? new Date(caseSimpleDetail.updatedAt).toLocaleDateString('vi-VN')
              : caseSimpleDetail.modifyDate
                ? new Date(caseSimpleDetail.modifyDate).toLocaleDateString('vi-VN')
                : 'Chưa cập nhật'}
          </CardFooter>
        </Card>
      </div>
    </AdminPageLayout>
  )
}
