'use client'

import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { caseSimpleApi } from '@/lib/services/case-simple-api'
import { AnyRecord, CaseSimple, Role } from '@/lib/types'
import { useUserStore } from '@/stores'
import { PERMISSIONS } from '@ac/permissions'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { PlusIcon } from 'lucide-react'
import moment from 'moment'
import { useTranslations } from 'next-intl'
import { redirect, useRouter } from 'next/navigation'
import React, { useEffect, useId, useState } from 'react'

import { CaseSimpleForm } from '../case-simple-form'

export function CaseSimplePage() {
  const t = useTranslations()
  const formId = useId()
  const { hasPermissions } = useUserStore()
  const router = useRouter()

  const [selectedItem, setSelectedItem] = useState<CaseSimple | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  useEffect(() => {}, [])

  const columns: ColumnDef<CaseSimple & { roles: Role[] }>[] = [
    selectColumn(t),
    {
      accessorKey: 'caseSimpleName',
      meta: {
        title: 'Tên vụ việc',
        filter: {
          type: 'text',
          placeholder: 'Tìm kiếm vụ việc',
          debounceMs: 500,
        },
      },
      cell: ({ row }) => {
        return (
          <RenderInteractiveLink
            onClick={() => {
              router.push(`/case-simple/list/${row.original.caseSimpleId}`)
            }}
          >
            {row?.original?.caseSimpleName || ''}
          </RenderInteractiveLink>
        )
      },
    },
    {
      accessorKey: 'rpName',
      meta: {
        title: 'Thông tin người yêu cầu',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground text-sm leading-normal font-semibold">{row?.original?.rpName}</p>
            <p className="text-muted-foreground text-sm leading-normal">{row?.original?.rpCardNumber}</p>
          </div>
        )
      },
    },
    {
      accessorKey: 'contentRequest',
      meta: {
        title: 'Nội dụng câu hỏi',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.contentRequest}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'content',
      meta: {
        title: 'Nội dụng trả lời',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.content}
            </p>
          </div>
        )
      },
    },
    {
      accessorKey: 'createdBy',
      meta: {
        title: 'Người tạo',
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-1">
            <p className="text-card-foreground line-clamp-2 text-sm leading-normal font-semibold">
              {row?.original?.createdBy?.name || ''}
            </p>
            <p className="text-muted-foreground text-sm leading-normal">
              {row?.original?.createdAt && moment(row?.original?.createdAt).format('DD/MM/YYYY')}
            </p>
          </div>
        )
      },
    },
  ]

  if (hasPermissions([PERMISSIONS.PM01_CASE_SIMPLE_UPDATE, PERMISSIONS.PM01_CASE_SIMPLE_DELETE])) {
    columns.push({
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },

      cell: ({ row }) => {
        const member = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM01_CASE_SIMPLE_UPDATE}
              onClick={() => {
                setSelectedItem(member)
                setIsViewDialogOpen(false)
                setIsEditDialogOpen(true)
              }}
            />
            <DeleteButtonIcon
              permission={PERMISSIONS.PM01_CASE_SIMPLE_DELETE}
              onClick={() => {
                setSelectedItem(member)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    })
  }

  const { table } = useEnhancedTable<CaseSimple & { roles: Role[] }>({
    columns,
    pageName: 'case-simples',
    keyObject: {},
    queryFn: async state => {
      const params = new URLSearchParams()
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))
      // if (state.sorting.length > 0) {
      //   const sort = state.sorting[0]
      //   if (sort) {
      //     params.append('orderBy', sort.id)
      //     params.append('orderDir', sort.desc ? 'DESC' : 'ASC')
      //   }
      // }

      if (state.keyword) {
        params.append('keyword', state.keyword)
      }
      const filterObj: AnyRecord = {}

      state.filter.forEach(filter => {
        if (Array.isArray(filter.value) && filter.value.length > 0) {
          const firstItem = filter.value[0]

          if (typeof firstItem === 'string') {
            filterObj[filter.id] = filter.value.join(',')
          } else if (typeof firstItem === 'object' && firstItem !== null && 'value' in firstItem) {
            const values = (filter.value as Array<{ label: string; value: string }>).map(item => item.value)
            filterObj[filter.id] = values.join(',')
          }
        } else if (typeof filter.value === 'string' && filter.value) {
          filterObj[filter.id] = filter.value
        } else if (filter.value && typeof filter.value === 'object' && 'from' in filter.value) {
          const dateRange = filter.value as { from?: Date; to?: Date }

          if (dateRange.from) {
            const fromStr = moment(dateRange.from).format('YYYY-MM-DD')
            const toStr = moment(dateRange.to || dateRange.from).format('YYYY-MM-DD')
            filterObj[filter.id] = `${fromStr},${toStr}`
          }
        } else if (
          filter.value &&
          typeof filter.value === 'object' &&
          'value' in filter.value &&
          'label' in filter.value
        ) {
          const comboboxValue = filter.value as { label: string; value: string }
          filterObj[filter.id] = comboboxValue.value
        }
      })

      if (Object.keys(filterObj).length > 0) {
        params.append('filter', JSON.stringify(filterObj))
      }
      // Build a plain params object expected by caseSimpleApi.getList instead of passing URLSearchParams
      const listParams = {
        page: state.pagination.pageIndex + 1,
        pageSize: state.pagination.pageSize,
        ...(state.keyword ? { keyword: state.keyword } : {}),
        ...(Object.keys(filterObj).length > 0 ? { filter: filterObj } : {}),
      }

      // Cast to any to satisfy the API signature if its specific type is not imported here
      const caseSimples = await caseSimpleApi.getList(listParams)

      return {
        items: caseSimples.data || [],
        totalItems: caseSimples.pagination.total || 0,
        totalPages: caseSimples.pagination.totalPages || 0,
      }
    },
    initialState: {},
    enabled: true,
    useQueryFn: true,
    queryKey: ['case-simples'],
  })

  // Force refetch on mount to bypass React Query's fresh cache for this table
  useEffect(() => {
    table.options.meta?.reload?.()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleDeleteCaseAnswer = async () => {
    if (!selectedItem) return

    try {
      const response = await fetch(`/ac-apis/case-simples/${selectedItem?.caseSimpleId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Xóa việc TGPL thất bại')
      }

      setSelectedItem(null)
      setIsDeleteDialogOpen(false)
      setTimeout(() => {
        table.options.meta?.reload?.()
        toast.success(`Đã xóa "${selectedItem?.caseSimpleName}" thành công`)
      }, 100)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('case-answer.title'),
            href: '/case-simple',
          },
        ]}
      >
        <AdminPageContent
          title={t('case-simple.title')}
          subtitle={t('case-simple.subTitle')}
          actions={[
            <AddButton
              permission={PERMISSIONS.PM01_CASE_SIMPLE_CREATE}
              tooltip={t('case-simple.form.create.title')}
              key="create"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={() => redirect('/case-simple/create')}
            />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>
      {(isEditDialogOpen || isViewDialogOpen) && selectedItem && (
        <Dialog
          open={isEditDialogOpen || isViewDialogOpen}
          onOpenChange={() => {
            setIsEditDialogOpen(false)
            setIsViewDialogOpen(false)
            setSelectedItem(null)
          }}
        >
          <DialogContent
            onInteractOutside={e => e.preventDefault()}
            className="flex flex-col p-0"
            style={{
              maxWidth: '80rem',
              width: '90vw',
              maxHeight: '90vh',
            }}
          >
            <DialogHeader className="border-b px-6 py-4">
              <DialogTitle>{isViewDialogOpen ? 'Chi tiết' : 'Chỉnh sửa'} việc trợ giúp pháp lý</DialogTitle>
            </DialogHeader>
            <div className="flex-1 overflow-auto px-6 py-4">
              <CaseSimpleForm
                isView={isViewDialogOpen ? true : false}
                initialData={selectedItem as CaseSimple & { roles: Role[] }}
                formId={formId}
                onHandleUpdateSuccess={() => {
                  setIsEditDialogOpen(false)
                  setIsViewDialogOpen(false)
                  setSelectedItem(null)
                  setTimeout(() => {
                    table.options.meta?.reload()
                  }, 100)
                }}
                onHandleCancel={() => {
                  setIsEditDialogOpen(false)
                  setIsViewDialogOpen(false)
                  setSelectedItem(null)
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
      {isDeleteDialogOpen && selectedItem && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa việc TGPL này &quot;{selectedItem?.caseSimpleName}&quot;? Hành động này không
                thể hoàn tác.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteCaseAnswer}
                className="bg-destructive hover:bg-destructive/90 text-white"
              >
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  )
}
