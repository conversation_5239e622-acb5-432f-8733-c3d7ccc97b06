'use client'

import { regexCitizenCode, regexPhoneNumber } from '@/constants'
import { Any, CaseSimpleStatus, Category } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { DatePickerInput } from '@workspace/ui/components/date-picker-input'
import { Drawer, DrawerClose, DrawerContent, DrawerHeader, DrawerTitle } from '@workspace/ui/components/drawer'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Textarea } from '@workspace/ui/components/textarea'
import { toast } from '@workspace/ui/lib/toast'
import { Required } from '@workspace/ui/mi/required'
import { X } from 'lucide-react'
import moment from 'moment'
import { useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { CaseAnswerDialog, CaseAnswerIdentity } from './case-answer'

const categoryFormSchema = z.object({
  rpName: z.string().min(1, 'Họ tên người được TGPL không được để trống'),
  rpBirthDate: z.string().min(1, 'Ngày sinh không được để trống'),
  rpSex: z.string().min(1, 'Giới tính không được để trống'),

  rpCardNumber: z
    .string()
    .trim()
    .min(1, 'Số CMND/CCCD không được để trống')
    .regex(regexCitizenCode, 'Chỉ được nhập số (0-9)'),
  rpPublishedDate: z.string().min(1, 'Ngày cấp không được để trống'),
  rpPublishedPlace: z.string().min(1, 'Nơi cấp không được để trống'),

  rpEthnic: z.string().nullable(),
  rpPhone: z.union([z.string().trim().regex(regexPhoneNumber, 'Số điện thoại không hợp lệ'), z.literal('')]).nullable(),

  rpProvince: z.string().nullable(),
  rpWard: z.string().nullable(),
  rpAddress: z.string().nullable(),
  supportType: z.string().min(1, 'Thuộc diện trợ giúp không được để trống'),

  caseSimpleName: z.string().min(1, 'Tên vụ việc không được để trống'),
  entryDate: z.string().min(1, 'Ngày tạo không được để trống'),

  organizationUnitName: z.string().nullable(),
  createByName: z.string().nullable(),

  contentRequest: z.string().min(1, 'Nội dung câu hỏi không được để trống'),
  content: z.string().min(1, 'Nội dung câu trả lời không được để trống'),
  caseSimpleStatus: z.enum(['APPROVED', 'REJECTED']).nullable().optional(),
})

type CategoryFormValues = z.infer<typeof categoryFormSchema>

export type CaseSimpleFormHandle = {
  resetForm: () => void
  isDirty: () => boolean
}

type props = {
  isView?: boolean
  initialData?: Any
  onHandleUpdateSuccess?: () => void
  onHandleCancel?: () => void
  formId: string
}

interface Option {
  id: string
  name: string
  type: string
}

// eslint-disable-next-line react/display-name
export const CaseSimpleForm = forwardRef<CaseSimpleFormHandle, props>(
  ({ isView = false, initialData, onHandleUpdateSuccess, onHandleCancel, formId }: props, ref) => {
    const t = useTranslations()
    const { data: session } = useSession()
    const user = session?.user

    const [isSubmitting, setIsSubmitting] = useState(false)
    const [ncOptions, setNcOptions] = useState<Option[]>([])
    const [tinhOptions, setTinhOptions] = useState<Option[]>([])
    const [gtOptions, setGtOptions] = useState<Option[]>([])
    const [dtOptions, setDtOptions] = useState<Option[]>([])
    const [dtgOptions, setDtgOptions] = useState<Option[]>([])

    const [isOpenSearchAnswer, setIsOpenSearchAnswer] = useState(false)
    const isDisabled = isView || isSubmitting

    useEffect(() => {
      handleGetCategory()
    }, [])

    const handleGetCategory = async () => {
      try {
        const filter = { type: 'TP, DTK, GT, NC, DTG' }
        const res = await fetch(`/ac-apis/categories/method/all?filter=${JSON.stringify(filter)}`, {
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!res.ok) {
          throw new Error('Failed to fetch categories')
        }

        const response = await res.json()
        const ncOptionData: Option[] = []
        const gtOptionData: Option[] = []
        const dtOptionData: Option[] = []
        const tpOptionData: Option[] = []
        const dtgOptionData: Option[] = []

        await response.forEach((item: Category) => {
          if (item.type === 'NC') {
            ncOptionData.push({
              id: item.categoryId,
              name: item.name,
              type: item.type,
            })
          } else if (item.type === 'GT') {
            if (item.keyword !== 'nam' && item.keyword !== 'nu') {
              gtOptionData.push({
                id: 'default',
                name: item.name,
                type: item.type,
              })
            } else {
              gtOptionData.push({
                id: item.keyword,
                name: item.name,
                type: item.type,
              })
            }
          } else if (item.type === 'DTK') {
            dtOptionData.push({
              id: item.categoryId,
              name: item.name,
              type: item.type,
            })
          } else if (item.type === 'TP') {
            tpOptionData.push({
              id: item.categoryId,
              name: item.name,
              type: item.type,
            })
          } else if (item.type === 'DTG') {
            dtgOptionData.push({
              id: item.categoryId,
              name: item.name,
              type: item.type,
            })
          }
        })

        setNcOptions(ncOptionData)
        setGtOptions(gtOptionData)
        setDtOptions(dtOptionData)
        setTinhOptions(tpOptionData)
        setDtgOptions(dtgOptionData)
      } catch (error) {
        console.log('Get category error', error)
      }
    }

    // Comment: Form
    const defaultValues: Partial<CategoryFormValues> = {
      rpName: initialData?.rpName || '',
      rpBirthDate: (initialData?.rpBirthDate && moment(initialData?.rpBirthDate).format('YYYY-MM-DD')) || '',
      rpSex: initialData?.rpSex ? String(initialData?.rpSex) : '',

      rpCardNumber: initialData?.rpCardNumber || '',
      rpPublishedDate:
        (initialData?.rpPublishedDate && moment(initialData?.rpPublishedDate).format('YYYY-MM-DD')) || '',
      rpPublishedPlace: initialData?.rpPublishedPlace || '',

      rpEthnic: initialData?.rpEthnic || null,
      rpPhone: initialData?.rpPhone || '',

      rpProvince: initialData?.rpProvince || '',
      rpWard: initialData?.rpWard || '',
      rpAddress: initialData?.rpAddress || '',

      caseSimpleName: initialData?.caseSimpleName || '',
      entryDate: (initialData?.entryDate && moment(initialData?.entryDate).format('YYYY-MM-DD')) || '',

      organizationUnitName: initialData?.orgUnit?.name || '',
      createByName: initialData?.createdBy?.name || user?.name || '',

      content: initialData?.content || '',
      contentRequest: initialData?.contentRequest || '',
      supportType: initialData?.objectLegalFieldId || '',
      caseSimpleStatus: initialData?.caseSimpleStatus || CaseSimpleStatus.APPROVED,
    }

    const form = useForm<CategoryFormValues>({
      resolver: zodResolver(categoryFormSchema),
      defaultValues: defaultValues,
    })

    // Keep track of initial values to robustly detect changes
    const initialValuesRef = useRef<CategoryFormValues | null>(null)

    useEffect(() => {
      if (!initialValuesRef.current) {
        initialValuesRef.current = form.getValues() as CategoryFormValues
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const optionHasId = (options: Option[], id?: string | null) =>
      !!id && options.some(o => String(o.id) === String(id))

    useEffect(() => {
      if (!initialData) return

      if (
        gtOptions.length === 0 &&
        ncOptions.length === 0 &&
        dtOptions.length === 0 &&
        tinhOptions.length === 0 &&
        dtgOptions.length === 0
      ) {
        return
      }

      // Avoid overriding user edits
      if (form.formState.isDirty) return

      const nextValues: Partial<CategoryFormValues> = {
        ...form.getValues(),
        rpSex: optionHasId(gtOptions, initialData.rpSex)
          ? String(initialData.rpSex)
          : gtOptions.find(o => o.id === 'default')
            ? 'default'
            : '',
        rpPublishedPlace: optionHasId(ncOptions, initialData.rpPublishedPlace)
          ? String(initialData.rpPublishedPlace)
          : '',
        rpEthnic: optionHasId(dtOptions, initialData.rpEthnic) ? String(initialData.rpEthnic) : '',
        rpProvince: optionHasId(tinhOptions, initialData.rpProvince) ? String(initialData.rpProvince) : '',
        rpBirthDate: (initialData?.rpBirthDate && moment(initialData?.rpBirthDate).format('YYYY-MM-DD')) || '',
        rpPublishedDate:
          (initialData?.rpPublishedDate && moment(initialData?.rpPublishedDate).format('YYYY-MM-DD')) || '',
        entryDate: (initialData?.entryDate && moment(initialData?.entryDate).format('YYYY-MM-DD')) || '',
      }

      form.reset(nextValues as CategoryFormValues)
      initialValuesRef.current = nextValues as CategoryFormValues
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialData, gtOptions, ncOptions, dtOptions, tinhOptions])

    const onSubmit = useCallback(
      async (values: CategoryFormValues) => {
        if (isView) {
          return
        }

        setIsSubmitting(true)

        try {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { createByName, organizationUnitName, ...dataWithoutExtraFields } = values

          const formDataFormat = {
            ...dataWithoutExtraFields,
            rpBirthDate: values.rpBirthDate ? moment(values.rpBirthDate).toISOString() : null,
            rpPublishedDate: values.rpPublishedDate ? moment(values.rpPublishedDate).toISOString() : null,
            entryDate: values.entryDate ? moment(values.entryDate).toISOString() : null,
            objectLegalFieldId: values.supportType || null,
          }

          const url = initialData ? `/ac-apis/case-simples/${initialData?.caseSimpleId}` : '/ac-apis/case-simples'

          const action = initialData ? 'PATCH' : 'POST'

          const res = await fetch(url, {
            method: action,
            body: JSON.stringify(formDataFormat),
            headers: {
              'Content-Type': 'application/json',
            },
          })

          if (!res.ok) {
            throw new Error(`${initialData ? 'Cập nhật' : 'Tạo'} việc TGPL thất bại`)
          }

          toast.success(`${initialData ? 'Cập nhật' : 'Tạo'} việc TGPL thành công`)
          form.reset()

          if (initialData && onHandleUpdateSuccess) {
            onHandleUpdateSuccess()
          }
        } catch {
          toast.error(`Lỗi khi ${initialData ? 'cập nhật' : 'tạo'} việc TGPL`)
        } finally {
          setIsSubmitting(false)
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [t]
    )

    const handleSelectAnswer = (answer: CaseAnswerIdentity) => {
      form.clearErrors(['content', 'contentRequest'])
      const currentContentRequest = form.getValues('contentRequest')
      form.setValue('content', answer.answer, {
        shouldDirty: true,
        shouldValidate: true,
      })

      if (!currentContentRequest || currentContentRequest.trim() === '') {
        form.setValue('contentRequest', answer.question, {
          shouldDirty: true,
          shouldValidate: true,
        })
      }
      setIsOpenSearchAnswer(false)
    }

    useImperativeHandle(ref, () => ({
      resetForm: () => {
        form.reset()
        form.clearErrors()
      },
      isDirty: () =>
        form.formState.isDirty ||
        ((): boolean => {
          try {
            const current = form.getValues() as CategoryFormValues
            const initial = initialValuesRef.current

            return JSON.stringify(current) !== JSON.stringify(initial)
          } catch {
            return form.formState.isDirty
          }
        })(),
    }))

    return (
      <>
        <Form {...form}>
          <form id={formId} onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Thông tin người được TGPL */}
            <div className="space-y-4">
              <div className="flex flex-row items-center justify-between">
                <p className="text-sm leading-normal font-semibold">Thông tin người được TGPL</p>
                {/* Hiện tại chưa có chức năng tra cứu */}
                {/* {!initialData && (
                <Button
                  type="button"
                  size={'sm'}
                  disabled={isDisabled}
                >
                  Tra cứu thông tin từ CSDLQG về dân cư
                </Button>
              )} */}
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Row 1 */}
                <div>
                  <FormField
                    control={form.control}
                    name="rpName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Họ tên người được TGPL
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập họ tên người được TGPL" {...field} disabled={isDisabled} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpBirthDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày sinh
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            placeholder="dd/mm/yyyy"
                            disabled={isDisabled}
                            {...field}
                            value={field.value || ''}
                            disallowFuture={true}
                            onValidate={(ok, msg) => {
                              if (ok) form.clearErrors('rpBirthDate')
                              else
                                form.setError('rpBirthDate', {
                                  type: 'manual',
                                  message: msg || 'Ngày không hợp lệ',
                                })
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpSex"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Giới tính
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn giới tính" />
                            </SelectTrigger>
                            <SelectContent>
                              {gtOptions.length > 0 &&
                                gtOptions.map(item => (
                                  <SelectItem key={item.id} value={String(item.id)}>
                                    {item.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Row 2 */}
                <div>
                  <FormField
                    control={form.control}
                    name="rpCardNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Số căn cước
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập số căn cước" {...field} disabled={isDisabled} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpPublishedDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày cấp
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            placeholder="dd/mm/yyyy"
                            disabled={isDisabled}
                            {...field}
                            value={field.value || ''}
                            disallowFuture={true}
                            onValidate={(ok, msg) => {
                              if (ok) form.clearErrors('rpPublishedDate')
                              else
                                form.setError('rpPublishedDate', {
                                  type: 'manual',
                                  message: msg || 'Ngày không hợp lệ',
                                })
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpPublishedPlace"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nơi cấp
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn nơi cấp" />
                            </SelectTrigger>
                            <SelectContent>
                              {ncOptions.length > 0 &&
                                ncOptions.map(item => (
                                  <SelectItem key={item.id} value={String(item.id)}>
                                    {item.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Row 3 */}
                <div>
                  <FormField
                    control={form.control}
                    name="rpEthnic"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dân tộc</FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                            <SelectContent>
                              {dtOptions.length > 0 &&
                                dtOptions.map(item => (
                                  <SelectItem key={item.id} value={String(item.id)}>
                                    {item.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="supportType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Thuộc diện trợ giúp
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full truncate" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn" className="min-w-0 flex-1 truncate" />
                            </SelectTrigger>
                            <SelectContent>
                              {dtgOptions.length > 0 &&
                                dtgOptions.map(item => (
                                  <SelectItem
                                    key={item.id}
                                    value={String(item.id)}
                                    className="break-words whitespace-normal"
                                  >
                                    {item.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Nhập số điện thoại"
                            {...field}
                            value={field.value || ''}
                            disabled={isDisabled}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              {/* Row 4 */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <FormField
                    control={form.control}
                    name="rpProvince"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tỉnh/Thành phố</FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                            <SelectContent>
                              {tinhOptions.length > 0 &&
                                tinhOptions.map(item => (
                                  <SelectItem key={item.id} value={String(item.id)}>
                                    {item.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="rpWard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phường/ Xã</FormLabel>
                        <FormControl>
                          <Select value={field.value ?? undefined} onValueChange={field.onChange}>
                            <SelectTrigger className="w-full" disabled={isDisabled}>
                              <SelectValue placeholder="Chọn" />
                            </SelectTrigger>
                            <SelectContent></SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <div>
                    <FormField
                      control={form.control}
                      name="rpAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Địa chỉ</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Nhập địa chỉ"
                              {...field}
                              value={field.value || ''}
                              disabled={isDisabled}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Thông tin việc TGPL */}
            <div className="space-y-4">
              <div className="flex flex-col items-start gap-2 md:flex-row md:items-center md:justify-between">
                <p className="text-sm leading-normal font-semibold">Thông tin việc TGPL</p>

                {isView ? (
                  <></>
                ) : (
                  <Button type="button" size="sm" onClick={() => setIsOpenSearchAnswer(true)} disabled={isDisabled}>
                    Tra cứu câu hỏi theo mẫu
                  </Button>
                )}
              </div>
              {/* Row 1 */}
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <FormField
                    control={form.control}
                    name="caseSimpleName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Tên việc
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên việc" {...field} disabled={isDisabled} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              {/* Row 2 */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <FormField
                    control={form.control}
                    name="entryDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ngày thụ lý
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <DatePickerInput
                            placeholder="dd/mm/yyyy"
                            disabled={isDisabled}
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="organizationUnitName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tên đơn vị</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên việc" {...field} value={field.value || ''} disabled />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="createByName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Người tiếp nhận</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tên việc" {...field} value={field.value || ''} disabled />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              {/* Row 3 */}
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <FormField
                    control={form.control}
                    name="contentRequest"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nội dung câu hỏi
                          <Required />
                        </FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Nhập nội dung" disabled={isDisabled} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Thông tin trả lời */}
            <Card>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-row items-center justify-between">
                    <p className="text-sm leading-normal font-semibold">Thông tin trả lời</p>
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="caseSimpleStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className="flex items-center gap-6">
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  id="status-approved"
                                  checked={field.value === 'APPROVED'}
                                  onCheckedChange={() => field.onChange('APPROVED')}
                                  disabled={isDisabled}
                                />
                                <label htmlFor="status-approved" className="text-sm">
                                  Tiếp nhận
                                </label>
                              </div>
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  id="status-rejected"
                                  checked={field.value === 'REJECTED'}
                                  onCheckedChange={() => field.onChange('REJECTED')}
                                  disabled={isDisabled}
                                />
                                <label htmlFor="status-rejected" className="text-sm">
                                  Từ chối
                                </label>
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Nội dung trả lời
                              <Required />
                            </FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Nhập nội dung" disabled={isDisabled} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {isView ? (
              <></>
            ) : initialData && onHandleCancel ? (
              <div className="flex justify-end">
                <div className="flex flex-row gap-2">
                  <Button variant="outline" size="sm" onClick={onHandleCancel} disabled={isDisabled}>
                    Huỷ
                  </Button>
                  <Button type="submit" disabled={isDisabled}>
                    Lưu thay đổi
                  </Button>
                </div>
              </div>
            ) : null}
          </form>
        </Form>
        {isOpenSearchAnswer && !isView && (
          <Drawer
            open={isOpenSearchAnswer}
            onOpenChange={() => setIsOpenSearchAnswer(false)}
            direction="right"
            dismissible={false}
          >
            <DrawerContent className="p-6 data-[vaul-drawer-direction=right]:w-[80vw] data-[vaul-drawer-direction=right]:sm:max-w-none">
              <DrawerHeader className="relative">
                <DrawerTitle>
                  <p className="text-lg leading-normal font-semibold">Tra cứu câu hỏi và trả lời theo mẫu có sẵn</p>
                </DrawerTitle>
                <DrawerClose asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-3 right-3"
                    aria-label="Close"
                    onClick={() => setIsOpenSearchAnswer(false)}
                  >
                    <X className="size-4" />
                  </Button>
                </DrawerClose>
              </DrawerHeader>
              <div className="w-full">
                <CaseAnswerDialog onHandleSelectAnswer={(answer: CaseAnswerIdentity) => handleSelectAnswer(answer)} />
              </div>
            </DrawerContent>
          </Drawer>
        )}
      </>
    )
  }
)
