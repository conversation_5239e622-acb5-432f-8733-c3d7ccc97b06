'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Alert, AlertDescription } from '@workspace/ui/components/alert'
import { Button } from '@workspace/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form'
import { Input } from '@workspace/ui/components/input'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { ModeToggle } from '@workspace/ui/mi/mode-toggle'
import { ArrowLeft, TriangleAlert } from 'lucide-react'
import { signIn, useSession } from 'next-auth/react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { ChooseAppModal } from './choose-app-modal'

export default function SigninPage() {
  const t = useTranslations()
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [openChooseAppModal, setOpenChooseAppModal] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (session) {
      setOpenChooseAppModal(true)
    }
  }, [session])

  const signInSchema = z.object({
    email: z.string().email(t('auth.field.email.validation')),
    password: z.string().min(6, t('auth.field.password.validation')),
  })

  type SignInValues = z.infer<typeof signInSchema>

  const form = useForm<SignInValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = async (values: SignInValues) => {
    setLoading(true)
    setError('')

    try {
      const result = await signIn('credentials', {
        email: values.email,
        password: values.password,
        redirect: false,
      })

      if (result?.error) {
        setError('Email hoặc mật khẩu không đúng')

        return
      }

      if (result?.ok) {
        setOpenChooseAppModal(true)
      }
    } catch (error) {
      const msg = error instanceof Error ? error.message : 'Đã có lỗi xảy ra khi đăng nhập'
      console.error(msg, error)
      setError(msg)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-background relative flex min-h-screen flex-col items-center justify-center px-4 sm:px-0">
      <ChooseAppModal open={openChooseAppModal} onClose={() => setOpenChooseAppModal(false)} showTrigger={false} />
      {/* Background gradient overlay */}
      <div className="from-primary/5 via-background to-accent/5 absolute inset-0 bg-gradient-to-br" />

      <div className="bg-card/80 border-border/50 relative w-full max-w-md space-y-8 rounded-xl border p-4 shadow-2xl backdrop-blur-md sm:p-8">
        <div className="relative z-10">
          <h2 className="text-card-foreground text-center text-3xl font-bold">{t('auth.form.signIn.title')}</h2>

          {error && (
            <Alert variant="destructive" className="my-3">
              <TriangleAlert className="size-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form method="post" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-card-foreground">{t('auth.field.email.label')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('auth.field.email.placeholder')}
                        className="bg-background/50 border-border/60 focus:border-primary"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-card-foreground">{t('auth.field.password.label')}</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={t('auth.field.password.placeholder')}
                        className="bg-background/50 border-border/60 focus:border-primary"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={loading} className="bg-primary hover:bg-primary/90 w-full text-white">
                {loading ? t('auth.form.signIn.button.loading') : t('auth.form.signIn.button.label')}
              </Button>

              <Button
                type="button"
                onClick={() => signIn('keycloak', { callbackUrl: '/dashboard' })}
                className="w-full"
                variant="outline"
              >
                {t('auth.form.signIn.buttonSso.label')}
              </Button>

              <div className="text-center text-sm">
                <span className="text-muted-foreground">{t('auth.form.signIn.help.noAccount')}</span>
                <Link href="/sign-up" className="text-primary hover:text-primary/80 ml-1">
                  {t('auth.form.signIn.help.register')}
                </Link>
              </div>
              <div className="flex items-center justify-between text-sm">
                <Button
                  type="button"
                  variant="link"
                  size="sm"
                  className="text-muted-foreground hover:text-card-foreground"
                >
                  <ArrowLeft className="size-4" />
                  <Link href="/">{t('auth.form.signIn.help.backToHome')}</Link>
                </Button>
                <div className="flex items-center gap-2">
                  <ModeToggle />
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  )
}
