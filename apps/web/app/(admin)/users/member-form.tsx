'use client'

import { Option } from '@/components/ui/search-combobox'
import { Any, AssignRole, OrganizationUnit } from '@/lib/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@workspace/ui/components/alert-dialog'
import { Button } from '@workspace/ui/components/button'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { toast, toastError } from '@workspace/ui/components/toast'
import {
  Card,
  CardContent,
  CardHeader,
  Combobox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@workspace/ui/mi'
import { PlusIcon, TrashIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

type Role = {
  id: string
  name: string
}

interface MemberFormProps {
  initialData?: Any
  onSuccess: () => void
  onCancel: () => void
  reloadTable: () => void
}

export function MemberForm({ initialData, onSuccess, onCancel, reloadTable }: MemberFormProps) {
  const [roleOptions, setRoleOptions] = useState<Option[]>([])
  const [loading, setLoading] = useState(false)
  const [role, setRole] = useState<string>()
  const [assignedRoleIds, setAssignedRoleIds] = useState<string[]>([])
  const [orgUnitOptions, setOrgUnitOptions] = useState<Option[]>([])
  const queryClient = useQueryClient()

  const { data: roles } = useQuery({
    queryKey: ['roles'],
    queryFn: () =>
      fetch(`/ac-apis/roles`, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(res => res.json().then(data => data.items)),
  })

  const { data: userAssignedRoles } = useQuery({
    queryKey: ['user-assigned-roles', initialData?.id],
    enabled: !!initialData?.id,
    queryFn: () =>
      fetch(`/ac-apis/users/method/user-assigned-roles/${initialData?.id}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(res => res.json().then(data => data)),
  })
  const { data: orgs } = useQuery({
    queryKey: ['orgs'],
    enabled: !!initialData?.id,
    queryFn: () =>
      fetch(`/ac-apis/organization-units/method/all`, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(res => res.json().then(data => data)),
  })

  useEffect(() => {
    if (roles) {
      setRoleOptions(
        roles.map((role: Role) => ({
          value: role.id,
          label: role.name,
        }))
      )
    }
  }, [roles])

  useEffect(() => {
    if (userAssignedRoles) {
      setAssignedRoleIds(userAssignedRoles.map((ar: AssignRole) => ar.role.id))
    }
  }, [userAssignedRoles])

  useEffect(() => {
    if (orgs) {
      setOrgUnitOptions(
        orgs.items.map((org: OrganizationUnit) => ({
          value: org.id,
          label: org.name,
        }))
      )
    }
  }, [orgs])

  // Schema cho form
  const formSchema = z.object({
    ...(initialData
      ? {}
      : {
          email: z.string().email('Email không hợp lệ').optional(),
        }),
    name: z.string().min(1, 'Tên thành viên không được để trống'),
    orgUnitId: z.string().optional(),
  })

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...(initialData ? {} : { email: initialData?.email || '' }),
      name: initialData?.name || '',
      orgUnitId: initialData?.orgUnitId || '',
    },
  })

  // Handle submit form
  const onSubmit = async (data: FormValues) => {
    try {
      setLoading(true)
      const payload = {
        ...(initialData ? {} : { email: data.email }),
        name: data.name,
        orgUnitId: data.orgUnitId || undefined,
      }

      if (initialData) {
        // Cập nhật identity
        const response = await fetch(`/ac-apis/users/${initialData.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || 'Failed to update user identity')
        }

        toast.success('Đã cập nhật thành viên tenant')
      } else {
        // Thêm identity mới
        const response = await fetch('/ac-apis/users', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || 'Failed to create user identity')
        }

        toast.success('Đã thêm thành viên tenant mới')
      }

      onSuccess()
    } catch (error) {
      toastError(error, 'Đã xảy ra lỗi khi lưu dữ liệu')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Tên thành viên</Label>
          <Input id="name" {...form.register('name')} placeholder="Nhập tên thành viên" />
        </div>

        {!initialData && (
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" {...form.register('email')} placeholder="Nhập email" type="email" />
            {form.formState.errors.email && (
              <p className="text-destructive text-sm">{form.formState.errors.email.message}</p>
            )}
          </div>
        )}

        <FormField
          control={form.control}
          name="orgUnitId"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Nhóm tổ chức</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value}
                  onChange={value => field.onChange(typeof value === 'string' ? value : '')}
                  options={orgUnitOptions}
                  placeholder="Chọn tổ chức"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex flex-col gap-2">
          <Label htmlFor="roles">Vai trò</Label>
          <div className="flex flex-row gap-2">
            <div className="flex-1">
              <Combobox
                value={role}
                onChange={value => setRole(value as string)}
                options={roleOptions.filter(option => !assignedRoleIds.includes(option.value))}
                placeholder="Chọn vai trò"
              />
            </div>
            <Button
              type="button"
              disabled={!role}
              variant={role ? 'warning' : 'outline'}
              onClick={async () => {
                const response = await fetch('/ac-apis/assign-roles', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    roleId: role,
                    subjectId: initialData?.id,
                    subjectType: 'user',
                  }),
                })

                if (!response.ok) {
                  toast.error('Đã xảy ra lỗi khi thêm vai trò vào thành viên')
                } else {
                  toast.success('Đã thêm vai trò vào thành viên')
                  reloadTable()
                  setRole(undefined)
                }
                queryClient.invalidateQueries({ queryKey: ['user-assigned-roles', initialData?.id] })
              }}
            >
              <PlusIcon className="h-4 w-4" /> Gán
            </Button>
          </div>
        </div>
        {userAssignedRoles?.length > 0 && (
          <Card className="gap-y-3 py-2">
            <CardHeader className="px-4">Vai trò hiện tại</CardHeader>
            <CardContent className="px-3">
              <div className="flex flex-col gap-3">
                {userAssignedRoles.map((ar: AssignRole) => (
                  <div
                    className="flex flex-row items-center justify-between gap-2 rounded-md bg-gray-100 p-2"
                    key={ar.id}
                  >
                    <div className="text-sm font-medium">{ar.role.name}</div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button type="button" size="sm" variant="destructive">
                          <TrashIcon className="h-4 w-4" /> Gỡ
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Thông báo</AlertDialogTitle>
                          <AlertDialogDescription>
                            Bạn có chắc chắn muốn xóa vai trò
                            <span className="text-destructive font-bold"> {ar.role.name}</span> với thành viên này
                            không?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Không</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={async () => {
                              const response = await fetch(
                                `/ac-apis/assign-roles/user/${initialData?.id}/roles/${ar.role.id}`,
                                {
                                  method: 'DELETE',
                                }
                              )

                              if (!response.ok) {
                                toast.error('Đã xảy ra lỗi khi xóa vai trò vào thành viên')
                              } else {
                                reloadTable()
                                toast.success('Đã xóa vai trò vào thành viên')
                              }
                              queryClient.invalidateQueries({ queryKey: ['user-assigned-roles', initialData?.id] })
                            }}
                          >
                            Có
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} type="button">
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Đang lưu...' : initialData ? 'Cập nhật' : 'Thêm mới'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
