'use client'

import { RenderColumn } from '@/components/render-column'
import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderStatus } from '@/components/render-status'
import { RenderText } from '@/components/render-text'
import { MODEL_STATUS, ModelStatus } from '@/constants'
import { Role, User } from '@/lib/types'
import { useGlobalStore } from '@/stores/global.store'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast, toastError } from '@workspace/ui/components/toast'
import { PERMISSIONS } from '@workspace/ui/constants/base'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { useUserStore } from '@workspace/ui/stores/user.store'
import { Users } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'

import { MemberForm } from './member-form'

export function UserList() {
  const t = useTranslations()
  const { hasPermissions } = useUserStore()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedMember, setSelectedMember] = useState<User | null>(null)
  const { roles, getRoles, orgs, getOrgs } = useGlobalStore()

  useEffect(() => {
    getRoles()
    getOrgs()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Function to delete a user identity
  const handleDeleteMember = async () => {
    if (!selectedMember) return

    try {
      const response = await fetch(`/ac-apis/users/${selectedMember.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete user identity')
      }

      toast.success(t('msg.deleted.success', { name: selectedMember.name || '' }))
      table.options.meta?.reload()
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error('Error deleting user identity:', error)
      toast.error(error instanceof Error ? error.message : t('common.error'))
    }
  }

  const columns: ColumnDef<User & { roles: Role[] }>[] = [
    selectColumn(t),
    {
      accessorKey: 'name',
      meta: {
        title: t('user-management.members.column.user'),
      },
      cell: ({ row }) => {
        return (
          <RenderColumn>
            <RenderInteractiveLink
              onClick={() => {
                setSelectedMember(row.original)
                setIsEditDialogOpen(true)
              }}
            >
              {row.original.name}
            </RenderInteractiveLink>
            <RenderText text={row.original.orgUnit?.name || '- Chưa gán đơn vị -'} prefix="Đơn vị" />
            <RenderText text={row.original.email} prefix="Email" />
            {row.original.phone && <RenderText text={row.original.phone} prefix="SĐT" />}
          </RenderColumn>
        )
      },
    },
    // {
    //   accessorKey: 'email',
    //   meta: {
    //     title: t('user-management.members.column.email'),
    //   },
    // },
    {
      enableHiding: true,
      accessorKey: 'orgUnitId',
      meta: {
        showToggle: false,
        title: 'Tổ chức',
        filter: {
          type: 'combobox',
          multiple: true,
          placeholder: 'Chọn tổ chức',
          options: orgs,
        },
      },
    },
    {
      id: 'assignRoles.roleId',
      accessorKey: 'assignRoles.roleId',
      meta: {
        title: t('user-management.members.column.roles'),
        filter: {
          type: 'combobox',
          multiple: true,
          placeholder: 'Chọn vai trò',
          // position: 'advanced',
          options: roles,
        },
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-wrap gap-1">
            {row.original.assignRoles?.length > 0 ? (
              row.original.assignRoles.map(ar => (
                <Badge key={ar.roleId} variant="outline" className="bg-primary rounded-full py-1.5 text-white">
                  {ar.role.name}
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground text-sm">{t('user-management.members.noRoles')}</span>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'status',
      size: TABLE_SIZE.STATUS,
      meta: {
        title: t('user-management.members.column.status'),
        filter: {
          type: 'select',
          placeholder: 'Chọn trạng thái',
          multiple: true,
          dataType: 'number',
          options: MODEL_STATUS.map(status => ({
            label: t(status.label),
            value: status.value.toString(),
            icon: status.icon,
          })),
        },
      },
      cell: ({ row }) => {
        const status = row.original.status

        return <RenderStatus status={status as unknown as ModelStatus} />
      },
    },
    {
      accessorKey: 'createdAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: t('field.createdAt.label'),
        filter: {
          position: 'advanced',
          type: 'date-range',
        },
      },
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('createdAt')} showTime={showTime} />
      },
    },
    {
      accessorKey: 'updatedAt',
      size: TABLE_SIZE.DATETIME,
      meta: {
        title: t('field.updatedAt.label'),
        filter: {
          position: 'advanced',
          type: 'date-range',
        },
      },
      cell: ({ row }) => {
        const showTime = table.options.meta?.uiState?.showTime ?? false

        return <RenderDateTime datetime={row.getValue('updatedAt')} showTime={showTime} />
      },
    },
  ]

  if (hasPermissions([PERMISSIONS.PM06_USER_UPDATE_ALL, PERMISSIONS.PM06_USER_DELETE_ALL])) {
    columns.push({
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const member = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM06_USER_UPDATE_ALL}
              onClick={() => {
                setSelectedMember(member)
                setIsEditDialogOpen(true)
              }}
            />
            <DeleteButtonIcon
              permission={PERMISSIONS.PM06_USER_DELETE_ALL}
              onClick={() => {
                setSelectedMember(member)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    })
  }

  const { table } = useEnhancedTable<User & { roles: Role[] }>({
    columns,
    pageName: 'users',
    keyObject: {},
    queryFn: async state => {
      const params = generateQueryParams(state, {
        baseParams: {},
        columns,
      })

      const res = await fetch(`/ac-apis/users?${params}`, {
        cache: 'no-store',
      })

      if (!res.ok) {
        toastError(res, 'Không thể tải danh sách người dùng')
      }

      return res.json()
    },
    initialState: {
      // filter: [{ id: 'status', value: 'ACTIVE' }],
      columnVisibility: {
        orgUnitId: false,
      },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: ['users'],
  })

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('user-management.members.title'),
            href: '/users',
          },
        ]}
      >
        <AdminPageContent
          title={t('user-management.members.title')}
          icon={<Users className="h-5 w-5" />}
          helpOptions={{
            content: {
              content:
                'Quản lý các tài khoản người dùng, phân quyền vai trò cho từng người dùng, gán đơn vị cho từng người dùng',
            },
            video: {
              url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            },
          }}
          actions={[
            <AddButton
              permission={PERMISSIONS.PM06_USER_CREATE_ALL}
              key="create"
              tooltip={t('user-management.members.actions.create')}
              onClick={() => setIsCreateDialogOpen(true)}
            />,
          ]}
        >
          <DataTable
            table={table}
            searchOptions={{
              helpContent: 'Tìm kiếm tên, số điện thoại, email cán bộ',
            }}
          />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create Member Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{t('user-management.members.actions.createTitle')}</DialogTitle>
          </DialogHeader>
          <MemberForm
            reloadTable={() => table.options.meta?.reload()}
            onSuccess={() => {
              setIsCreateDialogOpen(false)
              table.options.meta?.reload()
            }}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Member Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{t('user-management.members.actions.edit')}</DialogTitle>
          </DialogHeader>
          <MemberForm
            reloadTable={() => table.options.meta?.reload()}
            initialData={selectedMember as User & { roles: Role[] }}
            onSuccess={() => {
              setIsEditDialogOpen(false)
              table.options.meta?.reload()
            }}
            onCancel={() => setIsEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Member Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('user-management.members.actions.delete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('user-management.members.actions.deleteConfirm', {
                name: selectedMember?.email || '',
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('action.cancel.label')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMember}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              {t('action.delete.label')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
