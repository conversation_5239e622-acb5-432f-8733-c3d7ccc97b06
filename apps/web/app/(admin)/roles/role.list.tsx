'use client'

import { RenderAutoText } from '@/components/render-auto-text'
import { RenderColumn } from '@/components/render-column'
import { RenderCopyText } from '@/components/render-copy-text'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import {
  addColumCreatedAt,
  addColumnDescription,
  addColumnStatus,
  addColumnUpdatedAt,
  addColumnYesNo,
} from '@/components/table-helper'
import { ModelStatus } from '@/constants/models'
import { useGlobalStore } from '@/stores/global.store'
import { PERMISSIONS } from '@ac/permissions'
import { ColumnDef } from '@tanstack/react-table'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { ConfirmDeleteDialog } from '@workspace/ui/mi/confirm-delete-dialog'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import {
  ScrollableCreateFormDialog,
  ScrollableEditFormDialog,
  ScrollableFormDialogRef,
} from '@workspace/ui/mi/scrollable-dialog'
import { DataTable } from '@workspace/ui/mi/table/data-table'
import { selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table/data-table-helper'
import { useUserStore } from '@workspace/ui/stores/user.store'
import { ShieldIcon } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import { RoleForm } from './role-form'
import { createOrUpdateRole, deleteRole, getListRoles } from './roles.service'

type RoleIdentity = {
  id: string
  name: string
  code: string
  createdAt: string
  updatedAt: string
  description: string
  permissions: string[]
  isSystem: number
  status: number
  type: string
}

const ROLE_LIST_KEY = 'roles'

export function RoleList() {
  const t = useTranslations()
  const { hasPermissions } = useUserStore()
  const createDialogRef = useRef<ScrollableFormDialogRef>(null)
  const editDialogRef = useRef<ScrollableFormDialogRef>(null)

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<RoleIdentity | null>(null)
  const { orgTypes, getOrgTypes, orgTypesRepo } = useGlobalStore()

  useEffect(() => {
    getOrgTypes()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const columns: ColumnDef<RoleIdentity>[] = [
    selectColumn(t, { size: 25 }),
    {
      accessorKey: 'name',
      meta: {
        title: t('role.field.name'),
      },
      cell: ({ row }) => {
        const orgType = orgTypesRepo[row.original.type]

        return (
          <RenderColumn>
            <RenderInteractiveLink
              permission={PERMISSIONS.PM06_ROLE_UPDATE_ALL}
              onClick={() => {
                setSelectedRole(row.original)
                editDialogRef.current?.open()
              }}
            >
              {row.original.name}
            </RenderInteractiveLink>
            <RenderCopyText text={row.original.code} prefix="Mã" />
            <RenderAutoText
              text={orgType?.label || row.original.type || 'Khác'}
              prefix={t('role.field.type')}
              maxLines={2}
              textBehavior="wrap"
            />
          </RenderColumn>
        )
      },
      minSize: 300,
    },
    addColumnDescription(t),
    {
      accessorKey: 'type',
      enableHiding: true,
      meta: {
        title: t('role.field.type'),
        showToggle: false,
        filter: {
          type: 'combobox',
          multiple: true,
          dataType: 'number',
          options: orgTypes,
        },
      },
    },
    addColumnYesNo('isSystem', t('role.field.isSystem'), t),
    addColumnStatus(t),
    addColumCreatedAt(t),
    addColumnUpdatedAt(t),
  ]

  if (hasPermissions([PERMISSIONS.PM06_ROLE_UPDATE_ALL, PERMISSIONS.PM06_ROLE_DELETE_ALL])) {
    columns.push({
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },
      cell: ({ row }) => {
        const role = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM06_ROLE_UPDATE_ALL}
              onClick={() => {
                setSelectedRole(role)
                editDialogRef.current?.open()
              }}
            />
            <DeleteButtonIcon
              permission={PERMISSIONS.PM06_ROLE_DELETE_ALL}
              show={role.status !== ModelStatus.DELETED}
              onClick={() => {
                setSelectedRole(role)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    })
  }

  const handleDeleteRole = async () => {
    if (!selectedRole) return

    deleteRole(selectedRole.id, {
      onSuccess: () => {
        setIsDeleteDialogOpen(false)
        table.options.meta?.reload()
      },
    })
  }

  const { table } = useEnhancedTable<RoleIdentity>({
    columns,
    pageName: ROLE_LIST_KEY,
    keyObject: {},
    queryFn: async state => {
      const params = generateQueryParams(state, { columns, baseParams: {} })

      return getListRoles({ params })
    },
    initialState: {
      uiState: {
        showTime: true,
        showAdvancedFilter: false,
      },
      columnVisibility: {
        type: false,
      },
      sorting: [{ id: 'createdAt', desc: true }],
    },
    enabled: true,
    queryKey: [ROLE_LIST_KEY],
  })

  return (
    <>
      <AdminPageLayout breadcrumb={[]}>
        <AdminPageContent
          title={t('role.page.title')}
          icon={<ShieldIcon className="h-5 w-5" />}
          actions={[
            <AddButton
              key="create"
              permission={PERMISSIONS.PM06_ROLE_CREATE_ALL}
              tooltip={t('user-management.members.actions.create')}
              onClick={() => createDialogRef.current?.open()}
            />,
          ]}
        >
          <DataTable
            table={table}
            searchOptions={{
              helpContent: t('role.table.search.placeholder'),
            }}
          />
        </AdminPageContent>
      </AdminPageLayout>

      <ScrollableCreateFormDialog
        title={t('role.form.create.title')}
        ref={createDialogRef}
        onSuccess={() => table.options.meta?.reload()}
        customForm={RoleForm}
        formId="create-role-form"
        submitFn={createOrUpdateRole}
      />

      <ScrollableEditFormDialog
        title={t('role.form.edit.title')}
        initialData={selectedRole}
        ref={editDialogRef}
        onSuccess={() => table.options.meta?.reload()}
        customForm={RoleForm}
        formId="edit-role-form"
        submitFn={createOrUpdateRole}
      />

      <ConfirmDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        description={t('role.form.delete.confirmMessage', { name: selectedRole?.name || '' })}
        onConfirm={handleDeleteRole}
      />
    </>
  )
}
