import { ApiOptions, apiRequest } from '@/services/common/api-request'

const ROLE_ENDPOINT = '/ac-apis/roles'

interface RolePayload {
  name: string
  code: string
  description?: string
  permissions: string[]
  isSystem: number
  status: number
  type: number
}

function updateRole(id: string, data: RolePayload, options?: ApiOptions) {
  return apiRequest({
    url: `${ROLE_ENDPOINT}/${id}`,
    method: 'PUT',
    payload: data,
    successMessage: 'Cập nhật vai trò thành công',
    errorMessage: 'Lỗi cập nhật vai trò',
    ...options,
  })
}

function createRole(data: RolePayload, options?: ApiOptions) {
  return apiRequest({
    url: ROLE_ENDPOINT,
    method: 'POST',
    payload: data,
    successMessage: 'Tạo vai trò thành công',
    errorMessage: 'Lỗi tạo vai trò',
    ...options,
  })
}

function deleteRole(id: string, options?: ApiOptions) {
  return apiRequest({
    url: `${ROLE_ENDPOINT}/${id}`,
    method: 'DELETE',
    successMessage: 'Xóa vai trò thành công',
    errorMessage: 'Lỗi xóa vai trò',
    ...options,
  })
}

function getListRoles(options?: ApiOptions) {
  return apiRequest({
    url: ROLE_ENDPOINT,
    method: 'GET',
    // Don't need show success message because it's a list
    errorMessage: 'Lỗi lấy danh sách vai trò',
    ...options,
  })
}

// function getAllRoles(options?: ApiOptions) {
//   return apiRequest({
//     url: `${ROLE_ENDPOINT}/method/all`,
//     method: 'GET',
//     // Don't need show success message because it's a list
//     errorMessage: 'Lỗi lấy vai trò',
//     ...options,
//   })
// }

function createOrUpdateRole(id: string, data: RolePayload, options?: ApiOptions) {
  if (id) {
    return updateRole(id, data, options)
  }

  return createRole(data, options)
}

export { deleteRole, createOrUpdateRole, getListRoles }
