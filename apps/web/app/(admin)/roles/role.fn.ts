import { Any } from '@workspace/ui/types'

// Helper function to get all child permissions recursively
const getAllChildPermissions = (itemId: string, treeData: Any): string[] => {
  const item = treeData[itemId]

  if (!item || !item.children) {
    return []
  }

  const childPermissions: string[] = []

  const traverse = (children: string[]) => {
    children.forEach(childId => {
      const childItem = treeData[childId]

      if (childItem) {
        // If this child has no children, it's a leaf permission
        if (!childItem.children || childItem.children.length === 0) {
          childPermissions.push(childId)
        } else {
          // If this child has children, traverse them
          traverse(childItem.children)
        }
      }
    })
  }

  traverse(item.children)

  return childPermissions
}

// Helper function to expand checked items to include all child permissions
const expandCheckedPermissions = (checkedItems: string[], treeData: Any): string[] => {
  const expandedPermissions = new Set<string>()

  checkedItems.forEach(itemId => {
    expandedPermissions.add(itemId)

    // Get all child permissions for this item
    const childPermissions = getAllChildPermissions(itemId, treeData)
    childPermissions.forEach(permission => {
      expandedPermissions.add(permission)
    })
  })

  return Array.from(expandedPermissions)
}

// Helper function to filter only leaf permissions (permissions without children)
const filterLeafPermissions = (permissions: string[], treeData: Any): string[] => {
  return permissions.filter(permission => {
    const item = treeData[permission]

    // Only keep permissions that don't have children (leaf nodes)
    return !item || !item.children || item.children.length === 0
  })
}

export { expandCheckedPermissions, filterLeafPermissions }
