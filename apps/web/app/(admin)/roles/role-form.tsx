'use client'

import { RenderFieldStatus } from '@/components/render-field-status'
import { ROLE_TYPE } from '@/constants'
import { Any } from '@/lib/types'
import { useGlobalStore } from '@/stores'
import { zodResolver } from '@hookform/resolvers/zod'
import { Input } from '@workspace/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Switch } from '@workspace/ui/components/switch'
import { Textarea } from '@workspace/ui/components/textarea'
import { TreeCheckbox } from '@workspace/ui/components/tree-checkbox'
import { useTranslations } from '@workspace/ui/hooks/use-translations'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/mi'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { expandCheckedPermissions, filterLeafPermissions } from './role.fn'

interface RoleFormProps {
  initialData?: Any
  formId?: string
  onSubmit: (data: Any) => Promise<void>
}

export function RoleForm({ initialData, formId, onSubmit }: RoleFormProps) {
  const t = useTranslations()
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(initialData?.permissions || [])
  const { getOrgTypes, orgTypes, getPermissionsConfig, isLoadingOrgTypes, permissionsConfig } = useGlobalStore()

  useEffect(() => {
    getOrgTypes()
    getPermissionsConfig()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const formSchema = z.object({
    name: z.string().min(1),
    code: z.string().min(1),
    description: z.string().min(1),
    permissions: z.array(z.string()),
    isSystem: z.boolean(),
    status: z.number(),
    type: z.string(),
  })

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || '',
      code: initialData?.code || '',
      description: initialData?.description || '',
      permissions: initialData?.permissions || [],
      isSystem: initialData?.isSystem === 1 || false,
      status: initialData?.status !== undefined ? initialData.status : undefined,
      type: initialData?.type || initialData?.type === 0 ? `${initialData.type}` : '',
    },
  })

  const onSubmitForm = async (data: FormValues) => {
    const expandedPermissions = expandCheckedPermissions(data.permissions, permissionsConfig?.treeData || {})

    const leafPermissions = filterLeafPermissions(expandedPermissions, permissionsConfig?.treeData || {})

    const payload = {
      name: data.name,
      code: data.code,
      description: data.description || '',
      permissions: leafPermissions.filter(permission => !permissionsConfig?.permissionRootGroup?.includes(permission)),
      isSystem: data.isSystem ? 1 : 0,
      status: data.status,
      type: Number(data.type),
    }

    onSubmit(payload)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitForm)} id={formId}>
        <div className="space-y-4 px-1">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Tên vai trò</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nhập tên vai trò" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{t('role.field.code')}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nhập mã vai trò" disabled={!!initialData} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{t('role.field.description')}</FormLabel>
                <FormControl>
                  <Textarea {...field} rows={3} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{t('role.field.type')}</FormLabel>
                <FormControl>
                  <Select
                    value={form.watch('type')?.toString() || initialData?.type?.toString()}
                    onValueChange={value => field.onChange(`${value}`)}
                  >
                    <SelectTrigger>
                      <SelectValue loading={isLoadingOrgTypes} title={t('role.field.type')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...orgTypes, { value: ROLE_TYPE.KHAC, label: t('common.value.other') }].map(orgType => (
                        <SelectItem key={orgType.value} value={`${orgType.value}`}>
                          {orgType.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="isSystem"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('role.field.isSystem')}</FormLabel>
                <FormControl>
                  <Switch id="isSystem" checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <RenderFieldStatus label={t('role.field.status')} required={true} form={form} />

          {permissionsConfig?.treeData && (
            <FormField
              control={form.control}
              name="permissions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>{t('role.field.permissions')}</FormLabel>
                  <FormControl>
                    <TreeCheckbox
                      items={permissionsConfig?.treeData}
                      rootItemId="root"
                      initialExpandedItems={['root', 'admin', 'tenant', 'provisionAndCase']}
                      initialCheckedItems={selectedPermissions}
                      onChange={(checkedItems: string[]) => {
                        setSelectedPermissions(checkedItems)
                        field.onChange(checkedItems)
                      }}
                      showDescription={false}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </form>
    </Form>
  )
}
