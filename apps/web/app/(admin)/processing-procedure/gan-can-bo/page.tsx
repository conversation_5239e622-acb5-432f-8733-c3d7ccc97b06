'use client'

import WorkflowDesigner from '@/components/workflow/WorkflowDesigner'
import {
  BuocXuLyMau,
  CAN_BO,
  DON_VI,
  LHanhDongHoSo,
  PHONG_BAN,
  // BuocXuLyThucTe,
  QuyTrinhMau,
  QuyTrinhThucHien,
} from '@/lib/workflow/shared'
import { Badge } from '@workspace/ui/components/badge'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Separator } from '@workspace/ui/components/separator'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Building, Users } from 'lucide-react'
import React, { useState } from 'react'

import { quyTrinhTest } from '../tao-moi/quy-trinh-test'

interface BuocGanCanBo extends BuocXuLyMau {
  idPhongBan?: string
  idCanBoXuLy?: string
}

export default function GanCanCanBoPage() {
  const [quyTrinhMauDaChon, setQuyTrinhMauDaChon] = useState<QuyTrinhMau | null>(null)
  const [donViDaChon, setDonViDaChon] = useState<string>('')
  const [cacBuocGanCanBo, setCacBuocGanCanBo] = useState<BuocGanCanBo[]>([])
  const [buocDangChon, setBuocDangChon] = useState<BuocGanCanBo | null>(null)
  const [assignmentData, setAssignmentData] = useState<{
    [buocId: string]: { phongBan?: string; canBo?: string }
  }>({})

  // Danh sách quy trình mẫu có sẵn (giả lập)
  const danhSachQuyTrinhMau = [quyTrinhTest]

  const chonQuyTrinh = (quyTrinhId: string) => {
    const quyTrinh = danhSachQuyTrinhMau.find(qt => qt.id === quyTrinhId)

    if (quyTrinh) {
      setQuyTrinhMauDaChon(quyTrinh)
      setCacBuocGanCanBo(quyTrinh.cacBuoc.map(buoc => ({ ...buoc })))
      setBuocDangChon(null)
      setAssignmentData({})
    }
  }

  const chonDonVi = (donViId: string) => {
    setDonViDaChon(donViId)
  }

  const chonBuoc = (buoc: BuocXuLyMau | null) => {
    if (buoc) {
      const buocGanCanBo = cacBuocGanCanBo.find(b => b.id === buoc.id)
      setBuocDangChon(buocGanCanBo || null)
    } else {
      setBuocDangChon(null)
    }
  }

  const capNhatPhongBan = (buocId: string, phongBanId: string) => {
    setCacBuocGanCanBo(prev =>
      prev.map(buoc => (buoc.id === buocId ? { ...buoc, idPhongBan: phongBanId, idCanBoXuLy: '' } : buoc))
    )

    setAssignmentData(prev => ({
      ...prev,
      [buocId]: {
        ...prev[buocId],
        phongBan: PHONG_BAN.find(pb => pb.id === phongBanId)?.name,
        canBo: '',
      },
    }))

    if (buocDangChon?.id === buocId) {
      setBuocDangChon({
        ...buocDangChon,
        idPhongBan: phongBanId,
        idCanBoXuLy: '',
      })
    }
  }

  const capNhatCanBo = (buocId: string, canBoId: string) => {
    setCacBuocGanCanBo(prev => prev.map(buoc => (buoc.id === buocId ? { ...buoc, idCanBoXuLy: canBoId } : buoc)))

    setAssignmentData(prev => ({
      ...prev,
      [buocId]: {
        ...prev[buocId],
        canBo: CAN_BO.find(cb => cb.id === canBoId)?.name,
      },
    }))

    if (buocDangChon?.id === buocId) {
      setBuocDangChon({ ...buocDangChon, idCanBoXuLy: canBoId })
    }
  }

  const luuQuyTrinhThucHien = () => {
    if (!quyTrinhMauDaChon || !donViDaChon) return

    const quyTrinhThucHien: QuyTrinhThucHien = {
      id: `qtth_${Date.now()}`,
      ten: `${quyTrinhMauDaChon.ten} - ${DON_VI.find(dv => dv.id === donViDaChon)?.name}`,
      moTa: quyTrinhMauDaChon.moTa,
      buocDau: quyTrinhMauDaChon.buocDau,
      thuTucId: quyTrinhMauDaChon.thuTucId,
      donViId: donViDaChon,
      cacBuoc: cacBuocGanCanBo.map(buoc => ({
        id: buoc.id,
        ten: buoc.ten,
        loai: buoc.loai,
        vaiTro: 'TODO',
        hanhDongChoPhep: buoc.hanhDongChoPhep,
        buocTiepTheo: buoc.buocTiepTheo,
        idPhongBan: buoc.idPhongBan || '',
        idCanBoXuLy: buoc.idCanBoXuLy || '',
      })),
    }

    console.log('Quy trình thực hiện được tạo:', quyTrinhThucHien)
    // TODO: Gọi API để lưu quy trình thực hiện
  }

  const phongBanTheoDonVi = PHONG_BAN.filter(pb => pb.donViId === donViDaChon)
  const canBoTheoPhongBan = buocDangChon?.idPhongBan
    ? CAN_BO.filter(cb => cb.phongBanId === buocDangChon.idPhongBan)
    : []

  const kiemTraHoanThanh = () => {
    return cacBuocGanCanBo.every(buoc => buoc.idPhongBan && buoc.idCanBoXuLy)
  }

  return (
    <AdminPageLayout
      breadcrumb={[
        {
          label: 'Gán cán bộ vào quy trình',
          href: '/processing-procedure/gan-can-bo',
        },
      ]}
    >
      {/* Header - Chọn quy trình và đơn vị */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Gán cán bộ vào quy trình</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="quyTrinh">Chọn quy trình mẫu</Label>
            <Select onValueChange={chonQuyTrinh}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn quy trình cần gán cán bộ" />
              </SelectTrigger>
              <SelectContent>
                {danhSachQuyTrinhMau.map(qt => (
                  <SelectItem key={qt.id} value={qt.id}>
                    {qt.ten}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="donVi">Chọn đơn vị</Label>
            <Select onValueChange={chonDonVi} disabled={!quyTrinhMauDaChon}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn đơn vị thực hiện" />
              </SelectTrigger>
              <SelectContent>
                {DON_VI.map(dv => (
                  <SelectItem key={dv.id} value={dv.id}>
                    {dv.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Layout 2 cột với React Flow */}
      {quyTrinhMauDaChon && donViDaChon && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Cột trái - React Flow Workflow */}
          <div className="h-[600px] lg:col-span-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Quy trình: {quyTrinhMauDaChon.ten}</CardTitle>
              </CardHeader>
              <CardContent className="h-[calc(100%-80px)]">
                <WorkflowDesigner
                  cacBuoc={cacBuocGanCanBo}
                  onBuocChange={setCacBuocGanCanBo}
                  onSelectBuoc={chonBuoc}
                  buocDangChon={buocDangChon}
                  isAssignmentMode={true}
                  assignmentData={assignmentData}
                />
              </CardContent>
            </Card>
          </div>

          {/* Cột phải - Sidebar Properties */}
          <div>
            {buocDangChon ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Gán cán bộ</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="mb-2 text-sm font-medium">{buocDangChon.ten}</h4>
                    {/* <p className="text-xs text-gray-600">{buocDangChon.vaiTro}</p> */}
                    <Badge variant="secondary" className="mt-2 text-xs">
                      {buocDangChon.loai === 'TIEP_NHAN'
                        ? 'Tiếp nhận'
                        : buocDangChon.loai === 'XU_LY'
                          ? 'Xử lý'
                          : 'Trả kết quả'}
                    </Badge>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>Phòng ban thực hiện</Label>
                    <Select
                      value={buocDangChon.idPhongBan || ''}
                      onValueChange={value => capNhatPhongBan(buocDangChon.id, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn phòng ban" />
                      </SelectTrigger>
                      <SelectContent>
                        {phongBanTheoDonVi.map(pb => (
                          <SelectItem key={pb.id} value={pb.id}>
                            <div className="flex items-center space-x-2">
                              <Building className="h-4 w-4" />
                              <span>{pb.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Cán bộ xử lý</Label>
                    <Select
                      value={buocDangChon.idCanBoXuLy || ''}
                      onValueChange={value => capNhatCanBo(buocDangChon.id, value)}
                      disabled={!buocDangChon.idPhongBan}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn cán bộ" />
                      </SelectTrigger>
                      <SelectContent>
                        {canBoTheoPhongBan.map(cb => (
                          <SelectItem key={cb.id} value={cb.id}>
                            <div className="flex items-center space-x-2">
                              <Users className="h-4 w-4" />
                              <span>{cb.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>Hành động có thể thực hiện</Label>
                    <div className="space-y-1">
                      {buocDangChon.hanhDongChoPhep.map(hanhDong => (
                        <div key={hanhDong} className="rounded bg-gray-100 px-2 py-1 text-xs">
                          {LHanhDongHoSo[hanhDong]}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Quy tắc gán cán bộ */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-500">Lưu ý</Label>
                    <div className="space-y-1 text-xs text-gray-500">
                      {buocDangChon.loai === 'TIEP_NHAN' && (
                        <p>• Luôn có sẵn: Đồng ý tiếp nhận, Không tiếp nhận, Yêu cầu bổ sung, Từ chối</p>
                      )}
                      {buocDangChon.loai === 'XU_LY' && (
                        <p>• Cần chọn thêm hành động xử lý khi thiết kế quy trình mẫu</p>
                      )}
                      {buocDangChon.loai === 'TRA_KET_QUA' && <p>• Luôn có sẵn: Trả kết quả, Hủy kết quả</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="py-8 text-center text-gray-500">
                  <Users className="mx-auto mb-2 h-8 w-8 opacity-50" />
                  <p>Chọn một bước trong quy trình để gán cán bộ</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Nút lưu */}
      {quyTrinhMauDaChon && donViDaChon && (
        <div className="flex justify-end space-x-4">
          <Button variant="outline">Hủy</Button>
          <Button onClick={luuQuyTrinhThucHien} disabled={!kiemTraHoanThanh()}>
            Lưu quy trình thực hiện
          </Button>
        </div>
      )}
    </AdminPageLayout>
  )
}
