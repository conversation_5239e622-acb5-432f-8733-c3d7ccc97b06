'use client'

import { BPMNWorkflowDemo } from '@/components/workflow/BPMNWorkflowDemo'
import { WorkflowDemo } from '@/components/workflow/WorkflowDemo'
import WorkflowDesigner from '@/components/workflow/WorkflowDesigner'
import {
  BuocXuLyMau,
  CHanhDongCoBan,
  CHanhDongXuLy,
  IHanhDongHoSo,
  ILoaiBuocXuLy,
  LHanhDongHoSo,
  QuyTrinhMau,
  RHanhDongHoSo,
  RLoaiBuocXuLy,
  THU_TUC,
} from '@/lib/workflow/shared'
import { Button } from '@workspace/ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card'
import { Checkbox } from '@workspace/ui/components/checkbox'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { Input } from '@workspace/ui/components/input'
import { Label } from '@workspace/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select'
import { Separator } from '@workspace/ui/components/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs'
import { Textarea } from '@workspace/ui/components/textarea'
import { AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { Redo2, Undo2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import React, { useCallback, useEffect, useRef, useState } from 'react'

import { quyTrinhTest } from './quy-trinh-test'

// Type cho history state
interface HistoryState {
  quyTrinh: Partial<QuyTrinhMau>
  cacBuoc: BuocXuLyMau[]
  buocHienTai: BuocXuLyMau | null
}

export function QuyTrinhEditor({ quyTrinhMau }: { quyTrinhMau?: QuyTrinhMau }) {
  const t = useTranslations()

  const [quyTrinh, setQuyTrinh] = useState<Partial<QuyTrinhMau>>(
    quyTrinhMau || {
      ten: '',
      moTa: '',
      thuTucId: '',
      buocDau: '',
      cacBuoc: [],
    }
  )

  // console.log('quyTrinhMau', quyTrinhMau?.cacBuoc)
  const [cacBuoc, setCacBuoc] = useState<BuocXuLyMau[]>(quyTrinhMau?.cacBuoc || [])
  const [buocHienTai, setBuocHienTai] = useState<BuocXuLyMau | null>(null)

  const [renderKey, setRenderKey] = useState(1)

  // History management
  const [history, setHistory] = useState<HistoryState[]>([])
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1)
  const MAX_HISTORY = 200

  // Debouncer cho việc update render key
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const debouncedUpdateRenderKey = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setRenderKey(prev => prev + 1)
    }, 300) // Delay 300ms
  }, [])

  // Khởi tạo workflow mặc định khi component mount
  useEffect(() => {
    if (quyTrinhMau) {
      setQuyTrinh(quyTrinhMau)
      setCacBuoc(quyTrinhMau.cacBuoc)
      setBuocHienTai(quyTrinhMau.cacBuoc[0] || null)

      return
    }

    const workflowMacDinh: BuocXuLyMau[] = quyTrinhTest?.cacBuoc

    setCacBuoc(workflowMacDinh)

    // Tự động chọn bước tiếp nhận để cấu hình
    setBuocHienTai(workflowMacDinh[0] || null)

    // Khởi tạo history với state ban đầu
    const initialState: HistoryState = {
      quyTrinh: {
        ten: '',
        moTa: '',
        thuTucId: '',
        buocDau: '',
        cacBuoc: [],
      },
      cacBuoc: workflowMacDinh,
      buocHienTai: workflowMacDinh[0] ?? null,
    }
    setHistory([initialState])
    setCurrentHistoryIndex(0)

    // Cleanup debounce timeout khi component unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // History management functions
  const addToHistory = useCallback(
    (state: HistoryState) => {
      setHistory(prevHistory => {
        // Xóa tất cả history sau current index nếu đang ở giữa
        const newHistory = prevHistory.slice(0, currentHistoryIndex + 1)

        // Thêm state mới
        newHistory.push(state)

        // Giới hạn history tối đa 200 bước
        if (newHistory.length > MAX_HISTORY) {
          newHistory.shift() // Xóa item đầu tiên
          setCurrentHistoryIndex(prev => prev) // Giữ nguyên index vì đã shift
        } else {
          setCurrentHistoryIndex(newHistory.length - 1)
        }

        return newHistory
      })
    },
    [currentHistoryIndex, MAX_HISTORY]
  )

  const setCacBuocWithRenderKey = useCallback(
    (newCacBuoc: BuocXuLyMau[]) => {
      // Lưu state hiện tại vào history trước khi thay đổi
      addToHistory({
        quyTrinh,
        cacBuoc,
        buocHienTai,
      })

      setCacBuoc(newCacBuoc)
      debouncedUpdateRenderKey()
    },
    [quyTrinh, cacBuoc, buocHienTai, addToHistory, debouncedUpdateRenderKey]
  )

  const canUndo = currentHistoryIndex > 0
  const canRedo = currentHistoryIndex < history.length - 1

  const undo = useCallback(() => {
    if (canUndo) {
      const newIndex = currentHistoryIndex - 1
      const state = history[newIndex]

      if (state) {
        console.log(
          `Undo - index ${currentHistoryIndex} → ${newIndex} - state được khôi phục:`,
          JSON.stringify({
            buocHienTai: JSON.parse(JSON.stringify(state.buocHienTai)),
            cacBuoc: JSON.parse(JSON.stringify(state.cacBuoc)),
          })
        )
        setQuyTrinh(state.quyTrinh)
        setCacBuoc(state.cacBuoc)

        // Đảm bảo buocHienTai được đồng bộ chính xác từ cacBuoc
        if (state.buocHienTai) {
          const updatedBuocHienTai = state.cacBuoc.find(b => b.id === state.buocHienTai?.id)
          console.log(
            'Undo - buocHienTai được đồng bộ:',
            JSON.stringify({
              original: JSON.parse(JSON.stringify(state.buocHienTai)),
              synced: JSON.parse(JSON.stringify(updatedBuocHienTai)),
            })
          )
          setBuocHienTai(updatedBuocHienTai || state.buocHienTai)
        } else {
          setBuocHienTai(state.buocHienTai)
        }
      }
      setCurrentHistoryIndex(newIndex)
      debouncedUpdateRenderKey()
    }
  }, [canUndo, currentHistoryIndex, history, debouncedUpdateRenderKey])

  const redo = useCallback(() => {
    if (canRedo) {
      const newIndex = currentHistoryIndex + 1
      const state = history[newIndex]

      if (state) {
        console.log(
          `Redo - index ${currentHistoryIndex} → ${newIndex} - state được khôi phục:`,
          JSON.stringify({
            buocHienTai: JSON.parse(JSON.stringify(state.buocHienTai)),
            cacBuoc: JSON.parse(JSON.stringify(state.cacBuoc)),
          })
        )

        setQuyTrinh(state.quyTrinh)
        setCacBuoc(state.cacBuoc)

        // Đảm bảo buocHienTai được đồng bộ chính xác từ cacBuoc
        if (state.buocHienTai) {
          const updatedBuocHienTai = state.cacBuoc.find(b => b.id === state.buocHienTai?.id)
          console.log(
            'Redo - buocHienTai được đồng bộ:',
            JSON.stringify({
              original: JSON.parse(JSON.stringify(state.buocHienTai)),
              synced: JSON.parse(JSON.stringify(updatedBuocHienTai)),
            })
          )
          setBuocHienTai(updatedBuocHienTai || state.buocHienTai)
        } else {
          setBuocHienTai(state.buocHienTai)
        }
      }
      setCurrentHistoryIndex(newIndex)
      debouncedUpdateRenderKey()
    }
  }, [canRedo, currentHistoryIndex, history, debouncedUpdateRenderKey])

  // Tự động tạo hành động cho phép dựa vào loại bước
  const taoHanhDongChoPhep = (loai: ILoaiBuocXuLy, hanhDongTuyChon: IHanhDongHoSo[] = []): IHanhDongHoSo[] => {
    switch (loai) {
      case RLoaiBuocXuLy.TIEP_NHAN:
        return CHanhDongCoBan
      case RLoaiBuocXuLy.XU_LY:
        return [...hanhDongTuyChon, ...CHanhDongCoBan]
      case RLoaiBuocXuLy.TRA_KET_QUA:
        return [RHanhDongHoSo.DONG_TGPL]
      default:
        return CHanhDongCoBan
    }
  }

  const capNhatBuocVoiHistory = (buocCapNhat: Partial<BuocXuLyMau>) => {
    if (!buocHienTai) return

    // Lưu state hiện tại vào history trước khi thay đổi
    addToHistory({
      quyTrinh,
      cacBuoc,
      buocHienTai,
    })

    capNhatBuocKhongHistory(buocCapNhat)
  }

  const capNhatBuocKhongHistory = (buocCapNhat: Partial<BuocXuLyMau>) => {
    if (!buocHienTai) return

    const cacBuocMoi = cacBuoc.map(buoc => {
      if (buoc.id === buocHienTai.id) {
        const buocMoi = { ...buoc, ...buocCapNhat }

        if (buocCapNhat.loai) {
          const hanhDongTuyChon =
            buocCapNhat.loai === RLoaiBuocXuLy.XU_LY
              ? buocMoi.hanhDongChoPhep?.filter(hd => CHanhDongXuLy.includes(hd)) || []
              : []
          buocMoi.hanhDongChoPhep = taoHanhDongChoPhep(buocCapNhat.loai, hanhDongTuyChon)
        }

        return buocMoi
      }

      return buoc
    })

    const buocHienTaiMoi = { ...buocHienTai, ...buocCapNhat }

    console.log('capNhatBuocKhongHistory - dữ liệu cập nhật:', {
      buocCapNhat: JSON.stringify(buocCapNhat),
      buocHienTaiMoi: JSON.stringify(buocHienTaiMoi),
    })

    setCacBuoc(cacBuocMoi)
    setBuocHienTai(buocHienTaiMoi)
    debouncedUpdateRenderKey()
  }

  // Alias for backward compatibility
  const capNhatBuoc = capNhatBuocVoiHistory

  const luuQuyTrinh = () => {
    const quyTrinhHoanThien: QuyTrinhMau = {
      id: quyTrinhMau?.id || `qtm_${Date.now()}`,
      ten: quyTrinh.ten || '',
      moTa: quyTrinh.moTa || '',
      thuTucId: quyTrinh.thuTucId || '',
      buocDau: cacBuoc[0]?.id || '',
      cacBuoc: cacBuoc,
      macDinh: quyTrinh?.macDinh || false,
    }

    console.log('Quy trình mẫu được tạo:', quyTrinhHoanThien)
    // TODO: Gọi API để lưu quy trình
  }

  const capNhatHanhDongXuLy = (hanhDong: IHanhDongHoSo, checked: boolean) => {
    if (!buocHienTai || buocHienTai.loai !== RLoaiBuocXuLy.XU_LY) return

    // Nếu đang bỏ chọn, kiểm tra xem có kết nối không
    if (!checked) {
      const ketNoiCoSan = buocHienTai.ketNoiDen?.find(k => k.hanhDong === hanhDong)

      if (ketNoiCoSan) {
        // Tìm tên bước đích để hiển thị trong confirm dialog
        const targetBuoc = cacBuoc.find(b => b.id === ketNoiCoSan.targetId)
        const targetName = targetBuoc?.ten || ketNoiCoSan.targetId

        setPendingActionDelete({ hanhDong, targetName })
        setIsConfirmDeleteOpen(true)

        return // Dừng tại đây, chờ user confirm
      }
    }

    // Thực hiện cập nhật hành động
    thucHienCapNhatHanhDong(hanhDong, checked)
  }

  const thucHienCapNhatHanhDong = (hanhDong: IHanhDongHoSo, checked: boolean) => {
    if (!buocHienTai || buocHienTai.loai !== RLoaiBuocXuLy.XU_LY) return

    const hanhDongHienTai = buocHienTai.hanhDongChoPhep || []

    let hanhDongMoi

    if (checked) {
      hanhDongMoi = [...hanhDongHienTai, hanhDong]
    } else {
      hanhDongMoi = hanhDongHienTai.filter(hd => hd !== hanhDong)
    }

    // Đảm bảo luôn có hành động cơ bản
    const hanhDongTuyChon = hanhDongMoi.filter(hd => CHanhDongXuLy.includes(hd)) as IHanhDongHoSo[]
    const hanhDongCuoiCung: IHanhDongHoSo[] = [...hanhDongTuyChon, ...CHanhDongCoBan] as IHanhDongHoSo[]

    // Nếu bỏ chọn, xóa kết nối liên quan
    let ketNoiMoi = buocHienTai.ketNoiDen || []
    const buocTiepTheoMoi = { ...(buocHienTai.buocTiepTheo || {}) }

    if (!checked) {
      // Xóa kết nối của hành động này
      ketNoiMoi = ketNoiMoi.filter(k => k.hanhDong !== hanhDong)
      // Xóa trong buocTiepTheo
      delete buocTiepTheoMoi[hanhDong]
    }

    capNhatBuoc({
      hanhDongChoPhep: hanhDongCuoiCung,
      ketNoiDen: ketNoiMoi,
      buocTiepTheo: buocTiepTheoMoi,
    })
  }

  const confirmDeleteAction = () => {
    if (pendingActionDelete) {
      thucHienCapNhatHanhDong(pendingActionDelete.hanhDong, false)
    }
    setIsConfirmDeleteOpen(false)
    setPendingActionDelete(null)
  }

  const cancelDeleteAction = () => {
    setIsConfirmDeleteOpen(false)
    setPendingActionDelete(null)
  }

  // Keyboard shortcuts cho undo/redo
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        if (event.key === 'z' && !event.shiftKey) {
          event.preventDefault()
          undo()
        } else if (event.key === 'y' || (event.key === 'z' && event.shiftKey)) {
          event.preventDefault()
          redo()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [undo, redo])

  const renderHistoryToolbar = () => {
    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={undo}
            disabled={!canUndo}
            className="flex items-center space-x-1"
          >
            <Undo2 className="h-4 w-4" />
            {/* <span>Hoàn tác</span> */}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={redo}
            disabled={!canRedo}
            className="flex items-center space-x-1"
          >
            <Redo2 className="h-4 w-4" />
            {/* <span>Làm lại</span> */}
          </Button>
          <div className="ml-2 text-xs text-gray-500">
            {history.length > 0 ? `${currentHistoryIndex + 1}/${history.length}` : '0/0'}
          </div>
        </div>
      </div>
    )
  }

  // Modal cấu hình kết nối cho hành động
  const [isKetNoiModalOpen, setIsKetNoiModalOpen] = useState(false)
  const [hanhDongDangConfig, setHanhDongDangConfig] = useState<IHanhDongHoSo | null>(null)
  const [ketNoiLabel, setKetNoiLabel] = useState('')
  const [ketNoiTargetId, setKetNoiTargetId] = useState('')

  // Modal confirm xóa kết nối
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [pendingActionDelete, setPendingActionDelete] = useState<{
    hanhDong: IHanhDongHoSo
    targetName: string
  } | null>(null)

  const openKetNoiModal = (hanhDong: IHanhDongHoSo) => {
    if (!buocHienTai) return
    setHanhDongDangConfig(hanhDong)
    const existing = buocHienTai.ketNoiDen?.find(k => k.hanhDong === hanhDong)
    setKetNoiLabel(existing?.label || '')
    setKetNoiTargetId(existing?.targetId || '')
    setIsKetNoiModalOpen(true)
  }

  const saveKetNoi = () => {
    if (!buocHienTai || !hanhDongDangConfig || !ketNoiTargetId) {
      setIsKetNoiModalOpen(false)

      return
    }

    console.log(
      'Trước khi saveKetNoi:',
      JSON.stringify({
        buocHienTai: JSON.parse(JSON.stringify(buocHienTai)),
        hanhDongDangConfig,
        ketNoiTargetId,
        ketNoiLabel,
      })
    )

    // Lưu state hiện tại vào history trước khi thay đổi
    addToHistory({
      quyTrinh,
      cacBuoc,
      buocHienTai,
    })

    const existingList = buocHienTai.ketNoiDen || []
    const updatedList = [...existingList]
    const idx = updatedList.findIndex(k => k.hanhDong === hanhDongDangConfig)
    const newItem = {
      targetId: ketNoiTargetId,
      label: ketNoiLabel,
      hanhDong: hanhDongDangConfig as IHanhDongHoSo,
    }

    if (idx >= 0) updatedList[idx] = newItem
    else updatedList.push(newItem)

    const newBuocTiepTheo = {
      ...(buocHienTai.buocTiepTheo || {}),
      [hanhDongDangConfig]: ketNoiTargetId,
    }

    console.log(
      'Dữ liệu sẽ cập nhật:',
      JSON.stringify({
        ketNoiDen: updatedList,
        buocTiepTheo: newBuocTiepTheo,
      })
    )

    // Tính toán state mới trước
    const cacBuocMoi = cacBuoc.map(buoc => {
      if (buoc.id === buocHienTai.id) {
        return {
          ...buoc,
          ketNoiDen: updatedList,
          buocTiepTheo: newBuocTiepTheo,
        }
      }

      return buoc
    })
    const buocHienTaiMoi = {
      ...buocHienTai,
      ketNoiDen: updatedList,
      buocTiepTheo: newBuocTiepTheo,
    }

    console.log('State mới sẽ set:', {
      buocHienTaiMoi: JSON.stringify(buocHienTaiMoi),
    })

    // Cập nhật state
    setCacBuoc(cacBuocMoi)
    setBuocHienTai(buocHienTaiMoi)
    debouncedUpdateRenderKey()

    // Lưu state mới vào history cho redo ngay lập tức
    addToHistory({
      quyTrinh,
      cacBuoc: cacBuocMoi,
      buocHienTai: buocHienTaiMoi,
    })

    console.log('Đã lưu state mới vào history cho redo')
    setIsKetNoiModalOpen(false)
  }

  const deleteKetNoi = () => {
    if (!buocHienTai || !hanhDongDangConfig) {
      setIsKetNoiModalOpen(false)

      return
    }

    // Lưu state hiện tại vào history trước khi thay đổi
    addToHistory({
      quyTrinh,
      cacBuoc,
      buocHienTai,
    })

    const existingList = buocHienTai.ketNoiDen || []
    const updatedList = existingList.filter(k => k.hanhDong !== hanhDongDangConfig)

    const newBuocTiepTheo = { ...(buocHienTai.buocTiepTheo || {}) }
    delete newBuocTiepTheo[hanhDongDangConfig]

    // Tính toán state mới trước
    const cacBuocMoi = cacBuoc.map(buoc => {
      if (buoc.id === buocHienTai.id) {
        return {
          ...buoc,
          ketNoiDen: updatedList,
          buocTiepTheo: newBuocTiepTheo,
        }
      }

      return buoc
    })
    const buocHienTaiMoi = {
      ...buocHienTai,
      ketNoiDen: updatedList,
      buocTiepTheo: newBuocTiepTheo,
    }

    // Cập nhật state
    setCacBuoc(cacBuocMoi)
    setBuocHienTai(buocHienTaiMoi)
    debouncedUpdateRenderKey()

    // Lưu state mới vào history cho redo
    addToHistory({
      quyTrinh,
      cacBuoc: cacBuocMoi,
      buocHienTai: buocHienTaiMoi,
    })

    setIsKetNoiModalOpen(false)
  }

  const getHanhDongTen = (hanhDong?: IHanhDongHoSo | null) => {
    if (!hanhDong) return ''

    return LHanhDongHoSo[hanhDong] || `Không xác định - ${hanhDong}`
  }

  const formatThoiGian = (soGio?: number) => {
    if (!soGio || soGio <= 0) return ''
    const ngay = Math.floor(soGio / 8)
    const gio = soGio % 8
    let result = ''

    if (ngay > 0) result += `${ngay} ngày`

    if (gio > 0) result += `${ngay > 0 ? ' ' : ''}${gio} giờ`

    return result
  }

  return (
    <div>
      <AdminPageLayout
        breadcrumb={[
          {
            label: quyTrinhMau?.id ? t('processing-management.update.title') : t('processing-management.create.title'),
            href: quyTrinhMau?.id ? '/processing-procedure/chinh-sua' : '/processing-procedure/tao-moi',
          },
        ]}
      >
        <Card>
          <CardHeader>
            <CardTitle>{quyTrinhMau?.id ? 'Chỉnh sửa quy trình mẫu' : 'Tạo mới quy trình mẫu'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Thông tin cơ bản quy trình */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="ten">Tên quy trình</Label>
                <Input
                  id="ten"
                  value={quyTrinh.ten}
                  onChange={e => {
                    // Lưu state hiện tại vào history
                    addToHistory({
                      quyTrinh,
                      cacBuoc,
                      buocHienTai,
                    })
                    setQuyTrinh({ ...quyTrinh, ten: e.target.value })
                  }}
                  placeholder="Nhập tên quy trình"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="thuTuc">Thủ tục</Label>
                <Select
                  value={quyTrinh.thuTucId}
                  onValueChange={value => {
                    // Lưu state hiện tại vào history
                    addToHistory({
                      quyTrinh,
                      cacBuoc,
                      buocHienTai,
                    })
                    setQuyTrinh({ ...quyTrinh, thuTucId: value })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn thủ tục" />
                  </SelectTrigger>
                  <SelectContent>
                    {THU_TUC.map(thuTuc => (
                      <SelectItem key={thuTuc.id} value={thuTuc.id}>
                        {thuTuc.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="moTa">Mô tả</Label>
              <Textarea
                id="moTa"
                value={quyTrinh.moTa}
                onChange={e => {
                  // Lưu state hiện tại vào history
                  addToHistory({
                    quyTrinh,
                    cacBuoc,
                    buocHienTai,
                  })
                  setQuyTrinh({ ...quyTrinh, moTa: e.target.value })
                }}
                placeholder="Nhập mô tả quy trình"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="macDinh"
                checked={quyTrinh?.macDinh}
                onCheckedChange={checked => {
                  addToHistory({
                    quyTrinh,
                    cacBuoc,
                    buocHienTai,
                  })
                  setQuyTrinh({ ...quyTrinh, macDinh: checked === true })
                }}
              />
              <Label htmlFor="kichHoat">Đặt làm mặc định</Label>
            </div>
          </CardContent>
        </Card>
      </AdminPageLayout>

      {/* Layout với React Flow */}
      <Tabs defaultValue="editor" className="w-full px-5">
        <TabsList className="grid w-full grid-cols-3">
          {/* <TabsTrigger value="bpmn">BPMN Layout</TabsTrigger> */}
          <TabsTrigger value="editor">Editor</TabsTrigger>
          {/* <TabsTrigger value="demo">Demo Auto-Layout</TabsTrigger> */}
        </TabsList>

        <TabsContent value="editor" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
            <div className="h-[600px] lg:col-span-3">
              <WorkflowDesigner
                cacBuoc={cacBuoc}
                onBuocChange={setCacBuocWithRenderKey}
                onSelectBuoc={setBuocHienTai}
                buocDangChon={buocHienTai}
                renderHistoryToolbar={renderHistoryToolbar}
                key={renderKey}
              />
            </div>

            {/* Panel cấu hình bước */}
            <div>
              {buocHienTai ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Cấu hình bước</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="tenBuoc">Tên bước</Label>
                      <Input
                        id="tenBuoc"
                        value={buocHienTai.ten}
                        onChange={e => {
                          // Chỉ update UI, không lưu history
                          if (!buocHienTai) return
                          const cacBuocMoi = cacBuoc.map(buoc => {
                            if (buoc.id === buocHienTai.id) {
                              return { ...buoc, ten: e.target.value }
                            }

                            return buoc
                          })
                          setCacBuoc(cacBuocMoi)
                          setBuocHienTai({
                            ...buocHienTai,
                            ten: e.target.value,
                          })
                        }}
                        onBlur={() => {
                          // Lưu vào history khi blur
                          addToHistory({
                            quyTrinh,
                            cacBuoc,
                            buocHienTai,
                          })
                          debouncedUpdateRenderKey()
                        }}
                        placeholder="Nhập tên bước"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="loaiBuoc">Loại bước</Label>
                      <Select
                        value={buocHienTai.loai}
                        onValueChange={(value: ILoaiBuocXuLy) => capNhatBuoc({ loai: value })}
                        disabled={
                          buocHienTai.loai === RLoaiBuocXuLy.TIEP_NHAN || buocHienTai.loai === RLoaiBuocXuLy.TRA_KET_QUA
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="TIEP_NHAN">Tiếp nhận</SelectItem>
                          <SelectItem value="XU_LY">Xử lý</SelectItem>
                          <SelectItem value="TRA_KET_QUA">Trả kết quả</SelectItem>
                        </SelectContent>
                      </Select>
                      {(buocHienTai.loai === RLoaiBuocXuLy.TIEP_NHAN ||
                        buocHienTai.loai === RLoaiBuocXuLy.TRA_KET_QUA) && (
                        <div className="text-xs text-gray-500">
                          Loại bước này không thể thay đổi vì là bước bắt buộc trong quy trình.
                        </div>
                      )}
                    </div>

                    {/* <div className="space-y-2">
                      <Label htmlFor="vaiTro">Vai trò</Label>
                      <Input
                        id="vaiTro"
                        value={buocHienTai.vaiTro}
                        onChange={e => capNhatBuoc({ vaiTro: e.target.value })}
                        placeholder="Ví dụ: Cán bộ tiếp nhận"
                      />
                    </div> */}

                    <div className="space-y-2">
                      <Label htmlFor="thoiGian">Thời gian xử lý (giờ)</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="thoiGian"
                          type="number"
                          min="0"
                          step="0.5"
                          value={buocHienTai.thoiGianXuLy || ''}
                          onChange={e => {
                            const value = parseFloat(e.target.value)

                            if (!isNaN(value) && value >= 0) {
                              capNhatBuoc({ thoiGianXuLy: value })
                            } else if (e.target.value === '') {
                              capNhatBuoc({ thoiGianXuLy: undefined })
                            }
                          }}
                          placeholder="Nhập số giờ"
                          className="flex-1"
                        />
                        <span className="text-sm text-gray-500">
                          {buocHienTai.thoiGianXuLy && buocHienTai.thoiGianXuLy > 0
                            ? `= ${formatThoiGian(buocHienTai.thoiGianXuLy)}`
                            : ''}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">Mỗi ngày làm việc = 8 giờ</div>
                    </div>

                    {/* Hành động cho loại XU_LY */}
                    {buocHienTai.loai === RLoaiBuocXuLy.XU_LY && (
                      <div className="space-y-2">
                        <Label>Hành động xử lý (chọn ít nhất 1)</Label>
                        <div className="space-y-2">
                          {CHanhDongXuLy.sort().map(hanhDong => (
                            <div key={hanhDong} className="flex items-center space-x-2">
                              <Checkbox
                                id={hanhDong}
                                checked={buocHienTai.hanhDongChoPhep?.includes(hanhDong as IHanhDongHoSo)}
                                onCheckedChange={checked =>
                                  capNhatHanhDongXuLy(hanhDong as IHanhDongHoSo, checked as boolean)
                                }
                              />
                              <Label htmlFor={hanhDong} className="text-sm">
                                {LHanhDongHoSo[hanhDong]}
                              </Label>
                            </div>
                          ))}
                        </div>
                        <div className="mt-2 text-xs text-gray-500">
                          Lưu ý: Hệ thống tự động thêm &quot;Yêu cầu bổ sung&quot; và &quot;Từ chối&quot;
                        </div>
                      </div>
                    )}

                    <Separator />

                    {/* Hiển thị hành động đã chọn */}
                    <div className="space-y-2">
                      <Label>Hành động có thể thực hiện</Label>
                      <div className="space-y-1">
                        {buocHienTai.hanhDongChoPhep?.map(hanhDong => {
                          const ketNoi = buocHienTai.ketNoiDen?.find(k => k.hanhDong === hanhDong)
                          const targetBuoc = ketNoi ? cacBuoc.find(b => b.id === ketNoi.targetId) : null

                          return (
                            <button
                              key={hanhDong}
                              type="button"
                              onClick={() => openKetNoiModal(hanhDong)}
                              className="group flex w-full items-center justify-between rounded bg-gray-100 px-2 py-1 text-left text-xs text-gray-900 transition hover:bg-gray-200 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700"
                            >
                              <span>{getHanhDongTen(hanhDong)}</span>
                              {ketNoi && (
                                <span className="ml-2 text-[10px] text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300">
                                  → {targetBuoc?.ten || ketNoi.targetId}
                                  {ketNoi.label && ` (${ketNoi.label})`}
                                </span>
                              )}
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="py-8 text-center text-gray-500">
                    <div className="space-y-2">
                      <p>Chọn một bước trong workflow để cấu hình</p>
                      <p className="text-xs">
                        Hoặc kéo thả <strong>bước Xử lý</strong> từ toolbar để thêm bước mới
                      </p>
                      <p className="text-xs text-orange-600">Lưu ý: Chỉ có thể thêm bước Xử lý vào quy trình</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="bpmn">
          <BPMNWorkflowDemo />
        </TabsContent>

        <TabsContent value="demo">
          <WorkflowDemo />
        </TabsContent>
      </Tabs>

      {/* Nút lưu */}
      <div className="flex justify-end space-x-4 px-5 py-3">
        <Button variant="outline">Hủy</Button>
        <Button onClick={luuQuyTrinh} disabled={!quyTrinh.ten || !quyTrinh.thuTucId || cacBuoc.length < 2}>
          Lưu quy trình mẫu
        </Button>
      </div>

      {/* Modal cấu hình kết nối hành động */}
      <Dialog open={isKetNoiModalOpen} onOpenChange={setIsKetNoiModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cấu hình kết nối</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Hành động</Label>
              <Input value={getHanhDongTen(hanhDongDangConfig)} readOnly />
            </div>
            <div className="space-y-2">
              <Label>Nhãn (label) của đường nối</Label>
              <Input
                placeholder="Nhập nhãn hiển thị trên đường nối"
                value={ketNoiLabel}
                onChange={e => setKetNoiLabel(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Nối đến bước</Label>
              <Select value={ketNoiTargetId} onValueChange={value => setKetNoiTargetId(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn bước đích (trừ Bắt đầu/Kết thúc)" />
                </SelectTrigger>
                <SelectContent>
                  {cacBuoc
                    .filter(b => b.id !== buocHienTai?.id)
                    .map(b => (
                      <SelectItem key={b.id} value={b.id}>
                        {b.ten || b.id}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="">
            <div className="flex w-full flex-row justify-between">
              <div>
                {/* Nút xóa kết nối - chỉ hiển thị nếu đã có kết nối */}
                {buocHienTai?.ketNoiDen?.find(k => k.hanhDong === hanhDongDangConfig) && (
                  <Button variant="destructive" onClick={deleteKetNoi}>
                    Xóa kết nối
                  </Button>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => setIsKetNoiModalOpen(false)}>
                  Hủy
                </Button>
                <Button onClick={saveKetNoi} disabled={!ketNoiTargetId}>
                  Lưu
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal confirm xóa kết nối */}
      <Dialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận bỏ chọn hành động</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>
              Hành động <strong>&quot;{getHanhDongTen(pendingActionDelete?.hanhDong)}&quot;</strong> đã có kết nối đến
              bước <strong>&quot;{pendingActionDelete?.targetName}&quot;</strong>.
            </p>
            <p>Nếu bỏ chọn hành động này, kết nối sẽ bị xóa. Bạn có chắc chắn muốn tiếp tục?</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDeleteAction}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={confirmDeleteAction}>
              Xác nhận xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
