'use client'

import { APPS_OPTIONS } from '@/app/(guest)/sign-in/choose-app-modal'
import { zodVi } from '@/lib/zod'
import { useUserStore } from '@/stores'
import { Toaster } from '@workspace/ui/lib/toast'
import { AdminLayoutContent } from '@workspace/ui/mi/admin-layout-content'
import { LayoutProvider } from '@workspace/ui/mi/contexts/layout-context'
import { signOut, useSession } from 'next-auth/react'
import { useEffect } from 'react'
import * as z from 'zod'

z.config(zodVi())

function AdminLayout({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession()
  const { appId } = useUserStore()
  const { hasPermissions, setPermissions, clearPermissions, setAppId, setOrganizationId, organizationId } =
    useUserStore()

  const fetchUserRoles = async (userId: string) => {
    try {
      const response = await fetch(`/ac-apis/assign-roles/user/roles/${userId}`, {
        method: 'GET',
        headers: {
          accept: 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const roles = await response.json()
      const permissions = new Set()

      for (const role of roles) {
        for (const permission of role.role.permissions) {
          permissions.add(permission)
        }
      }
      setPermissions(Array.from(permissions) as string[])
    } catch (error) {
      console.error('Error fetching user roles:', error)

      return null
    }
  }

  // const fetchUser

  useEffect(() => {
    if (!session?.user) {
      signOut({ redirect: true, callbackUrl: '/sign-in' })
    } else {
      if (session.user.id) {
        if (session.user.orgUnit?.id) {
          setOrganizationId(session.user.orgUnit.id)
        }
        fetchUserRoles(session.user.id)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session])

  if (!session?.user || !organizationId) {
    return null
  }

  return (
    <LayoutProvider>
      <AdminLayoutContent
        user={{
          name: session.user.name,
          email: session.user.email || '',
          image: session.user.image || '',
        }}
        signOut={() => {
          clearPermissions()
          signOut({ callbackUrl: '/sign-in' })
        }}
        hasPermissions={hasPermissions}
        appId={appId}
        appsOptions={APPS_OPTIONS}
        onAppChange={setAppId}
      >
        <Toaster richColors position="top-right" />
        {children}
      </AdminLayoutContent>
    </LayoutProvider>
  )
}

export default AdminLayout
