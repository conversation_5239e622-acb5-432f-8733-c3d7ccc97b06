'use client'

import { RenderText } from '@/components/render-text'
import { Any, Role } from '@/lib/types'
import { useUserStore } from '@/stores'
import { useGlobalStore } from '@/stores/global.store'
import { PERMISSIONS } from '@ac/permissions'
import { ColumnDef } from '@tanstack/react-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast } from '@workspace/ui/components/toast'
import { useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'
import { Users } from 'lucide-react'
import moment from 'moment'
import { useTranslations } from 'next-intl'
import React, { useEffect, useState } from 'react'

import { CaseAnswerForm } from './case-answer-form'

export type ICategory = {
  categoryId: string
  name: string
}

type CaseAnswerIdentity = {
  answerId: string
  answer: string
  question: string
  category: ICategory
}

export function CaseAnswerPage() {
  const t = useTranslations()
  const { hasPermissions } = useUserStore()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<CaseAnswerIdentity | null>(null)
  const { roles, getRoles } = useGlobalStore()

  useEffect(() => {
    getRoles()
  }, [])

  const columns: ColumnDef<CaseAnswerIdentity>[] = [
    selectColumn(t),
    {
      accessorKey: 'question',
      meta: {
        title: t('case-answer.column.question'),
        filter: {
          type: 'text',
          placeholder: 'Tìm kiếm câu hỏi hoặc câu trả lời',
          debounceMs: 500,
        },
      },
      cell: ({ row }) => {
        return <RenderText text={row?.original?.question || ''} />
      },
    },
    {
      accessorKey: 'answer',
      meta: {
        title: t('case-answer.column.answer'),
      },
      cell: ({ row }) => {
        return <RenderText text={row?.original?.answer || ''} />
      },
    },
    {
      accessorKey: 'category',
      meta: {
        title: t('case-answer.column.category'),
        filter: {
          type: 'combobox',
          placeholder: 'Chọn lĩnh vực TGLP',
          apiRequest: async (keyword?: string) => {
            try {
              const filter = { type: 'LVC' }

              const res = await fetch(`/ac-apis/categories/method/all?filter=${JSON.stringify(filter)}`, {
                headers: {
                  'Content-Type': 'application/json',
                },
              })

              if (!res.ok) {
                throw new Error('Failed to fetch categories')
              }

              const response = await res.json()

              if (!Array.isArray(response) || response.length === 0) {
                return []
              }

              const formattedData: ICategory[] = response.map(item => ({
                categoryId: item?.categoryId || '',
                name: item?.name || '',
              }))

              let filteredData = formattedData

              if (keyword && keyword.trim()) {
                filteredData = formattedData.filter(item => item.name.toLowerCase().includes(keyword.toLowerCase()))
              }

              return filteredData.map(type => ({
                label: type.name,
                value: type.categoryId,
              }))
            } catch (error) {
              console.error('Error fetching categories:', error)

              return []
            }
          },
        },
      },
      cell: ({ row }) => {
        return <RenderText text={row?.original?.category?.name || ''} />
      },
    },
  ]

  if (hasPermissions([PERMISSIONS.PM01_CASE_ANSWER_UPDATE, PERMISSIONS.PM01_CASE_ANSWER_DELETE])) {
    columns.push({
      size: TABLE_SIZE.ACTIONS,
      id: 'actions',
      meta: {
        title: t('table.actions.label'),
      },

      cell: ({ row }) => {
        const member = row.original

        return (
          <div className={TABLE_ALIGN.ACTIONS}>
            <EditButtonIcon
              permission={PERMISSIONS.PM01_CASE_ANSWER_DELETE}
              onClick={() => {
                setSelectedItem(member)
                setIsEditDialogOpen(true)
              }}
            />
            <DeleteButtonIcon
              permission={PERMISSIONS.PM01_CASE_ANSWER_DELETE}
              onClick={() => {
                setSelectedItem(member)
                setIsDeleteDialogOpen(true)
              }}
            />
          </div>
        )
      },
    })
  }

  const { table } = useEnhancedTable<CaseAnswerIdentity>({
    columns,
    pageName: 'case-answers',
    keyObject: {},
    queryFn: async state => {
      const params = new URLSearchParams()
      params.append('page', String(state.pagination.pageIndex + 1))
      params.append('pageSize', String(state.pagination.pageSize))

      if (state.sorting.length > 0) {
        const sort = state.sorting[0]

        if (sort) {
          params.append('orderBy', sort.id)
          params.append('orderDir', sort.desc ? 'DESC' : 'ASC')
        }
      }

      if (state.keyword) {
        params.append('keyword', state.keyword)
      }
      const filterObj: Record<string, Any> = {}

      state.filter.forEach(filter => {
        if (Array.isArray(filter.value) && filter.value.length > 0) {
          const firstItem = filter.value[0]

          if (typeof firstItem === 'string') {
            filterObj[filter.id] = filter.value.join(',')
          } else if (typeof firstItem === 'object' && firstItem !== null && 'value' in firstItem) {
            const values = (filter.value as Array<{ label: string; value: string }>).map(item => item.value)
            filterObj[filter.id] = values.join(',')
          }
        } else if (typeof filter.value === 'string' && filter.value) {
          filterObj[filter.id] = filter.value
        } else if (filter.value && typeof filter.value === 'object' && 'from' in filter.value) {
          const dateRange = filter.value as { from?: Date; to?: Date }

          if (dateRange.from) {
            const fromStr = moment(dateRange.from).format('YYYY-MM-DD')
            const toStr = moment(dateRange.to || dateRange.from).format('YYYY-MM-DD')
            filterObj[filter.id] = `${fromStr},${toStr}`
          }
        } else if (
          filter.value &&
          typeof filter.value === 'object' &&
          'value' in filter.value &&
          'label' in filter.value
        ) {
          const comboboxValue = filter.value as { label: string; value: string }
          filterObj[filter.id] = comboboxValue.value
        }
      })

      if (Object.keys(filterObj).length > 0) {
        params.append('filter', JSON.stringify(filterObj))
      }
      const res = await fetch(`/ac-apis/case-answers?${params.toString()}`, {
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!res.ok) {
        throw new Error('Failed to fetch case answers')
      }
      const response = await res.json()
      const items = response?.items || response?.data || response?.content || (Array.isArray(response) ? response : [])
      const totalItems =
        response?.totalItems ||
        response?.total ||
        response?.totalElements ||
        response?.totalCount ||
        (Array.isArray(items) ? items.length : 0)
      const pageSize = response?.pageSize || response?.size || state.pagination.pageSize || 10

      return {
        items: Array.isArray(items) ? items : [],
        totalItems: Number(totalItems) || 0,
        totalPages: Math.ceil((Number(totalItems) || 0) / Number(pageSize)),
      }
    },
    initialState: {},
    enabled: true,
    queryKey: ['case-answers'],
  })

  // Bỏ cache cảm nhận: ép refetch khi component mount để luôn lấy dữ liệu mới
  useEffect(() => {
    table.options.meta?.reload?.()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const cleanSelectedItem = () => {
    setSelectedItem(null)
  }

  const handleDeleteCaseAnswer = async () => {
    if (!selectedItem) return

    try {
      const response = await fetch(`/ac-apis/case-answers/${selectedItem?.answerId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Xóa câu hỏi thất bại')
      }

      setSelectedItem(null)
      setIsDeleteDialogOpen(false)

      setTimeout(() => {
        table.options.meta?.reload()
        toast.success(`Đã xóa "${selectedItem.question}" thành công`)
      }, 100)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
    }
  }

  return (
    <>
      <AdminPageLayout
        breadcrumb={[
          {
            label: t('case-answer.title'),
            href: '/extensions/case-answer',
          },
        ]}
      >
        <AdminPageContent
          title={t('case-answer.title')}
          icon={<Users className="h-5 w-5" />}
          actions={[
            <AddButton
              permission={PERMISSIONS.PM01_CASE_ANSWER_CREATE}
              key="create"
              tooltip={t('case-answer.actions.create')}
              onClick={() => setIsCreateDialogOpen(true)}
            />,
          ]}
        >
          <DataTable table={table} />
        </AdminPageContent>
      </AdminPageLayout>

      {/* Create case answer Dialog */}
      {isCreateDialogOpen && (
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent onInteractOutside={e => e.preventDefault()}>
            <DialogHeader>
              <DialogTitle>{t('case-answer.actions.createTitle')}</DialogTitle>
            </DialogHeader>
            <CaseAnswerForm
              onSuccess={() => {
                setIsCreateDialogOpen(false)
                cleanSelectedItem()
                setTimeout(() => {
                  table.options.meta?.reload()
                  toast.success('Tạo mới thành công câu hỏi và câu trả lời.')
                }, 100)
              }}
              onCancel={() => {
                setIsCreateDialogOpen(false)
                cleanSelectedItem()
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit case answer Dialog */}
      {isEditDialogOpen && selectedItem && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent onInteractOutside={e => e.preventDefault()}>
            <DialogHeader>
              <DialogTitle>{t('case-answer.actions.editTitle')}</DialogTitle>
            </DialogHeader>
            <CaseAnswerForm
              initialData={selectedItem as CaseAnswerIdentity}
              onSuccess={() => {
                setIsEditDialogOpen(false)
                cleanSelectedItem()
                setTimeout(() => {
                  table.options.meta?.reload()
                  toast.success('Cập nhật thành công câu hỏi và câu trả lời.')
                }, 100)
              }}
              onCancel={() => {
                setIsEditDialogOpen(false)
                cleanSelectedItem()
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Delete case answer Dialog */}
      {isDeleteDialogOpen && selectedItem && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa câu hỏi này &quot;{selectedItem?.answer}&quot;? Hành động này không thể hoàn
                tác.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteCaseAnswer}
                className="bg-destructive hover:bg-destructive/90 text-white"
              >
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  )
}
