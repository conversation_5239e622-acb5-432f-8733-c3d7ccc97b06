// Staff types and constants aligned with backend entities
//
// MIGRATION GUIDE:
// This file has been restructured to align with the database-driven approach:
//
// 1. CODE-DEFINED ENUMS: Only enums that are defined in backend entities (StaffType, StaffContractType, etc.)
// 2. CATEGORY TYPES: Constants for fetching database-managed categories (gender, professional level, etc.)
// 3. API UTILITIES: Functions to fetch category options dynamically
// 4. FALLBACK OPTIONS: Deprecated hardcoded options (marked for removal)
//
// USAGE:
// - For code-defined values: Use the enums (StaffType, StaffContractType, etc.)
// - For database values: Use fetch* functions (fetchGenderOptions, fetchProfessionalLevelOptions, etc.)
// - For backward compatibility: Old constants still exist but are marked as deprecated
//
// EXAMPLES:
// - Staff types: Use StaffType.IN_ORGANIZATION instead of hardcoded values
// - Gender options: Use fetchGenderOptions() instead of GENDER_OPTIONS
// - Categories: Use CATEGORY_TYPES.STAFF.GENDER for API calls
import { StaffSpecialization } from '@/lib/types'
import { StaffContractStatus, StaffContractType, StaffType } from '@ac/data-types'

import { CategoryResponseDto } from './category'

// ============================================================================
// BACKEND RESPONSE STRUCTURES
// ============================================================================

// Category response structure from backend

// Organization unit response structure from backend
interface OrganizationUnitResponseDto {
  organizationUnitId: string
  name: string
  code: string
  type: string
  // Stitched current org history (if available)
  positionId?: string
  position?: CategoryResponseDto
  levelId?: string
  level?: CategoryResponseDto
  isHead?: number
  startDate?: string
  endDate?: string
  status?: number
  roles?: string[]
}

type CardStatus = 'ISSUED' | 'REISSUED' | 'REVOKED' | 'LOST' | 'EXPIRED'

// ============================================================================
// CATEGORY TYPE CONSTANTS (Database-managed)
// These match the category types defined in the backend service
// ============================================================================

export const CATEGORY_TYPES = {
  STAFF: {
    GENDER: 'GT', // Nam/Nữ/Khác
    ETHNIC: 'DTK', // Kinh/Tày/Thái/Mường/etc.
    PROVINCE: 'TP', // Tỉnh/Thành phố
    DISTRICT: 'QH', // Quận/Huyện
    WARD: 'PX', // Phường/Xã
    PROFESSIONAL_LEVEL: 'TDNV', // Đại học/Thạc sĩ/Tiến sĩ/etc.
    ACTIVITY_STATUS: 'TRANG_THAI_HOAT_DONG_CUA_CAN_BO', // Hoạt động/Nghỉ việc/Tạm nghỉ
  },
  CONTRACT: {
    LEGAL_AID_FORM: 'LEGAL_AID_FORM', // Loại hình trợ giúp pháp lý
  },
  ORGANIZATION_HISTORY: {
    POSITION: 'POSITION', // Chuyên viên/Trưởng phòng/Phó giám đốc
    LEVEL: 'HANG_CHUC_DANH_NGHE_NGHIEP_VIEN_CHUC', // Cấp bậc trong tổ chức
  },
  HISTORY_ACTION: {
    REWARD_DISCIPLINE: 'REWARD_DISCIPLINE', // Khen thưởng/Kỷ luật
  },
  SPECIALIZATION: {
    CATEGORY: 'SPECIALIZATION', // Chuyên môn: Java/PHP/Marketing/etc.
  },
  PREVIOUS_POSITION: {
    CATEGORY: 'POSITION_TYPE', // Loại vị trí trước đây
  },
  TRAINING: {
    TRAINING_TYPE: 'LHDT', // Loại đào tạo: Nội bộ/Bên ngoài/Online
    TRAINING_FORMAT: 'HTDT', // Hình thức: Tập trung/Từ xa/Hybrid
  },
} as const

// ============================================================================
// STATIC OPTIONS FOR CODE-DEFINED ENUMS
// ============================================================================

export const STAFF_TYPE_IN_ORGANIZATION = { label: 'Biên chế/Viên chức', value: StaffType.IN_ORGANIZATION }
export const STAFF_TYPE_CONTRACTOR = { label: 'Hợp đồng', value: StaffType.CONTRACTOR }
export const STAFF_TYPE_COLLABORATOR = { label: 'Cộng tác viên', value: StaffType.COLLABORATOR }

export const STAFF_TYPE_OPTIONS = [STAFF_TYPE_IN_ORGANIZATION, STAFF_TYPE_CONTRACTOR, STAFF_TYPE_COLLABORATOR]

export const CONTRACT_TYPE_OPTIONS = [
  { label: 'HĐ lao động', value: StaffContractType.LABOR },
  { label: 'HĐ dịch vụ/CTV', value: StaffContractType.SERVICE },
  { label: 'Biệt phái/điều động', value: StaffContractType.SECONDMENT },
  { label: 'Khác', value: StaffContractType.OTHER },
]

export const CONTRACT_STATUS_OPTIONS = [
  { label: 'Đang hiệu lực', value: StaffContractStatus.ACTIVE },
  { label: 'Đã hết hạn', value: StaffContractStatus.EXPIRED },
  { label: 'Đã chấm dứt', value: StaffContractStatus.TERMINATED },
  { label: 'Tạm dình chỉ', value: StaffContractStatus.SUSPENDED },
  { label: 'Chờ xử lý', value: StaffContractStatus.PENDING },
]

export const CARD_STATUS_OPTIONS = [
  { label: 'Đã cấp', value: 'ISSUED' as CardStatus },
  { label: 'Cấp lại', value: 'REISSUED' as CardStatus },
  { label: 'Thu hồi', value: 'REVOKED' as CardStatus },
  { label: 'Mất thẻ', value: 'LOST' as CardStatus },
  { label: 'Hết hạn', value: 'EXPIRED' as CardStatus },
]

// ============================================================================
// FALLBACK OPTIONS (for backward compatibility - should be replaced with API calls)
// ============================================================================

// Staff Business Roles - TODO: Should be managed through categories or separate API
export const STAFF_BUSINESS_ROLE_OPTIONS = [
  { label: 'Trợ giúp viên pháp lý', value: 'TGPL_OFFICER' },
  { label: 'Cộng tác viên TGPL', value: 'TGPL_COLLABORATOR' },
  { label: 'Tập sự TGPL', value: 'TGPL_INTERN' },
  { label: 'Luật sư ký hợp đồng', value: 'LAWYER_CONTRACT' },
  { label: 'Luật sư thuộc tổ chức', value: 'LAWYER_ORG' },
  { label: 'Tư vấn viên pháp luật', value: 'LEGAL_ADVISOR' },
  { label: 'Kế toán/Văn thư', value: 'ACCOUNTING_AND_OFFICE' },
  { label: 'Lái xe/Lao động', value: 'DRIVER_AND_WORKER' },
]

// Position Titles - TODO: Should use CATEGORY_TYPES.ORGANIZATION_HISTORY.POSITION
export const STAFF_TITLE = [
  { label: 'Giám đốc', value: '3cb1a410-3e98-4015-e063-4832800a5a09' },
  { label: 'Phó giám đốc', value: '3cb1a410-3e99-4015-e063-4832800a5a09' },
  { label: 'Trưởng phòng', value: '3cb1a410-3e9a-4015-e063-4832800a5a09' },
  { label: 'Phó trưởng phòng', value: '3cb1a410-3e9b-4015-e063-4832800a5a09' },
  { label: 'Trưởng chi nhánh', value: '3cb1a410-3e9c-4015-e063-4832800a5a09' },
  { label: 'Khác', value: '3cb1a410-3e9d-4015-e063-4832800a5a09' },
]

export const GENDER_OPTIONS = [
  { label: 'Nam', value: '3cb1a410-4051-4015-e063-4832800a5a09' },
  { label: 'Nữ', value: '3cb1a410-4052-4015-e063-4832800a5a09' },
  { label: 'Giới tính', value: '3cb1a410-4050-4015-e063-4832800a5a09' },
]

export const PROFESSIONAL_LEVEL_OPTIONS = [
  { label: 'Tiến sĩ', value: '04419f56-7305-4f11-a956-0ded2a9f2599' },
  { label: 'Thạc sĩ', value: '22d27d3a-6b91-442b-81e6-a7fbdbc4812a' },
  { label: 'Đại học/Cử nhân', value: '401d0bd6-ed9a-4191-a6de-24b151e0ce3d' },
  { label: 'Cao đẳng', value: '50a7a2d4-0335-4b85-b2b1-55f49dcc7f20' },
  { label: 'Trung cấp', value: '49b04597-7e36-48cb-bc2b-5d7d2ecbf055' },
  { label: 'Khác', value: 'a146a189-1066-4db0-9762-095a726c9006' },
]

export const ACTIVITY_STATUS_OPTIONS = [
  {
    label: 'Bị đình chỉ công tác',
    value: '3e5abfa0-0548-ea3c-e063-4832800aad50',
  },
  {
    label: 'Nghỉ chế độ',
    value: '3e5abfa0-0547-ea3c-e063-4832800aad50',
  },
  {
    label: 'Nghỉ việc/thôi việc',
    value: '3e5ab234-fc5b-ea0f-e063-4832800afdb3',
  },
  {
    label: 'Nghỉ hưu',
    value: '3e5ab234-fc5a-ea0f-e063-4832800afdb3',
  },
  {
    label: 'Đi học, đào tạo, bồi dưỡng',
    value: '3e5ab234-fc59-ea0f-e063-4832800afdb3',
  },
  {
    label: 'Đi biệt phái/đi luân chuyển',
    value: '3e5ab234-fc58-ea0f-e063-4832800afdb3',
  },
  {
    label: 'Đang công tác/đang làm việc',
    value: '3e5ab234-fc57-ea0f-e063-4832800afdb3',
  },
]

export const INTERNSHIP_RESULT_OPTIONS = [
  { label: 'Đạt', value: 'PASS' },
  { label: 'Không đạt', value: 'FAIL' },
]

export const INTERNSHIP_EVALUATION_OPTIONS = [
  { label: 'Chưa kiểm tra', value: 'NOT_EVALUATED' },
  { label: 'Đạt', value: 'PASS' },
  { label: 'Không đạt', value: 'FAIL' },
]

export const IS_CURRENT_OPTIONS = [
  { label: 'Có', value: 1 },
  { label: 'Không', value: 0 },
]

export const TGPL_FORM_OPTIONS = [
  {
    value: '3e6b343b-9b02-1704-e063-4832800ae507',
    label: 'Soạn thảo đơn từ, giấy tờ pháp lý',
  },
  {
    value: '3e6b343b-9b01-1704-e063-4832800ae507',
    label: 'Đại diện ngoài tố tụng',
  },
  {
    value: '3e6b343b-9b00-1704-e063-4832800ae507',
    label: 'Tham gia tố tụng',
  },
  {
    value: '3e6b343b-9afe-1704-e063-4832800ae507',
    label: 'Tư vấn pháp luật',
  },
]

export const DECISION_AUTHORITY_OPTIONS = [
  { label: 'Ban Giám đốc', value: 'BOARD_OF_DIRECTORS' },
  { label: 'Giám đốc', value: 'DIRECTOR' },
  { label: 'Phó giám đốc', value: 'DEPUTY_DIRECTOR' },
  { label: 'Trưởng phòng Nhân sự', value: 'HR_MANAGER' },
]

export const APPOINTMENT_REASON_OPTIONS = [
  { label: 'Bổ nhiệm', value: 'APPOINTMENT' },
  { label: 'Miễn nhiệm', value: 'DISMISSAL' },
  { label: 'Bổ nhiệm lại', value: 'REAPPOINTMENT' },
  { label: 'Điều chuyển', value: 'TRANSFER' },
  { label: 'Thăng chức', value: 'PROMOTION' },
  { label: 'Giáng chức', value: 'DEMOTION' },
]

// ============================================================================
// UTILITY FUNCTIONS FOR CATEGORY MANAGEMENT
// ============================================================================

// Helper functions for category data handling
export const getCategoryDisplayName = (category: CategoryResponseDto | string | undefined): string => {
  if (!category) return 'N/A'
  if (typeof category === 'string') return category
  return category.name || 'N/A'
}

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

// Staff interface aligned with StaffResponseDto from backend
export interface Staff {
  // Core fields
  id: string
  fullName: string
  dateOfBirth?: string
  genderId?: string
  gender?: CategoryResponseDto

  ethnicId?: string
  ethnic?: string

  email?: string
  phone?: string
  address?: string
  permanentAddress?: string

  provinceId?: string
  province?: string
  districtId?: string
  district?: string
  wardId?: string
  ward?: string

  maritalStatus?: string

  cccd?: string
  cccdIssuanceDate?: string
  cccdPlaceOfIssuance?: string

  professionalLevelId?: string
  professionalLevel?: CategoryResponseDto
  staffType?: StaffType

  activityStatusId?: string
  activityStatus?: CategoryResponseDto

  organizationUnitId?: string
  organizationUnit?: OrganizationUnitResponseDto

  organizationHistory?: Array<{
    id: string
    staffId: string
    organizationUnitId: string
    positionId?: string
    position?: CategoryResponseDto
    levelId?: string
    level?: CategoryResponseDto
    isHead: number
    startDate: string
    endDate?: string
    status: number
    roles?: string[]
    specializations?: StaffSpecialization[]
  }>

  avatarRef?: string
  yearsOfExperience?: number

  createdBy?: string
  updatedBy?: string
  createdAt: string
  updatedAt: string
  deletedAt?: string

  isHead?: number
  workStartDate?: string
  workEndDate?: string
  startDurationDate?: string
  endDurationDate?: string
  assistanceForms?: string[]
  specializedFields?: string[]
  civilServantRank?: string
  awards?: string[]
  disciplinaryActions?: string[]
  tgplAppointmentInfo?: string
  attachmentUrl?: string
  internshipStart?: string
  internshipEnd?: string
  contractNote?: string
  contractNumber?: string
  contractType?: string
  contractStatus?: string
  contractPosition?: string
  contractSalaryScheme?: string
  contractWorkPattern?: string
  contractEffectiveDate?: string
  contractExpiryDate?: string
  extensionMonths?: number
  extendedExpiryDate?: string
  contractTerminationReason?: string
  cardNumber?: string
  cardIssuedAt?: string
  cardStatus?: string
  cardIsCurrent?: number
  cardFileRefFront?: string
  cardFileRefBack?: string
  cardNote?: string
  appointmentDate?: string
  appointmentTerm?: number
  recruitmentDate?: string
  contractStartDate?: string
  contractEndDate?: string
  legalWorkStartDate?: string
  legalWorkEndDate?: string
  yearsInLegalField?: number
  notes?: string
  type?: string

  tgplStaff?: {
    id: string
    staffId: string
    tgplDepartmentId?: string
    professionalLevel?: string
    lawyerLicenseCode?: string
    lawyerLicenseIssuedAt?: string
    lawyerLicenseExpiresAt?: string
    barAssociation?: string
    contractNo?: string
    contractStart?: string
    contractEnd?: string
    createdAt: string
    updatedAt: string
  }
  staffRoles?: {
    id: string
    staffId: string
    role: string
    roleMetadata?: string
    createdAt: string
    updatedAt: string
  }[]
  lawyerLicenseHistory?: {
    id: string
    staffId: string
    licenseCode: string
    issuedAt: string
    expiresAt?: string
    barAssociation?: string
    status: 'ACTIVE' | 'RENEWED' | 'REVOKED' | 'SUSPENDED' | 'EXPIRED'
    note?: string
    createdAt: string
    updatedAt: string
  }[]
  staffContracts?: {
    id: string
    staffId: string
    organizationUnitId: string
    contractNo: string
    contractType: string
    startDate?: string
    endDate?: string
    status: string
    position?: string
    note?: string
    createdAt: string
    updatedAt: string
  }[]
}

// Alias for backward compatibility
export interface Lawyer extends Staff {}
