// <PERSON><PERSON><PERSON> cơ quan
export const ORGANIZATION_TYPE = {
  BO_TU_PHAP: 1,
  SO_TU_PHAP: 2,
  TRUNG_TAM_TGPL: 3,
  PHONG_BAN: 4,
  CHI_NHANH: 5,
  TO_CHUC_KY_HOP_DONG_TGPL: 6,
  TO_CHUC_DANG_KY_THAM_GIA_TGPL: 7,
} as const

// TODO: remove
// const ORGANIZATION_GROUP_TYPE = {
//   TO_CHUC_HANH_NGHE_LUAT_SU: 1,
//   TT_TU_VAN_PHAP_LUAT: 2,
//   TO_CHUC_TU_VAN_PHAP_LUAT: 3,
// } as const

export const ORGANIZATION_TYPE_OPTIONS = [
  { value: ORGANIZATION_TYPE.TRUNG_TAM_TGPL, label: 'Trung tâm trợ giúp pháp lý' },
  { value: ORGANIZATION_TYPE.SO_TU_PHAP, label: 'Sở Tư pháp' },
  { value: OR<PERSON><PERSON>ZATION_TYPE.BO_TU_PHAP, label: '<PERSON><PERSON> Tư pháp' },
  { value: ORGANIZATION_TYPE.PHONG_BAN, label: 'Phòng ban' },
  { value: ORGANIZATION_TYPE.CHI_NHANH, label: 'Chi nhánh' },
  { value: ORGANIZATION_TYPE.TO_CHUC_KY_HOP_DONG_TGPL, label: 'Tổ chức ký hợp đồng TGPL' },
  { value: ORGANIZATION_TYPE.TO_CHUC_DANG_KY_THAM_GIA_TGPL, label: 'Tổ chức đăng ký tham gia TGPL' },
]

// TODO: remove
// export const ORGANIZATION_GROUP_TYPE_OPTIONS = [
//   { value: ORGANIZATION_GROUP_TYPE.TO_CHUC_HANH_NGHE_LUAT_SU, label: 'Tổ chức hành nghề luật sư' },
//   { value: ORGANIZATION_GROUP_TYPE.TT_TU_VAN_PHAP_LUAT, label: 'Trung tâm tư vấn pháp luật của các tổ chức CT-XH' },
//   { value: ORGANIZATION_GROUP_TYPE.TO_CHUC_TU_VAN_PHAP_LUAT, label: 'Tổ chức tư vấn pháp luật' },
// ]
