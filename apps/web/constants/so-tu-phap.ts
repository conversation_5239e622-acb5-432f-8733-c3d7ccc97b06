// Interface cho category object
interface Category {
  categoryId: string
  parentId: string | null
  name: string
  code: string | null
  type: string
  description: string | null
  keyword: string
  status: string
  updatedById: string | null
  legacyId: number
  legacyParentId: number | null
  createdAt: string
  updatedAt: string
}

interface User {
  id: string
  name: string
  email: string
  password?: string
  status: number
  emailVerified: string
  image: string
  phone: string
  createdAt: string
  updatedAt: string
}

export interface Organization {
  id: string
  categoryId?: string
  oldId?: string
  code: string
  name: string
  fullName?: string
  representative?: string
  address?: string
  phone?: string
  fax?: string
  email?: string
  website?: string

  // Địa phương
  oldProvinceId?: string
  cityId?: number
  oldDistrictId?: string
  communeId?: number
  oldWardId?: string

  // Cấu trúc cây
  parentId?: string
  parent?: Organization
  children?: Organization[]

  type?: number
  status: number

  // Thông tin cơ bản mở rộng
  establishmentDate?: string
  establishmentDecision?: string
  taxCode?: string
  currentStaffNumber?: number
  organizationGroupId?: string
  managementUnitId?: string

  // Thông tin người đại diện mở rộng
  representativePosition?: string
  representativePhone?: string
  representativeEmail?: string
  representativeFax?: string

  // Thông tin đăng ký TGPL
  decisionDate?: string
  decisionNumber?: string
  registrationProfile?: string
  issuingAuthority?: string
  issuingReason?: string
  terminationReason?: string
  participationReason?: string
  registrationNumber?: string
  registrationTerminationReason?: string
  // Lĩnh vực pháp lý
  legalFieldId?: string
  legalFormId?: string

  // Phạm vi hoạt động
  rangeActivitiesId?: string

  // Tổ chức
  payrollNumber?: number
  roomNumber?: number
  clubNumber?: number

  // Quản trị
  adminGroupId?: string
  adminStatusId?: number
  experience?: string
  deadlineContractDate?: string
  note?: string

  // Thông tin hợp đồng TGPL (đặc thù cho tổ chức hợp đồng)
  extensionMonths?: number
  extendedDeadlineDate?: string
  contractScanFiles?: string[]
  documentCode?: string
  issueDate?: string
  issuingAgency?: string
  expiryDate?: string
  renewalMonths?: number
  renewedExpiryDate?: string
  contractNumber?: string
  contractTerminationReason?: string
  // File đính kèm
  attachedFiles?: string[]

  // Thời gian hệ thống
  createdAt: string
  updatedAt: string
  createById?: string
  updateById?: string

  // Category objects
  organizationGroup?: Category
  oldProvince?: Category
  oldDistrict?: Category
  oldWard?: Category
  legalField?: Category
  rangeActivities?: Category
  adminGroup?: Category

  // User objects
  createdBy?: User
  updatedBy?: User

  // Legacy fields for backward compatibility
  province?: string
  legalForm?: string[]

  contractFileRef?: string
  contractFileId?: string
}

// Trạng thái chung
const STATUS_SO_TU_PHAP = {
  INACTIVE: 0,
  ACTIVE: 1,
  STOP: -1,
} as const

export const STATUS_SO_TU_PHAP_OPTIONS = [
  { value: STATUS_SO_TU_PHAP.ACTIVE, label: 'Hoạt động' },
  { value: STATUS_SO_TU_PHAP.INACTIVE, label: 'Tạm dừng hoạt động' },
  { value: STATUS_SO_TU_PHAP.STOP, label: 'Dừng hoạt động' },
]

// Nhóm quản trị
const ADMIN_GROUP_SO_TU_PHAP = {
  GROUP_1: 1,
  GROUP_2: 2,
  GROUP_3: 3,
} as const

export const ADMIN_GROUP_SO_TU_PHAP_OPTIONS = [
  { value: ADMIN_GROUP_SO_TU_PHAP.GROUP_1, label: 'Nhóm 1' },
  { value: ADMIN_GROUP_SO_TU_PHAP.GROUP_2, label: 'Nhóm 2' },
  { value: ADMIN_GROUP_SO_TU_PHAP.GROUP_3, label: 'Nhóm 3' },
]
