import { Any, OrganizationUnit, Role } from '@/lib/types'
import { toastError } from '@workspace/ui/components/toast'
import { create } from 'zustand'

type RoleOption = {
  value: string
  label: string
  data: Role
}

type OrganizationUnitOption = {
  value: string
  label: string
  data: OrganizationUnit
}

type OrganizationUnitTypeOption = {
  value: string
  label: string
}

interface GlobalStoreState {
  isLoading: boolean
  isLoadingRoles: boolean
  isLoadingOrgs: boolean
  isLoadingOrgTypes: boolean
  roles: RoleOption[]
  orgs: OrganizationUnitOption[]
  orgTypes: OrganizationUnitTypeOption[]
  orgTypesRepo: { [key: string]: OrganizationUnitTypeOption }
  permissionsConfig: Any
  isLoadingPermissionsConfig: boolean
}

interface GlobalStoreActions {
  getRoles: () => Promise<RoleOption[]>
  getOrgs: () => Promise<OrganizationUnitOption[]>
  getOrgTypes: () => Promise<OrganizationUnitTypeOption[]>
  getPermissionsConfig: () => Promise<Any>
}

type GlobalStore = GlobalStoreState & GlobalStoreActions

const initialState: GlobalStoreState = {
  isLoading: false,
  isLoadingRoles: false,
  isLoadingOrgs: false,
  isLoadingOrgTypes: false,
  roles: [],
  orgs: [],
  orgTypes: [],
  orgTypesRepo: {},
  permissionsConfig: null,
  isLoadingPermissionsConfig: false,
}

export const useGlobalStore = create<GlobalStore>(set => ({
  ...initialState,
  getPermissionsConfig: async () => {
    set({ isLoadingPermissionsConfig: true })
    const response = await fetch('/ac-apis/roles/method/permission-config')
    set({ isLoadingPermissionsConfig: false })

    if (!response.ok) {
      toastError(response, 'Không thể tải cấu hình quyền')
    } else {
      const res = await response.json()
      set({ permissionsConfig: res.data })

      return res.data
    }

    return null
  },

  getOrgTypes: async () => {
    set({ isLoadingOrgTypes: true })
    const response = await fetch('/ac-apis/organization-units/method/types')
    set({ isLoadingOrgTypes: false })

    let items: OrganizationUnitTypeOption[] = []

    if (!response.ok) {
      toastError(response, 'Không thể tải danh sách loại cơ quan')
    } else {
      const data = await response.json()
      const orgTypesRepo: { [key: string]: OrganizationUnitTypeOption } = {}

      items = data.items.map((item: OrganizationUnit) => {
        if (item.id !== null && item.id !== undefined) {
          orgTypesRepo[item.id] = {
            value: `${item.id}`,
            label: item.name,
          }
        }

        return {
          value: `${item.id}`,
          label: item.name,
          data: item,
        }
      })

      set({ orgTypes: items, orgTypesRepo })
    }

    return []
  },
  getOrgs: async () => {
    set({ isLoadingRoles: true })
    const response = await fetch(`/ac-apis/organization-units/method/all`)
    set({ isLoadingRoles: false })

    let items: {
      value: string
      label: string
      data: OrganizationUnit
    }[] = []

    if (!response.ok) {
      toastError(response, 'Không thể tải danh sách cơ quan')
    } else {
      const data = await response.json()
      items = data.items.map((item: OrganizationUnit) => ({
        value: item.id,
        label: item.name,
        data: item,
      }))

      set({ orgs: items })

      return items
    }

    return []
  },
  getRoles: async () => {
    const params = new URLSearchParams()
    params.append('orderBy', 'createdAt')
    params.append('orderDir', 'DESC')
    params.append('fields', 'id,name,code')
    set({ isLoadingRoles: true })
    const response = await fetch(`/ac-apis/roles/method/all?${params.toString()}`)
    set({ isLoadingRoles: false })

    let items: {
      value: string
      label: string
      data: Role
    }[] = []

    if (!response.ok) {
      toastError(response, 'Không thể tải danh sách vai trò')
    } else {
      const data = await response.json()
      items = (data.items || data).map((role: Role) => ({
        value: role.id,
        label: role.name,
        data: role,
      }))

      set({ roles: items })

      return items
    }

    return []
  },
}))
