{"name": "ac-api", "version": "0.0.1", "private": true, "description": "", "license": "UNLICENSED", "author": "ThangKT <<EMAIL>>", "scripts": {"build": "nest build", "build:ac-worker": "pnpm nest build ac-worker && pnpm build:workflow", "build:ac-workflow": "pnpm nest build ac-workflow", "build:workflow": "ts-node libs/bpmn/scripts/build-workflow-bundle.ts", "build:workflow:dev": "nodemon libs/bpmn/scripts/build-workflow-bundle.ts --dev", "check-unused-code": "ts-prune --project tsconfig.json --error --ignore 'build|dist|jest.config.ts|packages/common|packages/i18n|packages/utils|packages/models|packages/be|apps/ac-workflow|apps/ac-worker|packages/bpmn|UploadPolicy|UpdateWorkTargetItem'", "dev": "pnpm start:dev", "docker": "./scripts/start-database.sh && ./scripts/start-redis.sh", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "split:api": "ts-node scripts/split-api.ts ./swaggers/biz", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch", "start:prod": "node dist/apps/api/main", "start:temporal": "temporal server start-dev --ui-port 8080 --db-filename clusterdata.db", "start:worker": "nest start ac-worker --watch", "start:workflow": "nest start ac-workflow --watch", "swagger": "tsoa spec -c ./tsoa.json && ts-node ../../tools/sort-swagger ./src/swagger.json && prettier --write --ignore-unknown --log-level=silent ./src/swagger.json", "swagger:workflow": "cd apps/ac-workflow && tsoa spec -c ./tsoa.json && ts-node ../../tools/sort-swagger ./src/swagger.json && prettier --write --ignore-unknown --log-level=silent ./src/swagger.json", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch"}, "jest": {"collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^@ac/bpmn(|/.*)$": "<rootDir>/packages/bpmn/src/$1", "^@ac/common(|/.*)$": "<rootDir>/packages/common/src/$1", "^@ac/i18n(|/.*)$": "<rootDir>/packages/i18n/src/$1", "^@ac/models(|/.*)$": "<rootDir>/packages/models/src/$1", "^@ac/utils(|/.*)$": "<rootDir>/packages/utils/src/$1"}, "rootDir": "apps/api/src", "roots": ["<rootDir>/apps/", "<rootDir>/packages/"], "testEnvironment": "node", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}}, "dependencies": {"@aws-sdk/client-s3": "^3.451.0", "@aws-sdk/s3-request-presigner": "^3.451.0", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@panva/hkdf": "^1.2.1", "@temporalio/activity": "^1.13.0", "@temporalio/client": "^1.13.0", "@temporalio/common": "^1.13.0", "@temporalio/worker": "^1.13.0", "@temporalio/workflow": "^1.13.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.2.0", "ioredis": "^5.6.1", "jose": "^4.15.9", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "nanoid": "3.3.7", "nestjs-i18n": "^10.5.1", "nestjs-temporal": "^2.0.1", "oracledb": "^6.9.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "skywalking-backend-js": "^0.8.0", "slugify": "^1.6.6", "tsoa": "^6.6.0", "typeorm": "^0.3.25", "typeorm-transactional": "0.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.20", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "^3.5.3", "prettier-plugin-sort-json": "^4.1.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "ts-prune": "^0.10.3", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typeorm-extension": "^3.7.1", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "engines": {"node": ">=20.0.0"}}