AC_LOG_LOG_REQUEST='1'
AC_LOG_LOG_RESPONSE='1'
AC_ENV='dev'
AC_WORKFLOW_APIS_ENDPOINT='http://localhost:3000/ac-workflow-apis'
AC_GENERATE_TYPES_DIR='auto'
AUTH_SECRET='7ePI+LbTtGlHyMMfuBwozQ8K71VJnlzxpcnhF+avjVA='

# STORAGE_S3
STORAGE_CORE_S3_SERVERS='[{"NAME":"default","CONFIG":{"PUBLIC_ENDPOINT":"https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online","ENDPOINT":"https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online","KEY":"admin","SECRET":"tJ3kTXmsPv8h","REGION":"us-east-1"}}]'

# TEMPORAL
TEMPORAL_ADDRESS='127.0.0.1'
TEMPORAL_NAMESPACE='default'

# DB ENV
DB_LOGGING='0' # Values [0,1]

DB_TYPE='postgres'
DB_URL='postgresql://postgres:postgres@localhost:5432/ac-monorepo'
