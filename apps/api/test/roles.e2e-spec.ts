import request from 'supertest'

const api = request('http://localhost:3001/ac-apis')
let token = ''

describe('AppController (e2e)', () => {
  beforeAll(async () => {
    const response = await api
      .post('/authentications/method/login-mobile')
      .set('Authorization', 'Basic Zm9vOmJhcg==')
      .send({
        email: '<EMAIL>',
        password: 'Abc@1234',
      })

    token = response.body.token
  })

  it('/ (GET)', () => {
    return api
      .get('/roles')
      .set('Authorization', `Bearer ${token}`)
      .expect(200)
      .expect(res => {
        expect(res.body.items).toBeDefined()
        expect(res.body.items.length).toBeGreaterThan(0)
      })
  })
})
