import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, GetListQueryHelperOptions } from '@ac/common'
import { EForm, EFormRepository, ModelStatus, OrganizationUnitRepository } from '@ac/models'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { CreateEFormDto } from './dto/create-e-form.dto'
import { UpdateEFormDto } from './dto/update-e-form.dto'

const findManyOptions: GetListQueryHelperOptions<EForm> = {
  filterKeywordType: 'contains',
  filterKeywordColumns: ['keyword'],
  filterColumns: [
    {
      columnName: 'status',
      type: 'int',
    },
    {
      columnName: 'createdById',
      type: 'string',
    },
    {
      columnName: 'updatedById',
      type: 'string',
    },
    {
      columnName: 'orgUnitId',
      type: 'string',
    },
    {
      columnName: 'createdAt',
      type: 'date-time',
    },
    {
      columnName: 'updatedAt',
      type: 'date-time',
    },
  ],
}

@Injectable({ scope: Scope.REQUEST })
export class EFormsService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly eFormRepository: EFormRepository
  ) {}

  async create(createEFormDto: CreateEFormDto) {
    const userId = this.request.metadata.getUser().getId()

    let organizationUnit = undefined
    if (createEFormDto.orgUnitId) {
      organizationUnit = await this.organizationUnitRepository.findOne({ id: createEFormDto.orgUnitId })
      if (!organizationUnit) {
        throw new BadRequestException('Không tìm thấy đơn vị tổ chức')
      }
    }

    const keyword = generateKeyword(createEFormDto.name || '', createEFormDto.code)

    const createdEForm = await this.eFormRepository.create({
      name: createEFormDto.name,
      code: createEFormDto.code,
      jsonData: createEFormDto.jsonData,
      orgUnit: organizationUnit,
      createdBy: { id: userId },
      keyword,
    })
    return createdEForm
  }

  async findAll(query: GetListQueryBaseAllDto, includeOrgUnit: boolean = true) {
    const items = await this.eFormRepository.findAll(query, findManyOptions)

    if (includeOrgUnit) {
      return this.eFormRepository.loadOrgUnitForItems(items)
    }

    return items
  }

  async findAllAndCount(query: GetListQueryBaseDto, includeOrgUnit: boolean = true) {
    const result = await this.eFormRepository.findAllAndCount(query, findManyOptions)

    if (includeOrgUnit) {
      const itemsWithOrgUnit = await this.eFormRepository.loadOrgUnitForItems(result.items)
      return {
        ...result,
        items: itemsWithOrgUnit,
      }
    }

    return result
  }

  async findOne(id: string) {
    const eForm = await this.eFormRepository.findOne({ id })
    if (!eForm) {
      throw new BadRequestException('Không tìm thấy biểu mẫu điện tử')
    }
    return eForm
  }

  async update(id: string, updateEFormDto: UpdateEFormDto) {
    const userId = this.request.metadata.getUser().getId()
    const eFormExisted = await this.eFormRepository.findOne({ id })
    if (!eFormExisted) {
      throw new BadRequestException('Không tìm thấy biểu mẫu điện tử')
    }

    let organizationUnit = undefined
    if (updateEFormDto.orgUnitId) {
      organizationUnit = await this.organizationUnitRepository.findOne({ id: updateEFormDto.orgUnitId })
      if (!organizationUnit) {
        throw new BadRequestException('Không tìm thấy đơn vị tổ chức')
      }
    }

    const keyword = generateKeyword(
      updateEFormDto.name || eFormExisted.name || '',
      updateEFormDto.code || eFormExisted.code
    )

    const updateData: QueryDeepPartialEntity<EForm> = {
      ...updateEFormDto,
      ...(organizationUnit && { orgUnitId: organizationUnit.id }),
      updatedBy: { id: userId },
      keyword,
    }

    const updated = await this.eFormRepository.update(id, updateData)
    return updated
  }

  async remove(id: string) {
    const userId = this.request.metadata.getUser().getId()
    const eFormExisted = await this.eFormRepository.findOne({ id })

    if (!eFormExisted) {
      throw new BadRequestException('Không tìm thấy biểu mẫu điện tử')
    }

    const newStatus = eFormExisted.status !== ModelStatus.DRAFT ? ModelStatus.SOFT_DELETE : ModelStatus.DELETED

    const updated = await this.eFormRepository.update(id, {
      status: newStatus,
      updatedBy: { id: userId },
    })

    return updated
  }
}
