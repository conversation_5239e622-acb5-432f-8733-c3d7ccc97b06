import { ApiProperty, ApiPropertyOptional, IsEnum, Is<PERSON>ptional, IsString, <PERSON><PERSON>ength, MinLength } from '@ac/common'
import { ModelStatus } from '@ac/models'

import defaultEForm from './default-e-form.json'

export class CreateEFormDto {
  @ApiProperty({
    example: 'Gi<PERSON>i quyết khiếu nại',
  })
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name: string

  @ApiProperty({
    example: 'giai-quyet-khieu-nai',
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  code: string

  @ApiPropertyOptional({
    example: ModelStatus.ACTIVE,
    enum: ModelStatus,
  })
  @IsEnum(ModelStatus)
  @IsOptional()
  status?: ModelStatus = ModelStatus.ACTIVE

  @ApiPropertyOptional({
    example: defaultEForm,
  })
  @IsString()
  @MinLength(1)
  @IsOptional()
  jsonData?: string

  @ApiPropertyOptional({
    example: '92078a57-9c78-47fb-b32d-73818f205f66',
  })
  @IsString()
  @IsOptional()
  orgUnitId?: string
}
