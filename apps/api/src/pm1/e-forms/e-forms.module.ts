import { NextAuthMiddleware } from '@ac/be'
import { DatabaseModule } from '@ac/models'
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common'

import { EFormsController } from './e-forms.controller'
import { EFormsService } from './e-forms.service'

@Module({
  imports: [DatabaseModule],
  controllers: [EFormsController],
  providers: [EFormsService],
  exports: [EFormsService],
})
export class EFormsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(NextAuthMiddleware).forRoutes('e-forms')
  }
}
