import { GetListQueryBaseAllDto, GetListQueryBaseDto, Queries } from '@ac/common'
import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { CreateEFormDto } from './dto/create-e-form.dto'
import { UpdateEFormDto } from './dto/update-e-form.dto'
import { EFormsService } from './e-forms.service'

@ApiTags('Biểu mẫu điện tử')
@Controller('e-forms')
export class EFormsController {
  constructor(private readonly eFormsService: EFormsService) {}

  @Post()
  create(@Body() createEFormDto: CreateEFormDto) {
    return this.eFormsService.create(createEFormDto)
  }

  @Get()
  findAllAndCount(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.eFormsService.findAllAndCount(query)
  }

  @Get('/method/all')
  findAll(@Query() query: GetListQueryBaseAllDto) {
    return this.eFormsService.findAll(query)
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.eFormsService.findOne(id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEFormDto: UpdateEFormDto) {
    return this.eFormsService.update(id, updateEFormDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.eFormsService.remove(id)
  }
}
