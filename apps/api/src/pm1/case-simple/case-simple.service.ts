import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, PERMISSIONS, REQUEST } from '@ac/common'
import { CaseSimple, CaseSimpleRepository, OrganizationUnitRepository, ProcessStatus } from '@ac/models'
import { CategoryRepository, UserRepository } from '@ac/models/data-access'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import { DeepPartial } from 'typeorm'

import { CreateCaseSimpleDto } from './dto/create-case-simple.dto'
import { UpdateCaseSimpleDto } from './dto/update-case-simple.dto'

const currentOrganizationUnitId = '92078a57-9c78-47fb-b32d-73818f205f66'

@Injectable({ scope: Scope.REQUEST })
export class CaseSimpleService {
  constructor(
    private readonly caseSimpleRepository: CaseSimpleRepository,
    private readonly userRepository: UserRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly categoryRepository: CategoryRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}

  async create(createCaseSimpleDto: CreateCaseSimpleDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_SIMPLE_CREATE)
    this.request?.metadata?.getUser().getOrgUnitId()

    const userId = this.request.metadata.getUser().getId()
    const organizationUnit = await this.organizationUnitRepository.findOne({
      id: currentOrganizationUnitId,
    })
    if (!organizationUnit) {
      throw new BadRequestException('Không tìm thấy cơ quan tư pháp')
    }
    const objectLegalField = await this.categoryRepository.findOne({
      categoryId: createCaseSimpleDto.objectLegalFieldId,
    })

    if (!objectLegalField) {
      throw new BadRequestException('Không tìm thấy đối tượng được TGPL')
    }
    const keyword = generateKeyword(createCaseSimpleDto.rpName)
    const finalData: DeepPartial<CaseSimple> = {
      caseSimpleName: createCaseSimpleDto.caseSimpleName,
      entryDate: createCaseSimpleDto.entryDate ? new Date(createCaseSimpleDto.entryDate) : undefined,
      content: createCaseSimpleDto.content,
      contentRequest: createCaseSimpleDto.contentRequest,
      caseSimpleStatus: createCaseSimpleDto.caseSimpleStatus,

      rpName: createCaseSimpleDto.rpName,
      rpBirthDate: createCaseSimpleDto.rpBirthDate ? new Date(createCaseSimpleDto.rpBirthDate) : undefined,
      rpSex: createCaseSimpleDto.rpSex,
      rpCardNumber: createCaseSimpleDto.rpCardNumber,
      rpPublishedDate: createCaseSimpleDto.rpPublishedDate ? new Date(createCaseSimpleDto.rpPublishedDate) : undefined,
      rpPublishedPlace: createCaseSimpleDto.rpPublishedPlace,
      rpEthnic: createCaseSimpleDto.rpEthnic,
      rpAddress: createCaseSimpleDto.rpAddress,
      rpProvince: createCaseSimpleDto.rpProvince,
      rpWard: createCaseSimpleDto.rpWard,
      rpPhone: createCaseSimpleDto.rpPhone,
      objectLegalField: objectLegalField.name,
      objectLegalFieldId: objectLegalField.categoryId,

      orgUnit: organizationUnit,
      createdBy: { id: userId },
      status: ProcessStatus.ACTIVE,
      keyword: keyword,
    }
    const createdLegalAidProvision = await this.caseSimpleRepository.create(finalData)
    return createdLegalAidProvision
  }

  async findAll(query: GetListQueryBaseAllDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_READ_CASE_SIMPLE_ALL)
    const result = await this.caseSimpleRepository.findAll(query)
    return result
  }

  async findAllAndCount(query: GetListQueryBaseDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_READ_CASE_SIMPLE_ALL)
    const result = await this.caseSimpleRepository.findAllAndCount(query)
    return result
  }

  async findOne(id: string) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_SIMPLE_READ)
    const legalAidProvision = await this.caseSimpleRepository.findOne({
      caseSimpleId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!legalAidProvision) {
      throw new BadRequestException('Không tìm thấy hồ sơ việc TGPL hoặc hồ sơ đã bị vô hiệu hóa')
    }
    return legalAidProvision
  }

  async update(id: string, updateLegalAidProvisionDto: UpdateCaseSimpleDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_SIMPLE_UPDATE)
    const userId = this.request.metadata.getUser().getId()
    const legalAidProvisionExisted = await this.caseSimpleRepository.findOne({
      caseSimpleId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!legalAidProvisionExisted) {
      throw new BadRequestException('Không tìm thấy hồ sơ việc TGPL hoặc hồ sơ đã bị vô hiệu hóa')
    }
    const objectLegalField = await this.categoryRepository.findOne({
      categoryId: updateLegalAidProvisionDto.objectLegalFieldId,
    })

    if (!objectLegalField) {
      throw new BadRequestException('Không tìm thấy đối tượng được TGPL')
    }
    const keyword = updateLegalAidProvisionDto.rpName ? generateKeyword(updateLegalAidProvisionDto.rpName) : undefined
    const updateData: DeepPartial<CaseSimple> = {
      caseSimpleName: updateLegalAidProvisionDto.caseSimpleName,
      entryDate: updateLegalAidProvisionDto.entryDate ? new Date(updateLegalAidProvisionDto.entryDate) : undefined,
      content: updateLegalAidProvisionDto.content,
      contentRequest: updateLegalAidProvisionDto.contentRequest,

      rpName: updateLegalAidProvisionDto.rpName,
      rpBirthDate: updateLegalAidProvisionDto.rpBirthDate
        ? new Date(updateLegalAidProvisionDto.rpBirthDate)
        : undefined,
      rpSex: updateLegalAidProvisionDto.rpSex,
      rpCardNumber: updateLegalAidProvisionDto.rpCardNumber,
      rpPublishedDate: updateLegalAidProvisionDto.rpPublishedDate
        ? new Date(updateLegalAidProvisionDto.rpPublishedDate)
        : undefined,
      rpPublishedPlace: updateLegalAidProvisionDto.rpPublishedPlace,
      rpEthnic: updateLegalAidProvisionDto.rpEthnic,
      rpAddress: updateLegalAidProvisionDto.rpAddress,
      rpProvince: updateLegalAidProvisionDto.rpProvince,
      rpWard: updateLegalAidProvisionDto.rpWard,
      rpPhone: updateLegalAidProvisionDto.rpPhone,
      ...(keyword && { keyword }),
      updatedBy: { id: userId },
      ...(updateLegalAidProvisionDto.caseSimpleStatus && {
        caseSimpleStatus: updateLegalAidProvisionDto.caseSimpleStatus,
      }),
      objectLegalField: objectLegalField.name,
      objectLegalFieldId: objectLegalField.categoryId,
    }
    const updated = await this.caseSimpleRepository.update(id, updateData)
    return updated
  }

  async remove(id: string) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_SIMPLE_DELETE)
    const userId = this.request.metadata.getUser().getId()
    const legalAidProvisionExisted = await this.caseSimpleRepository.findOne({
      caseSimpleId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!legalAidProvisionExisted) {
      throw new BadRequestException('Không tìm thấy hồ sơ việc TGPL hoặc hồ sơ đã bị vô hiệu hóa')
    }
    const removeData: DeepPartial<CaseSimple> = {
      status: ProcessStatus.INACTIVE,
      updatedBy: { id: userId },
    }
    const updated = await this.caseSimpleRepository.update(id, removeData)
    return updated
  }
}
