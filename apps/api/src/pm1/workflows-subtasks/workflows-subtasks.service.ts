import { AcRequest } from '@ac/be'
import { Any, GetListQueryBaseAllDto, GetListQueryBaseDto, REQUEST } from '@ac/common'
import {
  CaseAdvancedRepository,
  CreateWorkflowsSubtasksData,
  OrganizationUnitRepository,
  ProcessRepository,
  UpdateWorkflowsSubtasksData,
  UserRepository,
  UserStatus,
  WorkflowRepository,
  WorkflowsSubtasksRepository,
  WorkflowSubTaskStatus,
} from '@ac/models'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import moment from 'moment'

// import slugify from 'slugify'

import { CreateWorkflowsSubtasksCompleteDto, CreateWorkflowsSubtasksDto, UpdateWorkflowsSubtasksDto } from './dto'

// Helper function to generate keyword from multiple values
// function getSearchKeyword(keywords: string[]): string {
//   const searchKeywords = []
//   for (const keyword of keywords) {
//     if (keyword) {
//       searchKeywords.push(slugify(keyword, { lower: true, replacement: ' ', locale: 'vi', trim: true }))
//     }
//   }

//   return searchKeywords.join('|')
// }

// TODO: duplicate function
// Helper function to process filter with date range and other special formats
function processFilter(filter: Any): Any {
  if (!filter || typeof filter !== 'object') {
    return filter
  }

  const processedFilter: Any = {}

  Object.keys(filter as Record<string, unknown>).forEach(key => {
    const filterValue = filter[key]

    // Handle date range format "2025-08-11,2025-08-12" for date fields
    if (isDateField(key) && typeof filterValue === 'string' && filterValue.includes(',')) {
      const [startDate, endDate] = filterValue.split(',')
      processedFilter[`${key}Start`] = moment(startDate.trim()).startOf('day').toISOString()
      processedFilter[`${key}End`] = moment(endDate.trim()).endOf('day').toISOString()
    }
    // Handle array values for multiple selections (e.g., "INIT,COMPLETED" -> ["INIT","COMPLETED"])
    else if (typeof filterValue === 'string' && filterValue.includes(',') && isMultiSelectField(key)) {
      processedFilter[key] = filterValue.split(',').map(v => {
        const trimmed = v.trim()
        // Try to convert to number if it's numeric
        const num = Number(trimmed)
        return !isNaN(num) && isFinite(num) ? num : trimmed
      })
    } else {
      processedFilter[key] = filterValue
    }
  })

  return processedFilter
}

// Helper function to check if a field is a date field
function isDateField(fieldName: string): boolean {
  const dateFields = ['verificationDate', 'responseDate', 'responseDocumentDate', 'createdAt', 'updatedAt']
  return dateFields.includes(fieldName)
}

// Helper function to check if a field supports multiple selections
function isMultiSelectField(fieldName: string): boolean {
  const multiSelectFields = ['status', 'verificationResult', 'organizationUnitId']
  return multiSelectFields.includes(fieldName)
}

@Injectable({ scope: Scope.REQUEST })
export class WorkflowsSubtasksService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly workflowsSubtasksRepository: WorkflowsSubtasksRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly processRepository: ProcessRepository,
    private readonly caseAdvancedRepository: CaseAdvancedRepository,
    private readonly userRepository: UserRepository,
    private readonly workflowRepository: WorkflowRepository
  ) {}

  async create(createWorkflowsSubtasksDto: CreateWorkflowsSubtasksDto) {
    // Check if metadata exists before accessing
    const userId = this.request.metadata?.getUser()?.getId()

    // Validate all foreign key references exist
    await this.validateAllReferences(createWorkflowsSubtasksDto)

    const newData = {
      ...createWorkflowsSubtasksDto,
      status: WorkflowSubTaskStatus.PENDING,
    }

    const data: CreateWorkflowsSubtasksData = this.convertDtoToEntity(newData)

    // Set created by (only if userId exists)
    if (userId) {
      data.assignedById = userId
    }

    const entity = await this.workflowsSubtasksRepository.create(data)

    return entity
  }

  async createComplete(createSubtaskCompleteDto: CreateWorkflowsSubtasksCompleteDto) {
    const userId = this.request.metadata?.getUser()?.getId()

    await this.validateAllReferences(createSubtaskCompleteDto)

    const newData = {
      ...createSubtaskCompleteDto,
      status: WorkflowSubTaskStatus.PENDING,
    }

    const data: CreateWorkflowsSubtasksData = this.convertDtoToEntity(newData)

    if (userId) {
      data.assignedById = userId
      data.assignedToId = userId
    }

    const entity = await this.workflowsSubtasksRepository.create(data)

    return entity
  }

  async findAllAndCount(query: GetListQueryBaseDto, includeRelations: boolean = true) {
    // Process filter to handle date ranges and other special formats
    const processedQuery = {
      ...query,
      filter: processFilter(query.filter),
    }

    const result = await this.workflowsSubtasksRepository.findAllAndCount(processedQuery)

    if (includeRelations) {
      const itemsWithRelations = await this.workflowsSubtasksRepository.loadRelationsForItems(result.items)
      return {
        ...result,
        items: itemsWithRelations,
      }
    }

    return result
  }

  async findAll(query: GetListQueryBaseAllDto, includeRelations: boolean = true) {
    // Process filter to handle date ranges and other special formats
    const processedQuery = {
      ...query,
      filter: processFilter(query.filter),
    }

    const items = await this.workflowsSubtasksRepository.findAll(processedQuery)

    if (includeRelations) {
      const result = await this.workflowsSubtasksRepository.loadRelationsForItems(items)
      return result
    }

    return items
  }

  async findOne(id: string, includeRelations: boolean = true) {
    const workflowsSubtasks = await this.workflowsSubtasksRepository.findById(id)
    if (!workflowsSubtasks) {
      throw new BadRequestException('Không tìm thấy yêu cầu xác minh')
    }

    if (includeRelations) {
      const itemsWithRelations = await this.workflowsSubtasksRepository.loadRelationsForItems([workflowsSubtasks])
      return itemsWithRelations[0] || workflowsSubtasks
    }

    return workflowsSubtasks
  }

  async update(id: string, updateWorkflowsSubtasksDto: UpdateWorkflowsSubtasksDto) {
    // const userId = this.request.metadata.getUser().getId()
    const existingEntity = await this.workflowsSubtasksRepository.findById(id)
    if (!existingEntity) {
      throw new BadRequestException('Không tìm thấy yêu cầu xác minh')
    }

    // Validate all foreign key references exist
    await this.validateAllReferences(updateWorkflowsSubtasksDto)

    // let dataToUpdate: UpdateWorkflowsSubtasksDto | Record<string, unknown> = updateWorkflowsSubtasksDto

    // If verificationNumber or content is being updated, regenerate keyword
    // if (
    //   updateWorkflowsSubtasksDto.verificationNumber !== undefined ||
    //   updateWorkflowsSubtasksDto.content !== undefined
    // ) {
    // const keyword = getSearchKeyword([
    //   updateWorkflowsSubtasksDto.verificationNumber !== undefined
    //     ? updateWorkflowsSubtasksDto.verificationNumber || ''
    //     : existingEntity.verificationNumber || '',
    //   updateWorkflowsSubtasksDto.content !== undefined
    //     ? updateWorkflowsSubtasksDto.content || ''
    //     : existingEntity.content || '',
    // ])
    // dataToUpdate = { ...updateWorkflowsSubtasksDto, keyword }
    // }

    // Convert date strings to Date objects
    const data: UpdateWorkflowsSubtasksData = this.convertDtoToEntity(updateWorkflowsSubtasksDto)

    // Set updated by
    // data.updatedById = userId

    const result = await this.workflowsSubtasksRepository.update(id, data)
    return { affected: result.affected ?? undefined }
  }

  async remove(id: string) {
    const existingEntity = await this.workflowsSubtasksRepository.findById(id)
    if (!existingEntity) {
      throw new BadRequestException('Không tìm thấy yêu cầu xác minh')
    }

    const result = await this.workflowsSubtasksRepository.remove(id)
    return { affected: result.affected ?? undefined }
  }

  private convertDtoToEntity(dto: Any): Any {
    const entity = { ...dto }

    // Convert date strings to Date objects
    const dateFields = ['verificationDate', 'responseDate', 'responseDocumentDate']
    dateFields.forEach(field => {
      if (entity[field] && typeof entity[field] === 'string') {
        entity[field] = new Date(entity[field])
      }
    })

    return entity
  }

  /**
   * Validate all foreign key references exist and are active
   */
  private async validateAllReferences(
    dto: CreateWorkflowsSubtasksDto | UpdateWorkflowsSubtasksDto | CreateWorkflowsSubtasksCompleteDto
  ): Promise<void> {
    const validationPromises: Promise<void>[] = []

    // Validate workflowId
    if (dto.workflowId) {
      validationPromises.push(this.validateWorkflow(dto.workflowId))
    }

    // Validate processId
    if (dto.processId) {
      validationPromises.push(this.validateProcess(dto.processId))
    }

    // Validate caseAdvancedId
    if (dto.caseAdvancedId) {
      validationPromises.push(this.validateCaseAdvanced(dto.caseAdvancedId))
    }

    // Validate fromOrgUnitId
    if (dto.fromOrgUnitId) {
      validationPromises.push(this.validateOrganizationUnit(dto.fromOrgUnitId, 'đơn vị giao'))
    }

    // Validate toOrgUnitId
    if (dto.toOrgUnitId) {
      validationPromises.push(this.validateOrganizationUnit(dto.toOrgUnitId, 'đơn vị nhận'))
    }

    // Validate assignedById
    // if (dto.assignedById) {
    //   validationPromises.push(this.validateUser(dto.assignedById, 'người tạo'))
    // }

    // Validate assignedToId - only if the property exists in the DTO
    if ('assignedToId' in dto && dto.assignedToId) {
      validationPromises.push(this.validateUser(dto.assignedToId, 'người xác minh'))
    }

    // Execute all validations in parallel
    await Promise.all(validationPromises)
  }

  /**
   * Validate workflow exists
   */
  private async validateWorkflow(workflowId: string): Promise<void> {
    const workflow = await this.workflowRepository.findOne({ id: workflowId })
    if (!workflow) {
      throw new BadRequestException(`Không tìm thấy workflow với ID: ${workflowId}`)
    }
  }

  /**
   * Validate process exists
   */
  private async validateProcess(processId: string): Promise<void> {
    const process = await this.processRepository.findById(processId)
    if (!process) {
      throw new BadRequestException(`Không tìm thấy quy trình với ID: ${processId}`)
    }
  }

  /**
   * Validate case advanced exists
   */
  private async validateCaseAdvanced(caseAdvancedId: string): Promise<void> {
    const caseAdvanced = await this.caseAdvancedRepository.findById(caseAdvancedId)
    if (!caseAdvanced) {
      throw new BadRequestException(`Không tìm thấy vụ việc trợ giúp pháp lý với ID: ${caseAdvancedId}`)
    }
  }

  /**
   * Validate organization unit exists and is active
   */
  private async validateOrganizationUnit(orgUnitId: string, description: string): Promise<void> {
    const orgUnit = await this.organizationUnitRepository.findById(orgUnitId)
    if (!orgUnit) {
      throw new BadRequestException(`Không tìm thấy ${description} với ID: ${orgUnitId}`)
    }

    // Check if the organization unit is active/valid
    if (orgUnit.status === -99) {
      throw new BadRequestException(
        `${description.charAt(0).toUpperCase() + description.slice(1)} đã bị xóa: ${orgUnit.name}`
      )
    }
  }

  /**
   * Validate user exists and is active
   */
  private async validateUser(userId: string, description: string): Promise<void> {
    const user = await this.userRepository.findById(userId)
    if (!user) {
      throw new BadRequestException(`Không tìm thấy ${description} với ID: ${userId}`)
    }

    // Check if the user is active/valid
    if (user.status === UserStatus.DELETED) {
      throw new BadRequestException(
        `${description.charAt(0).toUpperCase() + description.slice(1)} đã bị xóa: ${user.name}`
      )
    }
  }
}
