import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, PERMISSIONS, REQUEST } from '@ac/common'
import {
  CaseAnswerRepository,
  CategoryRepository,
  OrganizationUnitRepository,
  ProcessStatus,
  UserRepository,
} from '@ac/models'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'

import { CreateCaseAnswerDto } from './dto/create-legal-aid-answer.dto'
import { UpdateCaseAnswerDto } from './dto/update-legal-aid-answer.dto'

const currentOrganizationUnitId = '92078a57-9c78-47fb-b32d-73818f205f66'

@Injectable({ scope: Scope.REQUEST })
export class CaseAnswerService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly userRepository: UserRepository,
    private readonly categoryRepository: CategoryRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly caseAnswerRepository: CaseAnswerRepository
  ) {}
  async create(createCaseAnswerDto: CreateCaseAnswerDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_ANSWER_CREATE)
    const userId = this.request.metadata.getUser().getId()
    let category = undefined
    if (createCaseAnswerDto.categoryId) {
      category = await this.categoryRepository.findOne({ categoryId: createCaseAnswerDto.categoryId })
      if (!category) {
        throw new BadRequestException('Không tìm thấy danh mục')
      }
    }
    const organizationUnit = await this.organizationUnitRepository.findOne({ id: currentOrganizationUnitId })
    if (!organizationUnit) {
      throw new BadRequestException('Không tìm thấy cơ quan tư pháp')
    }
    const keyword = generateKeyword(createCaseAnswerDto.answer, createCaseAnswerDto.question, category?.name)
    const createdCaseAnswer = await this.caseAnswerRepository.create({
      answer: createCaseAnswerDto.answer,
      question: createCaseAnswerDto.question,
      keyword: keyword,
      category: category,
      orgUnit: organizationUnit,
      createdBy: { id: userId },
      status: ProcessStatus.ACTIVE,
    })
    return createdCaseAnswer
  }

  async findAll(query: GetListQueryBaseAllDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_READ_CASE_ANSWER_ALL)
    return await this.caseAnswerRepository.findAll(query)
  }

  findAllAndCount(query: GetListQueryBaseDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_READ_CASE_ANSWER_ALL)
    return this.caseAnswerRepository.findAllAndCount(query)
  }

  async findOne(id: string) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_ANSWER_READ)
    const caseAnswer = await this.caseAnswerRepository.findOne({
      answerId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!caseAnswer) {
      throw new BadRequestException('Không tìm thấy câu trả lời')
    }
    return caseAnswer
  }

  async update(id: string, updateCaseAnswerDto: UpdateCaseAnswerDto) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_ANSWER_UPDATE)
    const userId = this.request.metadata.getUser().getId()
    const legalAidProvisionExisted = await this.caseAnswerRepository.findOne({
      answerId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!legalAidProvisionExisted) {
      throw new BadRequestException('Không tìm thấy hồ sơ việc TGPL hoặc hồ sơ đã bị vô hiệu hóa')
    }

    let category = undefined
    if (updateCaseAnswerDto.categoryId) {
      category = await this.categoryRepository.findOne({ categoryId: updateCaseAnswerDto.categoryId })
      if (!category) {
        throw new BadRequestException('Không tìm thấy danh mục')
      }
    }

    let keyword = undefined
    if (updateCaseAnswerDto.answer || updateCaseAnswerDto.question) {
      const currentAnswer = updateCaseAnswerDto.answer || legalAidProvisionExisted.answer
      const currentQuestion = updateCaseAnswerDto.question || legalAidProvisionExisted.question
      const currentCategoryName = category?.name || legalAidProvisionExisted.category?.name

      keyword = generateKeyword(currentAnswer, currentQuestion, currentCategoryName)
    }

    const updateDataClean = { ...updateCaseAnswerDto }
    delete updateDataClean.categoryId

    const updateData = {
      ...updateDataClean,
      ...(keyword && { keyword }), // Chỉ thêm keyword nếu có
      // Chỉ update relation nếu có dữ liệu mới
      ...(category && { categoryId: category.categoryId }),
      updatedBy: { id: userId },
    }
    const updated = await this.caseAnswerRepository.update(id, updateData)
    return updated
  }

  async remove(id: string) {
    this.request?.metadata?.getUser()?.isAllow(PERMISSIONS.PM01_CASE_ANSWER_DELETE)
    const userId = this.request.metadata.getUser().getId()
    const legalAidProvisionExisted = await this.caseAnswerRepository.findOne({
      answerId: id,
      status: ProcessStatus.ACTIVE,
    })
    if (!legalAidProvisionExisted) {
      throw new BadRequestException('Không tìm thấy hồ sơ việc TGPL hoặc hồ sơ đã bị vô hiệu hóa')
    }
    const updated = await this.caseAnswerRepository.update(id, {
      status: ProcessStatus.INACTIVE,
      updatedBy: { id: userId },
    })
    return updated
  }
}
