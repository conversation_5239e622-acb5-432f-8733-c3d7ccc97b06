import { AcRequest } from '@ac/be'
import { Any, GetListQueryBaseAllDto, GetListQueryBaseDto, GetListQueryHelperOptions, Injectable } from '@ac/common'
import { CaseAdvanced, CaseAdvancedRepository } from '@ac/models'
import { BadRequestException, Inject, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import moment from 'moment'
import slugify from 'slugify'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { CaseAdvanceResponseDto, CreateCaseAdvanceDto, UpdateCaseAdvanceDto } from './dto'

// Helper function to generate keyword from multiple values
function getSearchKeyword(keywords: string[]): string {
  const searchKeywords = []
  for (const keyword of keywords) {
    if (keyword) {
      searchKeywords.push(slugify(keyword, { lower: true, replacement: ' ', locale: 'vi', trim: true }))
    }
  }

  return searchKeywords.join('|')
}

// Helper function to process filter with date range and other special formats
function processFilter(filter: Record<string, unknown>): Record<string, unknown> {
  if (!filter || typeof filter !== 'object') {
    return filter
  }

  const processedFilter: Any = {}

  // Map external filter keys to entity column keys
  const mapKey = (key: string): string => {
    switch (key) {
      case 'objectLegalField':
        return 'objectLegalFieldId'
      case 'formsOfAssistance':
        return 'formsOfAssistanceId'
      case 'linhVucTroGiup':
        return 'legalFieldId'
      default:
        return key
    }
  }

  // Handle fromDate/toDate -> createDateStart/createDateEnd
  if (typeof filter['fromDate'] === 'string' && filter['fromDate']) {
    processedFilter['createDateStart'] = moment(String(filter['fromDate'])).startOf('day').toISOString()
  }
  if (typeof filter['toDate'] === 'string' && filter['toDate']) {
    processedFilter['createDateEnd'] = moment(String(filter['toDate'])).endOf('day').toISOString()
  }

  // Handle entryDateFrom/entryDateTo -> entryDateStart/entryDateEnd
  if (typeof filter['entryDateFrom'] === 'string' && filter['entryDateFrom']) {
    processedFilter['entryDateStart'] = moment(String(filter['entryDateFrom'])).startOf('day').toISOString()
  }
  if (typeof filter['entryDateTo'] === 'string' && filter['entryDateTo']) {
    processedFilter['entryDateEnd'] = moment(String(filter['entryDateTo'])).endOf('day').toISOString()
  }

  Object.keys(filter).forEach(originalKey => {
    // Skip helper range keys already processed
    if (
      originalKey === 'fromDate' ||
      originalKey === 'toDate' ||
      originalKey === 'entryDateFrom' ||
      originalKey === 'entryDateTo'
    ) {
      return
    }

    const key = mapKey(originalKey)
    const filterValue = filter[originalKey]

    // Handle date range format "2025-08-11,2025-08-12" for date fields
    if (isDateField(key) && typeof filterValue === 'string' && filterValue.includes(',')) {
      const [startDate, endDate] = filterValue.split(',')
      processedFilter[`${key}Start`] = moment(startDate.trim()).startOf('day').toISOString()
      processedFilter[`${key}End`] = moment(endDate.trim()).endOf('day').toISOString()
    }
    // Handle array values for multiple selections (e.g., "1,2,3" -> [1,2,3])
    else if (typeof filterValue === 'string' && filterValue.includes(',') && isMultiSelectField(key)) {
      processedFilter[key] = filterValue.split(',').map(v => {
        const trimmed = v.trim()
        // Try to convert to number if it's numeric
        const num = Number(trimmed)
        return !isNaN(num) && isFinite(num) ? num : trimmed
      })
    } else {
      processedFilter[key] = filterValue
    }
  })

  return processedFilter
}

// Helper function to check if a field is a date field
function isDateField(fieldName: string): boolean {
  const dateFields = [
    'createdAt',
    'updatedAt',
    'appointmentDate',
    'createDate',
    'datePrint',
    'entryDate',
    'rpDob',
    'rpIssueDate',
    'receivedDate',
  ]
  return dateFields.includes(fieldName)
}

// Helper function to check if a field supports multiple selections
function isMultiSelectField(fieldName: string): boolean {
  const multiSelectFields = [
    'orgUnitId',
    'status',
    'payStatus',
    'verifyStatus',
    'assignmentStatus',
    'formsOfAssistanceId',
    'objectLegalFieldId',
    'legalFieldId',
    'rpSex',
    'rpGender',
    'rpLiveInArea',
    'rpSocialPolicyObject',
    'rpMarriageStatus',
    'rpEducationLevel',
    'rpPoliticalLevel',
    'typeRequest',
    'code', // Nếu muốn filter nhiều mã vụ việc
    'legalType',
    'attachType',
    'rpRequestSource',
  ]
  return multiSelectFields.includes(fieldName)
}

// Filter options configuration for LegalAidCaseAdvanced
const findManyOptions: GetListQueryHelperOptions<CaseAdvanced> = {
  filterKeywordType: 'contains',
  filterKeywordColumns: ['keyword'], // Search by keyword column only
  filterColumns: [
    {
      columnName: 'oId',
      type: 'int',
    },
    {
      columnName: 'orgUnitId',
      type: 'int',
    },
    {
      columnName: 'status',
      type: 'int',
    },
    {
      columnName: 'legalType',
      type: 'int',
    },

    {
      columnName: 'attachType',
      type: 'int',
    },
    {
      columnName: 'assignmentStatus',
      type: 'int',
    },
    {
      columnName: 'payStatus',
      type: 'int',
    },
    {
      columnName: 'verifyStatus',
      type: 'int',
    },
    {
      columnName: 'isActive',
      type: 'int',
    },
    {
      columnName: 'rpName',
      type: 'string',
      compareType: 'like',
    },
    {
      columnName: 'rpSex',
      type: 'int',
    },
    {
      columnName: 'rpCardNumber',
      type: 'string',
    },
    {
      columnName: 'objectLegalFieldId',
      type: 'string',
    },
    {
      columnName: 'formsOfAssistanceId',
      type: 'string',
    },
    {
      columnName: 'legalFieldId',
      type: 'string',
    },
    {
      columnName: 'createdAt',
      type: 'date-time',
    },
    {
      columnName: 'createDate',
      type: 'date-time',
    },
    {
      columnName: 'entryDate',
      type: 'date-time',
    },
    {
      columnName: 'updatedAt',
      type: 'date-time',
    },
    {
      columnName: 'keyword',
      type: 'string',
    },
  ],
}

@Injectable()
export class CaseAdvancedService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly caseAdvancedRepository: CaseAdvancedRepository
  ) {}

  async create(createDto: CreateCaseAdvanceDto) {
    const userId = this.request.metadata?.getUser()?.getId()
    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()
    // Check unique constraints
    await this.validateUniqueFields(createDto.code, createDto.oId)

    const keyword = getSearchKeyword([createDto.name || '', String(createDto.code || '')])

    // Convert date strings to Date objects
    const dataWithKeyword = {
      ...createDto,
      keyword,
      orgUnitId,
    }
    const data = this.convertDtoToEntity(dataWithKeyword)
    if (userId) {
      data.updatedById = userId
    }
    const entity = await this.caseAdvancedRepository.create(data as Partial<CaseAdvanced>)
    return new CaseAdvanceResponseDto(entity)
  }

  async findAllAndCount(query: GetListQueryBaseDto) {
    // Process filter to handle date ranges and multiple selections
    const processedQuery = {
      ...query,
      filter: processFilter((query.filter as Record<string, unknown>) || {}),
    }

    const result = await this.caseAdvancedRepository.findAllAndCount(processedQuery, findManyOptions)

    return {
      ...result,
      items: result.items.map(item => new CaseAdvanceResponseDto(item)),
    }
  }

  async findAll(query: GetListQueryBaseAllDto) {
    // Process filter to handle date ranges and multiple selections
    const processedQuery = {
      ...query,
      filter: processFilter((query.filter as Record<string, unknown>) || {}),
    }

    const items = await this.caseAdvancedRepository.findAll(processedQuery, findManyOptions)
    return items.map(item => new CaseAdvanceResponseDto(item))
  }

  async findOne(id: string) {
    const entity = await this.caseAdvancedRepository.findById(id)
    if (!entity) {
      throw new NotFoundException(`Vụ việc với ID ${id} không tồn tại`)
    }
    return new CaseAdvanceResponseDto(entity)
  }

  async findByOldId(oId: number) {
    const entity = await this.caseAdvancedRepository.findByOldId(oId)
    if (!entity) {
      throw new NotFoundException(`Vụ việc với ID cũ ${oId} không tồn tại`)
    }
    return new CaseAdvanceResponseDto(entity)
  }

  async update(id: string, updateDto: UpdateCaseAdvanceDto): Promise<{ affected?: number }> {
    const existingEntity = await this.caseAdvancedRepository.findById(id)
    if (!existingEntity) {
      throw new NotFoundException(`Vụ việc với ID ${id} không tồn tại`)
    }

    // Check unique constraints (excluding current record)
    await this.validateUniqueFields(updateDto.code, updateDto.oId, id)

    let dataToUpdate: UpdateCaseAdvanceDto | Record<string, unknown> = updateDto

    // If name, code or rpName is being updated, regenerate keyword
    if (updateDto.name !== undefined || updateDto.code !== undefined || updateDto.rpName !== undefined) {
      const keyword = getSearchKeyword([
        updateDto.name !== undefined ? updateDto.name || '' : existingEntity.name || '',
        updateDto.code !== undefined ? String(updateDto.code || '') : String(existingEntity.code || ''),
        updateDto.rpName !== undefined ? updateDto.rpName || '' : existingEntity.rpName || '',
      ])

      dataToUpdate = { ...updateDto, keyword }
    }

    // Convert date strings to Date objects
    const data = this.convertDtoToEntity(dataToUpdate)
    const result = await this.caseAdvancedRepository.update(id, data)

    return result
  }

  async remove(id: string): Promise<{ affected?: number }> {
    const entity = await this.caseAdvancedRepository.findById(id)
    if (!entity) {
      throw new NotFoundException(`Vụ việc với ID ${id} không tồn tại`)
    }

    const result = await this.caseAdvancedRepository.delete(id)
    return { affected: result.affected ?? undefined }
  }

  // Helper method to convert DTO to entity format
  private convertDtoToEntity(
    dto: CreateCaseAdvanceDto | UpdateCaseAdvanceDto | Record<string, unknown>
  ): QueryDeepPartialEntity<CaseAdvanced> {
    const data: Record<string, unknown> = { ...dto }

    // Convert date strings to Date objects using moment
    const dateFields = [
      'appointmentDate',
      'createDate',
      'datePrint',
      'entryDate',
      'guardianPublishedDate',
      'modifyDate',
      'payDate',
      'rateDate',
      'rpBirthDate',
      'rpPublishedDate',
      'verifyDate',
    ]

    dateFields.forEach(field => {
      if (data[field] && typeof data[field] === 'string') {
        // Use moment to parse and convert to Date object
        const momentDate = moment(data[field])
        if (momentDate.isValid()) {
          // Set to start of day for date fields (except time-specific fields)
          const timeSpecificFields = ['appointmentDate', 'appointmentTime']
          if (!timeSpecificFields.includes(field)) {
            data[field] = momentDate.startOf('day').toDate()
          } else {
            data[field] = momentDate.toDate()
          }
        } else {
          // If invalid date, remove the field
          delete data[field]
        }
      }
    })

    return data as QueryDeepPartialEntity<CaseAdvanced>
  }

  // Helper method to validate unique fields
  private async validateUniqueFields(code?: string, oId?: number, excludeId?: string): Promise<void> {
    const errors: string[] = []

    // Check if code is unique
    if (code) {
      const existingByCode = await this.caseAdvancedRepository.findByCode(code)
      if (existingByCode && existingByCode.id !== excludeId) {
        errors.push(`Mã vụ việc "${code}" đã tồn tại`)
      }
    }

    // Check if oId is unique
    if (oId) {
      const existingByOId = await this.caseAdvancedRepository.findByOldId(oId)
      if (existingByOId && existingByOId.id !== excludeId) {
        errors.push(`ID cũ "${oId}" đã tồn tại`)
      }
    }

    if (errors.length > 0) {
      throw new BadRequestException(errors.join('. '))
    }
  }
}
