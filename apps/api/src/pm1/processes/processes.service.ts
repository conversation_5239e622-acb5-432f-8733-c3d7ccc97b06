import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, GetListQueryHelperOptions } from '@ac/common'
import { OrganizationUnitRepository, Process, ProcessRepository, ProcessStatus } from '@ac/models'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { CreateProcessDto } from './dto/create-process.dto'
import { UpdateProcessDto } from './dto/update-process.dto'

const findManyOptions: GetListQueryHelperOptions<Process> = {
  filterKeywordType: 'contains',
  filterKeywordColumns: ['keyword'],
  filterColumns: [
    {
      columnName: 'status',
      type: 'int',
    },
    {
      columnName: 'isDefault',
      type: 'int',
    },
    {
      columnName: 'createdById',
      type: 'string',
    },
    {
      columnName: 'updatedById',
      type: 'string',
    },
    {
      columnName: 'orgUnitId',
      type: 'string',
    },
    {
      columnName: 'createdAt',
      type: 'date-time',
    },
    {
      columnName: 'updatedAt',
      type: 'date-time',
    },
  ],
}

@Injectable({ scope: Scope.REQUEST })
export class ProcessesService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly processRepository: ProcessRepository
  ) {}

  async create(createProcessDto: CreateProcessDto) {
    const userId = this.request.metadata.getUser().getId()

    let organizationUnit = undefined
    if (createProcessDto.orgUnitId) {
      organizationUnit = await this.organizationUnitRepository.findOne({ id: createProcessDto.orgUnitId })
      if (!organizationUnit) {
        throw new BadRequestException('Không tìm thấy đơn vị tổ chức')
      }

      if (createProcessDto.isDefault) {
        await this.processRepository.updateMany(
          {
            orgUnitId: createProcessDto.orgUnitId,
          },
          { isDefault: 0 } satisfies QueryDeepPartialEntity<Process>
        )
      }
    }

    const keyword = generateKeyword(createProcessDto.name || '', createProcessDto.code)

    const createdProcess = await this.processRepository.create({
      name: createProcessDto.name,
      code: createProcessDto.code,
      jsonData: createProcessDto.jsonData,
      orgUnit: organizationUnit,
      createdBy: { id: userId },
      isDefault: createProcessDto.isDefault,
      keyword,
    })
    return createdProcess
  }

  async findAll(query: GetListQueryBaseAllDto, includeOrgUnit: boolean = true) {
    const items = await this.processRepository.findAll(query, findManyOptions)

    if (includeOrgUnit) {
      return this.processRepository.loadOrgUnitForItems(items)
    }

    return items
  }

  async findAllAndCount(query: GetListQueryBaseDto, includeOrgUnit: boolean = true) {
    const result = await this.processRepository.findAllAndCount(query, findManyOptions)

    if (includeOrgUnit) {
      const itemsWithOrgUnit = await this.processRepository.loadOrgUnitForItems(result.items)
      return {
        ...result,
        items: itemsWithOrgUnit,
      }
    }

    return result
  }

  async findOne(id: string) {
    const process = await this.processRepository.findOne({ id })
    if (!process) {
      throw new BadRequestException('Không tìm thấy quy trình')
    }
    return process
  }

  async update(id: string, updateProcessDto: UpdateProcessDto) {
    const userId = this.request.metadata.getUser().getId()
    const processExisted = await this.processRepository.findOne({ id })
    if (!processExisted) {
      throw new BadRequestException('Không tìm thấy quy trình')
    }

    let organizationUnit = undefined
    if (updateProcessDto.orgUnitId) {
      organizationUnit = await this.organizationUnitRepository.findOne({ id: updateProcessDto.orgUnitId })
      if (!organizationUnit) {
        throw new BadRequestException('Không tìm thấy đơn vị tổ chức')
      }
    }

    const keyword = generateKeyword(
      updateProcessDto.name || processExisted.name || '',
      updateProcessDto.code || processExisted.code
    )

    const updateData: QueryDeepPartialEntity<Process> = {
      ...updateProcessDto,
      ...(organizationUnit && { orgUnitId: organizationUnit.id }),
      updatedBy: { id: userId },
      keyword,
    }

    const updated = await this.processRepository.update(id, updateData)
    return updated
  }

  async remove(id: string) {
    const userId = this.request.metadata.getUser().getId()
    const processExisted = await this.processRepository.findOne({ id })
    if (!processExisted) {
      throw new BadRequestException('Không tìm thấy quy trình')
    }

    const updated = await this.processRepository.update(id, {
      status: ProcessStatus.INACTIVE,
      updatedBy: { id: userId },
    })

    return updated
  }
}
