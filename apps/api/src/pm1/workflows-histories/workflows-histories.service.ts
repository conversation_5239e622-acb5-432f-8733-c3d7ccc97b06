import { AcRequest } from '@ac/be'
import { Any, GetListQueryBaseAllDto, GetListQueryBaseDto, REQUEST } from '@ac/common'
import {
  CaseAdvancedRepository,
  OrganizationUnitRepository,
  ProcessRepository,
  UserRepository,
  UserStatus,
  WorkflowHistory,
  WorkflowHistoryRepository,
  WorkflowRepository,
  WorkflowSubTaskStatus,
} from '@ac/models'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import moment from 'moment'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

// import slugify from 'slugify'

import { CreateWorkflowsHistoriesCompleteDto, UpdateWorkflowsHistoriesCompleteDto } from './dto'

// Helper function to generate keyword from multiple values
// function getSearchKeyword(keywords: string[]): string {
//   const searchKeywords = []
//   for (const keyword of keywords) {
//     if (keyword) {
//       searchKeywords.push(slugify(keyword, { lower: true, replacement: ' ', locale: 'vi', trim: true }))
//     }
//   }

//   return searchKeywords.join('|')
// }

// TODO: duplicate function
// Helper function to process filter with date range and other special formats
function processFilter(filter: Any): Any {
  if (!filter || typeof filter !== 'object') {
    return filter
  }

  const processedFilter: Any = {}

  Object.keys(filter as Record<string, unknown>).forEach(key => {
    const filterValue = filter[key]

    // Handle date range format "2025-08-11,2025-08-12" for date fields
    if (isDateField(key) && typeof filterValue === 'string' && filterValue.includes(',')) {
      const [startDate, endDate] = filterValue.split(',')
      processedFilter[`${key}Start`] = moment(startDate.trim()).startOf('day').toISOString()
      processedFilter[`${key}End`] = moment(endDate.trim()).endOf('day').toISOString()
    }
    // Handle array values for multiple selections (e.g., "INIT,COMPLETED" -> ["INIT","COMPLETED"])
    else if (typeof filterValue === 'string' && filterValue.includes(',') && isMultiSelectField(key)) {
      processedFilter[key] = filterValue.split(',').map(v => {
        const trimmed = v.trim()
        // Try to convert to number if it's numeric
        const num = Number(trimmed)
        return !isNaN(num) && isFinite(num) ? num : trimmed
      })
    } else {
      processedFilter[key] = filterValue
    }
  })

  return processedFilter
}

// Helper function to check if a field is a date field
function isDateField(fieldName: string): boolean {
  const dateFields = ['verificationDate', 'responseDate', 'responseDocumentDate', 'createdAt', 'updatedAt']
  return dateFields.includes(fieldName)
}

// Helper function to check if a field supports multiple selections
function isMultiSelectField(fieldName: string): boolean {
  const multiSelectFields = ['status', 'verificationResult', 'organizationUnitId']
  return multiSelectFields.includes(fieldName)
}

@Injectable({ scope: Scope.REQUEST })
export class WorkflowsHistoriesService {
  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly workflowsHistoryRepository: WorkflowHistoryRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly processRepository: ProcessRepository,
    private readonly caseAdvancedRepository: CaseAdvancedRepository,
    private readonly userRepository: UserRepository,
    private readonly workflowRepository: WorkflowRepository
  ) {}

  async create(createWorkflowsHistoriesDto: CreateWorkflowsHistoriesCompleteDto) {
    // Check if metadata exists before accessing
    const userId = this.request.metadata?.getUser()?.getId()

    // Validate all foreign key references exist
    await this.validateAllReferences(createWorkflowsHistoriesDto)

    const newData = {
      ...createWorkflowsHistoriesDto,
      status: WorkflowSubTaskStatus.COMPLETED,
    }

    const data: CreateWorkflowsHistoriesCompleteDto & { assignedToId: string } = this.convertDtoToEntity(newData)

    // Set created by (only if userId exists)
    if (userId) {
      data.assignedToId = userId
    }

    const entity = await this.workflowsHistoryRepository.create(data)

    return entity
  }

  async findAllAndCount(query: GetListQueryBaseDto, includeRelations: boolean = true) {
    // Process filter to handle date ranges and other special formats
    const processedQuery = {
      ...query,
      filter: processFilter(query.filter),
    }

    const result = await this.workflowsHistoryRepository.findAllAndCount(processedQuery)

    if (includeRelations) {
      const itemsWithRelations = await this.workflowsHistoryRepository.loadRelationsForItems(result.items)
      return {
        ...result,
        items: itemsWithRelations,
      }
    }

    return result
  }

  async findAll(query: GetListQueryBaseAllDto, includeRelations: boolean = true) {
    // Process filter to handle date ranges and other special formats
    const processedQuery = {
      ...query,
      filter: processFilter(query.filter),
    }

    const items = await this.workflowsHistoryRepository.findAll(processedQuery)

    if (includeRelations) {
      const result = await this.workflowsHistoryRepository.loadRelationsForItems(items)
      return result
    }

    return items
  }

  async findOne(id: string, includeRelations: boolean = true) {
    const workflowsHistories = await this.workflowsHistoryRepository.findById(id)
    if (!workflowsHistories) {
      throw new BadRequestException('Không tìm thấy yêu cầu xác minh')
    }

    if (includeRelations) {
      const itemsWithRelations = await this.workflowsHistoryRepository.loadRelationsForItems([workflowsHistories])
      return itemsWithRelations[0] || workflowsHistories
    }

    return workflowsHistories
  }

  async update(id: string, updateWorkflowsHistoriesDto: UpdateWorkflowsHistoriesCompleteDto) {
    const existingEntity = await this.workflowsHistoryRepository.findById(id)
    if (!existingEntity) {
      throw new BadRequestException('Không tìm thấy yêu cầu kết thúc')
    }

    // Validate all foreign key references exist
    await this.validateAllReferences(updateWorkflowsHistoriesDto)

    const data: UpdateWorkflowsHistoriesCompleteDto = this.convertDtoToEntity(updateWorkflowsHistoriesDto)

    const result = await this.workflowsHistoryRepository.update(id, data as QueryDeepPartialEntity<WorkflowHistory>)
    return { affected: result.affected ?? undefined }
  }

  async remove(id: string) {
    const existingEntity = await this.workflowsHistoryRepository.findById(id)
    if (!existingEntity) {
      throw new BadRequestException('Không tìm thấy yêu cầu xác minh')
    }

    const result = await this.workflowsHistoryRepository.remove({ id })
    return { affected: result.affected ?? undefined }
  }

  private convertDtoToEntity(dto: Any): Any {
    const entity = { ...dto }

    // Convert date strings to Date objects
    const dateFields = ['verificationDate', 'responseDate', 'responseDocumentDate']
    dateFields.forEach(field => {
      if (entity[field] && typeof entity[field] === 'string') {
        entity[field] = new Date(entity[field])
      }
    })

    return entity
  }

  /**
   * Validate all foreign key references exist and are active
   */
  private async validateAllReferences(
    dto: CreateWorkflowsHistoriesCompleteDto | UpdateWorkflowsHistoriesCompleteDto
  ): Promise<void> {
    const validationPromises: Promise<void>[] = []

    // Validate workflowId
    if (dto.workflowId) {
      validationPromises.push(this.validateWorkflow(dto.workflowId))
    }

    // Validate processId
    if (dto.processId) {
      validationPromises.push(this.validateProcess(dto.processId))
    }

    // Validate caseAdvancedId
    if (dto.caseAdvancedId) {
      validationPromises.push(this.validateCaseAdvanced(dto.caseAdvancedId))
    }

    // Validate fromOrgUnitId
    if (dto.orgUnitId) {
      validationPromises.push(this.validateOrganizationUnit(dto.orgUnitId, 'đơn vị'))
    }

    // Execute all validations in parallel
    await Promise.all(validationPromises)
  }

  /**
   * Validate workflow exists
   */
  private async validateWorkflow(workflowId: string): Promise<void> {
    const workflow = await this.workflowRepository.findOne({ id: workflowId })
    if (!workflow) {
      throw new BadRequestException(`Không tìm thấy workflow với ID: ${workflowId}`)
    }
  }

  /**
   * Validate process exists
   */
  private async validateProcess(processId: string): Promise<void> {
    const process = await this.processRepository.findById(processId)
    if (!process) {
      throw new BadRequestException(`Không tìm thấy quy trình với ID: ${processId}`)
    }
  }

  /**
   * Validate case advanced exists
   */
  private async validateCaseAdvanced(caseAdvancedId: string): Promise<void> {
    const caseAdvanced = await this.caseAdvancedRepository.findById(caseAdvancedId)
    if (!caseAdvanced) {
      throw new BadRequestException(`Không tìm thấy vụ việc trợ giúp pháp lý với ID: ${caseAdvancedId}`)
    }
  }

  /**
   * Validate organization unit exists and is active
   */
  private async validateOrganizationUnit(orgUnitId: string, description: string): Promise<void> {
    const orgUnit = await this.organizationUnitRepository.findById(orgUnitId)
    if (!orgUnit) {
      throw new BadRequestException(`Không tìm thấy ${description} với ID: ${orgUnitId}`)
    }

    // Check if the organization unit is active/valid
    if (orgUnit.status === -99) {
      throw new BadRequestException(
        `${description.charAt(0).toUpperCase() + description.slice(1)} đã bị xóa: ${orgUnit.name}`
      )
    }
  }

  /**
   * Validate user exists and is active
   */
  private async validateUser(userId: string, description: string): Promise<void> {
    const user = await this.userRepository.findById(userId)
    if (!user) {
      throw new BadRequestException(`Không tìm thấy ${description} với ID: ${userId}`)
    }

    // Check if the user is active/valid
    if (user.status === UserStatus.DELETED) {
      throw new BadRequestException(
        `${description.charAt(0).toUpperCase() + description.slice(1)} đã bị xóa: ${user.name}`
      )
    }
  }
}
