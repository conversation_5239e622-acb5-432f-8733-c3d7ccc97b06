import { NextAuthMiddleware } from '@ac/be'
import { DatabaseModule } from '@ac/models'
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common'

import { RolesController } from './roles.controller'
import { RolesService } from './roles.service'

@Module({
  imports: [DatabaseModule],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(NextAuthMiddleware).forRoutes('roles')
  }
}
