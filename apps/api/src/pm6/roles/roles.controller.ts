import {
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  PERMISSION_ROOT_GROUP,
  PERMISSION_TREE_DATA,
  PERMISSIONS,
  Queries,
  Route,
} from '@ac/common'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'

import { CreateRoleDto, UpdateRoleDto } from './dto'
import { RolesService } from './roles.service'

@Controller('roles')
@Route('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo role' })
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto)
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy role theo id' })
  findOne(@Param('id') id: string) {
    return this.rolesService.findOne(id)
  }

  @Get()
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách role' })
  findAllAndCount(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.rolesService.findAllAndCount(query)
  }

  @Get('/method/all')
  @ApiOperation({ summary: 'Lấy tất cả role' })
  findAll(@Queries() @Query() query: GetListQueryBaseAllDto) {
    return this.rolesService.findAll(query)
  }

  @Get('/method/permission-config')
  @ApiOperation({ summary: 'Lấy cấu hình quyền' })
  getPermissionConfig() {
    return {
      kind: 'permission-config',
      data: {
        treeData: PERMISSION_TREE_DATA,
        permissions: PERMISSIONS,
        permissionRootGroup: PERMISSION_ROOT_GROUP,
      },
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật role' })
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.rolesService.update(id, updateRoleDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa role' })
  remove(@Param('id') id: string) {
    return this.rolesService.remove(id)
  }
}
