import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, PERMISSIONS, REQUEST } from '@ac/common'
import { Role, RoleRepository } from '@ac/models'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'

import { CreateRoleDto, UpdateRoleDto } from './dto'

@Injectable({ scope: Scope.REQUEST })
export class RolesService {
  constructor(
    private readonly roleRepository: RoleRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<Role> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_ROLE_CREATE_ALL)
    const role = await this.roleRepository.findOne({ name: createRoleDto.name })

    if (role) {
      throw new BadRequestException('Role already exists')
    }

    return this.roleRepository.create({
      ...createRoleDto,
    })
  }

  async findAll(query: GetListQueryBaseAllDto) {
    const roles = await this.roleRepository.findAll(query)

    return roles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      type: role.type,
      status: role.status,
      isSystem: role.isSystem,
    }))
  }

  async findAllAndCount(query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_ROLE_READ_ALL)
    return this.roleRepository.findAllAndCount(query)
  }

  async findOne(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_ROLE_READ_DETAIL)
    const role = await this.roleRepository.findOne({ id }, { throwError: true })

    return role
  }

  async update(id: string, updateRoleDto: UpdateRoleDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_ROLE_UPDATE_ALL)
    await this.roleRepository.findOne({ id }, { throwError: true })
    return this.roleRepository.update(id, updateRoleDto)
  }

  async remove(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_ROLE_DELETE_ALL)
    const role = await this.roleRepository.findOne({ id }, { throwError: true })

    let status = -1
    if (role?.status === -1) {
      status = -99
    }

    return this.roleRepository.update(id, { status })
  }
}
