import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from '@ac/common'
import { WorkTargetType } from '@ac/models'
import { ApiProperty } from '@nestjs/swagger'

class WorkTarget {
  @ApiProperty({
    description: 'Tên <PERSON>hóm chỉ',
    example: 'Chấ<PERSON> hành kỷ cương, kỷ luật hành chính, công vụ, chấp hành pháp luật',
  })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({
    description: 'Mô tả chỉ tiêu',
    example: 'Mô tả chỉ tiêu',
  })
  @IsString()
  @IsOptional()
  note?: string

  @ApiProperty({
    description: 'Loại chỉ tiêu',
    example: WorkTargetType.CASE_ADVANCED,
    enum: WorkTargetType,
  })
  @IsEnum(WorkTargetType)
  @IsNotEmpty()
  type: WorkTargetType

  @ApiProperty({
    description: '<PERSON><PERSON> lượng cụ thể của chỉ tiêu',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  targetNumber: number
}

export class CreateWorkTargetDto {
  @ApiProperty({
    description: 'Tên chỉ tiêu',
    example: 'Bộ chỉ tiêu tư vấn pháp luật',
  })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({
    description: 'ID của danh mục dùng chung có type = TG',
    example: '3cb1a410-400f-4015-e063-4832800a5a09',
  })
  @IsString()
  @IsNotEmpty()
  categoryId: string

  @ApiProperty({
    description: 'Danh sách chỉ tiêu',
    type: () => [WorkTarget],
    example: [
      {
        name: 'Vụ việc tư vấn pháp luật',
        type: WorkTargetType.CASE_ADVANCED,
        targetNumber: 15,
        note: 'ưu tiên 1',
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  workTargets: WorkTarget[]

  @ApiProperty({
    description: 'Tỷ lệ đạt yêu cầu chất lượng',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  requiredRate: number

  @ApiProperty({
    description: 'Tỷ lệ vụ việc hoàn thành đúng thời hạn',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  onTimeRate: number
}
