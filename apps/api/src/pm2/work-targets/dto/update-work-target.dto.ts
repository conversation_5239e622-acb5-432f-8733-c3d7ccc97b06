import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from '@ac/common'
import { WorkTargetType } from '@ac/models'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'

import { CreateWorkTargetDto } from './create-work-target.dto'

class UpdateWorkTargetItem {
  @ApiProperty({
    description: 'Mã chỉ tiêu (giữ nguyên khi cập nhật)',
    example: '5c9742be-b491-4423-8278-e6d9af50195c',
    required: false,
  })
  @IsString()
  @IsOptional()
  code?: string

  @ApiProperty({
    description: 'Tên chỉ tiêu',
    example: 'Chấp hành kỷ cương, kỷ luật hành chính',
  })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({
    description: 'Loại chỉ tiêu',
    example: WorkTargetType.CASE_ADVANCED,
    enum: WorkTargetType,
  })
  @IsEnum(WorkTargetType)
  type: WorkTargetType

  @ApiProperty({
    description: 'S<PERSON> lượng mục tiêu',
    example: 15,
  })
  @IsNumber()
  targetNumber: number

  @ApiProperty({
    description: 'Ghi chú',
    example: 'ưu tiên 1',
    required: false,
  })
  @IsString()
  @IsOptional()
  note?: string
}

export class UpdateWorkTargetDto extends CreateWorkTargetDto {
  @ApiProperty({
    description: 'Danh sách chỉ tiêu (có thể kèm code để cập nhật)',
    type: [UpdateWorkTargetItem],
  })
  @IsArray()
  @Type(() => UpdateWorkTargetItem)
  declare workTargets: UpdateWorkTargetItem[]
}
