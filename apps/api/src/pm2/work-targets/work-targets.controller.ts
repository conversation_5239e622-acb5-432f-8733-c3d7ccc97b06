import { GetListQueryBaseDto, Queries } from '@ac/common'
import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

import { CreateWorkTargetDto } from './dto/create-work-target.dto'
import { UpdateWorkTargetDto } from './dto/update-work-target.dto'
import { WorkTargetsService } from './work-targets.service'

@ApiTags('Chỉ tiêu')
@Controller('work-targets')
export class WorkTargetsController {
  constructor(private readonly workTargetsService: WorkTargetsService) {}

  @Post()
  @ApiOperation({
    summary: 'Tạo chỉ tiêu',
  })
  create(@Body() createWorkTargetDto: CreateWorkTargetDto) {
    return this.workTargetsService.create(createWorkTargetDto)
  }

  @Get()
  @ApiOperation({
    summary: 'Danh sách chỉ tiêu',
  })
  findAllAndCount(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.workTargetsService.findAllAndCount(query)
  }

  @Get('/method/all')
  @ApiOperation({
    summary: 'Lấy danh sách bộ chỉ tiêu',
  })
  findAllWorkTargetGroup(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.workTargetsService.findAll(query)
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chỉ tiêu theo id',
  })
  findOne(@Param('id') id: string) {
    return this.workTargetsService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật chỉ tiêu',
  })
  update(@Param('id') id: string, @Body() updateWorkTargetDto: UpdateWorkTargetDto) {
    return this.workTargetsService.update(id, updateWorkTargetDto)
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa chỉ tiêu',
  })
  remove(@Param('id') id: string) {
    return this.workTargetsService.remove(id)
  }
}
