import { AcRequest } from '@ac/be'
import { GetListQueryBaseAllDto, GetListQueryBaseDto, PERMISSIONS } from '@ac/common'
import { Category, CategoryRepository, OrganizationUnit, ProcessStatus, User, WorkTargetGroup } from '@ac/models'
import { WorkTargetRepository } from '@ac/models/data-access/work-target.repository'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'
import { v4 as uuidv4 } from 'uuid'

import { CreateWorkTargetDto } from './dto/create-work-target.dto'
import { UpdateWorkTargetDto } from './dto/update-work-target.dto'

@Injectable({ scope: Scope.REQUEST })
export class WorkTargetsService {
  constructor(
    private readonly workTargetRepository: WorkTargetRepository,
    private readonly categoryRepository: CategoryRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}
  async create(createWorkTargetDto: CreateWorkTargetDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_CREATE)
    const userId = this.request.metadata.getUser().getId()
    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()

    const category = await this.categoryRepository.findById(createWorkTargetDto.categoryId)
    if (!category) {
      throw new BadRequestException('Không tồn tại category')
    }

    // Mặc định hạn hoàn thành là 31/12 của năm hiện tại
    const endDate: Date = new Date(new Date().getFullYear(), 11, 31)

    // Map workTargets từ DTO sang entity JSON
    const workTargetsJson = (createWorkTargetDto.workTargets || []).map(item => {
      return {
        code: uuidv4(),
        name: item.name || '',
        type: item.type,
        targetNumber: item.targetNumber,
        note: item.note || '',
      }
    }) as WorkTargetGroup['workTargets']

    const keyword = generateKeyword(createWorkTargetDto.name)

    const record: Partial<WorkTargetGroup> = {
      name: createWorkTargetDto.name,
      category: { categoryId: category.categoryId } as Category,
      keyword,
      endDate,
      orgUnit: { id: orgUnitId } as OrganizationUnit,
      createdBy: { id: userId } as User,
      status: ProcessStatus.ACTIVE,
      workTargets: workTargetsJson,
      requiredRate: createWorkTargetDto.requiredRate,
      onTimeRate: createWorkTargetDto.onTimeRate,
    }

    const created = await this.workTargetRepository.create(record)
    return created
  }

  async findAllAndCount(query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_READ_ALL)
    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()

    const processedQuery = {
      ...query,
      filter: {
        ...(query.filter || {}),
      },
    }

    const { items, totalItems } = await this.workTargetRepository.findAllAndCount(processedQuery, orgUnitId)

    const pageSize = Number(query.pageSize) || 10
    return {
      items,
      totalItems,
      totalPages: Math.ceil((totalItems || 0) / pageSize),
    }
  }

  async findAll(query: GetListQueryBaseAllDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_READ_ALL)
    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()

    const processedQuery = {
      ...query,
    }
    const result = await this.workTargetRepository.findAll({ query: processedQuery, orgUnitId })
    return result
  }

  async findOne(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_READ)
    const workTarget = await this.workTargetRepository.findOne({ id: id })
    if (!workTarget) {
      throw new BadRequestException('Không tìm thấy chỉ tiêu')
    }
    return workTarget
  }

  async update(id: string, updateWorkTargetDto: UpdateWorkTargetDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_UPDATE)
    const userId = this.request.metadata.getUser().getId()

    const workTargetExisted = await this.workTargetRepository.findById({
      id,
      status: ProcessStatus.ACTIVE,
    })
    if (!workTargetExisted) {
      throw new BadRequestException('Không tìm thấy chỉ tiêu hoặc chỉ tiêu đã bị vô hiệu hóa')
    }

    const keyword = updateWorkTargetDto.name ? generateKeyword(updateWorkTargetDto.name) : workTargetExisted.keyword

    const existingItems = Array.isArray(workTargetExisted.workTargets) ? workTargetExisted.workTargets : []

    const workTargetsJson: WorkTargetGroup['workTargets'] = []

    updateWorkTargetDto.workTargets.forEach(item => {
      // Nếu không có code => thêm mới
      if (!item.code) {
        workTargetsJson.push({
          code: uuidv4(),
          name: item.name,
          type: item.type,
          targetNumber: item.targetNumber,
          note: item.note || '',
        })
        return
      }

      // Nếu có code => tìm trong DB
      const matched = existingItems.find(workTarget => workTarget.code === item.code)
      if (!matched) {
        throw new BadRequestException(`Không tìm thấy chỉ tiêu code: ${item.code}`)
      }

      workTargetsJson.push({
        code: item.code,
        name: item.name,
        type: item.type,
        targetNumber: item.targetNumber,
        note: item.note || '',
      })
    })

    const updateData: QueryDeepPartialEntity<WorkTargetGroup> = {
      name: updateWorkTargetDto.name,
      keyword,
      workTargets: workTargetsJson,
      updatedBy: { id: userId },
    }

    return this.workTargetRepository.update(id, updateData)
  }

  async remove(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_WORK_TARGET_GROUP_DELETE)
    const userId = this.request.metadata.getUser().getId()
    const workTargetExisted = await this.workTargetRepository.findById({
      id: id,
      status: ProcessStatus.ACTIVE,
    })

    if (!workTargetExisted) {
      throw new BadRequestException('Không tìm thấy chỉ tiêu hoặc chỉ tiêu đã bị vô hiệu hóa')
    }
    const removeData: QueryDeepPartialEntity<WorkTargetGroup> = {
      status: ProcessStatus.INACTIVE,
      updatedBy: { id: userId },
    }
    const updated = await this.workTargetRepository.update(id, removeData)
    return updated
  }
}
