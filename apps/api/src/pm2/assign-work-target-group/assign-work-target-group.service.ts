import { AcRequest } from '@ac/be'
import { GetListQueryBaseDto, PERMISSIONS, REQUEST } from '@ac/common'
import {
  AssignWorkTargetGroup,
  AssignWorkTargetGroupProcessStatus,
  AssignWorkTargetGroupRepository,
  OrganizationUnit,
  ProcessStatus,
  User,
  UserRepository,
  WorkTargetGroup,
  WorkTargetRepository,
} from '@ac/models'
import { BadRequestException, Inject, Injectable, Scope } from '@nestjs/common'
import { In } from 'typeorm'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { CreateAssignWorkTargetGroupDto } from './dto/create-assign-work-target-group.dto'
import { ResponseAssignWorkTargetGroupDto } from './dto/response-assign-work-target-group.dto'
import { UpdateAssignWorkTargetGroupDto } from './dto/update-assign-work-target-group.dto'
import { UpdateProcessStatusDto } from './dto/update-process-status.dto'

@Injectable({ scope: Scope.REQUEST })
export class AssignWorkTargetGroupService {
  constructor(
    private readonly assignWorkTargetGroupRepository: AssignWorkTargetGroupRepository,
    private readonly userRepository: UserRepository,
    private readonly workTargetRepository: WorkTargetRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}

  async create(createAssignWorkTargetGroupDto: CreateAssignWorkTargetGroupDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_CREATE)
    const userId = this.request.metadata.getUser().getId()

    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()

    const assignWorkTargetGroupExisted = await this.assignWorkTargetGroupRepository.findById({
      workTargetGroupId: createAssignWorkTargetGroupDto.workTargetGroupId,
      evaluatorId: createAssignWorkTargetGroupDto.evaluatorId,
      status: ProcessStatus.ACTIVE,
    })

    if (assignWorkTargetGroupExisted) {
      throw new BadRequestException('Bộ chỉ tiêu đã được gán cho càn bộ đánh giá')
    }

    const endDate: Date = new Date(new Date().getFullYear(), 11, 31)

    const [users, workTargetGroupExisted] = await Promise.all([
      this.userRepository.find({
        id: In([createAssignWorkTargetGroupDto.approvalId, createAssignWorkTargetGroupDto.evaluatorId]),
      }),

      this.workTargetRepository.findById({
        id: createAssignWorkTargetGroupDto.workTargetGroupId,
        status: ProcessStatus.ACTIVE,
      }),
    ])

    const userIds = (users || []).map(user => user.id)

    if (!userIds.includes(createAssignWorkTargetGroupDto.evaluatorId)) {
      throw new BadRequestException('Người đánh giá bộ chỉ tiêu không tồn tại')
    }
    if (!userIds.includes(createAssignWorkTargetGroupDto.approvalId)) {
      throw new BadRequestException('Người người phê duyệt bộ chỉ tiêu tồn tại')
    }

    if (!workTargetGroupExisted) {
      throw new BadRequestException('Bộ tiêu chí đánh giá không tồn tại hoặc bị dừng hoạt động')
    }

    const record: Partial<AssignWorkTargetGroup> = {
      orgUnit: { id: orgUnitId } as OrganizationUnit,
      createdBy: { id: userId } as User,
      workTargetGroup: { id: workTargetGroupExisted.id } as WorkTargetGroup,
      keyword: '',
      status: ProcessStatus.ACTIVE,
      endDate,
      evaluator: { id: createAssignWorkTargetGroupDto.evaluatorId } as User,
      approval: { id: createAssignWorkTargetGroupDto.approvalId } as User,
    }

    const assignWorkTargetGroupCreated = await this.assignWorkTargetGroupRepository.create(record)
    return assignWorkTargetGroupCreated
  }

  async findAll(query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_READ_ALL)
    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()

    const processedQuery = {
      ...query,
      filter: {
        ...(query.filter || {}),
      },
    }

    const { items, totalItems } = await this.assignWorkTargetGroupRepository.findAllAndCount({
      query: processedQuery,
      orgUnitId,
    })

    const pageSize = Number(query.pageSize) || 10
    return {
      items,
      totalItems,
      totalPages: Math.ceil((totalItems || 0) / pageSize),
    }
  }

  async findOne(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_READ)
    const AssignWorkTargetGroup = await this.assignWorkTargetGroupRepository.findOne({ id: id })

    if (!AssignWorkTargetGroup) {
      throw new BadRequestException('Không tìm thấy bộ chỉ tiêu được giao')
    }

    return AssignWorkTargetGroup
  }

  async update(assignWorkTargetGroupId: string, updateAssignWorkTargetGroupDto: UpdateAssignWorkTargetGroupDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_UPDATE)
    const userId = this.request.metadata.getUser().getId()

    const assignWorkTargetGroupExisted = await this.assignWorkTargetGroupRepository.findById({
      id: assignWorkTargetGroupId,
      status: ProcessStatus.ACTIVE,
    })

    if (!assignWorkTargetGroupExisted) {
      throw new BadRequestException('Không tìm thấy bộ chỉ tiêu được gán')
    }

    if (userId !== assignWorkTargetGroupExisted.evaluatorId) {
      throw new BadRequestException('Bạn không có quyền đánh gía bộ chỉ tiêu này')
    }

    const updateData: QueryDeepPartialEntity<AssignWorkTargetGroup> = {
      evaluatorId: updateAssignWorkTargetGroupDto.evaluatorId,
      approvalId: updateAssignWorkTargetGroupDto.approvalId,
    }

    const updateAssignWorkTargetGroup = await this.assignWorkTargetGroupRepository.update(
      assignWorkTargetGroupExisted.id,
      updateData
    )
    return updateAssignWorkTargetGroup
  }

  async updateResponse(
    assignWorkTargetGroupId: string,
    responseAssignWorkTargetGroupDto: ResponseAssignWorkTargetGroupDto
  ) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_EVALUATOR)
    const userId = this.request.metadata.getUser().getId()

    const assignWorkTargetGroupExisted = await this.assignWorkTargetGroupRepository.findById({
      id: assignWorkTargetGroupId,
      status: ProcessStatus.ACTIVE,
    })

    if (!assignWorkTargetGroupExisted) {
      throw new BadRequestException('Không tìm thấy bộ chỉ tiêu được gán')
    }

    if (userId !== assignWorkTargetGroupExisted.evaluatorId) {
      throw new BadRequestException('Bạn không có quyền đánh gía bộ chỉ tiêu này')
    }

    const updateData: QueryDeepPartialEntity<AssignWorkTargetGroup> = {
      workTargetGroupId: assignWorkTargetGroupExisted.workTargetGroupId,
      response: responseAssignWorkTargetGroupDto.response,
    }

    const updateAssignWorkTargetGroup = await this.assignWorkTargetGroupRepository.update(
      assignWorkTargetGroupExisted.id,
      updateData
    )
    return updateAssignWorkTargetGroup
  }

  async remove(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ASSIGN_WORK_TARGET_GROUP_DELETE)
    const userId = this.request.metadata.getUser().getId()

    const assignWorkTargetGroup = await this.assignWorkTargetGroupRepository.findById({
      id: id,
    })

    if (!assignWorkTargetGroup) {
      throw new BadRequestException('Không tìm thấy bộ chỉ tiêu được gán hoặc đã bị vô hiệu hoá')
    }

    const removeData: QueryDeepPartialEntity<AssignWorkTargetGroup> = {
      status: ProcessStatus.INACTIVE,
      updatedBy: { id: userId },
    }

    const updated = await this.assignWorkTargetGroupRepository.update(assignWorkTargetGroup.id, removeData)
    return updated
  }

  async findAssignWortTargetGroupByEvaluator(query: GetListQueryBaseDto) {
    const userId = this.request.metadata.getUser().getId()

    const processedQuery = {
      ...query,
      filter: {
        ...(query.filter || {}),
      },
    }

    const { items, totalItems } = await this.assignWorkTargetGroupRepository.findAllByOwner({
      query: processedQuery,
      evaluatorId: userId,
    })

    return {
      items,
      totalItems,
    }
  }

  async approvalAssignWorkTargetGroupList(query: GetListQueryBaseDto) {
    const userId = this.request.metadata.getUser().getId()

    const processedQuery = {
      ...query,
      filter: {
        ...(query.filter || {}),
      },
    }

    const { items, totalItems } = await this.assignWorkTargetGroupRepository.findAllByOwner({
      query: processedQuery,
      approvalId: userId,
    })

    return {
      items,
      totalItems,
    }
  }

  async updateApprovalAssignWorkTargetGroup(id: string, updateProcessStatusDto: UpdateProcessStatusDto) {
    const userId = this.request.metadata.getUser().getId()

    const assignWorkTargetGroup = await this.assignWorkTargetGroupRepository.findById({
      id: id,
      processStatus: AssignWorkTargetGroupProcessStatus.SUBMITTED,
    })

    if (!assignWorkTargetGroup) {
      throw new BadRequestException('Không tìm thấy bộ chỉ tiêu được gán hoặc bộ chi tiêu chưa được submit')
    }

    if (userId !== assignWorkTargetGroup.approvalId) {
      throw new BadRequestException('Bạn không có quyền đánh gía bộ chỉ tiêu này')
    }

    const updateData: QueryDeepPartialEntity<AssignWorkTargetGroup> = {
      processStatus: updateProcessStatusDto.status,
    }

    const updateAssignWorkTargetGroup = await this.assignWorkTargetGroupRepository.update(id, updateData)

    return updateAssignWorkTargetGroup
  }
}
