import { GetListQueryBaseDto, Queries } from '@ac/common'
import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

import { AssignWorkTargetGroupService } from './assign-work-target-group.service'
import { CreateAssignWorkTargetGroupDto } from './dto/create-assign-work-target-group.dto'
import { ResponseAssignWorkTargetGroupDto } from './dto/response-assign-work-target-group.dto'
import { UpdateAssignWorkTargetGroupDto } from './dto/update-assign-work-target-group.dto'
import { UpdateProcessStatusDto } from './dto/update-process-status.dto'

@ApiTags('Gán bộ chỉ tiêu')
@Controller('assign-work-target-groups')
export class AssignWorkTargetGroupController {
  constructor(private readonly assignWorkTargetGroupService: AssignWorkTargetGroupService) {}

  @Post()
  @ApiOperation({
    summary: '<PERSON><PERSON> bộ chỉ tiêu cho user',
  })
  create(@Body() createAssignWorkTargetGroupDto: CreateAssignWorkTargetGroupDto) {
    return this.assignWorkTargetGroupService.create(createAssignWorkTargetGroupDto)
  }

  @Get()
  findAll(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.assignWorkTargetGroupService.findAll(query)
  }

  @Get('/evaluator')
  @ApiOperation({
    summary: 'Lấy đanh sách bộ chỉ tiêu cần đánh gía',
  })
  findAssignWtgOwner(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.assignWorkTargetGroupService.findAssignWortTargetGroupByEvaluator(query)
  }

  @Patch(':assignWorkTargetGroupId')
  @ApiOperation({
    summary: 'Cập nhật bộ trỉ tiêu',
  })
  UpdateAssignWorkTargetGroup(
    @Param('assignWorkTargetGroupId') assignWorkTargetGroupId: string,
    @Body() updateAssignWorkTargetGroupDto: UpdateAssignWorkTargetGroupDto
  ) {
    return this.assignWorkTargetGroupService.update(assignWorkTargetGroupId, updateAssignWorkTargetGroupDto)
  }

  @Patch('/evaluator/:assignWorkTargetGroupId')
  @ApiOperation({
    summary: 'Đánh giá bộ trỉ tiêu',
  })
  evaluatorUpdateAssignWorkTargetGroup(
    @Param('assignWorkTargetGroupId') assignWorkTargetGroupId: string,
    @Body() responseAssignWorkTargetGroupDto: ResponseAssignWorkTargetGroupDto
  ) {
    return this.assignWorkTargetGroupService.updateResponse(assignWorkTargetGroupId, responseAssignWorkTargetGroupDto)
  }

  @Get('/approval')
  @ApiOperation({
    summary: 'Lấy danh sách bộ chỉ tiêu cần phê duyệt',
  })
  approvalAssignWorkTargetGroupList(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.assignWorkTargetGroupService.approvalAssignWorkTargetGroupList(query)
  }

  @Patch('/approval/:id')
  @ApiOperation({
    summary: 'Phê duyệt bộ chỉ tiêu',
  })
  updateApprovalAssignWorkTargetGroup(@Param('id') id: string, @Body() updateProcessStatusDto: UpdateProcessStatusDto) {
    return this.assignWorkTargetGroupService.updateApprovalAssignWorkTargetGroup(id, updateProcessStatusDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assignWorkTargetGroupService.remove(id)
  }
}
