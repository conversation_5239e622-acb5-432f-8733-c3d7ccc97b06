import { IsNotEmpty, IsString } from '@ac/common'
import { ApiProperty } from '@nestjs/swagger'

export class UpdateAssignWorkTargetGroupDto {
  @ApiProperty({
    description: 'Mã người đánh giá bộ chỉ tiêu',
    example: 'd6f6d9cc-41c0-40bd-be47-e33080c3c33d',
  })
  @IsString()
  @IsNotEmpty()
  evaluatorId: string

  @ApiProperty({
    description: 'Mã người phê duyệt bộ chỉ tiêu',
    example: 'd6f6d9cc-41c0-40bd-be47-e33080c3c33d',
  })
  @IsString()
  @IsNotEmpty()
  approvalId: string
}
