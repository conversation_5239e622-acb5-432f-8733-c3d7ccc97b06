import { IsNotEmpty, IsString } from '@ac/common'
import { ApiProperty } from '@nestjs/swagger'

class responseWorkTarget {
  @ApiProperty({
    description: 'Mã câu hỏi',
  })
  @IsNotEmpty()
  code: string

  @ApiProperty({
    description: 'kết quả trả lời',
  })
  @IsNotEmpty()
  value: string | number
}

export class ResponseAssignWorkTargetGroupDto {
  @ApiProperty({
    description: 'Mã bộ chỉ tiêu',
    example: '3f101355-9f16-5845-e063-020011ac5aae',
  })
  @IsString()
  @IsNotEmpty()
  workTargetGroupId: string

  @ApiProperty({
    description: 'Kết quả trả lời bộ chỉ tiêu',
  })
  @IsNotEmpty()
  response: responseWorkTarget[]
}
