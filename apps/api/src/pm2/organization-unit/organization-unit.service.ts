import { AcRequest } from '@ac/be'
import { E_ORDER_DIR, GetListQueryBaseAllDto, GetListQueryBaseDto, Injectable, PERMISSIONS, Scope } from '@ac/common'
import { CategoryRepository, OrganizationUnitRepository } from '@ac/models/data-access'
import { OrganizationUnitTypeLabel } from '@ac/models/entity'
import { generateKeyword } from '@ac/utils'
import { BadRequestException, Inject, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { v4 as uuidv4 } from 'uuid'

import { CreateOrganizationUnitDto, UpdateOrganizationUnitDto } from './dto'

@Injectable({ scope: Scope.REQUEST })
export class OrganizationUnitService {
  constructor(
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly categoryRepository: CategoryRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}

  /**
   * <PERSON><PERSON><PERSON> mới cơ quan tư pháp
   */
  async create(createDto: CreateOrganizationUnitDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_CREATE)
    const newId = uuidv4()
    const userId = this.request.metadata.getUser().getId()

    // Kiểm tra code trùng lặp
    if (createDto.code) {
      const existingByCode = await this.organizationUnitRepository.findByCode(createDto.code)
      if (existingByCode) {
        throw new BadRequestException(`Mã cơ quan "${createDto.code}" đã tồn tại`)
      }
    }

    // Kiểm tra parentId có tồn tại không (nếu có)
    if (createDto.parentId) {
      const parent = await this.organizationUnitRepository.findOne({ id: createDto.parentId })
      if (!parent) {
        throw new BadRequestException(`Không tìm thấy cơ quan cha với ID: ${createDto.parentId}`)
      }

      // Kiểm tra không tạo vòng tròn (parent không thể là con của chính nó)
      await this.validateNoCircularReference(createDto.parentId, newId)
    }

    // Generate keyword từ name, fullName, code
    const keyword = generateKeyword(createDto.name, createDto.fullName, createDto.code)

    const dataToCreate = {
      id: newId,
      ...createDto,
      keyword,
      createById: userId,
      updateById: userId,
    }

    const result = await this.organizationUnitRepository.create(dataToCreate)

    return result
  }

  /**
   * Lấy tất cả cơ quan tư pháp (không phân trang)
   */
  async findAll(query: GetListQueryBaseAllDto) {
    const items = await this.organizationUnitRepository.findAll(query)
    return items
  }

  /**
   * Lấy danh sách cơ quan tư pháp có phân trang
   */
  async findAllAndCount(query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const result = await this.organizationUnitRepository.findAllAndCount(query)
    return result
  }

  /**
   * Tìm cơ quan tư pháp theo ID
   */
  async findOne(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ)

    // Lấy data gốc
    const organizationUnit = await this.organizationUnitRepository.findById(id)

    if (!organizationUnit) {
      throw new NotFoundException(`Không tìm thấy cơ quan tư pháp với ID: ${id}`)
    }

    // Handle legalFormId array separately
    if (organizationUnit.legalFormId && Array.isArray(organizationUnit.legalFormId)) {
      const legalFormPromises = organizationUnit.legalFormId.map((id: string) =>
        this.categoryRepository.findOne({ categoryId: id })
      )
      const legalFormCategories = await Promise.all(legalFormPromises)
      ;(organizationUnit as unknown as Record<string, unknown>).legalFormNames = legalFormCategories
        .filter(cat => cat !== null)
        .map(cat => cat.name)
        .join(', ')
    }

    return organizationUnit
  }
  /**
   * Tìm cơ quan tư pháp theo ID với quan hệ
   */
  findOneWithRelations(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ)

    return this.organizationUnitRepository.findByIdWithRelations(id)
  }

  /**
   * Tìm các cơ quan con theo parentId
   */
  async findByParentId(parentId: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const items = await this.organizationUnitRepository.findByParentId(parentId)
    return items
  }

  /**
   * Tìm cơ quan gốc (không có parent)
   */
  async findRootUnits() {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const items = await this.organizationUnitRepository.findRootUnits()
    return items
  }

  /**
   * Tìm cơ quan với cấu trúc phân cấp
   * @param id ID của cơ quan cần tìm
   * @param depth Độ sâu của cây phân cấp (mặc định = 1, tức là chỉ lấy 1 cấp con)
   */
  async findWithHierarchy(id: string, depth: number = 1) {
    // Giới hạn depth tối đa để tránh performance issue
    const maxDepth = 10
    const safeDepth = Math.min(Math.max(depth, 0), maxDepth)

    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const result = await this.organizationUnitRepository.findWithHierarchy(id, safeDepth)
    if (!result) {
      throw new NotFoundException(`Không tìm thấy cơ quan tư pháp với ID: ${id}`)
    }

    return result
  }

  /**
   * Tìm kiếm theo tên
   */
  async searchByName(keyword: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const items = await this.organizationUnitRepository.searchByName(keyword)
    return items
  }

  /**
   * Tìm theo loại cơ quan
   */
  async findByType(type: number) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const items = await this.organizationUnitRepository.findByType(type)
    return items
  }

  /**
   * Tìm theo loại cơ quan với query parameters
   */
  async findByTypeWithQuery(type: number, query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const result = await this.organizationUnitRepository.findByTypeWithQuery(type, query)
    return result
  }

  /**
   * Tìm các trung tâm
   */
  findCenters() {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    return this.organizationUnitRepository.findCenters()
  }

  /**
   * Cập nhật cơ quan tư pháp
   */
  async update(id: string, updateDto: UpdateOrganizationUnitDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)

    const userId = this.request.metadata.getUser().getId()

    // Kiểm tra code trùng lặp (nếu có thay đổi code)
    if (updateDto.code) {
      const existingByCode = await this.organizationUnitRepository.findByCode(updateDto.code)
      if (existingByCode && existingByCode.id !== id) {
        throw new BadRequestException(`Mã cơ quan "${updateDto.code}" đã tồn tại`)
      }
    }

    // Kiểm tra parentId có tồn tại không (nếu có)
    if (updateDto.parentId) {
      const parent = await this.organizationUnitRepository.findOne({ id: updateDto.parentId })
      if (!parent) {
        throw new BadRequestException(`Không tìm thấy cơ quan cha với ID: ${updateDto.parentId}`)
      }

      // Kiểm tra không tạo vòng tròn
      await this.validateNoCircularReference(updateDto.parentId, id)
    }

    // Nếu có thay đổi name, fullName, hoặc code thì regenerate keyword
    let keyword = undefined
    if (updateDto.name || updateDto.fullName || updateDto.code) {
      // Lấy data hiện tại để merge
      const current = await this.organizationUnitRepository.findOne({ id })
      if (current) {
        const currentName = updateDto.name || current.name
        const currentFullName = updateDto.fullName !== undefined ? updateDto.fullName : current.fullName
        const currentCode = updateDto.code || current.code

        keyword = generateKeyword(currentName, currentFullName, currentCode)
      }
    }

    const dataToUpdate = {
      ...updateDto,
      ...(keyword && { keyword }), // Chỉ thêm keyword nếu có
      updateById: userId,
    }

    const result = await this.organizationUnitRepository.update(id, dataToUpdate)
    return result
  }

  /**
   * Xóa cơ quan tư pháp
   */
  remove(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_DELETE)

    return this.organizationUnitRepository.remove(id)
  }

  /**
   * Kiểm tra mã cơ quan có tồn tại không
   */
  async checkCodeExists(code: string, excludeId?: string) {
    const existing = await this.organizationUnitRepository.findOne({ code })
    if (!existing) return false
    if (excludeId && existing.id === excludeId) return false
    return true
  }

  /**
   * Lấy danh sách các loại cơ quan
   */
  getTypes() {
    const items = Object.entries(OrganizationUnitTypeLabel as Record<string, string>).map(([id, name]) => ({
      id: Number(id),
      name,
    }))

    return {
      items,
      total: items.length,
    }
  }

  /**
   * Regenerate keyword cho tất cả organization units (dùng để migrate data cũ)
   */
  async regenerateAllKeywords() {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_READ_ALL)

    const allUnits = await this.organizationUnitRepository.findAll({
      orderBy: 'createdAt',
      orderDir: E_ORDER_DIR.DESC,
    })

    const results = []
    for (const unit of allUnits) {
      const keyword = generateKeyword(unit.name, unit.fullName, unit.code)
      if (keyword !== unit.keyword) {
        await this.organizationUnitRepository.update(unit.id, { keyword })
        results.push({ id: unit.id, name: unit.name, keyword })
      }
    }

    return {
      message: `Đã regenerate keyword cho ${results.length} records`,
      results,
    }
  }

  /**
   * Kiểm tra không tạo vòng tròn trong quan hệ cha-con
   */
  private async validateNoCircularReference(parentId: string, childId: string): Promise<void> {
    // Trường hợp 1: Không thể tự làm cha của chính mình
    if (parentId === childId) {
      throw new BadRequestException('Không thể đặt cơ quan làm cha của chính nó')
    }

    // Trường hợp 2: Kiểm tra parent có phải là con của child không
    // Ví dụ: A -> B -> C, không cho phép C làm cha của A
    const isCircular = await this.checkIfParentIsChildOf(parentId, childId)
    if (isCircular) {
      throw new BadRequestException('Không thể tạo vòng tròn trong cây phân cấp')
    }
  }

  /**
   * Kiểm tra parentId có phải là con/cháu của childId không
   * Ví dụ: A(child) -> B -> C(parent), return true nếu parent là con của child
   */
  private async checkIfParentIsChildOf(parentId: string, childId: string): Promise<boolean> {
    let currentId = parentId

    // Đi ngược lên cây từ parent, nếu gặp childId thì có vòng tròn
    while (currentId) {
      const current = await this.organizationUnitRepository.findOne({ id: currentId })

      if (!current?.parentId) {
        break // Đã lên đến root
      }

      if (current.parentId === childId) {
        return true // Tìm thấy vòng tròn
      }

      currentId = current.parentId
    }

    return false // Không có vòng tròn
  }
}
