import { ApiPropertyOptional, <PERSON><PERSON><PERSON>y, IsOptional, IsString, MaxLength } from '@ac/common'
import { IsEmail, IsInt, IsUrl, Min } from 'class-validator'

export class UpdateOrganizationUnitDto {
  @ApiPropertyOptional({ description: '<PERSON>ã cơ quan', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  code?: string

  @ApiPropertyOptional({ description: 'Tên cơ quan', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string

  @ApiPropertyOptional({ description: 'Tên đ<PERSON>y đủ', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  fullName?: string

  @ApiPropertyOptional({ description: 'Địa chỉ', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  address?: string

  @ApiPropertyOptional({ description: 'Ngư<PERSON><PERSON> đạ<PERSON> diện', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  representative?: string

  @ApiPropertyOptional({ description: 'S<PERSON> điện thoại', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  phone?: string

  @ApiPropertyOptional({ description: 'Số fax', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  fax?: string

  @ApiPropertyOptional({ description: 'Email', maxLength: 255 })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string

  @ApiPropertyOptional({ description: 'Website', maxLength: 255 })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  website?: string

  // Thông tin địa phương
  @ApiPropertyOptional({ description: 'ID Tỉnh/Thành phố (dữ liệu cũ)', type: String })
  @IsOptional()
  @IsString()
  oldProvinceId?: string

  @ApiPropertyOptional({ description: 'ID Thành phố', type: Number })
  @IsOptional()
  @IsInt()
  cityId?: number

  @ApiPropertyOptional({ description: 'ID Quận/Huyện (dữ liệu cũ)', type: String })
  @IsOptional()
  @IsString()
  oldDistrictId?: string

  @ApiPropertyOptional({ description: 'ID Phường/Xã (dữ liệu cũ)', type: String })
  @IsOptional()
  @IsString()
  oldWardId?: string

  // Cấu trúc cây
  @ApiPropertyOptional({ description: 'ID cơ quan cha', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  parentId?: string

  // Trạng thái và phân loại
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động (0=Dừng hoạt động, 1=Hoạt động, -1=Xóa nháp)',
    type: Number,
    enum: [0, 1, -1],
  })
  @IsOptional()
  @IsInt()
  status?: number

  @ApiPropertyOptional({
    description: 'Loại cơ quan (1=Trung tâm TGPL, 2=Sở Tư pháp, 3=Tổ chức, 4=Nhóm tổ chức, 5=Bộ Tư pháp)',
    type: Number,
  })
  @IsOptional()
  @IsInt()
  type?: number

  @ApiPropertyOptional({ description: 'ID nhóm tổ chức', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  organizationGroupId?: string

  // Thông tin thành lập
  @ApiPropertyOptional({ description: 'Ngày thành lập', type: Date })
  @IsOptional()
  establishmentDate?: Date

  @ApiPropertyOptional({ description: 'Ngày quyết định thành lập', type: Date })
  @IsOptional()
  decisionDate?: Date

  @ApiPropertyOptional({ description: 'Số quyết định thành lập', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  decisionNumber?: string

  // Lĩnh vực pháp lý
  @ApiPropertyOptional({ description: 'ID lĩnh vực pháp lý', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  legalFieldId?: string

  @ApiPropertyOptional({
    description: 'ID hình thức pháp lý (mảng các ID)',
    type: [String],
    example: ['id1', 'id2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  legalFormId?: string[]

  // Phạm vi hoạt động
  @ApiPropertyOptional({ description: 'ID phạm vi hoạt động', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  rangeActivitiesId?: string

  // Thông tin tổ chức
  @ApiPropertyOptional({ description: 'Số lượng biên chế', type: Number, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  payrollNumber?: number

  @ApiPropertyOptional({ description: 'Số lượng phòng ban', type: Number, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  roomNumber?: number

  @ApiPropertyOptional({ description: 'Số câu lạc bộ', type: Number, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  clubNumber?: number

  // Quản trị
  @ApiPropertyOptional({ description: 'ID nhóm quản trị', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  adminGroupId?: string

  @ApiPropertyOptional({ description: 'Trạng thái quản trị', type: Number })
  @IsOptional()
  @IsInt()
  adminStatusId?: number

  @ApiPropertyOptional({ description: 'Kinh nghiệm hoạt động' })
  @IsOptional()
  @IsString()
  experience?: string

  @ApiPropertyOptional({ description: 'Ngày hết hạn hợp đồng', type: Date })
  @IsOptional()
  deadlineContractDate?: Date

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note?: string

  @ApiPropertyOptional({ description: 'Ngày đăng ký tham gia TGPL / ngày hiệu lực hợp đồng', type: Date })
  @IsOptional()
  issueDate?: Date

  @ApiPropertyOptional({ description: 'Đơn vị cấp cho tổ chức tham gia TGPL', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  issuingAgency?: string

  @ApiPropertyOptional({ description: 'Lý do cấp giấy đăng ký tham gia', maxLength: 255 })
  @IsOptional()
  @IsString()
  participationReason?: string

  @ApiPropertyOptional({ description: 'Lý do chấm dứt đăng ký tham gia TGPL' })
  @IsOptional()
  @IsString()
  registrationTerminationReason?: string

  @ApiPropertyOptional({ description: 'Lý do chấm dứt hợp đồng' })
  @IsOptional()
  @IsString()
  contractTerminationReason?: string

  @ApiPropertyOptional({ description: 'Ngày hết hạn hợp đồng', type: Date })
  @IsOptional()
  expiryDate?: Date

  @ApiPropertyOptional({ description: 'Số tháng gia hạn hợp đồng', type: Number, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  renewalMonths?: number

  @ApiPropertyOptional({ description: 'Ngày hết hạn khi gia hạn', type: Date })
  @IsOptional()
  renewedExpiryDate?: Date

  @ApiPropertyOptional({ description: 'Số hợp đồng', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  contractNumber?: string

  @ApiPropertyOptional({ description: 'Số đăng ký tham gia TGPL', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  registrationNumber?: string

  @ApiPropertyOptional({ description: 'ID file hợp đồng', type: String, maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  contractFileId?: string

  @ApiPropertyOptional({ description: 'Đường dẫn hoặc key file hợp đồng', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  contractFileRef?: string

  @ApiPropertyOptional({
    description: 'Danh sách file đính kèm',
    type: [Object],
    example: [{ fileId: '...', fileKey: '...', url: '...', name: '...' }],
  })
  @IsOptional()
  @IsArray()
  attachments?: { fileId?: string; fileKey?: string; url?: string; name?: string }[]
}
