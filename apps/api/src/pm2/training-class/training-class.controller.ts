import {
  Body,
  Controller,
  Delete,
  Get,
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  Param,
  Patch,
  Post,
  Queries,
  Route,
} from '@ac/common'
import { Query, ValidationPipe } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

import { AddParticipantsDto, CreateTrainingClassDto, UpdateTrainingClassDto } from './dto'
import { TrainingClassService } from './training-class.service'

@ApiTags('PM2 - Training Class Management')
@Route('training-class')
@Controller('training-class')
export class TrainingClassController {
  constructor(private readonly service: TrainingClassService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo lớp đào tạo' })
  create(@Body() dto: CreateTrainingClassDto) {
    return this.service.create(dto)
  }

  @Get()
  @ApiOperation({ summary: 'Danh sách lớp đào tạo (kèm tổng)' })
  findAllAndCount(@Queries() @Query(ValidationPipe) query: GetListQueryBaseDto) {
    return this.service.findAllAndCount(query)
  }

  @Get('/method/all')
  @ApiOperation({ summary: 'Danh sách lớp đào tạo (chỉ items)' })
  findAll(@Queries() @Query(ValidationPipe) query: GetListQueryBaseAllDto) {
    return this.service.findAll(query)
  }

  @Get(':trainingClassId')
  @ApiOperation({ summary: 'Chi tiết lớp đào tạo' })
  findOne(@Param('trainingClassId') trainingClassId: string) {
    return this.service.findOne(trainingClassId)
  }

  @Get(':trainingClassId/participants')
  @ApiOperation({ summary: 'Danh sách học viên đã tham gia lớp' })
  getParticipants(@Param('trainingClassId') trainingClassId: string) {
    return this.service.getParticipants(trainingClassId)
  }

  @Get(':trainingClassId/participants/count')
  @ApiOperation({ summary: 'Số lượng học viên đã tham gia lớp' })
  getParticipantCount(@Param('trainingClassId') trainingClassId: string) {
    return this.service.getParticipantCount(trainingClassId)
  }

  @Post(':trainingClassId/participants')
  @ApiOperation({ summary: 'Thêm học viên vào lớp (1 hoặc nhiều)' })
  addParticipantsFlexible(@Param('trainingClassId') trainingClassId: string, @Body() body: AddParticipantsDto) {
    return this.service.addParticipantsFlexible(trainingClassId, body)
  }

  @Patch(':trainingClassId')
  @ApiOperation({ summary: 'Cập nhật lớp đào tạo' })
  update(
    @Param('trainingClassId') trainingClassId: string,
    @Body() dto: UpdateTrainingClassDto
  ): Promise<{ affected?: number }> {
    return this.service.update(trainingClassId, dto)
  }

  @Delete(':trainingClassId')
  @ApiOperation({ summary: 'Xóa lớp đào tạo' })
  remove(@Param('trainingClassId') trainingClassId: string): Promise<{ affected?: number }> {
    return this.service.remove(trainingClassId)
  }
}
