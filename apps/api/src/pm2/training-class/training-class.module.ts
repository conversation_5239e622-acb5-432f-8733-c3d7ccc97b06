import { NextAuthMiddleware } from '@ac/be'
import { Module } from '@ac/common'
import { DatabaseModule, StaffTrainingHistoryRepository, TrainingClassRepository } from '@ac/models'
import { MiddlewareConsumer, NestModule } from '@nestjs/common'

import { TrainingClassController } from './training-class.controller'
import { TrainingClassService } from './training-class.service'

@Module({
  imports: [DatabaseModule],
  controllers: [TrainingClassController],
  providers: [TrainingClassService, TrainingClassRepository, StaffTrainingHistoryRepository],
  exports: [TrainingClassService],
})
export class TrainingClassModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(NextAuthMiddleware).forRoutes('training-class')
  }
}
