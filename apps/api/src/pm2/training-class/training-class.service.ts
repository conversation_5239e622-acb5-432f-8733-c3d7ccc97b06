import { AcRequest } from '@ac/be'
import {
  Any,
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  Injectable,
  LimitDepthLevel1,
  PERMISSIONS,
  Scope,
} from '@ac/common'
import { Staff, StaffTrainingHistoryRepository, TrainingClass, TrainingClassRepository } from '@ac/models'
import { Inject } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import moment from 'moment'
import slugify from 'slugify'

import { CreateTrainingClassDto, UpdateTrainingClassDto } from './dto'

interface UpdateData extends Omit<UpdateTrainingClassDto, 'startDate' | 'endDate'> {
  startDate?: Date
  endDate?: Date
  keyword?: string
}

// Tạo từ khóa tìm kiếm từ trainingClassName, description, certificate
function generateKeyword(name: string, description?: string, certificate?: string): string {
  const parts = [name, description, certificate].filter(Boolean).join(' ')
  return slugify(parts, {
    lower: true,
    replacement: ' ',
    locale: 'vi',
    trim: true,
    strict: true,
  })
}

// Chuẩn hóa filter: date range "YYYY-MM-DD,YYYY-MM-DD" & multi-select "a,b,c"
function processFilter(filter: Any = {} as Any): Any {
  if (!filter || typeof filter !== 'object') {
    return filter
  }

  const processed: Any = {}
  Object.keys(filter as Record<string, unknown>).forEach(key => {
    const value = filter[key]

    // Date range: áp dụng cho các field ngày
    if (isDateField(key) && typeof value === 'string' && value.includes(',')) {
      const [start, end] = value.split(',')
      processed[`${key}Start`] = moment(start.trim()).startOf('day').toISOString()
      processed[`${key}End`] = moment(end.trim()).endOf('day').toISOString()
    }
    // Multi-select: hỗ trợ tách bằng dấu phẩy
    else if (typeof value === 'string' && value.includes(',') && isMultiSelectField(key)) {
      processed[key] = value.split(',').map(v => {
        const trimmed = v.trim()
        const num = Number(trimmed)
        return !isNaN(num) && isFinite(num) ? num : trimmed
      })
    } else {
      processed[key] = value
    }
  })

  return processed
}

function isDateField(name: string): boolean {
  const dateFields = ['startDate', 'endDate', 'createdAt', 'updatedAt']
  return dateFields.includes(name)
}

function isMultiSelectField(name: string): boolean {
  const multi = ['trainingTypeId', 'trainingFormatId']
  return multi.includes(name)
}

// Cấu hình filter cho repository
// const findManyOptions: GetListQueryHelperOptions<StaffTrainingHistory> = {
//   filterKeywordType: 'contains',
//   filterKeywordColumns: ['keyword'],
//   filterColumns: [
//     { columnName: 'trainingTypeId', type: 'string' },
//     { columnName: 'trainingFormatId', type: 'string' },
//     { columnName: 'startDate', type: 'date-time' },
//     { columnName: 'endDate', type: 'date-time' },
//     { columnName: 'createdAt', type: 'date-time' },
//     { columnName: 'updatedAt', type: 'date-time' },
//   ],
// }

@Injectable({ scope: Scope.REQUEST })
export class TrainingClassService {
  constructor(
    private readonly trainingClassRepo: TrainingClassRepository,
    private readonly historyRepo: StaffTrainingHistoryRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}

  create(dto: CreateTrainingClassDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_CREATE)

    const orgUnitId = this.request.metadata.getUser().getOrgUnitId()
    const keyword = generateKeyword(dto.trainingClassName, dto.description, dto.certificate)
    const data = {
      ...dto,
      startDate: new Date(dto.startDate),
      endDate: new Date(dto.endDate),
      keyword,
      status: 'PLANNING', // Default status
      currentParticipants: 0, // Start with 0 participants
      trainingOrganizationId: dto.trainingOrganizationId || orgUnitId,
    }
    return this.trainingClassRepo.create(data)
  }

  async findAllAndCount(query: GetListQueryBaseDto): Promise<{
    items: LimitDepthLevel1<TrainingClass>[]
    total: number
    totalPages: number
  }> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_READ_ALL_TRAINING_CLASS)

    const safeQuery = query || {}
    const rawFilter = typeof (safeQuery as Any).filter === 'string' ? JSON.parse(safeQuery.filter) : safeQuery.filter
    const processed = { ...safeQuery, filter: processFilter(rawFilter) }
    if (!processed.filter && (safeQuery as Any).trainingOrganizationId) {
      processed.filter = { trainingOrganizationId: (safeQuery as Any).trainingOrganizationId }
    }

    let allItems: LimitDepthLevel1<TrainingClass>[] = []
    if (processed.filter && Array.isArray(processed.filter.trainingOrganizationId)) {
      allItems = await this.trainingClassRepo.find({})
    } else {
      const where: { trainingOrganizationId?: string } = {}
      if (processed.filter && processed.filter.trainingOrganizationId) {
        where.trainingOrganizationId = processed.filter.trainingOrganizationId
      }
      allItems = await this.trainingClassRepo.find(where)
    }

    let filteredItems = allItems
    if (processed.filter && Array.isArray(processed.filter.trainingOrganizationId)) {
      filteredItems = filteredItems.filter(item =>
        processed.filter.trainingOrganizationId.includes(item.trainingOrganizationId)
      )
    }
    if (processed.keyword) {
      filteredItems = filteredItems.filter(item =>
        item.keyword?.toLowerCase().includes(processed.keyword!.toLowerCase())
      )
    }

    const pageSize = processed.pageSize || 10
    const page = processed.page || 1
    const startIndex = (page - 1) * pageSize
    const items = filteredItems.slice(startIndex, startIndex + pageSize)

    if (processed.filter && Array.isArray(processed.filter.trainingTypeId)) {
      filteredItems = filteredItems.filter(item => processed.filter.trainingTypeId.includes(item.trainingTypeId))
    }
    if (processed.filter && Array.isArray(processed.filter.trainingFormatId)) {
      filteredItems = filteredItems.filter(item => processed.filter.trainingFormatId.includes(item.trainingFormatId))
    }

    if (!filteredItems || filteredItems.length === 0) {
      return {
        items: [],
        total: 0,
        totalPages: 0,
      }
    }
    return {
      items: items,
      total: filteredItems.length,
      totalPages: Math.ceil(filteredItems.length / pageSize),
    }
  }
  async findAll(query: GetListQueryBaseAllDto): Promise<LimitDepthLevel1<TrainingClass>[]> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_READ_ALL_TRAINING_CLASS)

    const safeQuery = query || {}
    const rawFilter =
      typeof (safeQuery as Any).filter === 'string' ? JSON.parse((safeQuery as Any).filter) : safeQuery.filter
    const processed = { ...safeQuery, filter: processFilter(rawFilter) }
    if (!processed.filter && (safeQuery as Any).trainingOrganizationId) {
      processed.filter = { trainingOrganizationId: (safeQuery as Any).trainingOrganizationId }
    }

    const where: { trainingOrganizationId?: string } = {}
    if (processed.filter && processed.filter.trainingOrganizationId) {
      where.trainingOrganizationId = processed.filter.trainingOrganizationId
    }

    const allItems = await this.trainingClassRepo.find(where)

    if (processed.keyword) {
      return allItems.filter(item =>
        item.keyword?.toLowerCase().includes(processed.keyword!.toLowerCase())
      ) as LimitDepthLevel1<TrainingClass>[]
    }

    return allItems as LimitDepthLevel1<TrainingClass>[]
  }

  async findOne(trainingClassId: string): Promise<LimitDepthLevel1<TrainingClass> | null> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_READ_TRAINING_CLASS)
    return (await this.trainingClassRepo.findById(trainingClassId)) as LimitDepthLevel1<TrainingClass> | null
  }

  // New: participants and count
  async getParticipants(trainingClassId: string): Promise<LimitDepthLevel1<Staff>[]> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_READ_TRAINING_CLASS)
    const histories = await this.historyRepo.findByTrainingClassId(trainingClassId)
    return histories.map(h => h.staff) as LimitDepthLevel1<Staff>[]
  }

  async getParticipantCount(trainingClassId: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_READ_TRAINING_CLASS)
    return this.historyRepo.countByTrainingClassId(trainingClassId)
  }

  // New: add a staff to class (enroll)
  async addParticipant(trainingClassId: string, staffId: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)
    // Check if staff already enrolled
    const existingParticipation = await this.historyRepo.findOne({ staffId, trainingClassId })
    if (existingParticipation) {
      throw new Error('Staff already enrolled in this training class')
    }

    await this.historyRepo.registerStaff(staffId, trainingClassId, { registrationDate: new Date() })
    const cls = await this.trainingClassRepo.findById(trainingClassId)
    if (cls) {
      const current = Number(cls.currentParticipants || 0)
      await this.trainingClassRepo.update(trainingClassId, { currentParticipants: current + 1 })
    }
    return { success: true }
  }

  async addParticipants(trainingClassId: string, staffIds: string[]) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)
    if (!Array.isArray(staffIds) || staffIds.length === 0) {
      throw new Error('staffIds must be a non-empty array')
    }

    // Đảm bảo không thêm trùng staff đã có
    const existing = await this.historyRepo.findByTrainingClassId(trainingClassId)
    const existingStaffIds = new Set(existing.map(h => h.staffId))

    const newStaffIds = staffIds.filter(id => !existingStaffIds.has(id))
    if (newStaffIds.length === 0) {
      return { success: true, added: 0 }
    }

    // Đăng ký từng staff mới
    for (const staffId of newStaffIds) {
      await this.historyRepo.registerStaff(staffId, trainingClassId, { registrationDate: new Date() })
    }

    // Cập nhật lại số lượng học viên
    const cls = await this.trainingClassRepo.findById(trainingClassId)
    if (cls) {
      const current = Number(cls.currentParticipants || 0)
      await this.trainingClassRepo.update(trainingClassId, { currentParticipants: current + newStaffIds.length })
    }

    return { success: true, added: newStaffIds.length }
  }

  // Alias for addParticipants for bulk endpoint
  async addParticipantsBulk(trainingClassId: string, staffIds: string[]) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)
    return this.addParticipants(trainingClassId, staffIds)
  }

  // Unified handler: accept staffIds only (DTO ensures array)
  async addParticipantsFlexible(trainingClassId: string, body: { staffIds: string[] }) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)
    const { staffIds } = body || { staffIds: [] }

    if (!Array.isArray(staffIds) || staffIds.length === 0) {
      throw new Error('staffIds must be a non-empty array')
    }

    return this.addParticipants(trainingClassId, Array.from(new Set(staffIds.filter(Boolean))))
  }

  async update(trainingClassId: string, dto: UpdateTrainingClassDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_UPDATE)
    const dataToUpdate: UpdateData = {
      ...dto,
      startDate: dto.startDate ? new Date(dto.startDate) : undefined,
      endDate: dto.endDate ? new Date(dto.endDate) : undefined,
    }

    // Regenerate keyword khi có thay đổi liên quan
    if (dto.trainingClassName || dto.description !== undefined || dto.certificate !== undefined) {
      const current = await this.trainingClassRepo.findById(trainingClassId)
      if (current) {
        const keyword = generateKeyword(
          dto.trainingClassName ?? current.trainingClassName,
          dto.description !== undefined ? dto.description : current.description,
          dto.certificate !== undefined ? dto.certificate : current.certificate
        )
        dataToUpdate.keyword = keyword
      }
    }

    const result = await this.trainingClassRepo.update(trainingClassId, dataToUpdate)
    return { affected: result.affected ?? undefined }
  }

  async remove(trainingClassId: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM02_ORGANIZATION_UNIT_DELETE)
    const result = await this.trainingClassRepo.remove(trainingClassId)
    return { affected: result.affected ?? undefined }
  }
}
