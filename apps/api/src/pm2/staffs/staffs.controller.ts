import {
  Body,
  Controller,
  Delete,
  Get,
  GetListQueryBaseAllDto,
  GetListQueryStaffDto,
  Param,
  Patch,
  Post,
  Queries,
  Query,
  Route,
} from '@ac/common'
import { ApiOperation } from '@nestjs/swagger'

import {
  AddStaffRolesDto,
  ChangeOrganizationUnitDto,
  ChangeOrganizationUnitResponseDto,
  CreateStaffContractDto,
  CreateStaffDto,
  CreateStaffOrganizationEventDto,
  CreateStaffOrganizationHistoryActionDto,
  StaffDetailResponseDto,
  StaffResponseDto,
  UpdateStaffDto,
  UpdateStaffFieldDto,
  UpdateStaffOrganizationHistoryDto,
  UpdateStaffOrganizationHistoryResponseDto,
} from './dto'
import {
  StaffContractService,
  StaffOrganizationHistoryService,
  StaffOrgEventService,
  StaffOrgHistoryActionService,
  StaffOrgRoleService,
  StaffSpecializationService,
} from './services'
import { StaffsService } from './staffs.service'

@Route('staffs')
@Controller('staffs')
export class StaffsController {
  constructor(
    private readonly staffsService: StaffsService,
    private readonly staffContractService: StaffContractService,
    private readonly staffOrgEventService: StaffOrgEventService,
    private readonly staffOrgHistoryActionService: StaffOrgHistoryActionService,
    private readonly staffOrganizationHistoryService: StaffOrganizationHistoryService,
    private readonly staffOrgRoleService: StaffOrgRoleService,
    private readonly staffSpecializationService: StaffSpecializationService
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Tạo nhân viên',
  })
  create(@Body() createStaffDto: CreateStaffDto): Promise<StaffResponseDto> {
    return this.staffsService.create(createStaffDto)
  }

  @Get()
  @ApiOperation({
    summary: 'Danh sách nhân viên',
  })
  findAllAndCount(@Queries() @Query() query: GetListQueryStaffDto) {
    return this.staffsService.findAllAndCount(query)
  }

  @Get('/method/all')
  @ApiOperation({
    summary: 'Danh sách tất cả nhân viên',
  })
  findAll(@Queries() @Query() query: GetListQueryBaseAllDto): Promise<StaffResponseDto[]> {
    return this.staffsService.findAll(query)
  }

  @Patch('/organization-history')
  @ApiOperation({
    summary: 'Cập nhật thông tin lịch sử tổ chức của nhân viên',
    description: `
    Cập nhật các thông tin chi tiết trong bản ghi lịch sử tổ chức như:
    - Thông tin quyết định (số, ngày, cơ quan ban hành)
    - Thông tin tập sự
    - Ghi chú và lý do
    - Ngày bắt đầu/kết thúc
    Không ảnh hưởng đến việc phân công tổ chức.
    `,
  })
  updateStaffOrganizationHistory(
    @Body() updateStaffOrganizationHistoryDto: UpdateStaffOrganizationHistoryDto
  ): Promise<UpdateStaffOrganizationHistoryResponseDto> {
    return this.staffOrganizationHistoryService.updateStaffOrganizationHistory(updateStaffOrganizationHistoryDto)
  }

  @Get(':staffId')
  @ApiOperation({
    summary: 'Chi tiết nhân viên',
  })
  findOne(@Param('staffId') staffId: string): Promise<StaffResponseDto> {
    return this.staffsService.findOne(staffId)
  }

  @Get(':staffId/detail')
  @ApiOperation({
    summary: 'Chi tiết đầy đủ nhân viên (bao gồm tất cả thông tin liên quan)',
  })
  findOneDetail(@Param('staffId') staffId: string): Promise<StaffDetailResponseDto> {
    return this.staffsService.findOneDetail(staffId)
  }

  @Patch(':staffId')
  @ApiOperation({
    summary: 'Cập nhật thông tin nhân viên',
  })
  update(@Param('staffId') staffId: string, @Body() updateStaffDto: UpdateStaffDto): Promise<{ affected?: number }> {
    return this.staffsService.update(staffId, updateStaffDto)
  }

  @Patch(':staffId/fields')
  @ApiOperation({
    summary: 'Cập nhật các trường thông tin cụ thể của nhân viên',
    description:
      'Endpoint này cho phép cập nhật một hoặc nhiều trường thông tin của nhân viên một cách linh hoạt. Bao gồm cả gán/hủy gán tài khoản người dùng bằng cách sử dụng trường userId',
  })
  updateFields(
    @Param('staffId') staffId: string,
    @Body() updateStaffFieldDto: UpdateStaffFieldDto
  ): Promise<StaffResponseDto> {
    console.log('updateStaffFieldDto', updateStaffFieldDto)
    return this.staffsService.updateFields(staffId, updateStaffFieldDto)
  }

  @Delete(':staffId')
  @ApiOperation({
    summary: 'Xóa nhân viên',
  })
  remove(@Param('staffId') staffId: string): Promise<{ affected?: number }> {
    return this.staffsService.remove(staffId)
  }

  @Post('/change-organization-unit')
  @ApiOperation({
    summary: 'Thay đổi đơn vị tổ chức cho User/Staff',
    description: `
    Thay đổi đơn vị tổ chức cho một User và Staff liên kết. Quy trình:
    1. Đánh dấu lịch sử tổ chức hiện tại thành không hoạt động (status=0)
    2. Tạo bản ghi lịch sử tổ chức mới với trạng thái hoạt động (status=1)
    3. Cập nhật organizationUnitId trong cả bảng User và Staff (để cache)
    `,
  })
  changeOrganizationUnit(
    @Body() changeOrganizationUnitDto: ChangeOrganizationUnitDto
  ): Promise<ChangeOrganizationUnitResponseDto> {
    return this.staffOrganizationHistoryService.changeOrganizationUnit(changeOrganizationUnitDto)
  }

  // ========================================
  // STAFF ORGANIZATION ROLES MANAGEMENT
  // ========================================

  @Post(':staffId/roles')
  @ApiOperation({
    summary: 'Thêm vai trò mới cho nhân viên trong tổ chức hiện tại',
    description: `
      Thêm vai trò nghiệp vụ mới cho nhân viên. Hệ thống sẽ tự động:
      - Tìm lịch sử tổ chức hiện tại của nhân viên
      - Thêm các vai trò vào tổ chức đó
      - Validate vai trò hợp lệ
      - Đảm bảo không trùng lặp
    `,
  })
  async addStaffRoles(@Param('staffId') staffId: string, @Body() addRolesDto: AddStaffRolesDto) {
    // Get current active organization history for the staff
    const currentOrgHistory =
      await this.staffsService['staffOrganizationHistoryRepository'].findLatestCurrentByStaffId(staffId)
    if (!currentOrgHistory) {
      throw new Error('Không tìm thấy lịch sử tổ chức hiện tại cho nhân viên')
    }

    return this.staffOrgRoleService.addStaffOrganizationRoles(currentOrgHistory.id, addRolesDto.roles)
  }

  // ========================================
  // STAFF SPECIALIZATIONS MANAGEMENT
  // ========================================

  @Post(':staffId/organization/specializations')
  @ApiOperation({
    summary: 'Thêm chuyên môn cho nhân viên trong tổ chức hiện tại',
    description: `
    Thêm các chuyên môn mới cho nhân viên trong lịch sử tổ chức đang hoạt động.
    Tạo các bản ghi StaffSpecialization mới cho tổ chức hiện tại.
    `,
  })
  async addOrganizationSpecializations(
    @Param('staffId') staffId: string,
    @Body()
    addSpecializationsDto: {
      specializations: Array<{
        categoryId: string
        level?: string
        certificateNo?: string
        certIssuedAt?: string
        certExpiresAt?: string
        note?: string
      }>
    }
  ) {
    // Get current active organization history for the staff
    const currentOrgHistory =
      await this.staffsService['staffOrganizationHistoryRepository'].findLatestCurrentByStaffId(staffId)
    if (!currentOrgHistory) {
      throw new Error('Không tìm thấy lịch sử tổ chức hiện tại cho nhân viên')
    }

    return this.staffSpecializationService.addStaffSpecializations(
      currentOrgHistory.id,
      addSpecializationsDto.specializations
    )
  }

  // ========================================
  // STAFF CONTRACTS MANAGEMENT
  // ========================================

  @Post(':staffId/contracts')
  @ApiOperation({
    summary: 'Tạo hợp đồng mới cho nhân viên',
    description: `
    Tạo hợp đồng mới cho nhân viên với đơn vị tổ chức.
    Có thể tự động kết thúc các hợp đồng cũ nếu được chỉ định.
    `,
  })
  async createContract(@Param('staffId') staffId: string, @Body() createContractDto: CreateStaffContractDto) {
    return this.staffContractService.createStaffContract(staffId, createContractDto)
  }

  @Get(':staffId/contracts')
  @ApiOperation({ summary: 'Lấy danh sách hợp đồng của nhân viên' })
  async getContracts(@Param('staffId') staffId: string) {
    return this.staffContractService.getStaffContracts(staffId)
  }

  // ========================================
  // STAFF ORGANIZATION EVENTS MANAGEMENT
  // ========================================

  @Post(':staffId/organization/events')
  @ApiOperation({
    summary: 'Tạo sự kiện tổ chức cho nhân viên',
    description: `
    Tạo sự kiện tổ chức mới cho nhân viên trong lịch sử tổ chức hiện tại.
    Các loại sự kiện: APPOINTMENT, REAPPOINTMENT, DISMISSAL, TRANSFER.
    `,
  })
  async createOrganizationEvent(
    @Param('staffId') staffId: string,
    @Body() createEventDto: CreateStaffOrganizationEventDto
  ) {
    // Get current active organization history for the staff
    const currentOrgHistory =
      await this.staffsService['staffOrganizationHistoryRepository'].findLatestCurrentByStaffId(staffId)
    if (!currentOrgHistory) {
      throw new Error('Không tìm thấy lịch sử tổ chức hiện tại cho nhân viên')
    }

    return this.staffOrgEventService.createStaffOrganizationEvent(currentOrgHistory.id, createEventDto)
  }

  // ========================================
  // STAFF ORGANIZATION HISTORY ACTIONS MANAGEMENT
  // ========================================

  @Post(':staffId/organization/actions')
  @ApiOperation({
    summary: 'Tạo hành động tổ chức cho nhân viên',
    description: `
    Tạo hành động tổ chức mới (khen thưởng/kỷ luật) cho nhân viên trong lịch sử tổ chức hiện tại.
    `,
  })
  async createOrganizationAction(
    @Param('staffId') staffId: string,
    @Body() createActionDto: CreateStaffOrganizationHistoryActionDto
  ) {
    // Get current active organization history for the staff
    const currentOrgHistory =
      await this.staffsService['staffOrganizationHistoryRepository'].findLatestCurrentByStaffId(staffId)
    if (!currentOrgHistory) {
      throw new Error('Không tìm thấy lịch sử tổ chức hiện tại cho nhан viên')
    }

    return this.staffOrgHistoryActionService.createStaffOrganizationHistoryAction(currentOrgHistory.id, createActionDto)
  }

  // ========================================
  // DELETE ENDPOINTS FOR ORGANIZATION RELATIONS
  // ========================================

  @Delete(':staffId/organization/specializations/:specializationId')
  @ApiOperation({
    summary: 'Xóa chuyên môn của nhân viên',
    description: 'Xóa một chuyên môn khỏi lịch sử tổ chức của nhân viên.',
  })
  async deleteOrganizationSpecialization(
    @Param('staffId') staffId: string,
    @Param('specializationId') specializationId: string
  ) {
    return await this.staffSpecializationService.deleteStaffSpecialization(specializationId)
  }

  @Delete(':staffId/organization/events/:eventId')
  @ApiOperation({
    summary: 'Xóa sự kiện tổ chức của nhân viên',
    description: 'Xóa một sự kiện khỏi lịch sử tổ chức của nhân viên.',
  })
  async deleteOrganizationEvent(@Param('staffId') staffId: string, @Param('eventId') eventId: string) {
    return await this.staffOrgEventService.deleteStaffOrganizationEvent(eventId)
  }

  @Delete(':staffId/organization/actions/:actionId')
  @ApiOperation({
    summary: 'Xóa hành động tổ chức của nhân viên',
    description: 'Xóa một hành động khen thưởng/kỷ luật khỏi lịch sử tổ chức của nhân viên.',
  })
  async deleteOrganizationAction(@Param('staffId') staffId: string, @Param('actionId') actionId: string) {
    return await this.staffOrgHistoryActionService.deleteStaffOrganizationHistoryAction(actionId)
  }
}
