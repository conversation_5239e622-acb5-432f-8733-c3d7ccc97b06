import {
  BadRequestException,
  GetListQueryBaseAllDto,
  GetListQueryHelperOptions,
  GetListQueryStaffDto,
  Injectable,
  NotFoundException,
} from '@ac/common'
import {
  Category,
  CategoryRepository,
  CategoryStatus,
  CollabCardStatus,
  OrganizationUnitRepository,
  Staff,
  StaffCardHistoryRepository,
  StaffContractRepository,
  StaffContractStatus,
  StaffOrganizationHistoryRepository,
  StaffOrgEventRepository,
  StaffOrgRoleRepository,
  StaffPreviousPositionRepository,
  StaffRepository,
  StaffSpecializationRepository,
  StaffTrainingHistoryRepository,
  UserRepository,
  UserStatus,
} from '@ac/models'
import { isArray } from 'class-validator'
import moment from 'moment'

import { CreateStaffDto, StaffDetailResponseDto, StaffResponseDto, UpdateStaffDto, UpdateStaffFieldDto } from './dto'
import { StaffOrganizationHistoryService, StaffOrgHistoryActionService } from './services'

// Category type mapping for validation
// These category types must exist in the database for proper validation
const CATEGORY_TYPE_MAPPING = {
  STAFF: {
    genderId: 'GT', // Nam/Nữ/Khác
    ethnicId: 'DTK', // Kinh/Tày/Thái/Mường/etc.
    provinceId: 'TP', // Tỉnh/Thành phố
    districtId: 'QH', // Quận/Huyện
    wardId: 'PX', // Phường/Xã
    professionalLevelId: 'TDNV', // Đại học/Thạc sĩ/Tiến sĩ/etc.
    activityStatusId: 'TRANG_THAI_HOAT_DONG_CUA_CAN_BO', // Hoạt động/Nghỉ việc/Tạm nghỉ
  },
  CONTRACT: {
    // Moved legalAidFormId to ORGANIZATION_HISTORY
  },
  ORGANIZATION_HISTORY: {
    positionId: 'CDH', // Chuyên viên/Trưởng phòng/Phó giám đốc
    levelId: 'HANG_CHUC_DANH_NGHE_NGHIEP_VIEN_CHUC', // Cấp bậc trong tổ chức
    legalAidFormId: 'CONG_VIEC_THEO_LINH_VUC_HINH_THUC_THUC_HIEN_TGPL', // Loại hình trợ giúp pháp lý
  },
  HISTORY_ACTION: {
    rewardId: 'HINH_THUC_KHEN_THUONG', // Khen thưởng/Kỷ luật
    disciplineId: 'HINH_THUC_KY_LUAT', // Khen thưởng/Kỷ luật
  },
  SPECIALIZATION: {
    categoryId: 'LINH_VUC_TGPL', // Chuyên môn: Java/PHP/Marketing/etc.
  },
  PREVIOUS_POSITION: {
    categoryId: 'POSITION_TYPE', // Loại vị trí trước đây
  },
  TRAINING: {
    trainingTypeId: 'TRAINING_TYPE', // Loại đào tạo: Nội bộ/Bên ngoài/Online
    trainingFormatId: 'TRAINING_FORMAT', // Hình thức: Tập trung/Từ xa/Hybrid
  },
} as const

// Helper function to map Staff entity to StaffResponseDto
function mapStaffToResponseDto(staff: Staff): StaffResponseDto {
  return {
    id: staff.id,
    fullName: staff.fullName,
    dateOfBirth: staff.dateOfBirth,

    // Gender information
    genderId: staff.genderId,
    gender: staff.gender
      ? {
          categoryId: staff.gender.categoryId,
          name: staff.gender.name,
          code: staff.gender.code,
          type: staff.gender.type,
        }
      : undefined,

    // Ethnic information
    ethnicId: staff.ethnicId,
    ethnic: staff.ethnic?.name, // Extract name from Category entity

    // Contact and address information
    email: staff.email,
    phone: staff.phone,
    address: staff.address,
    permanentAddress: staff.permanentAddress,

    // Location information with IDs
    provinceId: staff.provinceId,
    province: staff.province?.name, // Extract name from Category entity
    districtId: staff.districtId,
    district: staff.district?.name, // Extract name from Category entity
    wardId: staff.wardId,
    ward: staff.ward?.name, // Extract name from Category entity

    // Personal information
    maritalStatus: staff.maritalStatus,

    // Identity document
    cccd: staff.cccd,
    cccdIssuanceDate: staff.cccdIssuanceDate,
    cccdPlaceOfIssuance: staff.cccdPlaceOfIssuance,

    // Professional information
    professionalLevelId: staff.professionalLevelId,
    professionalLevel: staff.professionalLevel
      ? {
          categoryId: staff.professionalLevel.categoryId,
          name: staff.professionalLevel.name,
          code: staff.professionalLevel.code,
          type: staff.professionalLevel.type,
        }
      : undefined,
    staffType: staff.staffType,

    // Activity status
    activityStatusId: staff.activityStatusId,
    activityStatus: staff.activityStatus
      ? {
          categoryId: staff.activityStatus.categoryId,
          name: staff.activityStatus.name,
          code: staff.activityStatus.code,
          type: staff.activityStatus.type,
        }
      : undefined,

    // Other information
    avatarRef: staff.avatarRef,
    organizationUnitId: staff.organizationUnitId,
    organizationUnit: staff.organizationUnit
      ? {
          organizationUnitId: staff.organizationUnit.id,
          name: staff.organizationUnit.name,
          code: staff.organizationUnit.code,
          type: staff.organizationUnit.type?.toString() || '',
        }
      : undefined,

    yearsOfExperience: staff.yearsOfExperience,
    createdBy: staff.createdBy,
    updatedBy: staff.updatedBy,
    createdAt: staff.createdAt,
    updatedAt: staff.updatedAt,
  }
}

// Helper function to process filter with date range and other special formats
function processFilter(filter: Record<string, unknown> | undefined): Record<string, unknown> | undefined {
  if (!filter || typeof filter !== 'object') {
    return filter
  }

  const processedFilter: Record<string, unknown> = {}

  Object.keys(filter).forEach(key => {
    const filterValue = filter[key]

    // Handle date range format "2025-08-11,2025-08-12" for date fields
    if (isDateField(key) && typeof filterValue === 'string' && filterValue.includes(',')) {
      const [startDate, endDate] = filterValue.split(',')
      processedFilter[`${key}Start`] = moment(startDate.trim()).startOf('day').toISOString()
      processedFilter[`${key}End`] = moment(endDate.trim()).endOf('day').toISOString()
    }
    // Handle array values for multiple selections (e.g., "MALE,FEMALE" -> ["MALE","FEMALE"])
    else if (typeof filterValue === 'string' && filterValue.includes(',') && isMultiSelectField(key)) {
      processedFilter[key] = filterValue.split(',').map(v => {
        const trimmed = v.trim()
        // Try to convert to number if it's numeric
        const num = Number(trimmed)
        return !isNaN(num) && isFinite(num) ? num : trimmed
      })
    } else {
      processedFilter[key] = filterValue
    }
  })

  return processedFilter
}

// Helper function to check if a field is a date field
function isDateField(fieldName: string): boolean {
  const dateFields = ['createdAt', 'updatedAt', 'dateOfBirth', 'cccdIssuanceDate']
  return dateFields.includes(fieldName)
}

// Helper function to check if a field supports multiple selections
function isMultiSelectField(fieldName: string): boolean {
  const multiSelectFields = [
    'activityStatusId', // Support filtering by multiple activity statuses
    'genderId', // Support filtering by multiple genders
    'professionalLevelId', // Support filtering by multiple professional levels
    'organizationUnitId', // Support filtering by multiple org units
    'staffType', // Support filtering by multiple staff types
  ]
  return multiSelectFields.includes(fieldName)
}

// Extracts bracket-style filter params from raw query: filter[foo]=bar → { foo: 'bar' }
function extractFilterFromRawQuery(rawQuery?: Record<string, unknown>): Record<string, unknown> {
  const extraFilter: Record<string, unknown> = {}
  if (!rawQuery) return extraFilter
  Object.entries(rawQuery).forEach(([key, value]) => {
    const match = key.match(/^filter\[(.+)\]$/)
    if (match) {
      extraFilter[match[1]] = value as string
    }
  })
  return extraFilter
}

// Filter options configuration for Staff
const findManyOptions: GetListQueryHelperOptions<Staff> = {
  filterKeywordType: 'contains',
  filterKeywordColumns: ['fullName', 'email', 'phone', 'cccd'], // Search by staff identifying fields
  filterColumns: [
    {
      columnName: 'activityStatusId',
      type: 'string',
    },
    {
      columnName: 'genderId',
      type: 'string',
    },
    {
      columnName: 'professionalLevelId',
      type: 'string',
    },
    {
      columnName: 'organizationUnitId',
      type: 'string',
    },
    {
      columnName: 'staffType',
      type: 'string',
    },
    {
      columnName: 'ethnicId',
      type: 'string',
    },
    {
      columnName: 'provinceId',
      type: 'string',
    },
    {
      columnName: 'districtId',
      type: 'string',
    },
    {
      columnName: 'wardId',
      type: 'string',
    },
    {
      columnName: 'dateOfBirth',
      type: 'date-time',
    },
    {
      columnName: 'cccdIssuanceDate',
      type: 'date-time',
    },
    {
      columnName: 'createdAt',
      type: 'date-time',
    },
    {
      columnName: 'updatedAt',
      type: 'date-time',
    },
  ],
}

@Injectable()
export class StaffsService {
  constructor(
    private readonly staffRepository: StaffRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly staffContractRepository: StaffContractRepository,
    private readonly staffOrganizationHistoryRepository: StaffOrganizationHistoryRepository,
    private readonly staffCardHistoryRepository: StaffCardHistoryRepository,
    private readonly staffTrainingHistoryRepository: StaffTrainingHistoryRepository,
    private readonly staffOrgEventRepository: StaffOrgEventRepository,
    private readonly staffOrgRoleRepository: StaffOrgRoleRepository,
    private readonly staffPreviousPositionRepository: StaffPreviousPositionRepository,
    private readonly staffSpecializationRepository: StaffSpecializationRepository,
    private readonly categoryRepository: CategoryRepository,
    private readonly userRepository: UserRepository,
    private readonly staffOrgHistoryActionService: StaffOrgHistoryActionService,
    private readonly staffOrganizationHistoryService: StaffOrganizationHistoryService
  ) {}

  /**
   * Validates all category IDs in the CreateStaffDto and nested objects
   * Uses a single query to fetch all categories and validates them in JavaScript
   * Also validates that each category has the correct TYPE for its usage
   */
  private async validateCategories(createStaffDto: CreateStaffDto): Promise<void> {
    const categoriesToValidate: { id: string; fieldName: string; expectedType: string | string[] }[] = []

    // Main staff category fields with their expected types
    if (createStaffDto.genderId) {
      categoriesToValidate.push({
        id: createStaffDto.genderId,
        fieldName: 'genderId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.genderId,
      })
    }
    if (createStaffDto.ethnicId) {
      categoriesToValidate.push({
        id: createStaffDto.ethnicId,
        fieldName: 'ethnicId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.ethnicId,
      })
    }
    if (createStaffDto.provinceId) {
      categoriesToValidate.push({
        id: createStaffDto.provinceId,
        fieldName: 'provinceId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.provinceId,
      })
    }
    if (createStaffDto.districtId) {
      categoriesToValidate.push({
        id: createStaffDto.districtId,
        fieldName: 'districtId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.districtId,
      })
    }
    if (createStaffDto.wardId) {
      categoriesToValidate.push({
        id: createStaffDto.wardId,
        fieldName: 'wardId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.wardId,
      })
    }
    if (createStaffDto.professionalLevelId) {
      categoriesToValidate.push({
        id: createStaffDto.professionalLevelId,
        fieldName: 'professionalLevelId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.professionalLevelId,
      })
    }
    if (createStaffDto.activityStatusId) {
      categoriesToValidate.push({
        id: createStaffDto.activityStatusId,
        fieldName: 'activityStatusId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.activityStatusId,
      })
    }

    // Organization history category fields
    if (createStaffDto.organizationHistory?.legalAidFormId) {
      categoriesToValidate.push({
        id: createStaffDto.organizationHistory.legalAidFormId,
        fieldName: 'organizationHistory.legalAidFormId',
        expectedType: CATEGORY_TYPE_MAPPING.ORGANIZATION_HISTORY.legalAidFormId,
      })
    }
    if (createStaffDto.organizationHistory?.positionId) {
      categoriesToValidate.push({
        id: createStaffDto.organizationHistory.positionId,
        fieldName: 'organizationHistory.positionId',
        expectedType: CATEGORY_TYPE_MAPPING.ORGANIZATION_HISTORY.positionId,
      })
    }
    if (createStaffDto.organizationHistory?.levelId) {
      categoriesToValidate.push({
        id: createStaffDto.organizationHistory.levelId,
        fieldName: 'organizationHistory.levelId',
        expectedType: CATEGORY_TYPE_MAPPING.ORGANIZATION_HISTORY.levelId,
      })
    }

    // Organization history actions
    if (createStaffDto.organizationHistory?.actions) {
      createStaffDto.organizationHistory.actions.forEach((action, index) => {
        categoriesToValidate.push({
          id: action.rewardDisciplineId,
          fieldName: `organizationHistory.actions[${index}].rewardDisciplineId`,
          expectedType: [
            CATEGORY_TYPE_MAPPING.HISTORY_ACTION.disciplineId,
            CATEGORY_TYPE_MAPPING.HISTORY_ACTION.rewardId,
          ],
        })
      })
    }

    // Organization history specializations
    if (createStaffDto.organizationHistory?.specializations) {
      createStaffDto.organizationHistory.specializations.forEach((spec, index) => {
        categoriesToValidate.push({
          id: spec.categoryId,
          fieldName: `organizationHistory.specializations[${index}].categoryId`,
          expectedType: CATEGORY_TYPE_MAPPING.SPECIALIZATION.categoryId,
        })
      })
    }

    // Previous positions
    if (createStaffDto.previousPositions) {
      createStaffDto.previousPositions.forEach((position, index) => {
        categoriesToValidate.push({
          id: position.categoryId,
          fieldName: `previousPositions[${index}].categoryId`,
          expectedType: CATEGORY_TYPE_MAPPING.PREVIOUS_POSITION.categoryId,
        })
      })
    }

    // Training history category fields
    if (createStaffDto.trainingHistory?.trainingTypeId) {
      categoriesToValidate.push({
        id: createStaffDto.trainingHistory.trainingTypeId,
        fieldName: 'trainingHistory.trainingTypeId',
        expectedType: CATEGORY_TYPE_MAPPING.TRAINING.trainingTypeId,
      })
    }
    if (createStaffDto.trainingHistory?.trainingFormatId) {
      categoriesToValidate.push({
        id: createStaffDto.trainingHistory.trainingFormatId,
        fieldName: 'trainingHistory.trainingFormatId',
        expectedType: CATEGORY_TYPE_MAPPING.TRAINING.trainingFormatId,
      })
    }

    // If no categories to validate, return early
    if (categoriesToValidate.length === 0) {
      return
    }

    // Extract unique category IDs to avoid duplicate queries
    const uniqueCategoryIds = [...new Set(categoriesToValidate.map(item => item.id))]

    // Single query to fetch all required categories
    const existingCategories = await this.categoryRepository.findByIds(uniqueCategoryIds)

    // Create a map for fast lookups
    const categoryMap = new Map<string, Category>()

    for (const cat of existingCategories) {
      categoryMap.set(cat.categoryId, cat)
    }

    // Validate all categories in JavaScript
    const errors: string[] = []

    for (const { id, fieldName, expectedType } of categoriesToValidate) {
      const category = categoryMap.get(id)

      if (!category) {
        errors.push(`Không tìm thấy category với ID: ${id} (field: ${fieldName})`)
        continue
      }

      // Check if category is published (active)
      if (category.status !== CategoryStatus.PUBLISHED) {
        errors.push(`Category với ID: ${id} chưa được xuất bản (field: ${fieldName})`)
        continue
      }

      // Check if category has the correct type for this field
      if (typeof expectedType === 'string') {
        if (category.type !== expectedType) {
          errors.push(
            `Category với ID: ${id} có type '${category.type}' nhưng cần type '${expectedType}' (field: ${fieldName})`
          )
        }
      } else if (isArray(expectedType)) {
        if (!expectedType.includes(category.type)) {
          errors.push(
            `Category với ID: ${id} có type '${category.type}' nhưng cần type '${expectedType.join(
              ', '
            )}' (field: ${fieldName})`
          )
        }
      }
    }

    // Throw error with all validation issues at once
    if (errors.length > 0) {
      throw new BadRequestException(`Validation errors: ${errors.join('; ')}`)
    }

    // Also validate organization units in previousPositions using single query
    if (createStaffDto.previousPositions) {
      const orgUnitsToValidate = createStaffDto.previousPositions
        .map((position, index) => ({
          id: position.organizationUnitId,
          fieldName: `previousPositions[${index}].organizationUnitId`,
        }))
        .filter(item => item.id) // Only include positions with organizationUnitId

      if (orgUnitsToValidate.length > 0) {
        const uniqueOrgIds = [...new Set(orgUnitsToValidate.map(item => item.id!))]
        const existingOrgUnits = await this.organizationUnitRepository.findByIds(uniqueOrgIds)
        const orgUnitMap = new Map<string, (typeof existingOrgUnits)[0]>()
        existingOrgUnits.forEach(org => orgUnitMap.set(org.id, org))

        const orgErrors: string[] = []

        for (const { id, fieldName } of orgUnitsToValidate) {
          const orgUnit = orgUnitMap.get(id!)

          if (!orgUnit) {
            orgErrors.push(`Không tìm thấy đơn vị tổ chức với ID: ${id} (field: ${fieldName})`)
            continue
          }

          if (orgUnit.status === -99) {
            orgErrors.push(`Đơn vị tổ chức với ID: ${id} đã bị xóa (field: ${fieldName})`)
          }
        }

        if (orgErrors.length > 0) {
          throw new BadRequestException(`Organization unit validation errors: ${orgErrors.join('; ')}`)
        }
      }
    }
  }

  async create(createStaffDto: CreateStaffDto): Promise<StaffResponseDto> {
    // Validate all category IDs first
    await this.validateCategories(createStaffDto)

    // Validate organization unit if provided
    if (createStaffDto.organizationUnitId) {
      const organizationUnit = await this.organizationUnitRepository.findById(createStaffDto.organizationUnitId)
      if (!organizationUnit) {
        throw new BadRequestException(`Không tìm thấy đơn vị tổ chức với ID: ${createStaffDto.organizationUnitId}`)
      }
      // Check if organization unit is active (not deleted)
      if (organizationUnit.status === -99) {
        throw new BadRequestException(`Đơn vị tổ chức với ID: ${createStaffDto.organizationUnitId} đã bị xóa`)
      }
    }

    // Check for duplicate contract number if provided
    if (createStaffDto.contract?.contractNo) {
      const existingContract = await this.staffContractRepository.findByContractNo(
        createStaffDto.contract.contractNo,
        createStaffDto.organizationUnitId
      )
      if (existingContract) {
        throw new BadRequestException(`Số hợp đồng ${createStaffDto.contract.contractNo} đã tồn tại trong đơn vị`)
      }
    }

    // Check for duplicate card number if provided
    if (createStaffDto.cardHistory?.cardNumber) {
      const existingCard = await this.staffCardHistoryRepository.findByCardNumber(createStaffDto.cardHistory.cardNumber)
      if (existingCard) {
        throw new BadRequestException(`Số thẻ ${createStaffDto.cardHistory.cardNumber} đã tồn tại`)
      }
    }

    // Create staff record
    const { contract, organizationHistory, cardHistory, trainingHistory, previousPositions, ...staffData } =
      createStaffDto
    const createdStaff = await this.staffRepository.create(staffData)

    // Create contract record if contract data provided
    if (contract) {
      const contractData = {
        staffId: createdStaff.id,
        organizationUnitId: createStaffDto.organizationUnitId,
        contractNo: contract.contractNo,
        contractType: contract.contractType,
        startDate: contract.startDate,
        endDate: contract.endDate,
        fileRef: contract.fileRef,
        note: contract.note,
        status: StaffContractStatus.ACTIVE,
      }
      await this.staffContractRepository.create(contractData)
    }

    // Create card history record if card history data provided
    if (cardHistory) {
      // If creating a new card, set all existing cards for this staff as not current
      if (cardHistory.isCurrent === 1 || cardHistory.isCurrent === undefined) {
        await this.staffCardHistoryRepository.setAllNotCurrentForStaff(createdStaff.id)
      }

      const cardHistoryData = {
        staffId: createdStaff.id,
        cardNumber: cardHistory.cardNumber,
        issuedAt: cardHistory.issuedAt,
        status: cardHistory.status || CollabCardStatus.ISSUED,
        isCurrent: cardHistory.isCurrent || 1,
        fileRefFront: cardHistory.fileRefFront,
        fileRefBack: cardHistory.fileRefBack,
        note: cardHistory.note,
      }
      await this.staffCardHistoryRepository.create(cardHistoryData)
    }

    // Create training history record if training history data provided
    if (trainingHistory) {
      const trainingHistoryData = {
        staffId: createdStaff.id,
        startDate: trainingHistory.startDate,
        endDate: trainingHistory.endDate,
        trainingOrganization: trainingHistory.trainingOrganization,
        trainingClassName: trainingHistory.trainingClassName,
        trainingTypeId: trainingHistory.trainingTypeId,
        trainingFormatId: trainingHistory.trainingFormatId,
        certificate: trainingHistory.certificate,
        decisionNumber: trainingHistory.decisionNumber,
        decisionDate: trainingHistory.decisionDate,
        fileRef: trainingHistory.fileRef,
        note: trainingHistory.note,
        createdBy: trainingHistory.createdBy,
        updatedBy: trainingHistory.updatedBy,
      }
      await this.staffTrainingHistoryRepository.create(trainingHistoryData)
    }

    // Create previous positions if provided
    if (previousPositions && previousPositions.length > 0) {
      for (const previousPosition of previousPositions) {
        const previousPositionData = {
          staffId: createdStaff.id,
          categoryId: previousPosition.categoryId,
          organizationUnitId: previousPosition.organizationUnitId,
          startDate: previousPosition.startDate,
          endDate: previousPosition.endDate,
          decisionNumber: previousPosition.decisionNumber,
          decisionDate: previousPosition.decisionDate,
          evidenceFileRef: previousPosition.evidenceFileRef,
          note: previousPosition.note,
        }
        await this.staffPreviousPositionRepository.create(previousPositionData)
      }
    }

    // Create organization history first (needed for other dependent records)
    let createdOrgHistory: { id: string } | null = null
    if (organizationHistory && createStaffDto.organizationUnitId) {
      const { actions, events, specializations, roles, ...orgHistoryData } = organizationHistory
      const organizationHistoryData = {
        staffId: createdStaff.id,
        organizationUnitId: createStaffDto.organizationUnitId,
        positionId: orgHistoryData.positionId,
        levelId: orgHistoryData.levelId,
        legalAidFormId: orgHistoryData.legalAidFormId,
        isHead: orgHistoryData.isHead || 0,
        startDate: orgHistoryData.startDate,
        endDate: orgHistoryData.endDate,
        status: orgHistoryData.status || 1, // Default to active
        decisionNumber: orgHistoryData.decisionNumber,
        decisionDate: orgHistoryData.decisionDate,
        decisionAuthority: orgHistoryData.decisionAuthority,
        reason: orgHistoryData.reason,
        note: orgHistoryData.note,
        isProbation: orgHistoryData.isProbation || 0,
        probationStartDate: orgHistoryData.probationStartDate,
        probationDurationMonths: orgHistoryData.probationDurationMonths,
        probationResult: orgHistoryData.probationResult,
        probationAssessmentStatus: orgHistoryData.probationAssessmentStatus,
        probationExemptionReason: orgHistoryData.probationExemptionReason,
        probationAttachments: orgHistoryData.probationAttachments,
      }
      createdOrgHistory = await this.staffOrganizationHistoryRepository.create(organizationHistoryData)

      // Create nested history actions
      if (actions && actions.length > 0) {
        for (const historyAction of actions) {
          await this.staffOrgHistoryActionService.createStaffOrganizationHistoryAction(createdOrgHistory.id, {
            rewardDisciplineId: historyAction.rewardDisciplineId,
          })
        }
      }

      // Create nested org events
      if (events && events.length > 0) {
        for (const event of events) {
          const eventData = {
            historyId: createdOrgHistory.id,
            eventType: event.eventType,
            eventDate: event.eventDate,
            termYears: event.termYears,
            decisionNumber: event.decisionNumber,
            decisionDate: event.decisionDate,
            decisionAuthority: event.decisionAuthority,
            reason: event.reason,
            note: event.note,
            fileRef: event.fileRef,
          }
          await this.staffOrgEventRepository.create(eventData)
        }
      }

      // Create nested specializations
      if (specializations && specializations.length > 0) {
        for (const specialization of specializations) {
          const specializationData = {
            staffOrganizationId: createdOrgHistory.id,
            categoryId: specialization.categoryId,
            level: specialization.level,
            certificateNo: specialization.certificateNo,
            certIssuedAt: specialization.certIssuedAt,
            certExpiresAt: specialization.certExpiresAt,
            note: specialization.note,
          }
          await this.staffSpecializationRepository.create(specializationData)
        }
      }

      // Create staff organizational roles
      if (roles && roles.length > 0) {
        for (const role of roles) {
          const roleData = {
            staffOrgId: createdOrgHistory.id,
            role: role,
          }
          await this.staffOrgRoleRepository.create(roleData)
        }
      }
    }

    // Fetch the created staff with relations for proper DTO mapping
    const staffWithRelations = await this.staffRepository.findById(createdStaff.id)
    return mapStaffToResponseDto(staffWithRelations!)
  }

  async findAllAndCount(
    query: GetListQueryStaffDto
  ): Promise<{ items: StaffResponseDto[]; total: number; totalPages: number }> {
    // Process filter to handle date ranges and other special formats
    const processedQuery = {
      ...query,
      filter: processFilter(query.filter as Record<string, unknown> | undefined),
    }
    // Expect nested filter[activityStatusId] from FE; do not map top-level params here
    const result = await this.staffRepository.findAllAndCountWithComplexFilters(processedQuery, findManyOptions)
    // Stitch current organization history into organizationUnit payload
    const staffIds = result.items.map(s => s.id)
    const allHistories = await this.staffOrganizationHistoryRepository.findByStaffIds(staffIds)
    const currentHistories = allHistories.filter(h => h.status === 1)
    const latestByStaff = new Map<string, (typeof currentHistories)[number]>()
    for (const hist of currentHistories) {
      const existing = latestByStaff.get(hist.staffId)
      if (!existing || (hist.startDate && existing.startDate && hist.startDate > existing.startDate)) {
        latestByStaff.set(hist.staffId, hist)
      }
    }

    const items = result.items.map(staff => {
      const dto = mapStaffToResponseDto(staff)
      const hist = latestByStaff.get(staff.id)
      if (dto.organizationUnit && hist) {
        dto.organizationUnit.positionId = hist.positionId
        dto.organizationUnit.position = hist.position
          ? {
              categoryId: hist.position.categoryId,
              name: hist.position.name,
              code: hist.position.code,
              type: hist.position.type,
            }
          : undefined
        dto.organizationUnit.levelId = hist.levelId
        dto.organizationUnit.level = hist.level
          ? {
              categoryId: hist.level.categoryId,
              name: hist.level.name,
              code: hist.level.code,
              type: hist.level.type,
            }
          : undefined
        dto.organizationUnit.isHead = hist.isHead
        dto.organizationUnit.startDate = hist.startDate
        dto.organizationUnit.endDate = hist.endDate
        dto.organizationUnit.status = hist.status
        dto.organizationUnit.roles = hist.staffOrgRoles?.map(r => r.role)
      }
      // Attach full organization history array for FE to choose current (always include array)
      const historiesForStaff = allHistories.filter(h => h.staffId === staff.id)
      dto.organizationHistory = historiesForStaff.map(h => ({
        id: h.id,
        staffId: h.staffId,
        organizationUnitId: h.organizationUnitId,
        positionId: h.positionId,
        position: h.position
          ? { categoryId: h.position.categoryId, name: h.position.name, code: h.position.code, type: h.position.type }
          : undefined,
        levelId: h.levelId,
        level: h.level
          ? { categoryId: h.level.categoryId, name: h.level.name, code: h.level.code, type: h.level.type }
          : undefined,
        legalAidFormId: h.legalAidFormId,
        legalAidForm: h.legalAidForm
          ? {
              categoryId: h.legalAidForm.categoryId,
              name: h.legalAidForm.name,
              code: h.legalAidForm.code,
              type: h.legalAidForm.type,
            }
          : undefined,
        isHead: h.isHead,
        startDate: h.startDate,
        endDate: h.endDate,
        status: h.status,
        decisionNumber: h.decisionNumber,
        decisionDate: h.decisionDate,
        decisionAuthority: h.decisionAuthority,
        reason: h.reason,
        note: h.note,
        isProbation: h.isProbation,
        probationStartDate: h.probationStartDate,
        probationDurationMonths: h.probationDurationMonths,
        probationResult: h.probationResult,
        probationAssessmentStatus: h.probationAssessmentStatus,
        probationExemptionReason: h.probationExemptionReason,
        probationAttachments: h.probationAttachments,
        roles: h.staffOrgRoles?.map(r => r.role),
        specializations: h.specializations?.map(s => ({
          id: s.id,
          staffOrganizationId: s.staffOrganizationId,
          categoryId: s.categoryId,
          specialization: s.specialization
            ? {
                categoryId: s.specialization.categoryId,
                name: s.specialization.name,
                code: s.specialization.code,
                type: s.specialization.type,
              }
            : undefined,
          level: s.level,
          certificateNo: s.certificateNo,
          certIssuedAt: s.certIssuedAt,
          certExpiresAt: s.certExpiresAt,
          note: s.note,
        })),
      }))
      return dto
    })

    return { items, total: result.total, totalPages: result.totalPages }
  }

  async findAll(query: GetListQueryBaseAllDto): Promise<StaffResponseDto[]> {
    // Bracket-style params are normalized in DTO; just process
    const extraFilter = extractFilterFromRawQuery(undefined)
    const mergedFilter = {
      ...(query.filter as Record<string, unknown> | undefined),
      ...extraFilter,
    }
    const processedQuery = {
      ...query,
      filter: processFilter(mergedFilter),
    }
    // Expect nested filter[activityStatusId] from FE; do not map top-level params here
    const items = await this.staffRepository.findAllWithComplexFilters(
      processedQuery as unknown as GetListQueryStaffDto,
      findManyOptions
    )

    // Stitch current organization history into organizationUnit payload
    const staffIds = items.map(s => s.id)
    const allHistories = await this.staffOrganizationHistoryRepository.findByStaffIds(staffIds)
    const currentHistories = allHistories.filter(h => h.status === 1)
    const latestByStaff = new Map<string, (typeof currentHistories)[number]>()
    for (const hist of currentHistories) {
      const existing = latestByStaff.get(hist.staffId)
      if (!existing || (hist.startDate && existing.startDate && hist.startDate > existing.startDate)) {
        latestByStaff.set(hist.staffId, hist)
      }
    }

    return items.map(staff => {
      const dto = mapStaffToResponseDto(staff)
      const hist = latestByStaff.get(staff.id)
      if (dto.organizationUnit && hist) {
        dto.organizationUnit.positionId = hist.positionId
        dto.organizationUnit.position = hist.position
          ? {
              categoryId: hist.position.categoryId,
              name: hist.position.name,
              code: hist.position.code,
              type: hist.position.type,
            }
          : undefined
        dto.organizationUnit.levelId = hist.levelId
        dto.organizationUnit.level = hist.level
          ? {
              categoryId: hist.level.categoryId,
              name: hist.level.name,
              code: hist.level.code,
              type: hist.level.type,
            }
          : undefined
        dto.organizationUnit.isHead = hist.isHead
        dto.organizationUnit.startDate = hist.startDate
        dto.organizationUnit.endDate = hist.endDate
        dto.organizationUnit.status = hist.status
      }
      // Attach full organization history array for FE to choose current
      const historiesForStaff = allHistories.filter(h => h.staffId === staff.id)
      if (historiesForStaff.length) {
        dto.organizationHistory = historiesForStaff.map(h => ({
          id: h.id,
          staffId: h.staffId,
          organizationUnitId: h.organizationUnitId,
          positionId: h.positionId,
          position: h.position
            ? { categoryId: h.position.categoryId, name: h.position.name, code: h.position.code, type: h.position.type }
            : undefined,
          levelId: h.levelId,
          level: h.level
            ? { categoryId: h.level.categoryId, name: h.level.name, code: h.level.code, type: h.level.type }
            : undefined,
          legalAidFormId: h.legalAidFormId,
          legalAidForm: h.legalAidForm
            ? {
                categoryId: h.legalAidForm.categoryId,
                name: h.legalAidForm.name,
                code: h.legalAidForm.code,
                type: h.legalAidForm.type,
              }
            : undefined,
          isHead: h.isHead,
          startDate: h.startDate,
          endDate: h.endDate,
          status: h.status,
          decisionNumber: h.decisionNumber,
          decisionDate: h.decisionDate,
          decisionAuthority: h.decisionAuthority,
          reason: h.reason,
          note: h.note,
          isProbation: h.isProbation,
          probationStartDate: h.probationStartDate,
          probationDurationMonths: h.probationDurationMonths,
          probationResult: h.probationResult,
          probationAssessmentStatus: h.probationAssessmentStatus,
          probationExemptionReason: h.probationExemptionReason,
          probationAttachments: h.probationAttachments,
          roles: h.staffOrgRoles?.map(r => r.role),
          specializations: h.specializations?.map(s => ({
            id: s.id,
            staffOrganizationId: s.staffOrganizationId,
            categoryId: s.categoryId,
            specialization: s.specialization
              ? {
                  categoryId: s.specialization.categoryId,
                  name: s.specialization.name,
                  code: s.specialization.code,
                  type: s.specialization.type,
                }
              : undefined,
            level: s.level,
            certificateNo: s.certificateNo,
            certIssuedAt: s.certIssuedAt,
            certExpiresAt: s.certExpiresAt,
            note: s.note,
          })),
        }))
      }
      console.log(allHistories, 'allHistories')
      return dto
    })
  }

  async findOne(staffId: string): Promise<StaffResponseDto> {
    const staff = await this.staffRepository.findById(staffId)
    if (!staff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }
    const dto = mapStaffToResponseDto(staff)
    const hist = await this.staffOrganizationHistoryRepository.findLatestCurrentByStaffId(staffId)
    if (dto.organizationUnit && hist) {
      dto.organizationUnit.positionId = hist.positionId
      dto.organizationUnit.position = hist.position
        ? {
            categoryId: hist.position.categoryId,
            name: hist.position.name,
            code: hist.position.code,
            type: hist.position.type,
          }
        : undefined
      dto.organizationUnit.levelId = hist.levelId
      dto.organizationUnit.level = hist.level
        ? {
            categoryId: hist.level.categoryId,
            name: hist.level.name,
            code: hist.level.code,
            type: hist.level.type,
          }
        : undefined
      dto.organizationUnit.isHead = hist.isHead
      dto.organizationUnit.startDate = hist.startDate
      dto.organizationUnit.endDate = hist.endDate
      dto.organizationUnit.status = hist.status
      dto.organizationUnit.roles = hist.staffOrgRoles?.map(r => r.role)
    }
    // Attach full organization history array
    const allHistories = await this.staffOrganizationHistoryRepository.findAllByStaffId(staffId)
    if (allHistories?.length) {
      dto.organizationHistory = allHistories.map(h => ({
        id: h.id,
        staffId: h.staffId,
        organizationUnitId: h.organizationUnitId,
        positionId: h.positionId,
        position: h.position
          ? { categoryId: h.position.categoryId, name: h.position.name, code: h.position.code, type: h.position.type }
          : undefined,
        levelId: h.levelId,
        level: h.level
          ? { categoryId: h.level.categoryId, name: h.level.name, code: h.level.code, type: h.level.type }
          : undefined,
        legalAidFormId: h.legalAidFormId,
        legalAidForm: h.legalAidForm
          ? {
              categoryId: h.legalAidForm.categoryId,
              name: h.legalAidForm.name,
              code: h.legalAidForm.code,
              type: h.legalAidForm.type,
            }
          : undefined,
        isHead: h.isHead,
        startDate: h.startDate,
        endDate: h.endDate,
        status: h.status,
        decisionNumber: h.decisionNumber,
        decisionDate: h.decisionDate,
        decisionAuthority: h.decisionAuthority,
        reason: h.reason,
        note: h.note,
        isProbation: h.isProbation,
        probationStartDate: h.probationStartDate,
        probationDurationMonths: h.probationDurationMonths,
        probationResult: h.probationResult,
        probationAssessmentStatus: h.probationAssessmentStatus,
        probationExemptionReason: h.probationExemptionReason,
        probationAttachments: h.probationAttachments,
        roles: h.staffOrgRoles?.map(r => r.role),
        specializations: h.specializations?.map(s => ({
          id: s.id,
          staffOrganizationId: s.staffOrganizationId,
          categoryId: s.categoryId,
          specialization: s.specialization
            ? {
                categoryId: s.specialization.categoryId,
                name: s.specialization.name,
                code: s.specialization.code,
                type: s.specialization.type,
              }
            : undefined,
          level: s.level,
          certificateNo: s.certificateNo,
          certIssuedAt: s.certIssuedAt,
          certExpiresAt: s.certExpiresAt,
          note: s.note,
        })),
      }))
    }

    return dto
  }

  async update(staffId: string, updateStaffDto: UpdateStaffDto) {
    // Check if staff exists
    const existingStaff = await this.staffRepository.findById(staffId)
    if (!existingStaff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    // Validate organization unit if being updated
    if (updateStaffDto.organizationUnitId) {
      const organizationUnit = await this.organizationUnitRepository.findById(updateStaffDto.organizationUnitId)
      if (!organizationUnit) {
        throw new BadRequestException(`Không tìm thấy đơn vị tổ chức với ID: ${updateStaffDto.organizationUnitId}`)
      }
      // Check if organization unit is active (not deleted)
      if (organizationUnit.status === -99) {
        throw new BadRequestException(`Đơn vị tổ chức với ID: ${updateStaffDto.organizationUnitId} đã bị xóa`)
      }
    }

    const result = await this.staffRepository.update(staffId, updateStaffDto)
    return { affected: result.affected ?? undefined }
  }

  async remove(staffId: string) {
    // Check if staff exists
    const existingStaff = await this.staffRepository.findById(staffId)
    if (!existingStaff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    const result = await this.staffRepository.softRemove(staffId)
    return { affected: result.affected ?? undefined }
  }

  async findOneDetail(staffId: string): Promise<StaffDetailResponseDto> {
    // Get basic staff information first
    const staff = await this.staffRepository.findById(staffId)
    if (!staff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    // Convert to base response DTO and extend with detail information
    const dto = (await this.findOne(staffId)) as StaffDetailResponseDto

    try {
      // Get contracts
      const contracts = await this.staffContractRepository.findDetailedByStaffId(staffId)
      if (contracts?.length) {
        dto.contracts = contracts.map(c => ({
          id: c.id,
          contractNo: c.contractNo,
          contractType: c.contractType,
          startDate: c.startDate,
          endDate: c.endDate,
          status: c.status,
          organizationUnitId: c.organizationUnitId,
          organizationUnit: c.organizationUnit
            ? {
                id: c.organizationUnit.id,
                name: c.organizationUnit.name,
                code: c.organizationUnit.code,
                type: c.organizationUnit.type || 0,
              }
            : undefined,
          fileRef: c.fileRef,
          fileId: c.fileId,
          file: c.file
            ? {
                id: c.file.id,
                bucket: c.file.bucket,
                objectKey: c.file.objectKey,
                publicUrl: c.file.publicUrl,
                contentType: c.file.contentType,
              }
            : undefined,
          note: c.note,
          createdAt: c.createdAt,
          updatedAt: c.updatedAt,
        }))
      }
    } catch (error) {
      console.warn('Error getting contracts:', error.message)
      dto.contracts = []
    }

    try {
      // Get card history
      const cardHistory = await this.staffCardHistoryRepository.findByStaffId(staffId)
      if (cardHistory?.length) {
        dto.cardHistory = cardHistory.map(ch => ({
          id: ch.id,
          cardNumber: ch.cardNumber,
          cardType: 'CTV', // Default card type for collaborator cards
          issuedDate: ch.issuedAt, // Using correct field name issuedAt
          expiryDate: undefined, // Field doesn't exist in current entity
          status: ch.status,
          note: ch.note,
          createdAt: ch.createdAt,
          updatedAt: ch.updatedAt,
        }))
      }
    } catch (error) {
      console.warn('Error getting card history:', error.message)
      dto.cardHistory = []
    }

    try {
      // Get training history
      const trainingHistory = await this.staffTrainingHistoryRepository.findByStaffId(staffId)
      if (trainingHistory?.length) {
        dto.trainingHistory = trainingHistory.map(th => ({
          id: th.id,
          staffId: th.staffId || staffId, // Ensure staffId is not undefined
          trainingClassId: th.trainingClassId || '', // Ensure trainingClassId is not undefined
          trainingClass: th.trainingClass
            ? {
                id: th.trainingClass.id,
                courseName: th.trainingClass.trainingClassName || th.trainingClass.description || '',
                description: th.trainingClass.description,
                startDate: th.trainingClass.startDate,
                endDate: th.trainingClass.endDate || th.trainingClass.startDate, // Ensure endDate is not undefined
              }
            : undefined,
          registrationDate: th.registrationDate || th.createdAt, // Ensure registrationDate is not undefined
          note: '', // Note field might not exist in StaffTrainingHistory
          createdAt: th.createdAt,
          updatedAt: th.updatedAt,
        }))
      }
    } catch (error) {
      console.warn('Error getting training history:', error.message)
      dto.trainingHistory = []
    }

    try {
      // Get previous positions
      const previousPositions = await this.staffPreviousPositionRepository.findByStaffId(staffId)
      if (previousPositions?.length) {
        dto.previousPositions = previousPositions.map(pp => ({
          id: pp.id,
          staffId: pp.staffId,
          categoryId: pp.categoryId,
          category: pp.category
            ? {
                categoryId: pp.category.categoryId,
                name: pp.category.name,
                code: pp.category.code || '', // Ensure code is not undefined
                type: pp.category.type,
              }
            : undefined,
          organizationUnitId: pp.organizationUnitId || '', // Ensure organizationUnitId is not undefined
          organizationUnit: pp.organizationUnit
            ? {
                id: pp.organizationUnit.id,
                name: pp.organizationUnit.name,
                code: pp.organizationUnit.code,
                type: pp.organizationUnit.type || 0,
              }
            : undefined,
          startDate: pp.startDate || pp.createdAt, // Ensure startDate is not undefined
          endDate: pp.endDate,
          decisionNumber: pp.decisionNumber,
          decisionDate: pp.decisionDate,
          evidenceFileRef: pp.evidenceFileRef,
          note: pp.note,
          createdAt: pp.createdAt,
          updatedAt: pp.updatedAt,
        }))
      }
    } catch (error) {
      console.warn('Error getting previous positions:', error.message)
      dto.previousPositions = []
    }

    try {
      // Get assigned user
      const assignedUser = await this.userRepository.findOne({ staffId })
      if (assignedUser) {
        dto.assignedUser = {
          id: assignedUser.id,
          username: assignedUser.email, // Using email as username since User entity uses email as unique identifier
          email: assignedUser.email,
          phone: assignedUser.phone,
          isActive: assignedUser.status === UserStatus.ACTIVE, // Use proper enum comparison
          createdAt: assignedUser.createdAt,
        }
      }
    } catch (error) {
      console.warn('Error getting assigned user:', error.message)
    }

    try {
      // Get organization history
      const organizationHistory = await this.staffOrganizationHistoryRepository.findAllByStaffId(staffId)
      if (organizationHistory?.length) {
        // Get all organization history IDs
        const orgHistoryIds = organizationHistory.map(oh => oh.id)

        // Get all actions for these organization histories
        const allActionsPromises = orgHistoryIds.map(async id => {
          const result = await this.staffOrgHistoryActionService.getStaffOrganizationHistoryActions(id)
          return { historyId: id, actions: result.actions }
        })
        const allActionsArrays = await Promise.all(allActionsPromises)
        const actionsByHistoryId = new Map(allActionsArrays.map(item => [item.historyId, item.actions]))

        // Get all events for these organization histories (fetch for each history ID)
        const allEventsPromises = orgHistoryIds.map(id => this.staffOrgEventRepository.findByHistoryId(id))
        const allEventsArrays = await Promise.all(allEventsPromises)
        const allEvents = allEventsArrays.flat()
        dto.organizationHistory = organizationHistory.map(oh => ({
          id: oh.id,
          staffId: oh.staffId,
          organizationUnitId: oh.organizationUnitId,
          organizationUnit: oh.organizationUnit
            ? {
                organizationUnitId: oh.organizationUnit.id,
                name: oh.organizationUnit.name,
                code: oh.organizationUnit.code,
                type: oh.organizationUnit.type?.toString() || '0',
              }
            : undefined,
          positionId: oh.positionId,
          position: oh.position
            ? {
                categoryId: oh.position.categoryId,
                name: oh.position.name,
                code: oh.position.code || '',
                type: oh.position.type,
              }
            : undefined,
          levelId: oh.levelId,
          level: oh.level
            ? {
                categoryId: oh.level.categoryId,
                name: oh.level.name,
                code: oh.level.code || '',
                type: oh.level.type,
              }
            : undefined,
          legalAidFormId: oh.legalAidFormId,
          legalAidForm: oh.legalAidForm
            ? {
                categoryId: oh.legalAidForm.categoryId,
                name: oh.legalAidForm.name,
                code: oh.legalAidForm.code || '',
                type: oh.legalAidForm.type,
              }
            : undefined,
          isHead: oh.isHead,
          startDate: oh.startDate,
          endDate: oh.endDate,
          status: oh.status,
          decisionNumber: oh.decisionNumber,
          decisionDate: oh.decisionDate,
          decisionAuthority: oh.decisionAuthority,
          reason: oh.reason,
          note: oh.note,
          isProbation: oh.isProbation,
          probationStartDate: oh.probationStartDate,
          probationDurationMonths: oh.probationDurationMonths,
          probationResult: oh.probationResult,
          probationAssessmentStatus: oh.probationAssessmentStatus,
          probationExemptionReason: oh.probationExemptionReason,
          probationAttachments: oh.probationAttachments,
          roles: oh.staffOrgRoles?.map(role => role.role) || [],
          specializations:
            oh.specializations?.map(spec => ({
              id: spec.id,
              staffOrganizationId: spec.staffOrganizationId,
              categoryId: spec.categoryId,
              specialization: spec.specialization
                ? {
                    categoryId: spec.specialization.categoryId,
                    name: spec.specialization.name,
                    code: spec.specialization.code || '',
                    type: spec.specialization.type,
                  }
                : undefined,
              level: spec.level,
              certificateNo: spec.certificateNo,
              certIssuedAt: spec.certIssuedAt,
              certExpiresAt: spec.certExpiresAt,
              note: spec.note,
            })) || [],
          // Add actions for this organization history
          actions:
            actionsByHistoryId.get(oh.id)?.map(action => ({
              id: action.id,
              historyId: oh.id, // Add the historyId from the current organization history
              rewardDisciplineId: action.rewardDisciplineId,
              rewardDiscipline: action.rewardDiscipline
                ? {
                    categoryId: action.rewardDiscipline.categoryId,
                    name: action.rewardDiscipline.name,
                    code: action.rewardDiscipline.code || '',
                    type: action.rewardDiscipline.type,
                  }
                : undefined,
              createdAt: action.createdAt,
              updatedAt: action.updatedAt,
            })) || [],
          // Add events for this organization history
          events:
            allEvents
              .filter(event => event.historyId === oh.id)
              .map(event => ({
                id: event.id,
                historyId: event.historyId,
                eventType: event.eventType,
                eventDate: event.eventDate,
                termYears: event.termYears,
                decisionNumber: event.decisionNumber,
                decisionDate: event.decisionDate,
                decisionAuthority: event.decisionAuthority,
                reason: event.reason,
                note: event.note,
                fileRef: event.fileRef,
                createdAt: event.createdAt,
                updatedAt: event.updatedAt,
              })) || [],
        }))
      }
    } catch (error) {
      console.warn('Error getting organization history:', error.message)
      dto.organizationHistory = []
    }

    return dto
  }

  async updateFields(staffId: string, updateStaffFieldDto: UpdateStaffFieldDto): Promise<StaffResponseDto> {
    // Validate staff exists
    const existingStaff = await this.staffRepository.findById(staffId)
    if (!existingStaff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    // Validate category IDs if provided (similar to create method but only for provided fields)
    const categoriesToValidate: { id: string; fieldName: string; expectedType: string }[] = []

    if (updateStaffFieldDto.genderId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.genderId,
        fieldName: 'genderId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.genderId,
      })
    }

    if (updateStaffFieldDto.ethnicId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.ethnicId,
        fieldName: 'ethnicId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.ethnicId,
      })
    }

    if (updateStaffFieldDto.provinceId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.provinceId,
        fieldName: 'provinceId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.provinceId,
      })
    }

    if (updateStaffFieldDto.districtId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.districtId,
        fieldName: 'districtId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.districtId,
      })
    }

    if (updateStaffFieldDto.wardId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.wardId,
        fieldName: 'wardId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.wardId,
      })
    }

    if (updateStaffFieldDto.professionalLevelId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.professionalLevelId,
        fieldName: 'professionalLevelId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.professionalLevelId,
      })
    }

    if (updateStaffFieldDto.activityStatusId) {
      categoriesToValidate.push({
        id: updateStaffFieldDto.activityStatusId,
        fieldName: 'activityStatusId',
        expectedType: CATEGORY_TYPE_MAPPING.STAFF.activityStatusId,
      })
    }

    // Validate categories
    if (categoriesToValidate.length > 0) {
      const categoryIds = categoriesToValidate.map(c => c.id)
      const categories = await this.categoryRepository.findByIds(categoryIds)

      const errors: string[] = []

      for (const categoryToValidate of categoriesToValidate) {
        const category = categories.find(c => c.categoryId === categoryToValidate.id)

        if (!category) {
          errors.push(`${categoryToValidate.fieldName}: Category không tồn tại`)
          continue
        }

        if (category.status !== CategoryStatus.PUBLISHED) {
          errors.push(`${categoryToValidate.fieldName}: Category không hoạt động`)
          continue
        }

        if (category.type !== categoryToValidate.expectedType) {
          errors.push(
            `${categoryToValidate.fieldName}: Category type không đúng (expected: ${categoryToValidate.expectedType}, actual: ${category.type})`
          )
        }
      }

      if (errors.length > 0) {
        throw new BadRequestException(`Validation failed: ${errors.join(', ')}`)
      }
    }

    // Validate organization unit if provided
    if (updateStaffFieldDto.organizationUnitId) {
      const organizationUnit = await this.organizationUnitRepository.findById(updateStaffFieldDto.organizationUnitId)
      if (!organizationUnit) {
        throw new NotFoundException(`Không tìm thấy đơn vị với ID: ${updateStaffFieldDto.organizationUnitId}`)
      }
    }

    // Set updatedBy if not provided
    if (!updateStaffFieldDto.updatedBy) {
      updateStaffFieldDto.updatedBy = 'system' // or get from authentication context
    }

    // Handle user assignment/unassignment if userId is provided
    if ('userId' in updateStaffFieldDto) {
      const { userId } = updateStaffFieldDto

      if (userId === null || userId === undefined || userId === '') {
        // Unassign user from staff
        const currentUser = await this.userRepository.findOne({ staffId })
        console.log(currentUser, 'currentUser')
        if (currentUser) {
          await this.userRepository.update(currentUser.id, { staffId: null })
        }
      } else {
        // Assign user to staff
        const userToAssign = await this.userRepository.findById(userId)
        if (!userToAssign) {
          throw new NotFoundException(`Không tìm thấy người dùng với ID: ${userId}`)
        }

        // Check if user is already assigned to another staff
        if (userToAssign.staffId && userToAssign.staffId !== staffId) {
          const assignedStaff = await this.staffRepository.findById(userToAssign.staffId)
          throw new BadRequestException({
            message: `Tài khoản đã được gán cho nhân viên khác`,
            conflictStaff: {
              id: assignedStaff?.id,
              fullName: assignedStaff?.fullName,
            },
          })
        }

        // Assign user to staff
        await this.userRepository.update(userId, { staffId })
      }

      // Remove userId from the update data since it's not a staff field
      delete updateStaffFieldDto.userId
    }

    // Update the staff record
    await this.staffRepository.update(staffId, updateStaffFieldDto)

    // Return updated staff data
    return this.findOne(staffId)
  }
}
