import { IsDateString, IsEnum, IsNotEmpty, Is<PERSON><PERSON>ber, IsOptional, IsString } from '@ac/common'
import {
  CollabCardStatus,
  ProbationAssessmentStatus,
  ProbationResult,
  StaffBusinessRole,
  StaffContractStatus,
  StaffContractType,
  StaffOrgEventType,
} from '@ac/models'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsIn, ValidateNested } from 'class-validator'

const COLLAB_CARD_STATUS = ['ISSUED', 'REISSUED', 'REVOKED', 'LOST', 'EXPIRED'] as const

export class CreateStaffContractDetailDto {
  @ApiProperty({
    description: 'Số hợp đồng',
    example: 'HD-2024-001',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  contractNo!: string

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hợp đồng',
    example: StaffContractType.LABOR,
    enum: StaffContractType,
  })
  @IsString()
  @IsNotEmpty()
  contractType!: StaffContractType

  @ApiProperty({
    description: 'Ngày bắt đầu hợp đồng',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsNotEmpty()
  startDate!: Date

  @ApiPropertyOptional({
    description: 'Ngày kết thúc hợp đồng',
    example: '2024-12-31T23:59:59Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  endDate?: Date

  @ApiPropertyOptional({
    description: 'File đính kèm hợp đồng',
    example: 'contracts/HD-2024-001.pdf',
    type: String,
  })
  @IsString()
  @IsOptional()
  fileRef?: string

  @ApiPropertyOptional({
    description: 'Ghi chú về hợp đồng',
    example: 'Hợp đồng lao động có thời hạn 1 năm',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string

  @ApiPropertyOptional({
    description: 'Trạng thái hợp đồng',
    example: StaffContractStatus.ACTIVE,
    enum: StaffContractStatus,
    default: StaffContractStatus.ACTIVE,
  })
  @IsString()
  @IsOptional()
  status?: StaffContractStatus
}

class CreateStaffOrgHistoryActionDto {
  @ApiProperty({
    description: 'ID hành động khen thưởng/kỷ luật (Category)',
    example: '550e8400-e29b-41d4-a716-446655440010',
    type: String,
  })
  @IsNotEmpty()
  rewardDisciplineId!: string
}

class CreateStaffOrgEventDto {
  @ApiProperty({
    description: 'Loại sự kiện tổ chức',
    example: 'APPOINTMENT',
    enum: StaffOrgEventType,
  })
  @IsString()
  @IsNotEmpty()
  eventType!: StaffOrgEventType

  @ApiProperty({
    description: 'Ngày diễn ra sự kiện',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsNotEmpty()
  eventDate!: Date

  @ApiPropertyOptional({
    description: 'Thời hạn bổ nhiệm (năm)',
    example: 3,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  termYears?: number

  @ApiPropertyOptional({
    description: 'Số quyết định',
    example: 'QD-BN-2024-001',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionNumber?: string

  @ApiPropertyOptional({
    description: 'Ngày ra quyết định',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  decisionDate?: Date

  @ApiPropertyOptional({
    description: 'Cơ quan ra quyết định',
    example: 'Ban Giám đốc',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionAuthority?: string

  @ApiPropertyOptional({
    description: 'Lý do',
    example: 'Bổ nhiệm do năng lực xuất sắc',
    type: String,
  })
  @IsString()
  @IsOptional()
  reason?: string

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Bổ nhiệm thêm nhiệm vụ quản lý',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string

  @ApiPropertyOptional({
    description: 'File đính kèm',
    example: 'decisions/appointment-2024-001.pdf',
    type: String,
  })
  @IsString()
  @IsOptional()
  fileRef?: string
}

export class CreateStaffPreviousPositionDto {
  @ApiProperty({
    description: 'ID loại vị trí trước đây (Category)',
    example: '550e8400-e29b-41d4-a716-446655440011',
    type: String,
  })
  @IsNotEmpty()
  categoryId!: string

  @ApiPropertyOptional({
    description: 'ID đơn vị tổ chức trước đây',
    example: '550e8400-e29b-41d4-a716-446655440012',
    type: String,
  })
  @IsOptional()
  organizationUnitId?: string

  @ApiPropertyOptional({
    description: 'Ngày bắt đầu làm việc ở vị trí trước',
    example: '2020-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  startDate?: Date

  @ApiPropertyOptional({
    description: 'Ngày kết thúc làm việc ở vị trí trước',
    example: '2023-12-31T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  endDate?: Date

  @ApiPropertyOptional({
    description: 'Số quyết định',
    example: 'QD-2020-001',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionNumber?: string

  @ApiPropertyOptional({
    description: 'Ngày ra quyết định',
    example: '2020-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  decisionDate?: Date

  @ApiPropertyOptional({
    description: 'File minh chứng',
    example: 'evidence/previous-position-001.pdf',
    type: String,
  })
  @IsString()
  @IsOptional()
  evidenceFileRef?: string

  @ApiPropertyOptional({
    description: 'Ghi chú về vị trí trước',
    example: 'Làm việc tại công ty ABC trước khi chuyển về',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string
}

class CreateStaffSpecializationDto {
  @ApiProperty({
    description: 'ID chuyên môn (Category)',
    example: '550e8400-e29b-41d4-a716-446655440013',
    type: String,
  })
  @IsNotEmpty()
  categoryId!: string

  @ApiPropertyOptional({
    description: 'Cấp độ chuyên môn',
    example: 'Senior',
  })
  @IsString()
  @IsOptional()
  level?: string

  @ApiPropertyOptional({
    description: 'Số chứng chỉ',
    example: 'CERT-2024-001',
    type: String,
  })
  @IsString()
  @IsOptional()
  certificateNo?: string

  @ApiPropertyOptional({
    description: 'Ngày cấp chứng chỉ',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  certIssuedAt?: Date

  @ApiPropertyOptional({
    description: 'Ngày hết hạn chứng chỉ',
    example: '2026-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  certExpiresAt?: Date

  @ApiPropertyOptional({
    description: 'Ghi chú về chuyên môn',
    example: 'Chuyên môn về phát triển phần mềm Java',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string
}

export class CreateStaffOrganizationHistoryDto {
  @ApiPropertyOptional({
    description: 'ID chức danh vị trí (Category)',
    example: '550e8400-e29b-41d4-a716-446655440007',
    type: String,
  })
  @IsOptional()
  positionId?: string

  @ApiPropertyOptional({
    description: 'ID cấp bậc (Category)',
    example: '550e8400-e29b-41d4-a716-446655440009',
    type: String,
  })
  @IsOptional()
  levelId?: string

  @ApiPropertyOptional({
    description: 'ID loại form trợ giúp pháp lý (Category)',
    example: '550e8400-e29b-41d4-a716-446655440008',
    type: String,
  })
  @IsOptional()
  legalAidFormId?: string

  @ApiPropertyOptional({
    description: 'Có phải là người đứng đầu không (1=có, 0=không)',
    example: 0,
    type: Number,
    default: 0,
  })
  @IsNumber()
  @IsIn([0, 1])
  @IsOptional()
  isHead?: number

  @ApiPropertyOptional({
    description: 'Ngày bắt đầu công tác',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  startDate?: Date

  @ApiPropertyOptional({
    description: 'Ngày kết thúc công tác',
    example: '2024-12-31T23:59:59Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  endDate?: Date

  @ApiPropertyOptional({
    description: 'Trạng thái (1=hiện tại/hoạt động, 0=đã kết thúc)',
    example: 1,
    type: Number,
    default: 1,
  })
  @IsNumber()
  @IsOptional()
  status?: number

  @ApiPropertyOptional({
    description: 'Số quyết định',
    example: 'QD-2024-001',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionNumber?: string

  @ApiPropertyOptional({
    description: 'Ngày ra quyết định',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  decisionDate?: Date

  @ApiPropertyOptional({
    description: 'Cơ quan ra quyết định',
    example: 'Ban Giám đốc',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionAuthority?: string

  @ApiPropertyOptional({
    description: 'Lý do',
    example: 'Bổ nhiệm',
    type: String,
  })
  @IsString()
  @IsOptional()
  reason?: string

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Bổ nhiệm chức danh Chuyên viên',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string

  @ApiPropertyOptional({
    description: 'Có phải tập sự không (1=có, 0=không)',
    example: 0,
    type: Number,
    default: 0,
  })
  @IsNumber()
  @IsOptional()
  isProbation?: number

  @ApiPropertyOptional({
    description: 'Ngày bắt đầu tập sự',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  probationStartDate?: Date

  @ApiPropertyOptional({
    description: 'Thời gian tập sự (tháng)',
    example: 6,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  probationDurationMonths?: number

  @ApiPropertyOptional({
    description: 'Kết quả tập sự',
    example: 'DAT',
    enum: ProbationResult,
  })
  @IsString()
  @IsOptional()
  probationResult?: ProbationResult

  @ApiPropertyOptional({
    description: 'Trạng thái đánh giá tập sự',
    example: 'CHUA_KIEM_TRA',
    enum: ProbationAssessmentStatus,
  })
  @IsString()
  @IsOptional()
  probationAssessmentStatus?: ProbationAssessmentStatus

  @ApiPropertyOptional({
    description: 'Lý do miễn giảm tập sự',
    example: 'Có kinh nghiệm làm việc trước đó',
    type: String,
  })
  @IsString()
  @IsOptional()
  probationExemptionReason?: string

  @ApiPropertyOptional({
    description: 'Đính kèm quyết định tập sự',
    example: 'decisions/probation-2024-001.pdf',
    type: String,
  })
  @IsString()
  @IsOptional()
  probationAttachments?: string

  @ApiPropertyOptional({
    description: 'Danh sách hành động khen thưởng/kỷ luật',
    type: [CreateStaffOrgHistoryActionDto],
  })
  @Type(() => CreateStaffOrgHistoryActionDto)
  @ValidateNested({ each: true })
  @IsOptional()
  actions?: CreateStaffOrgHistoryActionDto[]

  @ApiPropertyOptional({
    description: 'Danh sách sự kiện tổ chức',
    type: [CreateStaffOrgEventDto],
  })
  @Type(() => CreateStaffOrgEventDto)
  @ValidateNested({ each: true })
  @IsOptional()
  events?: CreateStaffOrgEventDto[]

  @ApiPropertyOptional({
    description: 'Danh sách chuyên môn',
    type: [CreateStaffSpecializationDto],
  })
  @Type(() => CreateStaffSpecializationDto)
  @ValidateNested({ each: true })
  @IsOptional()
  specializations?: CreateStaffSpecializationDto[]

  @ApiPropertyOptional({
    description: 'Danh sách vai trò nghiệp vụ của nhân viên trong tổ chức',
    type: [String],
    enum: StaffBusinessRole,
    example: [StaffBusinessRole.TGPL_OFFICER, StaffBusinessRole.LEGAL_ADVISOR],
  })
  @IsEnum(StaffBusinessRole, { each: true })
  @IsOptional()
  roles?: StaffBusinessRole[]
}

export class CreateStaffCardHistoryDto {
  @ApiProperty({
    description: 'Số thẻ cộng tác viên',
    example: 'CTV-2024-001',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  cardNumber!: string

  @ApiProperty({
    description: 'Ngày cấp thẻ',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsNotEmpty()
  issuedAt!: Date

  @ApiPropertyOptional({
    description: 'Trạng thái thẻ',
    example: 'ISSUED',
    enum: COLLAB_CARD_STATUS,
    default: 'ISSUED',
  })
  @IsEnum(COLLAB_CARD_STATUS)
  @IsOptional()
  status?: CollabCardStatus

  @ApiPropertyOptional({
    description: 'Có phải thẻ hiện tại không (1=có, 0=không)',
    example: 1,
    type: Number,
    default: 1,
  })
  @IsNumber()
  @IsOptional()
  isCurrent?: number

  @ApiPropertyOptional({
    description: 'File ảnh mặt trước thẻ',
    example: 'cards/front/CTV-2024-001.jpg',
    type: String,
  })
  @IsString()
  @IsOptional()
  fileRefFront?: string

  @ApiPropertyOptional({
    description: 'File ảnh mặt sau thẻ',
    example: 'cards/back/CTV-2024-001.jpg',
    type: String,
  })
  @IsString()
  @IsOptional()
  fileRefBack?: string

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Cấp thẻ lần đầu',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string
}

export class CreateStaffTrainingHistoryDto {
  @ApiProperty({
    description: 'Ngày bắt đầu đào tạo',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsNotEmpty()
  startDate!: Date

  @ApiPropertyOptional({
    description: 'Ngày kết thúc đào tạo',
    example: '2024-03-31T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  endDate?: Date

  @ApiProperty({
    description: 'Tổ chức đào tạo',
    example: 'Viện Đào tạo Nâng cao Năng lực',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  trainingOrganization!: string

  @ApiProperty({
    description: 'Tên lớp đào tạo',
    example: 'Lớp Quản lý Dự án Chuyên nghiệp',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  trainingClassName!: string

  @ApiPropertyOptional({
    description: 'ID loại hình đào tạo (reference đến category)',
    example: '550e8400-e29b-41d4-a716-446655440000',
    type: String,
  })
  @IsOptional()
  trainingTypeId?: string

  @ApiPropertyOptional({
    description: 'ID hình thức đào tạo (reference đến category)',
    example: '550e8400-e29b-41d4-a716-446655440001',
    type: String,
  })
  @IsOptional()
  trainingFormatId?: string

  @ApiPropertyOptional({
    description: 'Văn bằng chứng chỉ',
    example: 'Chứng chỉ Quản lý Dự án PMP',
    type: String,
  })
  @IsString()
  @IsOptional()
  certificate?: string

  @ApiPropertyOptional({
    description: 'Số quyết định',
    example: 'QD-DT-2024-001',
    type: String,
  })
  @IsString()
  @IsOptional()
  decisionNumber?: string

  @ApiPropertyOptional({
    description: 'Ngày ra quyết định',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  decisionDate?: Date

  @ApiPropertyOptional({
    description: 'File đính kèm',
    example: 'training/certificates/cert-2024-001.pdf',
    type: String,
  })
  @IsString()
  @IsOptional()
  fileRef?: string

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Hoàn thành khóa đào tạo với kết quả xuất sắc',
    type: String,
  })
  @IsString()
  @IsOptional()
  note?: string

  @ApiPropertyOptional({
    description: 'Người tạo',
    example: '<EMAIL>',
    type: String,
  })
  @IsString()
  @IsOptional()
  createdBy?: string

  @ApiPropertyOptional({
    description: 'Người cập nhật',
    example: '<EMAIL>',
    type: String,
  })
  @IsString()
  @IsOptional()
  updatedBy?: string
}
