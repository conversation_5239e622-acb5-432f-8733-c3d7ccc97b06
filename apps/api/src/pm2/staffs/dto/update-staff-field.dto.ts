import { IsDateS<PERSON>, IsEmail, IsNumber, <PERSON>Optional, IsString } from '@ac/common'
import { StaffType } from '@ac/models'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsDate, IsEnum } from 'class-validator'

export class UpdateStaffFieldDto {
  @ApiPropertyOptional({ description: 'Họ và tên' })
  @IsOptional()
  @IsString()
  fullName?: string

  @ApiPropertyOptional({ description: 'Ngày sinh' })
  @IsOptional()
  @IsDate()
  @Transform(({ value }) => (value ? new Date(value as string) : undefined))
  dateOfBirth?: Date

  @ApiPropertyOptional({ description: 'ID giới tính' })
  @IsOptional()
  genderId?: string

  @ApiPropertyOptional({ description: 'ID dân tộc' })
  @IsOptional()
  ethnicId?: string

  @ApiPropertyOptional({ description: 'Email' })
  @IsOptional()
  @IsString()
  @IsEmail()
  email?: string

  @ApiPropertyOptional({ description: 'Số điện thoại' })
  @IsOptional()
  @IsString()
  phone?: string

  @ApiPropertyOptional({ description: 'Địa chỉ thường trú' })
  @IsOptional()
  @IsString()
  permanentAddress?: string

  @ApiPropertyOptional({ description: 'Địa chỉ tạm trú' })
  @IsOptional()
  @IsString()
  temporaryAddress?: string

  @ApiPropertyOptional({ description: 'Số CMND/CCCD' })
  @IsOptional()
  @IsString()
  idCardNumber?: string

  @ApiPropertyOptional({ description: 'Ngày cấp CMND/CCCD' })
  @IsOptional()
  @IsDateString()
  idCardIssuedDate?: Date

  @ApiPropertyOptional({ description: 'Nơi cấp CMND/CCCD' })
  @IsOptional()
  @IsString()
  idCardIssuedPlace?: string

  @ApiPropertyOptional({ description: 'ID tỉnh/thành phố' })
  @IsOptional()
  provinceId?: string

  @ApiPropertyOptional({ description: 'ID quận/huyện' })
  @IsOptional()
  districtId?: string

  @ApiPropertyOptional({ description: 'ID phường/xã' })
  @IsOptional()
  wardId?: string

  @ApiPropertyOptional({ description: 'ID trình độ chuyên môn' })
  @IsOptional()
  professionalLevelId?: string

  @ApiPropertyOptional({ description: 'Loại nhân viên', enum: StaffType })
  @IsOptional()
  @IsEnum(StaffType)
  staffType?: StaffType

  @ApiPropertyOptional({ description: 'ID trạng thái hoạt động' })
  @IsOptional()
  activityStatusId?: string

  @ApiPropertyOptional({ description: 'Số năm kinh nghiệm' })
  @IsOptional()
  @IsNumber()
  experienceYears?: number

  @ApiPropertyOptional({ description: 'ID đơn vị tổ chức' })
  @IsOptional()
  organizationUnitId?: string

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note?: string

  @ApiPropertyOptional({ description: 'Người cập nhật' })
  @IsOptional()
  @IsString()
  updatedBy?: string

  @ApiPropertyOptional({
    description: 'ID người dùng để gán/hủy gán tài khoản. Để null/undefined để hủy gán.',
  })
  @IsOptional()
  userId?: string | null
}
