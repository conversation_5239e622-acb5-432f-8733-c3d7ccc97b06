import { IsDateS<PERSON>, IsEmail, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from '@ac/common'
import { StaffType } from '@ac/models'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ValidateNested } from 'class-validator'

import {
  CreateStaffCardHistoryDto,
  CreateStaffContractDetailDto,
  CreateStaffOrganizationHistoryDto,
  CreateStaffPreviousPositionDto,
  CreateStaffTrainingHistoryDto,
} from './create-staff-with-contract.dto'

export class CreateStaffDto {
  @ApiProperty({
    description: 'Họ và tên nhân viên',
    example: 'Nguyễn Văn An',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  fullName!: string

  @ApiPropertyOptional({
    description: 'Ngày sinh',
    example: '1990-01-15T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  dateOfBirth?: Date

  @ApiPropertyOptional({
    description: 'ID giới tính (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  genderId?: string

  @ApiPropertyOptional({
    description: 'ID dân tộc (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  ethnicId?: string

  @ApiPropertyOptional({
    description: 'Email nhân viên',
    example: '<EMAIL>',
    type: String,
  })
  @IsEmail()
  @IsOptional()
  email?: string

  @ApiPropertyOptional({
    description: 'Số điện thoại',
    example: '0901234567',
    type: String,
  })
  @IsString()
  @IsOptional()
  phone?: string

  @ApiPropertyOptional({
    description: 'Địa chỉ thường trú',
    example: '456 Đường XYZ, Huyện DEF, Tỉnh GHI',
    type: String,
  })
  @IsString()
  @IsOptional()
  permanentAddress?: string

  @ApiPropertyOptional({
    description: 'ID Tỉnh/Thành phố (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  provinceId?: string

  @ApiPropertyOptional({
    description: 'ID Quận/Huyện (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  districtId?: string

  @ApiPropertyOptional({
    description: 'ID Phường/Xã (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  @IsUUID()
  @IsOptional()
  wardId?: string

  @ApiPropertyOptional({
    description: 'Số CCCD/CMND',
    example: '123456789012',
    type: String,
  })
  @IsString()
  @IsOptional()
  cccd?: string

  @ApiPropertyOptional({
    description: 'Ngày cấp CCCD',
    example: '2015-01-01T00:00:00Z',
    type: Date,
  })
  @IsDateString()
  @IsOptional()
  cccdIssuanceDate?: Date

  @ApiPropertyOptional({
    description: 'Nơi cấp CCCD',
    example: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
    type: String,
  })
  @IsString()
  @IsOptional()
  cccdPlaceOfIssuance?: string

  @ApiPropertyOptional({
    description: 'ID trình độ chuyên môn (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  professionalLevelId?: string

  @ApiPropertyOptional({
    description: 'Loại nhân viên',
    enum: StaffType,
    example: StaffType.IN_ORGANIZATION,
  })
  @IsString()
  @IsOptional()
  staffType?: StaffType

  @ApiPropertyOptional({
    description: 'Ảnh đại diện',
    example: 'avatar/profile123.jpg',
    type: String,
  })
  @IsString()
  @IsOptional()
  avatarRef?: string

  @ApiPropertyOptional({
    description: 'ID đơn vị tổ chức',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  organizationUnitId?: string

  @ApiPropertyOptional({
    description: 'ID trạng thái hoạt động (Category)',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  // @IsUUID()
  @IsOptional()
  activityStatusId?: string

  @ApiPropertyOptional({
    description: 'Số năm kinh nghiệm',
    example: 5,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  yearsOfExperience?: number

  @ApiPropertyOptional({
    description: 'Người tạo',
    example: '<EMAIL>',
    type: String,
  })
  @IsString()
  @IsOptional()
  createdBy?: string

  @ApiPropertyOptional({
    description: 'Người cập nhật',
    example: '<EMAIL>',
    type: String,
  })
  @IsString()
  @IsOptional()
  updatedBy?: string

  @ApiPropertyOptional({
    description: 'Thông tin hợp đồng (tùy chọn)',
    type: CreateStaffContractDetailDto,
  })
  @Type(() => CreateStaffContractDetailDto)
  @ValidateNested()
  @IsOptional()
  contract?: CreateStaffContractDetailDto

  @ApiPropertyOptional({
    description: 'Thông tin lịch sử tổ chức (tùy chọn)',
    type: CreateStaffOrganizationHistoryDto,
  })
  @Type(() => CreateStaffOrganizationHistoryDto)
  @ValidateNested()
  @IsOptional()
  organizationHistory?: CreateStaffOrganizationHistoryDto

  @ApiPropertyOptional({
    description: 'Thông tin thẻ cộng tác viên (tùy chọn)',
    type: CreateStaffCardHistoryDto,
  })
  @Type(() => CreateStaffCardHistoryDto)
  @ValidateNested()
  @IsOptional()
  cardHistory?: CreateStaffCardHistoryDto

  @ApiPropertyOptional({
    description: 'Thông tin lịch sử đào tạo (tùy chọn)',
    type: CreateStaffTrainingHistoryDto,
  })
  @Type(() => CreateStaffTrainingHistoryDto)
  @ValidateNested()
  @IsOptional()
  trainingHistory?: CreateStaffTrainingHistoryDto

  @ApiPropertyOptional({
    description: 'Danh sách vị trí công việc trước đây (tùy chọn)',
    type: [CreateStaffPreviousPositionDto],
  })
  @Type(() => CreateStaffPreviousPositionDto)
  @ValidateNested({ each: true })
  @IsOptional()
  previousPositions?: CreateStaffPreviousPositionDto[]
}
