import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

import { StaffResponseDto } from './staff-response.dto'

class StaffContractDetailDto {
  @ApiProperty({ description: 'ID hợp đồng' })
  id!: string

  @ApiProperty({ description: 'Số hợp đồng' })
  contractNo!: string

  @ApiProperty({ description: 'Loại hợp đồng' })
  contractType!: string

  @ApiProperty({ description: 'Ngày bắt đầu' })
  startDate!: Date

  @ApiPropertyOptional({ description: 'Ngày kết thúc' })
  endDate?: Date

  @ApiProperty({ description: 'Trạng thái hợp đồng' })
  status!: string

  @ApiProperty({ description: 'ID đơn vị tổ chức' })
  organizationUnitId!: string

  @ApiPropertyOptional({ description: 'Thông tin đơn vị tổ chức' })
  organizationUnit?: {
    id: string
    name: string
    code: string
    type: number
  }

  @ApiPropertyOptional({ description: 'Tham chiếu file' })
  fileRef?: string

  @ApiPropertyOptional({ description: 'ID tệp lưu trữ' })
  fileId?: string

  @ApiPropertyOptional({
    description: 'Thông tin tệp đính kèm',
    type: () => ({
      id: { type: String },
      bucket: { type: String },
      objectKey: { type: String },
      publicUrl: { type: String },
      contentType: { type: String },
    }),
  })
  file?: {
    id: string
    bucket: string
    objectKey: string
    publicUrl?: string
    contentType?: string
  }

  @ApiPropertyOptional({ description: 'Ghi chú' })
  note?: string

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt!: Date

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt!: Date
}

class StaffCardHistoryDetailDto {
  @ApiProperty({ description: 'ID lịch sử thẻ' })
  id!: string

  @ApiProperty({ description: 'Số thẻ' })
  cardNumber!: string

  @ApiProperty({ description: 'Loại thẻ' })
  cardType!: string

  @ApiProperty({ description: 'Ngày cấp' })
  issuedDate!: Date

  @ApiPropertyOptional({ description: 'Ngày hết hạn' })
  expiryDate?: Date

  @ApiProperty({ description: 'Trạng thái thẻ' })
  status!: string

  @ApiPropertyOptional({ description: 'Ghi chú' })
  note?: string

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt!: Date

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt!: Date
}

class StaffTrainingHistoryDetailDto {
  @ApiProperty({ description: 'ID lịch sử đào tạo' })
  id!: string

  @ApiProperty({ description: 'ID nhân viên' })
  staffId!: string

  @ApiProperty({ description: 'ID lớp đào tạo' })
  trainingClassId!: string

  @ApiPropertyOptional({ description: 'Thông tin lớp đào tạo' })
  trainingClass?: {
    id: string
    courseName: string
    description?: string
    startDate: Date
    endDate: Date
  }

  @ApiProperty({ description: 'Ngày đăng ký' })
  registrationDate!: Date

  @ApiPropertyOptional({ description: 'Ghi chú' })
  note?: string

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt!: Date

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt!: Date
}

class StaffPreviousPositionDetailDto {
  @ApiProperty({ description: 'ID vị trí trước đây' })
  id!: string

  @ApiProperty({ description: 'ID nhân viên' })
  staffId!: string

  @ApiProperty({ description: 'ID danh mục chức vụ' })
  categoryId!: string

  @ApiPropertyOptional({ description: 'Thông tin danh mục chức vụ' })
  category?: {
    categoryId: string
    name: string
    code: string
    type: string
  }

  @ApiProperty({ description: 'ID đơn vị tổ chức' })
  organizationUnitId!: string

  @ApiPropertyOptional({ description: 'Thông tin đơn vị tổ chức' })
  organizationUnit?: {
    id: string
    name: string
    code: string
    type: number
  }

  @ApiProperty({ description: 'Ngày bắt đầu' })
  startDate!: Date

  @ApiPropertyOptional({ description: 'Ngày kết thúc' })
  endDate?: Date

  @ApiPropertyOptional({ description: 'Số quyết định' })
  decisionNumber?: string

  @ApiPropertyOptional({ description: 'Ngày quyết định' })
  decisionDate?: Date

  @ApiPropertyOptional({ description: 'Tham chiếu file minh chứng' })
  evidenceFileRef?: string

  @ApiPropertyOptional({ description: 'Ghi chú' })
  note?: string

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt!: Date

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt!: Date
}

class StaffAssignedUserDto {
  @ApiProperty({ description: 'ID người dùng' })
  id!: string

  @ApiProperty({ description: 'Tên đăng nhập' })
  username!: string

  @ApiProperty({ description: 'Email' })
  email!: string

  @ApiPropertyOptional({ description: 'Số điện thoại' })
  phone?: string

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  isActive!: boolean

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt!: Date
}

export class StaffDetailResponseDto extends StaffResponseDto {
  @ApiPropertyOptional({ description: 'Danh sách hợp đồng', type: [StaffContractDetailDto] })
  contracts?: StaffContractDetailDto[]

  @ApiPropertyOptional({ description: 'Lịch sử thẻ', type: [StaffCardHistoryDetailDto] })
  cardHistory?: StaffCardHistoryDetailDto[]

  @ApiPropertyOptional({ description: 'Lịch sử đào tạo', type: [StaffTrainingHistoryDetailDto] })
  trainingHistory?: StaffTrainingHistoryDetailDto[]

  @ApiPropertyOptional({ description: 'Vị trí trước đây', type: [StaffPreviousPositionDetailDto] })
  previousPositions?: StaffPreviousPositionDetailDto[]

  @ApiPropertyOptional({ description: 'Tài khoản được gán', type: StaffAssignedUserDto })
  assignedUser?: StaffAssignedUserDto
}
