import { IsBoolean, IsDateString, IsEnum, IsOptional, IsString } from '@ac/common'
import { StaffContractType } from '@ac/data-types'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class CreateStaffContractDto {
  @ApiProperty({
    description: 'ID đơn vị tổ chức',
    example: '550e8400-e29b-41d4-a716-446655440000',
    type: String,
  })
  organizationUnitId!: string

  @ApiProperty({
    description: 'S<PERSON> hợp đồng',
    example: 'HD-001/2024',
    type: String,
  })
  @IsString()
  contractNo!: string

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hợp đồng',
    example: StaffContractType.LABOR,
    enum: StaffContractType,
  })
  @IsEnum(StaffContractType)
  contractType!: StaffContractType

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu',
    example: '2024-01-01',
    type: String,
  })
  @IsDateString()
  startDate!: string

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc',
    example: '2025-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string

  @ApiPropertyOptional({
    description: 'Có chấm dứt hợp đồng cũ không',
    example: true,
    type: Boolean,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  terminateOldContracts?: boolean

  @ApiPropertyOptional({
    description: 'Đường dẫn tệp hợp đồng (object key hoặc URL)',
    example: 'staff/contracts/2024/01/01/contract.pdf',
    type: String,
  })
  @IsOptional()
  @IsString()
  fileRef?: string

  @ApiPropertyOptional({
    description: 'ID tệp lưu trữ trong hệ thống',
    example: '5d5d7ae7-8d1f-4f38-9f9c-2f0f82f6c111',
    type: String,
  })
  @IsOptional()
  @IsString()
  fileId?: string
}
