import { BadRequestException, Injectable, NotFoundException } from '@ac/common'
import { StaffContractType } from '@ac/data-types'
import {
  MediaRepository,
  OrganizationUnitRepository,
  StaffContractRepository,
  StaffContractStatus,
  StaffRepository,
} from '@ac/models'

@Injectable()
export class StaffContractService {
  constructor(
    private readonly staffContractRepository: StaffContractRepository,
    private readonly staffRepository: StaffRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly mediaRepository: MediaRepository
  ) {}

  /**
   * Create new staff contract and terminate old ones
   * When staff moves to new organization, create new contract and mark old ones as terminated
   */
  async createStaffContract(
    staffId: string,
    contractData: {
      organizationUnitId: string
      contractNo: string
      contractType: StaffContractType
      startDate: string
      endDate?: string
      terminateOldContracts?: boolean
      fileRef?: string
      fileId?: string
    }
  ) {
    // 1. Validate staff exists
    const staff = await this.staffRepository.findById(staffId)
    if (!staff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    // 2. Validate organization unit exists
    const orgUnit = await this.organizationUnitRepository.findById(contractData.organizationUnitId)
    if (!orgUnit) {
      throw new NotFoundException(`Không tìm thấy đơn vị tổ chức với ID: ${contractData.organizationUnitId}`)
    }

    // 3. Validate contract number uniqueness within organization
    const existingContract = await this.staffContractRepository.findOne({
      contractNo: contractData.contractNo,
      organizationUnitId: contractData.organizationUnitId,
    })
    if (existingContract) {
      throw new BadRequestException(`Số hợp đồng ${contractData.contractNo} đã tồn tại trong đơn vị`)
    }

    // 4. Validate dates
    const startDate = new Date(contractData.startDate)
    const endDate = contractData.endDate ? new Date(contractData.endDate) : undefined

    if (endDate && startDate >= endDate) {
      throw new BadRequestException('Ngày bắt đầu phải nhỏ hơn ngày kết thúc')
    }

    let mediaRef = contractData.fileRef
    const mediaId = contractData.fileId

    if (mediaId) {
      const media = await this.mediaRepository.findById(mediaId)
      if (!media) {
        throw new BadRequestException('Tệp đính kèm không tồn tại hoặc đã bị xóa')
      }

      mediaRef = mediaRef ?? media.objectKey ?? media.publicUrl ?? undefined
    }

    try {
      // 5. Terminate old contracts if requested
      if (contractData.terminateOldContracts) {
        const activeContracts = await this.staffContractRepository.find({
          staffId: staffId,
          status: StaffContractStatus.ACTIVE,
        })

        for (const contract of activeContracts) {
          await this.staffContractRepository.update(contract.id, {
            status: StaffContractStatus.TERMINATED,
            endDate: startDate, // End date is the start date of new contract
          })
        }
      }

      // 6. Create new contract
      const newContract = await this.staffContractRepository.create({
        staffId: staffId,
        staff,
        organizationUnitId: contractData.organizationUnitId,
        organizationUnit: orgUnit,
        contractNo: contractData.contractNo,
        contractType: contractData.contractType,
        startDate: startDate,
        endDate: endDate,
        status: StaffContractStatus.ACTIVE,
        fileRef: mediaRef,
        fileId: mediaId,
      })

      return {
        message: 'Tạo hợp đồng mới thành công',
        contractId: newContract.id,
        staffId: staffId,
        organizationUnitId: contractData.organizationUnitId,
        contractNo: contractData.contractNo,
        terminatedContracts: contractData.terminateOldContracts ? 'Đã kết thúc các hợp đồng cũ' : undefined,
      }
    } catch (error) {
      console.error('Error creating staff contract:', error)
      throw new BadRequestException('Có lỗi xảy ra khi tạo hợp đồng. Vui lòng thử lại sau.')
    }
  }

  /**
   * Get staff contracts
   */
  async getStaffContracts(staffId: string) {
    const staff = await this.staffRepository.findById(staffId)
    if (!staff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    const contracts = await this.staffContractRepository.findDetailedByStaffId(staffId)
    return {
      staffId,
      contracts: contracts.map(contract => ({
        id: contract.id,
        contractNo: contract.contractNo,
        contractType: contract.contractType,
        startDate: contract.startDate,
        endDate: contract.endDate,
        status: contract.status,
        organizationUnitId: contract.organizationUnitId,
        organizationUnit: contract.organizationUnit
          ? {
              id: contract.organizationUnit.id,
              name: contract.organizationUnit.name,
              code: contract.organizationUnit.code,
              type: contract.organizationUnit.type ?? 0,
            }
          : undefined,
        fileRef: contract.fileRef,
        fileId: contract.fileId,
        file: contract.file
          ? {
              id: contract.file.id,
              bucket: contract.file.bucket,
              objectKey: contract.file.objectKey,
              publicUrl: contract.file.publicUrl,
              contentType: contract.file.contentType,
            }
          : undefined,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
      })),
    }
  }

  /**
   * Update staff contract
   */
  async updateStaffContract(
    contractId: string,
    updateData: {
      contractNo?: string
      contractType?: StaffContractType
      startDate?: string
      endDate?: string
      status?: StaffContractStatus
    }
  ) {
    const contract = await this.staffContractRepository.findById(contractId)
    if (!contract) {
      throw new NotFoundException(`Không tìm thấy hợp đồng với ID: ${contractId}`)
    }

    // Validate dates if provided
    let startDate: Date | undefined
    let endDate: Date | undefined

    if (updateData.startDate) {
      startDate = new Date(updateData.startDate)
    }
    if (updateData.endDate) {
      endDate = new Date(updateData.endDate)
    }

    if (startDate && endDate && startDate >= endDate) {
      throw new BadRequestException('Ngày bắt đầu phải nhỏ hơn ngày kết thúc')
    }

    // Validate contract number uniqueness if being updated
    if (updateData.contractNo && updateData.contractNo !== contract.contractNo) {
      const existingContract = await this.staffContractRepository.findOne({
        contractNo: updateData.contractNo,
        organizationUnitId: contract.organizationUnitId,
      })
      if (existingContract && existingContract.id !== contractId) {
        throw new BadRequestException(`Số hợp đồng ${updateData.contractNo} đã tồn tại trong đơn vị`)
      }
    }

    try {
      const updateObject: Record<string, unknown> = {}

      if (updateData.contractNo !== undefined) updateObject.contractNo = updateData.contractNo
      if (updateData.contractType !== undefined) updateObject.contractType = updateData.contractType
      if (startDate) updateObject.startDate = startDate
      if (endDate) updateObject.endDate = endDate
      if (updateData.status !== undefined) updateObject.status = updateData.status

      await this.staffContractRepository.update(contractId, updateObject)

      return {
        message: 'Cập nhật hợp đồng thành công',
        contractId,
        updatedAt: new Date(),
      }
    } catch (error) {
      console.error('Error updating staff contract:', error)
      throw new BadRequestException('Có lỗi xảy ra khi cập nhật hợp đồng. Vui lòng thử lại sau.')
    }
  }

  /**
   * Delete staff contract
   */
  async deleteStaffContract(contractId: string) {
    const contract = await this.staffContractRepository.findById(contractId)
    if (!contract) {
      throw new NotFoundException(`Không tìm thấy hợp đồng với ID: ${contractId}`)
    }

    await this.staffContractRepository.remove(contractId)
    return {
      message: 'Xóa hợp đồng thành công',
      contractId,
    }
  }

  /**
   * Terminate staff contract
   */
  async terminateStaffContract(contractId: string, endDate?: string) {
    const contract = await this.staffContractRepository.findById(contractId)
    if (!contract) {
      throw new NotFoundException(`Không tìm thấy hợp đồng với ID: ${contractId}`)
    }

    if (contract.status === StaffContractStatus.TERMINATED) {
      throw new BadRequestException('Hợp đồng đã được kết thúc trước đó')
    }

    const terminationDate = endDate ? new Date(endDate) : new Date()

    try {
      await this.staffContractRepository.update(contractId, {
        status: StaffContractStatus.TERMINATED,
        endDate: terminationDate,
      })

      return {
        message: 'Kết thúc hợp đồng thành công',
        contractId,
        endDate: terminationDate,
      }
    } catch (error) {
      console.error('Error terminating staff contract:', error)
      throw new BadRequestException('Có lỗi xảy ra khi kết thúc hợp đồng. Vui lòng thử lại sau.')
    }
  }
}
