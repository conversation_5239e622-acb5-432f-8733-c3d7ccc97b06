import { BadRequestException, Injectable, NotFoundException } from '@ac/common'
import { CategoryRepository, StaffOrganizationHistoryRepository, StaffOrgHistoryActionRepository } from '@ac/models'

// Category type mapping for validation
const CATEGORY_TYPE_MAPPING = {
  HISTORY_ACTION: {
    rewardId: 'HINH_THUC_KHEN_THUONG', // Khen thưởng
    disciplineId: 'HINH_THUC_KY_LUAT', // Kỷ luật
  },
} as const

@Injectable()
export class StaffOrgHistoryActionService {
  constructor(
    private readonly staffOrgHistoryActionRepository: StaffOrgHistoryActionRepository,
    private readonly staffOrganizationHistoryRepository: StaffOrganizationHistoryRepository,
    private readonly categoryRepository: CategoryRepository
  ) {}

  /**
   * Create organization history action (rewards/discipline)
   */
  async createStaffOrganizationHistoryAction(
    staffOrganizationHistoryId: string,
    actionData: {
      rewardDisciplineId: string // Category ID for reward/discipline type
    }
  ) {
    // 1. Validate organization history exists
    const orgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!orgHistory) {
      throw new NotFoundException(`Không tìm thấy lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    // 2. Validate reward/discipline category exists and is correct type
    const category = await this.categoryRepository.findById(actionData.rewardDisciplineId)
    if (!category) {
      throw new NotFoundException(`Không tìm thấy danh mục với ID: ${actionData.rewardDisciplineId}`)
    }

    if (
      ![CATEGORY_TYPE_MAPPING.HISTORY_ACTION.disciplineId, CATEGORY_TYPE_MAPPING.HISTORY_ACTION.rewardId].includes(
        category.type as 'HINH_THUC_KHEN_THUONG' | 'HINH_THUC_KY_LUAT'
      )
    ) {
      throw new BadRequestException('Danh mục không phải loại khen thưởng/kỷ luật')
    }

    // 3. Check for duplicate action (unique constraint on historyId + rewardDisciplineId)
    const existingAction = await this.staffOrgHistoryActionRepository.findOne({
      historyId: staffOrganizationHistoryId,
      rewardDisciplineId: actionData.rewardDisciplineId,
    })

    if (existingAction) {
      throw new BadRequestException('Hành động này đã tồn tại cho lịch sử tổ chức')
    }

    try {
      // 4. Create new action record
      const newAction = await this.staffOrgHistoryActionRepository.create({
        historyId: staffOrganizationHistoryId,
        rewardDisciplineId: actionData.rewardDisciplineId,
      })

      return {
        message: 'Tạo hành động tổ chức thành công',
        actionId: newAction.id,
        staffOrganizationHistoryId: staffOrganizationHistoryId,
        rewardDisciplineId: actionData.rewardDisciplineId,
      }
    } catch (error) {
      console.error('Error creating staff organization history action:', error)
      throw new BadRequestException('Có lỗi xảy ra khi tạo hành động tổ chức. Vui lòng thử lại sau.')
    }
  }

  /**
   * Delete staff organization history action
   */
  async deleteStaffOrganizationHistoryAction(actionId: string) {
    const action = await this.staffOrgHistoryActionRepository.findById(actionId)
    if (!action) {
      throw new NotFoundException(`Không tìm thấy hành động với ID: ${actionId}`)
    }

    await this.staffOrgHistoryActionRepository.remove(actionId)
    return {
      message: 'Xóa hành động tổ chức thành công',
      actionId,
    }
  }

  /**
   * Get all actions for a specific organization history
   */
  async getStaffOrganizationHistoryActions(staffOrganizationHistoryId: string) {
    // Validate organization history exists
    const orgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!orgHistory) {
      throw new NotFoundException(`Không tìm thấy lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    const actions = await this.staffOrgHistoryActionRepository.find({
      historyId: staffOrganizationHistoryId,
    })

    return {
      staffOrganizationHistoryId,
      actions: actions.map(action => ({
        id: action.id,
        rewardDisciplineId: action.rewardDisciplineId,
        rewardDiscipline: action.rewardDiscipline,
        createdAt: action.createdAt,
        updatedAt: action.updatedAt,
      })),
    }
  }

  /**
   * Validate action category during creation/update
   */
  async validateActionCategory(rewardDisciplineId: string): Promise<boolean> {
    const category = await this.categoryRepository.findById(rewardDisciplineId)
    if (!category) {
      return false
    }

    return [CATEGORY_TYPE_MAPPING.HISTORY_ACTION.disciplineId, CATEGORY_TYPE_MAPPING.HISTORY_ACTION.rewardId].includes(
      category.type as 'HINH_THUC_KHEN_THUONG' | 'HINH_THUC_KY_LUAT'
    )
  }

  /**
   * Bulk create actions for organization history
   */
  async createMultipleStaffOrganizationHistoryActions(
    staffOrganizationHistoryId: string,
    actionsData: Array<{ rewardDisciplineId: string }>
  ) {
    // Validate organization history exists
    const orgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!orgHistory) {
      throw new NotFoundException(`Không tìm thấy lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    const results: Array<{
      success: boolean
      actionId?: string
      rewardDisciplineId: string
      error?: string
    }> = []

    for (const actionData of actionsData) {
      try {
        const result = await this.createStaffOrganizationHistoryAction(staffOrganizationHistoryId, actionData)
        results.push({
          success: true,
          actionId: result.actionId,
          rewardDisciplineId: actionData.rewardDisciplineId,
        })
      } catch (error) {
        results.push({
          success: false,
          rewardDisciplineId: actionData.rewardDisciplineId,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }

    return {
      message: 'Xử lý tạo nhiều hành động hoàn tất',
      staffOrganizationHistoryId,
      results,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length,
    }
  }

  /**
   * Get actions by type (rewards or discipline)
   */
  async getActionsByType(staffOrganizationHistoryId: string, actionType: 'reward' | 'discipline') {
    const orgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!orgHistory) {
      throw new NotFoundException(`Không tìm thấy lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    const actions = await this.staffOrgHistoryActionRepository.find({
      historyId: staffOrganizationHistoryId,
    })

    // Filter by action type
    const categoryType =
      actionType === 'reward'
        ? CATEGORY_TYPE_MAPPING.HISTORY_ACTION.rewardId
        : CATEGORY_TYPE_MAPPING.HISTORY_ACTION.disciplineId

    const filteredActions = []
    for (const action of actions) {
      const category = await this.categoryRepository.findById(action.rewardDisciplineId)
      if (category && category.type === categoryType) {
        filteredActions.push({
          id: action.id,
          rewardDisciplineId: action.rewardDisciplineId,
          categoryName: category.name,
          categoryCode: category.code,
          createdAt: action.createdAt,
          updatedAt: action.updatedAt,
        })
      }
    }

    return {
      staffOrganizationHistoryId,
      actionType,
      actions: filteredActions,
    }
  }
}
