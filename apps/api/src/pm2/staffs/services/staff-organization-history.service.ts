import { BadRequestException, Injectable, NotFoundException } from '@ac/common'
import { StaffBusinessRole } from '@ac/data-types'
import {
  OrganizationUnitRepository,
  StaffOrganizationHistoryRepository,
  StaffOrgRoleRepository,
  StaffRepository,
  UserRepository,
  UserStatus,
} from '@ac/models'

import {
  ChangeOrganizationUnitDto,
  ChangeOrganizationUnitResponseDto,
  UpdateStaffOrganizationHistoryDto,
  UpdateStaffOrganizationHistoryResponseDto,
} from '../dto'

@Injectable()
export class StaffOrganizationHistoryService {
  constructor(
    private readonly staffOrganizationHistoryRepository: StaffOrganizationHistoryRepository,
    private readonly staffRepository: StaffRepository,
    private readonly organizationUnitRepository: OrganizationUnitRepository,
    private readonly userRepository: UserRepository,
    private readonly staffOrgRoleRepository: StaffOrgRoleRepository
  ) {}

  /**
   * Validates all organization move data before executing any changes
   */
  private async validateOrganizationMove(changeOrganizationUnitDto: ChangeOrganizationUnitDto) {
    const { staffId, newOrganizationUnitId, startDate, ...organizationData } = changeOrganizationUnitDto

    // Validation: Start date should not be in the past (more than 1 day)
    const startDateTime = new Date(startDate)
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    if (startDateTime < yesterday) {
      throw new BadRequestException(`Ngày bắt đầu không thể là ngày quá khứ`)
    }

    // 1. Find staff by staffId
    const staffRecord = await this.staffRepository.findById(staffId)
    if (!staffRecord) {
      throw new NotFoundException(`Không tìm thấy staff với ID: ${staffId}`)
    }

    // 2. Find corresponding user (optional - some staff may not have linked users)
    const user = await this.userRepository.findOne({ staffId: staffId })
    if (user && user.status !== UserStatus.ACTIVE) {
      throw new BadRequestException(`User liên kết với staff ${staffId} không ở trạng thái hoạt động`)
    }

    const staff = staffRecord

    // 2. Validate new organization unit exists and is active
    const newOrganizationUnit = await this.organizationUnitRepository.findById(newOrganizationUnitId)
    if (!newOrganizationUnit) {
      throw new NotFoundException(`Không tìm thấy đơn vị tổ chức với ID: ${newOrganizationUnitId}`)
    }
    if (newOrganizationUnit.status === -99) {
      throw new BadRequestException(`Đơn vị tổ chức với ID: ${newOrganizationUnitId} đã bị xóa`)
    }

    // 3. Check if this is actually a change (not same organization)
    if (staff.organizationUnitId === newOrganizationUnitId) {
      throw new BadRequestException(`Nhân viên đã thuộc đơn vị tổ chức này`)
    }

    // 4. Validation: If probation is set, validate probation data
    if (organizationData.isProbation === 1) {
      if (!organizationData.probationStartDate) {
        throw new BadRequestException(`Nếu chọn tập sự, phải có ngày bắt đầu tập sự`)
      }
      if (!organizationData.probationDurationMonths || organizationData.probationDurationMonths <= 0) {
        throw new BadRequestException(`Thời gian tập sự phải lớn hơn 0 tháng`)
      }
    }

    // 5. Find current active organization history records
    const currentOrgHistories = await this.staffOrganizationHistoryRepository.find({
      staffId: staff.id,
      status: 1, // Currently active
    })

    if (currentOrgHistories.length === 0) {
      throw new BadRequestException(`Không tìm thấy lịch sử tổ chức hiện tại cho nhân viên`)
    }

    return {
      user,
      staff,
      newOrganizationUnit,
      currentOrgHistories,
      startDateTime,
    }
  }

  /**
   * Change organization unit for a user (and associated staff)
   */
  async changeOrganizationUnit(
    changeOrganizationUnitDto: ChangeOrganizationUnitDto
  ): Promise<ChangeOrganizationUnitResponseDto> {
    const { newOrganizationUnitId, ...organizationData } = changeOrganizationUnitDto

    // PHASE 1: VALIDATE EVERYTHING (fail fast - no data changes if anything is wrong)
    const validationResult = await this.validateOrganizationMove(changeOrganizationUnitDto)
    const { staff, user, currentOrgHistories, startDateTime } = validationResult

    // PHASE 2: EXECUTE OPERATIONS
    const createdRecords: Array<{ type: string; id: string }> = []

    try {
      // 1. Mark current organization history records as inactive
      for (const history of currentOrgHistories) {
        await this.staffOrganizationHistoryRepository.update(history.id, {
          status: 0, // Mark as inactive
          endDate: startDateTime, // Set end date to start date of new org
        })
        console.log(`Marked organization history ${history.id} as inactive`)
      }

      // 2. Create new organization history record
      const newOrganizationHistory = await this.staffOrganizationHistoryRepository.create({
        staffId: staff.id,
        organizationUnitId: newOrganizationUnitId,
        startDate: startDateTime,
        status: 1, // Active
        isHead: organizationData.isHead || 0,
        decisionNumber: organizationData.decisionNumber,
        decisionDate: organizationData.decisionDate ? new Date(organizationData.decisionDate) : undefined,
        decisionAuthority: organizationData.decisionAuthority,
        reason: organizationData.reason,
        note: organizationData.note,
        isProbation: organizationData.isProbation || 0,
        probationStartDate: organizationData.probationStartDate
          ? new Date(organizationData.probationStartDate)
          : undefined,
        probationDurationMonths: organizationData.probationDurationMonths,
      })

      createdRecords.push({ type: 'StaffOrganizationHistory', id: newOrganizationHistory.id })
      console.log(`Created new organization history ${newOrganizationHistory.id}`)

      // 3. Add business roles if provided
      if (organizationData.businessRoles && organizationData.businessRoles.length > 0) {
        console.log(
          `Adding ${organizationData.businessRoles.length} business roles to organization history ${newOrganizationHistory.id}`
        )

        for (const role of organizationData.businessRoles) {
          const newRole = await this.staffOrgRoleRepository.create({
            staffOrgId: newOrganizationHistory.id,
            role: role as StaffBusinessRole,
          })
          createdRecords.push({ type: 'StaffOrgRole', id: newRole.id })
        }
      }

      // 4. Update cached organizationUnitId in Staff entity
      await this.staffRepository.update(staff.id, {
        organizationUnitId: newOrganizationUnitId,
      })
      console.log(`Updated staff ${staff.id} organizationUnitId to ${newOrganizationUnitId}`)

      // 5. Update cached orgUnitId in User entity (if user exists)
      if (user) {
        await this.userRepository.update(user.id, {
          orgUnitId: newOrganizationUnitId,
        })
        console.log(`Updated user ${user.id} orgUnitId to ${newOrganizationUnitId}`)
      } else {
        console.log(`No linked user found for staff ${staff.id}, skipping user update`)
      }

      return {
        message: 'Thay đổi đơn vị tổ chức thành công',
        staffId: staff.id,
        newOrganizationUnitId: newOrganizationUnitId,
        newOrganizationHistoryId: newOrganizationHistory.id,
        startDate: startDateTime,
        addedRoles: organizationData.businessRoles || [],
      }
    } catch (error) {
      // Log detailed error information for debugging
      console.error('Error during organization unit change operation:', {
        error: error.message,
        stack: error.stack,
        staffId: staff.id,
        newOrganizationUnitId,
        createdRecords, // Log what was created before failure
      })

      throw new BadRequestException(
        `Có lỗi xảy ra khi thay đổi đơn vị tổ chức. Vui lòng liên hệ quản trị viên với mã lỗi: ${Date.now()}`
      )
    }
  }

  /**
   * Update existing staff organization history data
   */
  async updateStaffOrganizationHistory(
    updateStaffOrganizationHistoryDto: UpdateStaffOrganizationHistoryDto
  ): Promise<UpdateStaffOrganizationHistoryResponseDto> {
    const { staffOrganizationHistoryId, ...updateData } = updateStaffOrganizationHistoryDto

    // 1. Validate that the organization history record exists
    const existingOrgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!existingOrgHistory) {
      throw new NotFoundException(`Không tìm thấy bản ghi lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    // 2. Validate dates if provided
    if (updateData.startDate && updateData.endDate) {
      const startDate = new Date(updateData.startDate)
      const endDate = new Date(updateData.endDate)

      if (startDate >= endDate) {
        throw new BadRequestException('Ngày bắt đầu phải nhỏ hơn ngày kết thúc')
      }
    }

    // 3. Validate probation data if provided
    if (updateData.isProbation === 1) {
      if (updateData.probationStartDate === undefined && !existingOrgHistory.probationStartDate) {
        throw new BadRequestException('Nếu chọn tập sự, phải có ngày bắt đầu tập sự')
      }
      if (updateData.probationDurationMonths !== undefined && updateData.probationDurationMonths <= 0) {
        throw new BadRequestException('Thời gian tập sự phải lớn hơn 0 tháng')
      }
    }

    // 4. Prepare update object (filter out undefined values)
    const updateObject: Record<string, unknown> = {}

    Object.keys(updateData).forEach(key => {
      const value = (updateData as Record<string, unknown>)[key]
      if (value !== undefined) {
        // Convert date strings to Date objects for date fields
        if (
          (key === 'startDate' || key === 'endDate' || key === 'decisionDate' || key === 'probationStartDate') &&
          value
        ) {
          updateObject[key] = new Date(value as string)
        } else {
          updateObject[key] = value
        }
      }
    })

    try {
      // 5. Update the organization history record
      await this.staffOrganizationHistoryRepository.update(staffOrganizationHistoryId, updateObject)

      return {
        message: 'Cập nhật lịch sử tổ chức thành công',
        staffOrganizationHistoryId: staffOrganizationHistoryId,
        updatedAt: new Date(),
      }
    } catch (error) {
      console.error('Error updating staff organization history:', error)
      throw new BadRequestException('Có lỗi xảy ra khi cập nhật lịch sử tổ chức. Vui lòng thử lại sau.')
    }
  }

  /**
   * Get staff organization history
   */
  async getStaffOrganizationHistory(staffId: string) {
    const staff = await this.staffRepository.findById(staffId)
    if (!staff) {
      throw new NotFoundException(`Không tìm thấy nhân viên với ID: ${staffId}`)
    }

    const histories = await this.staffOrganizationHistoryRepository.find({ staffId })

    return {
      staffId,
      histories: histories.map(history => ({
        id: history.id,
        organizationUnitId: history.organizationUnitId,
        startDate: history.startDate,
        endDate: history.endDate,
        status: history.status,
        isHead: history.isHead,
        decisionNumber: history.decisionNumber,
        decisionDate: history.decisionDate,
        decisionAuthority: history.decisionAuthority,
        reason: history.reason,
        note: history.note,
        isProbation: history.isProbation,
        probationStartDate: history.probationStartDate,
        probationDurationMonths: history.probationDurationMonths,
        createdAt: history.createdAt,
        updatedAt: history.updatedAt,
      })),
    }
  }

  /**
   * Add roles to staff organization history
   */
  async addStaffOrganizationRoles(staffOrganizationHistoryId: string, roles: StaffBusinessRole[]) {
    // 1. Validate organization history exists
    const orgHistory = await this.staffOrganizationHistoryRepository.findById(staffOrganizationHistoryId)
    if (!orgHistory) {
      throw new NotFoundException(`Không tìm thấy lịch sử tổ chức với ID: ${staffOrganizationHistoryId}`)
    }

    // 2. Validate organization history is active
    if (orgHistory.status !== 1) {
      throw new BadRequestException('Chỉ có thể thêm vai trò cho lịch sử tổ chức đang hoạt động')
    }

    // 3. Check for duplicate roles
    const existingRoles = await this.staffOrgRoleRepository.findByStaffOrgId(staffOrganizationHistoryId)
    const existingRoleValues = existingRoles.map(r => r.role)
    const duplicateRoles = roles.filter(role => existingRoleValues.includes(role))

    if (duplicateRoles.length > 0) {
      throw new BadRequestException(`Vai trò đã tồn tại: ${duplicateRoles.join(', ')}`)
    }

    try {
      // 4. Create new role records
      const createdRoles = []
      for (const role of roles) {
        const newRole = await this.staffOrgRoleRepository.create({
          staffOrgId: staffOrganizationHistoryId,
          role: role,
        })
        createdRoles.push(newRole)
      }

      return {
        message: 'Thêm vai trò thành công',
        staffOrganizationHistoryId,
        addedRoles: createdRoles.map(r => ({ id: r.id, role: r.role })),
      }
    } catch (error) {
      console.error('Error adding staff organization roles:', error)
      throw new BadRequestException('Có lỗi xảy ra khi thêm vai trò. Vui lòng thử lại sau.')
    }
  }
}
