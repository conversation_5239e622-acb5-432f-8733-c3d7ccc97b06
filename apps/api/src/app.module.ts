import { Accept<PERSON><PERSON><PERSON>ge<PERSON>esolver, Cookie<PERSON>esolver, HeaderResolver, I18nModule, Module, QueryResolver } from '@ac/common'
import { isProd } from '@ac/utils'
import { join } from 'path'

import { AuthenticationsModule } from './core/authentications/authentications.module'
import { MediasModule } from './core/storage/medias/medias.module'
import { UsersModule } from './core/users/users.module'
import { CaseAdvancedModule } from './pm1/case-advanced/case-advanced.module'
import { CaseAnswerModule } from './pm1/case-answer/case-answer.module'
import { CaseSimpleModule } from './pm1/case-simple/case-simple.module'
import { EFormsModule } from './pm1/e-forms/e-forms.module'
import { ProcessesModule } from './pm1/processes/processes.module'
import { WorkflowsHistoriesModule } from './pm1/workflows-histories/workflows-histories.module'
import { WorkflowsSubtasksModule } from './pm1/workflows-subtasks/workflows-subtasks.module'
import { AssignWorkTargetGroupModule } from './pm2/assign-work-target-group/assign-work-target-group.module'
import { CategoriesModule } from './pm2/categories/categories.module'
import { OrganizationUnitModule } from './pm2/organization-unit/organization-unit.module'
import { StaffsModule } from './pm2/staffs/staffs.module'
import { TrainingClassModule } from './pm2/training-class/training-class.module'
import { WorkTargetsModule } from './pm2/work-targets/work-targets.module'
import { AssignPermissionsModule } from './pm6/assign-permissions/assign-permissions.module'
import { AssignRolesModule } from './pm6/assign-roles/assign-roles.module'
import { RolesModule } from './pm6/roles/roles.module'

const loaderFolderPath = isProd() ? join(__dirname, './i18n') : join(__dirname, '../../../packages/i18n/src')
const typesOutputPath = join(__dirname, `../../../packages/i18n/src/generated/i18n.generated.ts`)
const i18nDevConfig = isProd()
  ? {}
  : {
      typesOutputPath,
    }

@Module({
  imports: [
    I18nModule.forRoot({
      fallbackLanguage: 'vi',
      logging: isProd(),
      loaderOptions: {
        path: loaderFolderPath,
        watch: !isProd(),
      },
      resolvers: [
        new HeaderResolver(['Accept-Language', 'x-custom-lang']),
        new QueryResolver(['lang', 'l']),
        new CookieResolver(),
        AcceptLanguageResolver,
      ],
      ...i18nDevConfig,
    }),
    UsersModule,
    CategoriesModule,
    StaffsModule,
    TrainingClassModule,
    AuthenticationsModule,
    CaseAnswerModule,
    RolesModule,
    AssignRolesModule,
    AssignPermissionsModule,
    CaseSimpleModule,
    OrganizationUnitModule,
    MediasModule,
    CaseAdvancedModule,
    ProcessesModule,
    EFormsModule,
    WorkTargetsModule,
    WorkflowsSubtasksModule,
    AssignWorkTargetGroupModule,
    WorkflowsHistoriesModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
