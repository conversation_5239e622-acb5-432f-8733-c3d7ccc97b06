import {
  AbortMultipartUploadCommand,
  CompletedPart,
  CompleteMultipartUploadCommand,
  CreateBucket<PERSON>ommand,
  CreateMultipartUploadCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  HeadBucketCommand,
  HeadObjectCommand,
  PutO<PERSON>Command,
  S3<PERSON>lient,
  UploadPartCommand,
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { Inject, Injectable, NotFoundException } from '@nestjs/common'

type IS3RepoConfig = {
  credentials: {
    accessKeyId: string
    secretAccessKey: string
  }
  endpoint: string
  forcePathStyle: boolean
  region: string
  publicEndpoint: string
}

type IS3Config = {
  NAME: string
  CONFIG: {
    KEY: string
    SECRET: string
    ENDPOINT: string
    REGION: string
    PUBLIC_ENDPOINT: string
  }
}

@Injectable()
export class StorageService {
  private s3Repo: {
    [key: string]: S3Client
  } = {}

  private s3PresignRepo: {
    [key: string]: S3Client
  } = {}

  private s3RepoConfig: { [key: string]: IS3RepoConfig } = {}

  constructor(
    @Inject('STORAGE_SERVICE_CONFIG')
    s3Config: IS3Config[]
  ) {
    s3Config.map(
      (item: {
        NAME: string
        CONFIG: {
          KEY: string
          SECRET: string
          ENDPOINT: string
          REGION: string
          PUBLIC_ENDPOINT: string
        }
      }) => {
        const { ENDPOINT, PUBLIC_ENDPOINT, REGION, KEY, SECRET } = item.CONFIG

        const baseClient = new S3Client({
          credentials: {
            accessKeyId: KEY,
            secretAccessKey: SECRET,
          },
          endpoint: ENDPOINT,
          forcePathStyle: true,
          region: REGION,
        })

        const publicEndpoint = PUBLIC_ENDPOINT || ENDPOINT
        const presignClient =
          publicEndpoint === ENDPOINT
            ? baseClient
            : new S3Client({
                credentials: {
                  accessKeyId: KEY,
                  secretAccessKey: SECRET,
                },
                endpoint: publicEndpoint,
                forcePathStyle: true,
                region: REGION,
              })

        this.s3Repo[item.NAME] = baseClient
        this.s3PresignRepo[item.NAME] = presignClient

        this.s3RepoConfig[item.NAME] = {
          credentials: {
            accessKeyId: KEY,
            secretAccessKey: SECRET,
          },
          endpoint: ENDPOINT,
          forcePathStyle: true,
          region: REGION,
          publicEndpoint,
        }
      }
    )
  }

  getClient(key: string) {
    if (!this.s3Repo[key]) {
      return this.s3Repo['default']
    }

    return this.s3Repo[key]
  }

  getPresignClient(key: string) {
    if (!this.s3PresignRepo[key]) {
      return this.s3PresignRepo['default']
    }

    return this.s3PresignRepo[key]
  }

  getClientConfig(key: string) {
    if (!this.s3RepoConfig[key]) {
      return this.s3RepoConfig['default']
    }

    return this.s3RepoConfig[key]
  }

  async getObjectInfo(key: string, bucketName: string, server: string) {
    const client = this.getClient(server)

    return await client.send(
      new HeadObjectCommand({
        Key: key,
        Bucket: bucketName,
      })
    )
  }

  async saveXml(bucketName: string, xml: string, filePath: string, server: string) {
    bucketName = bucketName.toLowerCase()
    const params = {
      Bucket: bucketName,
      Key: filePath,
      Body: xml,
      // ACL: 'public-read',
    }
    try {
      const client = this.getClient(server)

      return await client.send(new PutObjectCommand(params))
    } catch (err) {
      throw new NotFoundException({
        message: err.Code,
      })
    }
  }

  async checkBucket(server: string, bucketName: string) {
    const client = this.getClient(server)
    try {
      const isExist = await client
        .send(new HeadBucketCommand({ Bucket: bucketName }))
        .then(data => {
          return data
        })
        .catch(err => console.log('Error catch checkBucket', err))
      if (!isExist) {
        await client.send(new CreateBucketCommand({ Bucket: bucketName }))
      }

      return true
    } catch (error) {
      console.log('Error checkBucket', error)

      return false
    }
  }

  async getFileUrl(bucketName: string, filePath: string, expiresIn = 900, server: string): Promise<string | null> {
    bucketName = bucketName.toLowerCase()

    if (!filePath) {
      return null
    }

    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: filePath,
    })

    const client = this.getClient(server)

    const url = await getSignedUrl(client, command, { expiresIn: expiresIn })

    return url
  }

  async getStream(
    bucketName: string,
    filePath: string,
    server: string,
    start?: number,
    end?: number
  ): Promise<NodeJS.ReadableStream> {
    bucketName = bucketName.toLowerCase()
    const client = this.getClient(server)

    const command = new GetObjectCommand(
      start !== undefined && end !== undefined
        ? {
            Bucket: bucketName,
            Key: filePath,
            Range: `bytes=${start}-${end}`,
          }
        : {
            Bucket: bucketName,
            Key: filePath,
          }
    )

    const result = await client.send(command)

    if (!result.Body) {
      throw new Error('Empty body received from storage service')
    }

    return result.Body as NodeJS.ReadableStream
  }

  async deleteFile(bucketName: string, filePath: string, server: string) {
    const client = this.getClient(server)

    return await client.send(new DeleteObjectCommand({ Bucket: bucketName, Key: filePath }))
  }

  async getSignedUploadUrl(
    bucketName: string,
    filePath: string,
    expiresIn = 900,
    server: string,
    contentType?: string
  ) {
    bucketName = bucketName.toLowerCase()
    if (!filePath) {
      return null
    }
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: filePath,
      ContentType: contentType,
    })
    const presignClient = this.getPresignClient(server)
    const presignedUrl = await getSignedUrl(presignClient, command, { expiresIn: expiresIn })
    return {
      publicUrl: this.buildPublicUrl(bucketName, filePath, server),
      presignedUrl,
      expiresIn: expiresIn,
    }
  }

  buildPublicUrl(bucketName: string, filePath: string, server: string) {
    bucketName = bucketName.toLowerCase()
    const clientConfig = this.getClientConfig(server)
    const normalizedPath = filePath
      .split('/')
      .filter((segment: string) => !!segment)
      .join('/')

    return `${clientConfig.publicEndpoint}/${bucketName}/${normalizedPath}`
  }

  async initiateMultipartUpload(bucketName: string, filePath: string, server: string, contentType?: string) {
    bucketName = bucketName.toLowerCase()
    const client = this.getClient(server)
    const command = new CreateMultipartUploadCommand({
      Bucket: bucketName,
      Key: filePath,
      ContentType: contentType,
    })
    try {
      return await client.send(command)
    } catch (error) {
      console.error('initiateMultipartUpload error', error)
      const message = error instanceof Error ? error.message : String(error)
      throw new Error(message)
    }
  }

  async getSignedUploadPartUrl(
    bucketName: string,
    filePath: string,
    uploadId: string,
    partNumber: number,
    expiresIn = 900,
    server: string
  ) {
    bucketName = bucketName.toLowerCase()
    const command = new UploadPartCommand({
      Bucket: bucketName,
      Key: filePath,
      UploadId: uploadId,
      PartNumber: partNumber,
    })

    const presignClient = this.getPresignClient(server)
    const presignedUrl = await getSignedUrl(presignClient, command, { expiresIn })

    return presignedUrl
  }

  async completeMultipartUpload(
    bucketName: string,
    filePath: string,
    uploadId: string,
    parts: CompletedPart[],
    server: string
  ) {
    bucketName = bucketName.toLowerCase()
    const client = this.getClient(server)

    const command = new CompleteMultipartUploadCommand({
      Bucket: bucketName,
      Key: filePath,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts,
      },
    })

    return await client.send(command)
  }

  async abortMultipartUpload(bucketName: string, filePath: string, uploadId: string, server: string) {
    bucketName = bucketName.toLowerCase()
    const client = this.getClient(server)

    const command = new AbortMultipartUploadCommand({
      Bucket: bucketName,
      Key: filePath,
      UploadId: uploadId,
    })

    return await client.send(command)
  }
}
