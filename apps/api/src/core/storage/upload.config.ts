const DEFAULT_ALLOWED_MIME =
  'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/plain,application/json,application/xml,image/*,audio/mpeg,audio/wav,audio/webm,audio/ogg,video/mp4,video/webm,application/zip,application/x-7z-compressed,application/x-rar-compressed'

const DEFAULT_PRESIGN_EXPIRES = 900
const DEFAULT_PART_SIZE = 5 * 1024 * 1024
const DEFAULT_MULTIPART_THRESHOLD = 16 * 1024 * 1024
const DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024

export type UploadPolicy = {
  bucket?: string
  presignExpiresSeconds: number
  partSizeBytes: number
  multipartThresholdBytes: number
  maxFileSizeBytes: number
  allowedMimePatterns: string[]
}

const parsePositiveInt = (value: string | undefined, fallback: number) => {
  if (!value) {
    return fallback
  }

  const parsed = Number.parseInt(value, 10)

  if (Number.isNaN(parsed) || parsed <= 0) {
    return fallback
  }

  return parsed
}

const parseAllowedMime = (value: string | undefined) => {
  const raw = value ?? DEFAULT_ALLOWED_MIME

  return raw
    .split(',')
    .map(item => item.trim())
    .filter(Boolean)
}

export const getUploadPolicy = (): UploadPolicy => {
  return {
    bucket: process.env.STORAGE_BUCKET,
    presignExpiresSeconds: parsePositiveInt(process.env.STORAGE_PRESIGN_EXPIRES_SECONDS, DEFAULT_PRESIGN_EXPIRES),
    partSizeBytes: parsePositiveInt(process.env.UPLOAD_PART_SIZE_BYTES, DEFAULT_PART_SIZE),
    multipartThresholdBytes: parsePositiveInt(
      process.env.UPLOAD_MULTIPART_THRESHOLD_BYTES,
      DEFAULT_MULTIPART_THRESHOLD
    ),
    maxFileSizeBytes: parsePositiveInt(process.env.MAX_FILE_SIZE_BYTES, DEFAULT_MAX_FILE_SIZE),
    allowedMimePatterns: parseAllowedMime(process.env.ALLOWED_MIME),
  }
}

export const isMimeAllowed = (mime: string | undefined, allowedPatterns: string[]) => {
  if (!mime) {
    return false
  }

  if (!allowedPatterns.length) {
    return true
  }

  return allowedPatterns.some(pattern => matchesMimePattern(mime, pattern))
}

const matchesMimePattern = (mime: string, pattern: string) => {
  const [mimeType, mimeSubtype] = mime.toLowerCase().split('/')
  const [patternType, patternSubtype] = pattern.toLowerCase().split('/')

  if (!mimeType || !mimeSubtype || !patternType || !patternSubtype) {
    return false
  }

  const typeMatches = patternType === '*' || patternType === mimeType
  const subtypeMatches = patternSubtype === '*' || patternSubtype === mimeSubtype

  return typeMatches && subtypeMatches
}
