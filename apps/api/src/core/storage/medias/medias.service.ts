import { AcRequest } from '@ac/be'
import { MediaRepository, UploadSessionRepository, UploadSessionStatus } from '@ac/models'
import { CompletedPart } from '@aws-sdk/client-s3'
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Scope,
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import type { Request, Response } from 'express'

import { StorageService } from '../services/storage.service'
import { getUploadPolicy, isMimeAllowed } from '../upload.config'
import { CompleteSingleUploadDto } from './dto/complete-single-upload.dto'
import { CreatePresignedUrlDto, MediaKind } from './dto/create-media.dto'
import { GetSignedFileUrlDto } from './dto/get-media.dto'
import {
  AbortMultipartUploadDto,
  CompleteMultipartUploadDto,
  CreateMultipartUploadDto,
  GetMultipartUploadPartUrlDto,
} from './dto/multipart-media.dto'

const fallbackBucket = 'btp'

@Injectable({ scope: Scope.REQUEST })
export class MediasService {
  private readonly uploadPolicy = getUploadPolicy()

  constructor(
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly storageService: StorageService,
    private readonly uploadSessionRepository: UploadSessionRepository,
    private readonly mediaRepository: MediaRepository
  ) {}

  async getSignedUploadUrl(createPresignedUrl: CreatePresignedUrlDto) {
    const { kind, fileName, expiresIn, server = 'default', mimeType, fileSize, pathPrefix } = createPresignedUrl

    this.ensureKind(kind)
    this.ensureMimeAllowed(mimeType)
    this.ensureFileSizeWithinLimit(fileSize)

    const bucketName = this.resolveBucket(server)
    const sanitizedPathPrefix = pathPrefix ? this.sanitizePathPrefix(pathPrefix) : undefined
    const fileKey = this.buildFilePath(kind, fileName, sanitizedPathPrefix)
    const ttl = this.resolveExpiry(expiresIn)

    const signed = await this.storageService.getSignedUploadUrl(bucketName, fileKey, ttl, server, mimeType)

    return {
      strategy: fileSize && fileSize >= this.uploadPolicy.multipartThresholdBytes ? 'multipart' : 'single',
      bucket: bucketName,
      fileKey,
      expiresIn: ttl,
      publicUrl: signed?.publicUrl,
      pathPrefix: sanitizedPathPrefix,
      presignedUrl: signed?.presignedUrl,
    }
  }

  async getSignedFileUrl(fileId: string, options: GetSignedFileUrlDto) {
    const { expiresIn, server = 'default' } = options ?? {}

    const media = await this.mediaRepository.findById(fileId)

    if (!media) {
      throw new NotFoundException('File not found')
    }

    const ttl = this.resolveExpiry(expiresIn)

    try {
      await this.storageService.getObjectInfo(media.objectKey, media.bucket, server)
    } catch {
      throw new NotFoundException('File not found')
    }

    const signedUrl = await this.storageService.getFileUrl(media.bucket, media.objectKey, ttl, server)

    if (!signedUrl) {
      throw new NotFoundException('Unable to generate download URL')
    }

    return {
      url: signedUrl,
      expiresIn: ttl,
      bucket: media.bucket,
      objectKey: media.objectKey,
      fileName: media.fileName ?? this.extractFileName(media.objectKey),
      contentType: media.contentType,
      contentLength: media.contentLength,
      type: media.type,
    }
  }

  async streamFile(fileId: string, options: GetSignedFileUrlDto, request: Request, response: Response) {
    const { server = 'default' } = options ?? {}
    const range = request.headers.range

    const media = await this.mediaRepository.findById(fileId)

    if (!media) {
      throw new NotFoundException('File not found')
    }

    let head

    try {
      head = await this.storageService.getObjectInfo(media.objectKey, media.bucket, server)
    } catch {
      throw new NotFoundException('File not found')
    }

    const fileSize = head.ContentLength ?? media.contentLength

    if (!fileSize || fileSize <= 0) {
      throw new NotFoundException('File size information is missing')
    }

    response.setHeader('Accept-Ranges', 'bytes')

    if (!range) {
      const stream = await this.storageService.getStream(media.bucket, media.objectKey, server)

      response.status(200)
      response.setHeader('Content-Length', String(fileSize))

      if (media.contentType) {
        response.setHeader('Content-Type', media.contentType)
      }

      stream.on('error', (error: Error | undefined) => {
        response.destroy(error)
      })

      stream.pipe(response)

      return
    }

    const CHUNK_SIZE = 5 * 1024 * 1024
    const rangeMatch = /bytes=(\d+)-(\d*)/.exec(range)

    if (!rangeMatch) {
      throw new BadRequestException('Invalid Range header format')
    }

    const start = Number.parseInt(rangeMatch[1], 10)
    const requestedEnd = rangeMatch[2] ? Number.parseInt(rangeMatch[2], 10) : undefined

    if (Number.isNaN(start) || start >= fileSize) {
      response.status(416)
      response.setHeader('Content-Range', `bytes */${fileSize}`)
      response.end()
      return
    }

    const end = Math.min(requestedEnd ?? start + CHUNK_SIZE - 1, fileSize - 1)

    const stream = await this.storageService.getStream(media.bucket, media.objectKey, server, start, end)

    response.status(206)
    response.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`)
    response.setHeader('Content-Length', String(end - start + 1))

    if (media.contentType) {
      response.setHeader('Content-Type', media.contentType)
    }

    stream.on('error', (error: Error | undefined) => {
      response.destroy(error)
    })

    stream.pipe(response)
  }

  async createMultipartUpload(createMultipartUploadDto: CreateMultipartUploadDto) {
    const {
      kind,
      fileName,
      server = 'default',
      mimeType,
      fileSize,
      checksum,
      metadata,
      pathPrefix,
    } = createMultipartUploadDto

    this.ensureKind(kind)
    this.ensureMimeAllowed(mimeType)
    this.ensureFileSizeWithinLimit(fileSize)

    const bucketName = this.resolveBucket(server)
    const sanitizedPathPrefix = pathPrefix ? this.sanitizePathPrefix(pathPrefix) : undefined
    const fileKey = this.buildFilePath(kind, fileName, sanitizedPathPrefix)
    const { UploadId } = await this.storageService.initiateMultipartUpload(bucketName, fileKey, server, mimeType)
    if (!UploadId) {
      throw new InternalServerErrorException('Unable to initiate multipart upload')
    }

    const ttl = this.uploadPolicy.presignExpiresSeconds
    const expiresAt = new Date(Date.now() + ttl * 1000)

    const metadataType = metadata && typeof metadata['type'] === 'string' ? metadata['type'] : undefined
    const userOrgUnitId = this.getOrgUnitId()

    const session = await this.uploadSessionRepository.create({
      bucket: bucketName,
      objectKey: fileKey,
      uploadId: UploadId,
      ownerId: this.getUserId(),
      contentType: mimeType,
      contentLength: fileSize,
      checksum,
      metadata: {
        ...(metadata ?? {}),
        ...(sanitizedPathPrefix ? { pathPrefix: sanitizedPathPrefix } : {}),
        type: metadataType ?? kind,
        ...(userOrgUnitId ? { orgUnitId: userOrgUnitId } : {}),
      },
      expiresAt,
    })

    await this.uploadSessionRepository.markStatus(session.id, UploadSessionStatus.IN_PROGRESS)

    return {
      strategy: 'multipart',
      sessionId: session.id,
      uploadId: UploadId,
      fileKey,
      bucket: bucketName,
      expiresIn: ttl,
      partSize: this.uploadPolicy.partSizeBytes,
      publicUrl: this.storageService.buildPublicUrl(bucketName, fileKey, server),
      pathPrefix: sanitizedPathPrefix,
    }
  }

  async getMultipartUploadPartUrl(getMultipartUploadPartUrlDto: GetMultipartUploadPartUrlDto) {
    const { fileKey, uploadId, partNumber, expiresIn, server = 'default' } = getMultipartUploadPartUrlDto

    if (!uploadId) {
      throw new BadRequestException('Upload ID is required')
    }

    if (!partNumber || partNumber < 1 || partNumber > 10000) {
      throw new BadRequestException('Invalid part number')
    }

    const session = await this.getUploadSessionOrThrow(fileKey, uploadId)
    this.ensureSessionActive(session)

    const bucketName = session.bucket ?? this.resolveBucket(server)
    const ttl = this.resolveExpiry(expiresIn)

    const presignedUrl = await this.storageService.getSignedUploadPartUrl(
      bucketName,
      fileKey,
      uploadId,
      partNumber,
      ttl,
      server
    )

    const expiresAt = new Date(Date.now() + ttl * 1000)
    await this.uploadSessionRepository.update(session.id, { expiresAt })

    return {
      sessionId: session.id,
      uploadId,
      fileKey,
      partNumber,
      expiresIn: ttl,
      presignedUrl,
    }
  }

  async completeMultipartUpload(completeMultipartUploadDto: CompleteMultipartUploadDto) {
    const { fileKey, uploadId, parts, server = 'default', checksum, totalSize, type } = completeMultipartUploadDto

    if (!parts?.length) {
      throw new BadRequestException('Multipart upload parts are required')
    }

    const session = await this.getUploadSessionOrThrow(fileKey, uploadId)
    this.ensureSessionActive(session)

    const bucketName = session.bucket ?? this.resolveBucket(server)

    const completedParts: CompletedPart[] = parts
      .map(part => ({
        ETag: part.eTag,
        PartNumber: part.partNumber,
      }))
      .sort((a, b) => (a.PartNumber ?? 0) - (b.PartNumber ?? 0))

    const response = await this.storageService.completeMultipartUpload(
      bucketName,
      fileKey,
      uploadId,
      completedParts,
      server
    )

    await Promise.all(
      parts.map(part =>
        this.uploadSessionRepository.upsertPart({
          sessionId: session.id,
          partNumber: part.partNumber,
          eTag: part.eTag,
          size: part.size,
        })
      )
    )

    const contentLength = totalSize ?? this.sumPartSizes(parts)

    await this.uploadSessionRepository.update(session.id, {
      status: UploadSessionStatus.COMPLETED,
      checksum: checksum ?? session.checksum,
      contentLength: contentLength ?? session.contentLength,
    })

    const rawPublicUrl = this.storageService.buildPublicUrl(bucketName, fileKey, server)
    const publicUrl = this.normalizePublicUrl(rawPublicUrl)

    const metadataType = this.extractMetadataString(session.metadata, 'type')
    const metadataPathPrefix = this.extractMetadataString(session.metadata, 'pathPrefix')
    const metadataOrgUnitId = this.extractMetadataString(session.metadata, 'orgUnitId')
    const resolvedOrgUnitId = metadataOrgUnitId ?? this.getOrgUnitId()
    const fileType = this.resolveMediaType({
      explicitType: type,
      metadataType,
      kind: this.inferKindFromKey(fileKey),
      pathPrefix: metadataPathPrefix,
      fileKey,
    })

    const media = await this.mediaRepository.upsert({
      bucket: (response.Bucket ?? bucketName).toLowerCase(),
      objectKey: response.Key ?? fileKey,
      fileName: this.extractFileName(response.Key ?? fileKey),
      contentType: session.contentType,
      contentLength: contentLength ?? session.contentLength,
      checksum: checksum ?? session.checksum,
      eTag: response.ETag ?? undefined,
      publicUrl,
      type: fileType ?? metadataType,
      ownerId: session.ownerId,
      sessionId: session.id,
      orgUnitId: resolvedOrgUnitId,
      metadata: session.metadata,
    })

    return {
      sessionId: session.id,
      bucket: (response.Bucket ?? bucketName).toLowerCase(),
      fileKey: response.Key ?? fileKey,
      location: response.Location ?? publicUrl ?? rawPublicUrl,
      eTag: response.ETag,
      publicUrl: publicUrl ?? rawPublicUrl,
      type: media.type ?? fileType ?? metadataType,
      fileId: media.id,
    }
  }

  async abortMultipartUpload(abortMultipartUploadDto: AbortMultipartUploadDto) {
    const { fileKey, uploadId, server = 'default' } = abortMultipartUploadDto

    if (!uploadId) {
      throw new BadRequestException('Upload ID is required')
    }

    const session = await this.getUploadSessionOrThrow(fileKey, uploadId)
    const alreadyAborted = session.status === UploadSessionStatus.ABORTED
    this.ensureSessionActive(session, true)

    const bucketName = session.bucket ?? this.resolveBucket(server)

    if (!alreadyAborted) {
      await this.storageService.abortMultipartUpload(bucketName, fileKey, uploadId, server)
      await this.uploadSessionRepository.deleteParts(session.id)
      await this.uploadSessionRepository.update(session.id, {
        status: UploadSessionStatus.ABORTED,
      })
    }

    return {
      sessionId: session.id,
      uploadId,
      fileKey,
      aborted: true,
    }
  }

  async completeSingleUpload(completeSingleUploadDto: CompleteSingleUploadDto) {
    const {
      fileKey,
      bucket,
      server = 'default',
      contentType,
      contentLength,
      publicUrl,
      eTag,
      pathPrefix,
      type,
    } = completeSingleUploadDto

    if (!fileKey) {
      throw new BadRequestException('File key is required')
    }

    const bucketName = (bucket ?? this.resolveBucket(server)).toLowerCase()
    const sanitizedPrefix = pathPrefix ? this.sanitizePathPrefix(pathPrefix) : undefined

    const resolvedPublicUrl = publicUrl ?? this.storageService.buildPublicUrl(bucketName, fileKey, server)
    const safePublicUrl = this.normalizePublicUrl(resolvedPublicUrl)

    const fileType = this.resolveMediaType({
      explicitType: type,
      kind: this.inferKindFromKey(fileKey),
      pathPrefix: sanitizedPrefix,
      fileKey,
    })

    const userOrgUnitId = this.getOrgUnitId()

    const media = await this.mediaRepository.upsert({
      bucket: bucketName,
      objectKey: fileKey,
      fileName: this.extractFileName(fileKey),
      contentType,
      contentLength,
      eTag,
      publicUrl: safePublicUrl,
      type: fileType,
      ownerId: this.getUserId(),
      orgUnitId: userOrgUnitId,
      metadata:
        sanitizedPrefix || fileType
          ? {
              ...(sanitizedPrefix ? { pathPrefix: sanitizedPrefix } : {}),
              ...(fileType ? { type: fileType } : {}),
              ...(userOrgUnitId ? { orgUnitId: userOrgUnitId } : {}),
            }
          : undefined,
    })

    return {
      bucket: bucketName,
      fileKey,
      publicUrl: media.publicUrl ?? safePublicUrl ?? resolvedPublicUrl,
      eTag: media.eTag ?? eTag,
      type: media.type ?? fileType,
      fileId: media.id,
    }
  }

  private ensureKind(kind: MediaKind) {
    if (!kind) {
      throw new BadRequestException('Kind is required')
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private resolveBucket(_server?: string) {
    const sourceBucket = this.uploadPolicy.bucket ?? fallbackBucket

    if (!sourceBucket) {
      throw new InternalServerErrorException('Storage bucket is not configured')
    }

    return sourceBucket.toLowerCase()
  }

  private resolveExpiry(expiresIn?: number) {
    if (!expiresIn || expiresIn <= 0) {
      return this.uploadPolicy.presignExpiresSeconds
    }

    return expiresIn
  }

  private ensureMimeAllowed(mimeType?: string) {
    if (!mimeType) {
      return
    }

    if (!isMimeAllowed(mimeType, this.uploadPolicy.allowedMimePatterns)) {
      throw new BadRequestException(`Mime type ${mimeType} is not allowed`)
    }
  }

  private ensureFileSizeWithinLimit(fileSize?: number) {
    if (!fileSize) {
      return
    }

    if (fileSize > this.uploadPolicy.maxFileSizeBytes) {
      throw new BadRequestException('File size exceeds allowed limit')
    }
  }

  private async getUploadSessionOrThrow(objectKey: string, uploadId: string) {
    const session = await this.uploadSessionRepository.findByUpload(objectKey, uploadId)

    if (!session) {
      throw new BadRequestException('Upload session not found')
    }

    return session
  }

  private ensureSessionActive(session: { status: UploadSessionStatus }, allowAbort = false) {
    if (session.status === UploadSessionStatus.COMPLETED) {
      throw new BadRequestException('Upload session is already completed')
    }

    if (!allowAbort && session.status === UploadSessionStatus.ABORTED) {
      throw new BadRequestException('Upload session was aborted')
    }

    if (session.status === UploadSessionStatus.FAILED) {
      throw new BadRequestException('Upload session is not recoverable')
    }
  }

  private getOrgUnitId() {
    const orgUnitId = this.request?.metadata?.getUser()?.getOrgUnitId()

    if (typeof orgUnitId === 'string' && orgUnitId.trim()) {
      return orgUnitId
    }

    return undefined
  }

  private getUserId() {
    const userId = this.request?.metadata?.getUser()?.getId()

    if (!userId) {
      throw new BadRequestException('User information is missing in request metadata')
    }

    return userId
  }

  private buildFilePath(kind: MediaKind, fileName: string, pathPrefix?: string) {
    const userId = this.getUserId()
    const sanitizedName = this.sanitizeFileName(fileName)

    const now = new Date()
    const yyyy = now.getFullYear()
    const mm = String(now.getMonth() + 1).padStart(2, '0')
    const dd = String(now.getDate()).padStart(2, '0')
    const currentDate = Math.floor(Date.now() / 1000).toString()

    const sanitizedPrefix = pathPrefix ? this.sanitizePathPrefix(pathPrefix) : undefined
    const baseSegments = sanitizedPrefix ? [sanitizedPrefix] : [kind]

    return [...baseSegments, yyyy.toString(), mm, dd, `${userId}-${currentDate}-${sanitizedName}`]
      .filter(segment => segment !== undefined && segment !== '')
      .join('/')
  }

  private sanitizeFileName(fileName: string) {
    const MAX_BASE_LENGTH = 120
    const trimmed = (fileName ?? '').trim()

    if (!trimmed) {
      return 'file'
    }

    const normalized = trimmed.normalize('NFKD').replace(/[\u0300-\u036f]/g, '')
    const lastDotIndex = normalized.lastIndexOf('.')

    const basePart = lastDotIndex > 0 ? normalized.slice(0, lastDotIndex) : normalized
    const extPart = lastDotIndex > 0 ? normalized.slice(lastDotIndex + 1) : ''

    const sanitizedBase = this.sanitizeSlug(basePart)
    const constrainedBase = sanitizedBase.slice(0, MAX_BASE_LENGTH) || 'file'

    const sanitizedExt = this.sanitizeExtension(extPart)

    return sanitizedExt ? `${constrainedBase}.${sanitizedExt}` : constrainedBase
  }

  private sanitizePathPrefix(prefix: string) {
    return prefix
      .split('/')
      .map(segment => this.sanitizeSlug(segment).slice(0, 60))
      .filter(Boolean)
      .join('/')
  }

  private sanitizeSlug(value: string) {
    return value
      .normalize('NFKD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z0-9-]+/g, '-')
      .replace(/-{2,}/g, '-')
      .replace(/^-+|-+$/g, '')
      .toLowerCase()
  }

  private sanitizeExtension(value: string) {
    return value
      .normalize('NFKD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z0-9]/g, '')
      .toLowerCase()
  }

  private normalizePublicUrl(url?: string) {
    if (!url) {
      return undefined
    }

    const trimmed = url.trim()

    if (!trimmed) {
      return undefined
    }

    if (trimmed.length <= 255) {
      return trimmed
    }

    return undefined
  }

  private resolveMediaType(options: {
    explicitType?: string
    metadataType?: string | null
    kind?: MediaKind
    pathPrefix?: string
    fileKey: string
  }) {
    const { explicitType, metadataType, kind, pathPrefix, fileKey } = options

    const normalizedExplicit = explicitType?.trim()
    if (normalizedExplicit) {
      return normalizedExplicit
    }

    const normalizedMetadata = metadataType?.trim()
    if (normalizedMetadata) {
      return normalizedMetadata
    }

    if (kind) {
      return kind
    }

    if (pathPrefix) {
      const prefixSegment = pathPrefix
        .split('/')
        .map(segment => segment.trim())
        .filter(Boolean)[0]

      if (prefixSegment) {
        return prefixSegment
      }
    }

    const firstSegment = fileKey
      .split('/')
      .map(segment => segment.trim())
      .filter(Boolean)[0]

    return firstSegment
  }

  private inferKindFromKey(key: string): MediaKind | undefined {
    const firstSegment = key
      .split('/')
      .map(segment => segment.trim())
      .filter(Boolean)[0]

    if (!firstSegment) {
      return undefined
    }

    return (Object.values(MediaKind) as string[]).includes(firstSegment) ? (firstSegment as MediaKind) : undefined
  }

  private extractMetadataString(metadata: unknown, key: string): string | undefined {
    if (!metadata || typeof metadata !== 'object') {
      return undefined
    }

    const value = (metadata as Record<string, unknown>)[key]
    return typeof value === 'string' ? value : undefined
  }
  private sumPartSizes(parts: { size?: number }[]) {
    const sizes = parts
      .map(part => part.size)
      .filter((size): size is number => typeof size === 'number' && Number.isFinite(size))

    if (!sizes.length) {
      return undefined
    }

    return sizes.reduce((total, size) => total + size, 0)
  }

  private extractFileName(key: string) {
    const segments = key.split('/')

    return segments[segments.length - 1] || key
  }
}
