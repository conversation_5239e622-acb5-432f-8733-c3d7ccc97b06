import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator'

export class CompletedMultipartUploadPartDto {
  @IsInt()
  @Min(1)
  @Max(10000)
  @ApiProperty({ example: 1, minimum: 1, maximum: 10000 })
  partNumber: number

  @IsString()
  @ApiProperty({ example: '"d41d8cd98f00b204e9800998ecf8427e"' })
  eTag: string

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ example: 5242880 })
  size?: number
}
