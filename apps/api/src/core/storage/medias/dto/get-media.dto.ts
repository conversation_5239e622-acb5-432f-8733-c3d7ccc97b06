import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsOptional, IsString } from 'class-validator'

export class GetSignedFileUrlDto {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ required: false, type: Number, example: 300 })
  expiresIn?: number

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'
}
