import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator'

export enum MediaKind {
  CASE_SIMPLE = 'CASE_SIMPLE',
  CASE_ADVANCED = 'CASE_ADVANCED',
  STAFF_DOCUMENT = 'STAFF_DOCUMENT',
}

export class CreatePresignedUrlDto {
  @IsEnum(MediaKind)
  @ApiProperty({
    example: MediaKind.CASE_ADVANCED,
  })
  kind: MediaKind

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'

  @ApiProperty({ example: 'can-cuoc-cong-dan.pdf' })
  @IsString()
  fileName: string

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'application/pdf' })
  mimeType?: string

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ required: false, type: Number, example: 1048576 })
  fileSize?: number

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'staff/documents' })
  pathPrefix?: string

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ required: false, type: Number, example: 900 })
  expiresIn? = 900
}
