import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsEnum, IsInt, IsObject, IsOptional, IsString, Max, Min, ValidateNested } from 'class-validator'

import { CompletedMultipartUploadPartDto } from './complete-multipart-upload-part.dto'
import { MediaKind } from './create-media.dto'

export class CreateMultipartUploadDto {
  @IsEnum(MediaKind)
  @ApiProperty({ example: MediaKind.CASE_ADVANCED })
  kind: MediaKind

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'

  @IsString()
  @ApiProperty({ example: 'large-document.pdf' })
  fileName: string

  @IsString()
  @ApiProperty({ example: 'application/pdf' })
  mimeType: string

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ example: 104857600 })
  fileSize?: number

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'staff/documents' })
  pathPrefix?: string

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'sha256:abcdef' })
  checksum?: string

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ example: { caseId: '123' } })
  metadata?: Record<string, unknown>
}

export class GetMultipartUploadPartUrlDto {
  @IsString()
  @ApiProperty({ example: 'CASE_ADVANCED/2024/06/01/user-file-key.pdf' })
  fileKey: string

  @IsString()
  @ApiProperty({ example: 'abcd1234exampleUploadId' })
  uploadId: string

  @IsInt()
  @Min(1)
  @Max(10000)
  @ApiProperty({ example: 1, minimum: 1, maximum: 10000 })
  partNumber: number

  @IsOptional()
  @IsInt()
  @Min(1)
  @ApiPropertyOptional({ example: 900, minimum: 1 })
  expiresIn?: number = 900

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'
}

export class CompleteMultipartUploadDto {
  @IsString()
  @ApiProperty({ example: 'CASE_ADVANCED/2024/06/01/user-file-key.pdf' })
  fileKey: string

  @IsString()
  @ApiProperty({ example: 'abcd1234exampleUploadId' })
  uploadId: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompletedMultipartUploadPartDto)
  @ApiProperty({
    type: [CompletedMultipartUploadPartDto],
    example: [
      { partNumber: 1, eTag: '"etag1"' },
      { partNumber: 2, eTag: '"etag2"' },
    ],
  })
  parts: CompletedMultipartUploadPartDto[]

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'sha256:abcdef' })
  checksum?: string

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @ApiPropertyOptional({ example: 20971520 })
  totalSize?: number

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'STAFF_DOCUMENT' })
  type?: string
}

export class AbortMultipartUploadDto {
  @IsString()
  @ApiProperty({ example: 'CASE_ADVANCED/2024/06/01/user-file-key.pdf' })
  fileKey: string

  @IsString()
  @ApiProperty({ example: 'abcd1234exampleUploadId' })
  uploadId: string

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'default' })
  server?: string = 'default'
}
