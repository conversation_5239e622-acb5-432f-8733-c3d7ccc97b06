import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsInt, IsOptional, IsString } from 'class-validator'

export class CompleteSingleUploadDto {
  @ApiProperty({ description: 'Đường dẫn đối tượng trong bucket', example: 'staff/documents/2025/10/02/file.pdf' })
  @IsString()
  fileKey!: string

  @ApiPropertyOptional({ description: 'Tên bucket lưu trữ', example: 'btp' })
  @IsOptional()
  @IsString()
  bucket?: string

  @ApiPropertyOptional({ description: '<PERSON><PERSON>y chủ lưu trữ', example: 'default' })
  @IsOptional()
  @IsString()
  server?: string

  @ApiPropertyOptional({ description: 'Kiểu nội dung của tệp', example: 'application/pdf' })
  @IsOptional()
  @IsString()
  contentType?: string

  @ApiPropertyOptional({ description: '<PERSON><PERSON><PERSON> thước tệp (byte)', example: 102400 })
  @IsOptional()
  @IsInt()
  contentLength?: number

  @ApiPropertyOptional({ description: 'Đường dẫn công khai nếu có', example: 'https://...' })
  @IsOptional()
  @IsString()
  publicUrl?: string

  @ApiPropertyOptional({ description: 'Giá trị ETag trả về từ MinIO', example: '"fba9dede5f27731c9771645a39863328"' })
  @IsOptional()
  @IsString()
  eTag?: string

  @ApiPropertyOptional({ description: 'Tiền tố đường dẫn đã sử dụng', example: 'staff/contracts/abc' })
  @IsOptional()
  @IsString()
  pathPrefix?: string

  @ApiPropertyOptional({ description: 'Loại tệp lưu trữ', example: 'STAFF_DOCUMENT' })
  @IsOptional()
  @IsString()
  type?: string
}
