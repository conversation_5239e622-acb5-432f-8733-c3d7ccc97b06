import { Body, Controller, Get, Param, Post, Query, Req, Res } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import type { Request, Response } from 'express'
import { Route } from 'tsoa'

import { CompleteSingleUploadDto } from './dto/complete-single-upload.dto'
import { CreatePresignedUrlDto } from './dto/create-media.dto'
import { GetSignedFileUrlDto } from './dto/get-media.dto'
import {
  AbortMultipartUploadDto,
  CompleteMultipartUploadDto,
  CreateMultipartUploadDto,
  GetMultipartUploadPartUrlDto,
} from './dto/multipart-media.dto'
import { MediasService } from './medias.service'

@ApiTags('Medias')
@Route('medias')
@Controller('medias')
export class MediasController {
  constructor(private readonly mediasService: MediasService) {}

  @Get('files/:fileId/download-url')
  @ApiOperation({ summary: '<PERSON><PERSON>y presigned URL để tải xuống tệp đã được lưu trữ' })
  getSignedFileUrl(@Param('fileId') fileId: string, @Query() query: GetSignedFileUrlDto) {
    return this.mediasService.getSignedFileUrl(fileId, query)
  }

  @Get('files/:fileId/stream')
  @ApiOperation({ summary: 'Phát trực tiếp tệp có hỗ trợ Range cho audio/video' })
  async streamFile(
    @Param('fileId') fileId: string,
    @Query() query: GetSignedFileUrlDto,
    @Req() request: Request,
    @Res() response: Response
  ): Promise<void> {
    await this.mediasService.streamFile(fileId, query, request, response)
  }

  @Post('uploads/presign-single')
  @ApiOperation({ summary: 'Khởi tạo presigned URL cho tải lên đơn phần' })
  presignSingleUpload(@Body() createPresignedUrl: CreatePresignedUrlDto) {
    return this.mediasService.getSignedUploadUrl(createPresignedUrl)
  }

  @Post('signed-upload-url')
  @ApiOperation({ summary: 'Khởi tạo presigned URL cho tải lên đơn phần (legacy)' })
  legacyPresignSingleUpload(@Body() createPresignedUrl: CreatePresignedUrlDto) {
    return this.presignSingleUpload(createPresignedUrl)
  }

  @Post('uploads/initiate')
  @ApiOperation({ summary: 'Khởi tạo multipart upload cho tệp lớn' })
  createMultipartUpload(@Body() createMultipartUploadDto: CreateMultipartUploadDto) {
    return this.mediasService.createMultipartUpload(createMultipartUploadDto)
  }

  @Post('multipart/init')
  @ApiOperation({ summary: 'Khởi tạo multipart upload cho tệp lớn (legacy)' })
  legacyCreateMultipartUpload(@Body() createMultipartUploadDto: CreateMultipartUploadDto) {
    return this.createMultipartUpload(createMultipartUploadDto)
  }

  @Post('uploads/presign-part')
  @ApiOperation({ summary: 'Lấy presigned URL cho từng phần của multipart upload' })
  getMultipartUploadPartUrl(@Body() multipartUploadPartDto: GetMultipartUploadPartUrlDto) {
    return this.mediasService.getMultipartUploadPartUrl(multipartUploadPartDto)
  }

  @Post('multipart/sign-part')
  @ApiOperation({ summary: 'Lấy presigned URL cho từng phần của multipart upload (legacy)' })
  legacyGetMultipartUploadPartUrl(@Body() multipartUploadPartDto: GetMultipartUploadPartUrlDto) {
    return this.getMultipartUploadPartUrl(multipartUploadPartDto)
  }

  @Post('uploads/complete')
  @ApiOperation({ summary: 'Hoàn tất multipart upload' })
  completeMultipartUpload(@Body() completeMultipartUploadDto: CompleteMultipartUploadDto) {
    return this.mediasService.completeMultipartUpload(completeMultipartUploadDto)
  }

  @Post('uploads/complete-single')
  @ApiOperation({ summary: 'Hoàn tất tải lên đơn phần' })
  completeSingleUpload(@Body() completeSingleUploadDto: CompleteSingleUploadDto) {
    return this.mediasService.completeSingleUpload(completeSingleUploadDto)
  }

  @Post('multipart/complete')
  @ApiOperation({ summary: 'Hoàn tất multipart upload (legacy)' })
  legacyCompleteMultipartUpload(@Body() completeMultipartUploadDto: CompleteMultipartUploadDto) {
    return this.completeMultipartUpload(completeMultipartUploadDto)
  }

  @Post('uploads/abort')
  @ApiOperation({ summary: 'Huỷ multipart upload đang dang dở' })
  abortMultipartUpload(@Body() abortMultipartUploadDto: AbortMultipartUploadDto) {
    return this.mediasService.abortMultipartUpload(abortMultipartUploadDto)
  }

  @Post('multipart/abort')
  @ApiOperation({ summary: 'Huỷ multipart upload đang dang dở (legacy)' })
  legacyAbortMultipartUpload(@Body() abortMultipartUploadDto: AbortMultipartUploadDto) {
    return this.abortMultipartUpload(abortMultipartUploadDto)
  }
}
