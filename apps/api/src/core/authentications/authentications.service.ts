import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  RETURN_BOOLEAN,
  UnauthorizedException,
} from '@ac/common'
import {
  AccountRepository,
  SessionRepository,
  UserRepository,
  VerificationTokenRepository,
} from '@ac/models/data-access'
import { getExpires } from '@ac/models/entity/db.fn'
import { AC_ENVS, isProd, sendVerificationEmail } from '@ac/utils'
import hkdf from '@panva/hkdf'
import * as bcryptjs from 'bcryptjs'
import { randomBytes } from 'crypto'
import { EncryptJWT } from 'jose'
import { MoreThan } from 'typeorm'

import {
  CreateProviderUserDto,
  CreateSessionDto,
  GetProviderUserDto,
  GetSessionAndUserDto,
  LinkAccountDto,
  LoginDto,
  RegisterUserDto,
  ResendVerificationEmailDto,
  VerifyTokenDto,
} from './dto'

const AUTH_SECRET = AC_ENVS.AUTH_SECRET
const COOKIE_NAME = isProd() ? '__Secure-authjs.session-token' : 'authjs.session-token'

@Injectable()
export class AuthenticationsService {
  constructor(
    private readonly user: UserRepository,
    private readonly token: VerificationTokenRepository,
    private readonly account: AccountRepository,
    private readonly session: SessionRepository
  ) {}

  async createProviderUser(createProviderUserDto: CreateProviderUserDto) {
    const user = await this.user.create(createProviderUserDto)
    return user
  }

  async register(registerUserDto: RegisterUserDto) {
    registerUserDto.email = registerUserDto.email.toLowerCase()
    // Check if email already exists
    const existingUser = await this.user.findByEmail(registerUserDto.email)

    if (existingUser) {
      throw new BadRequestException('Email already used')
    }

    const password = await bcryptjs.hash(registerUserDto.password, 12)
    registerUserDto.password = password

    // Save user to database
    const createdUser = await this.user.create(registerUserDto)

    // Create verification token
    const token = randomBytes(32).toString('hex')

    // Save verification token
    const verificationToken = await this.token.create({
      identifier: registerUserDto.email,
      token,
      expires: getExpires(),
    })

    // Send verification email
    try {
      await sendVerificationEmail(registerUserDto.email, verificationToken.token)
    } catch (emailError) {
      console.error('Error when sending verification email:', emailError)
    }

    return {
      kind: 'user',
      ...createdUser,
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.user.findOneWithPassword(loginDto.email)

    if (!user) {
      throw new NotFoundException('User not found')
    }

    if (!user.password) {
      throw new UnauthorizedException('User password has not been created yet')
    }

    const passwordsMatch = await bcryptjs.compare(loginDto.password, user.password)

    if (!passwordsMatch) {
      throw new UnauthorizedException('Invalid password')
    }

    return {
      kind: 'user',
      ...user,
    }
  }

  async loginMobile(loginDto: LoginDto) {
    const user = await this.user.findOneWithPassword(loginDto.email)

    if (!user) {
      throw new NotFoundException('User not found')
    }

    if (!user.password) {
      throw new UnauthorizedException('User password has not been created yet')
    }

    const passwordsMatch = await bcryptjs.compare(loginDto.password, user.password)

    if (!passwordsMatch) {
      throw new UnauthorizedException('Invalid password')
    }

    // 4. Generate token
    const token = await this.generateToken({
      sub: user.id,
      name: user.name || '',
      email: user.email,
      picture: user.image || '',
    })

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userWithoutPassword } = user

    return {
      success: true,
      token,
      ...userWithoutPassword,
    }
  }

  async generateToken(payload: { sub: string; name: string; email: string; picture?: string }) {
    if (!AUTH_SECRET) {
      throw new Error('AUTH_SECRET is not configured')
    }

    // Generate encryption key giống NextAuth
    const encryptionKey = await hkdf(
      'sha256',
      AUTH_SECRET,
      COOKIE_NAME,
      `Auth.js Generated Encryption Key (${COOKIE_NAME})`,
      64
    )

    const sessionId = crypto.randomUUID()

    const tokenPayload = {
      ...payload,
      sessionId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30 days
    }

    // Encrypt JWT giống NextAuth
    const token = await new EncryptJWT(tokenPayload)
      .setProtectedHeader({ alg: 'dir', enc: 'A256CBC-HS512' })
      .setIssuedAt()
      .setExpirationTime('30d')
      .encrypt(encryptionKey)

    return token
  }

  async verifyToken(verifyTokenDto: VerifyTokenDto) {
    const email = verifyTokenDto.email
    const user = await this.user.findByEmail(email)

    if (!user) {
      throw new NotFoundException('User not found')
    }

    if (user.emailVerified) {
      return {
        kind: RETURN_BOOLEAN,
        value: true,
      }
    }

    await this.user.update(user.id, { emailVerified: new Date() })

    const verificationToken = await this.token.findOne({
      token: verifyTokenDto.token,
      identifier: verifyTokenDto.email,
      expires: MoreThan(new Date()),
    })

    if (!verificationToken) {
      throw new NotFoundException('Token not found')
    }

    await this.token.remove(verificationToken.identifier, verificationToken.token)

    return {
      kind: RETURN_BOOLEAN,
      value: true,
    }
  }

  async resendVerificationEmail(resendVerificationEmailDto: ResendVerificationEmailDto) {
    const user = await this.user.findByEmail(resendVerificationEmailDto.email)

    if (!user) {
      throw new NotFoundException('User not found')
    }

    if (user.emailVerified) {
      return {
        kind: RETURN_BOOLEAN,
        value: true,
      }
    }

    // count email verification token
    const countEmailVerificationToken = await this.token.count({
      identifier: user.email,
      expires: MoreThan(new Date()),
    })

    if (countEmailVerificationToken >= 3) {
      throw new BadRequestException('Email verification token limit reached in day')
    }

    const token = randomBytes(32).toString('hex')

    const verificationToken = await this.token.create({
      identifier: user.email,
      token,
      expires: getExpires(),
    })

    try {
      await sendVerificationEmail(user.email, verificationToken.token)
    } catch (error) {
      console.error('Error when sending verification email:', error)
      throw new InternalServerErrorException('Failed to send verification email')
    }

    return {
      kind: RETURN_BOOLEAN,
      value: true,
    }
  }

  async getProviderUser(getProviderUserDto: GetProviderUserDto) {
    const account = await this.account.findOne({
      provider: getProviderUserDto.provider,
      providerAccountId: getProviderUserDto.providerAccountId,
    })

    if (!account) {
      return {
        kind: RETURN_BOOLEAN,
        value: false,
      }
    }

    const user = await this.user.findOne({
      id: account.userId,
    })

    if (!user) {
      throw new NotFoundException('User not found')
    }

    return user
  }

  async linkAccount(linkAccountDto: LinkAccountDto) {
    const account = await this.account.create(linkAccountDto)

    return account
  }

  async getUserById(id: string) {
    const user = await this.user.findOne({
      id,
    })

    return user
      ? {
          kind: 'user',
          ...user,
        }
      : {
          kind: RETURN_BOOLEAN,
          value: false,
        }
  }

  async getUserByEmail(email: string) {
    const user = await this.user.findByEmail(email)

    return user
      ? {
          kind: 'user',
          ...user,
        }
      : {
          kind: RETURN_BOOLEAN,
          value: false,
        }
  }

  async createSession(createSessionDto: CreateSessionDto) {
    console.log(createSessionDto, 'createSessionDto')

    const session = await this.session.create(createSessionDto)

    return session
  }

  async getSessionAndUser(getSessionAndUserDto: GetSessionAndUserDto) {
    console.log(getSessionAndUserDto, 'getSessionAndUserDto')

    const session = await this.session.findOne({
      sessionToken: getSessionAndUserDto.sessionToken,
    })

    if (!session) {
      return {
        kind: RETURN_BOOLEAN,
        value: false,
      }
    }

    const user = await this.user.findOne({
      id: session.userId,
    })

    return {
      kind: 'session_user',
      session,
      user,
    }
  }
}
