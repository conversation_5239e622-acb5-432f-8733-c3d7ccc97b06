import { Body, Controller, Get, Param, Post, Route } from '@ac/common'
import { ApiBasicAuth } from '@nestjs/swagger'

import { AuthenticationsService } from './authentications.service'
import {
  CreateProviderUserDto,
  CreateSessionDto,
  GetProviderUserDto,
  GetSessionAndUserDto,
  LinkAccountDto,
  LoginDto,
  RegisterUserDto,
  ResendVerificationEmailDto,
  VerifyTokenDto,
} from './dto'

@Route('authentications')
@Controller('authentications')
@ApiBasicAuth()
export class AuthenticationsController {
  constructor(private readonly authenticationsService: AuthenticationsService) {}

  @Post('/method/register')
  register(@Body() registerUserDto: RegisterUserDto) {
    return this.authenticationsService.register(registerUserDto)
  }

  @Post('/method/create-provider-user')
  createProviderUser(@Body() createProviderUserDto: CreateProviderUserDto) {
    return this.authenticationsService.createProviderUser(createProviderUserDto)
  }

  @Post('/method/login')
  login(@Body() loginDto: LoginDto) {
    return this.authenticationsService.login(loginDto)
  }

  @Post('/method/login-mobile')
  loginMobile(@Body() loginDto: LoginDto) {
    return this.authenticationsService.loginMobile(loginDto)
  }

  @Post('/method/verify-token')
  verifyToken(@Body() verifyTokenDto: VerifyTokenDto) {
    return this.authenticationsService.verifyToken(verifyTokenDto)
  }

  @Post('/method/resend-verification-email')
  resendVerificationEmail(@Body() resendVerificationEmailDto: ResendVerificationEmailDto) {
    return this.authenticationsService.resendVerificationEmail(resendVerificationEmailDto)
  }

  @Post('/method/get-provider-user')
  getProviderUser(@Body() getProviderUserDto: GetProviderUserDto) {
    return this.authenticationsService.getProviderUser(getProviderUserDto)
  }

  @Post('/method/link-account')
  linkAccount(@Body() linkAccountDto: LinkAccountDto) {
    return this.authenticationsService.linkAccount(linkAccountDto)
  }

  @Get('/method/get-user-by-id/:id')
  getUserById(@Param('id') id: string) {
    return this.authenticationsService.getUserById(id)
  }

  @Get('/method/get-user-by-email/:email')
  getUserByEmail(@Param('email') email: string) {
    return this.authenticationsService.getUserByEmail(email)
  }

  @Post('/method/create-session')
  createSession(@Body() createSessionDto: CreateSessionDto) {
    return this.authenticationsService.createSession(createSessionDto)
  }

  @Post('/method/get-session-and-user')
  getSessionAndUser(@Body() getSessionAndUserDto: GetSessionAndUserDto) {
    return this.authenticationsService.getSessionAndUser(getSessionAndUserDto)
  }
}
