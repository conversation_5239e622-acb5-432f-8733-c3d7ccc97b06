import { AcRequest } from '@ac/be'
import {
  BadRequestException,
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  Inject,
  Injectable,
  PERMISSIONS,
  REQUEST,
  Scope,
  SUBJECT_TYPE,
} from '@ac/common'
import { User } from '@ac/models'
import { AssignRoleRepository, UserRepository } from '@ac/models/data-access'
import * as bcryptjs from 'bcryptjs'

import { CreateUserDto } from './dto/create-user.dto'
import { UpdateUserDto } from './dto/update-user.dto'

@Injectable({ scope: Scope.REQUEST })
export class UsersService {
  constructor(
    private readonly user: UserRepository,
    private readonly assignRole: AssignRoleRepository,
    @Inject(REQUEST) private readonly request: AcRequest
  ) {}
  async create(createUserDto: CreateUserDto): Promise<User & { kind: 'user' }> {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_USER_CREATE_ALL)
    createUserDto.email = createUserDto.email.toLowerCase()
    // Check if email already exists
    const existingUser = await this.user.findByEmail(createUserDto.email)

    if (existingUser) {
      throw new BadRequestException('Email đã được sử dụng')
    }

    if (createUserDto.password) {
      createUserDto.password = await bcryptjs.hash(createUserDto.password, 12)
    }

    // Save user to database
    const createdUser = await this.user.create({
      ...createUserDto,
      emailVerified: new Date(),
      status: 1,
    })

    return {
      kind: 'user',
      ...createdUser,
    }
  }

  async findAll(query: GetListQueryBaseAllDto) {
    return this.user.findAll(query)
  }

  async findAllAndCount(query: GetListQueryBaseDto) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_USER_READ_ALL)
    return this.user.findAllAndCount(query)
  }

  findOne(id: string) {
    this.request.metadata.getUser().isAllow(PERMISSIONS.PM06_USER_READ_DETAIL_ALL)
    return this.user.findOne({ id })
  }

  async getMyAssignedRoles() {
    const user = this.request.metadata.getUser()
    return this.assignRole.getSubjectRoles(user.getId(), SUBJECT_TYPE.USER)
  }

  async getUserAssignedRoles(id: string) {
    this.request.metadata.getUser().isAllows([PERMISSIONS.PM06_USER_CREATE_ALL, PERMISSIONS.PM06_USER_UPDATE_ALL])
    return this.assignRole.getSubjectRoles(id, SUBJECT_TYPE.USER)
  }

  update(id: string, updateUserDto: UpdateUserDto) {
    this.request.metadata.getUser().isAllows([PERMISSIONS.PM06_USER_UPDATE_ALL])
    return this.user.update(id, updateUserDto)
  }

  remove(id: string) {
    this.request.metadata.getUser().isAllows([PERMISSIONS.PM06_USER_DELETE_ALL])
    return this.user.remove(id)
  }
}
