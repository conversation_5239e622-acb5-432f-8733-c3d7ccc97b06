import {
  Body,
  Controller,
  Delete,
  Get,
  GetListQueryBaseAllDto,
  GetListQueryBaseDto,
  Param,
  Patch,
  Post,
  Queries,
  Query,
  Route,
} from '@ac/common'
import { User } from '@ac/models'
import { ApiBearerAuth } from '@nestjs/swagger'

import { CreateUserDto, UpdateUserDto } from './dto'
import { UsersService } from './users.service'

@Route('users')
@Controller('users')
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto)
  }

  @Get()
  findAllAndCount(@Queries() @Query() query: GetListQueryBaseDto) {
    return this.usersService.findAllAndCount(query)
  }

  @Get('/method/all')
  findAll(@Queries() @Query() query: GetListQueryBaseAllDto) {
    return this.usersService.findAll(query)
  }

  @Get('/method/my-assigned-roles')
  getMyAssignedRoles() {
    return this.usersService.getMyAssignedRoles()
  }

  @Get('/method/user-assigned-roles/:userId')
  getUserAssignedRoles(@Param('userId') userId: string) {
    return this.usersService.getUserAssignedRoles(userId)
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<User | null> {
    return this.usersService.findOne(id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto): Promise<{ affected?: number }> {
    return this.usersService.update(id, updateUserDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<{ affected?: number }> {
    return this.usersService.remove(id)
  }
}
