import {
  CoreResponseInterceptor,
  CustomConsoleLogger,
  CustomErrorInterceptor,
  DocumentBuilder,
  I18nResponseBodyFormatter,
  I18nValidationExceptionFilter,
  I18nValidationPipe,
  Logger,
  LoggingInterceptor,
  mapSwaggerResponse,
  NestFactory,
  StripUndefinedPipe,
  SwaggerModule,
  useRequestIdMiddleware,
  ValidationPipe,
} from '@ac/common'
import { generateTypesFromEntities } from '@ac/models/generate-frontend-types'
import { AC_ENVS, isProd } from '@ac/utils'

import { AppModule } from './app.module'
import * as swaggerResponse from './swagger.json'

export async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new CustomConsoleLogger({
      // json: true,
    }),
  })
  app.enableCors()

  app.use(useRequestIdMiddleware)
  app.useGlobalInterceptors(new CoreResponseInterceptor())
  app.setGlobalPrefix(AC_ENVS.GLOBAL_API_PREFIX)

  app.useGlobalPipes(
    new I18nValidationPipe(),
    new ValidationPipe({
      whitelist: true,
      transform: true,
    }),
    new StripUndefinedPipe()
  )

  app.useGlobalFilters(
    new I18nValidationExceptionFilter({
      detailedErrors: true,
      responseBodyFormatter: I18nResponseBodyFormatter,
    })
  )

  app.useGlobalInterceptors(new CustomErrorInterceptor())
  app.useGlobalInterceptors(new LoggingInterceptor())
  const COOKIE_NAME = isProd() ? '__Secure-authjs.session-token' : 'authjs.session-token'

  const config = new DocumentBuilder()
    .setTitle('Biz API')
    .setDescription('Biz API description')
    .setVersion('1.0')
    .addCookieAuth(COOKIE_NAME, {
      type: 'apiKey',
      in: 'cookie',
      name: COOKIE_NAME,
      description: 'Session token stored in cookie',
    })
    .addBasicAuth()
    .addBearerAuth()
    .build()
  let document = SwaggerModule.createDocument(app, config)

  document = mapSwaggerResponse(swaggerResponse, document, true, AC_ENVS.GLOBAL_API_PREFIX)
  // if (!isProd()) {
  //   writeJsonFile('./swaggers/biz/biz-api-swagger.json', document)
  // }

  SwaggerModule.setup(`${AC_ENVS.GLOBAL_API_PREFIX}/docs`, app, document, {
    customSiteTitle: 'Biz API',
    swaggerOptions: {
      persistAuthorization: true,
      withCredentials: true,
    },
  })

  // Start the server

  await app.listen(AC_ENVS.PORT)
  Logger.log(`🚀 AC APIs is swagger on: http://localhost:${AC_ENVS.PORT}/${AC_ENVS.GLOBAL_API_PREFIX}/docs`)
  Logger.log(`🚀 AC APIs is running on: http://localhost:${AC_ENVS.PORT}/${AC_ENVS.GLOBAL_API_PREFIX}`)

  if (!isProd()) {
    setTimeout(() => {
      const result = generateTypesFromEntities()
      Logger.log(`✅ AC Workflow is generate types success: ${result.outputDirCustom}`)
    }, 1000)
  }
}
