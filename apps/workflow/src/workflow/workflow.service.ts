import { AcRequest } from '@ac/be'
import { HoSoData, IHanhDongHoSo, QuyTrinhThucHien, RHanhDongHoSo, VU_VIEC_TASK_QUEUE } from '@ac/bpmn'
import {
  cbNopBoSungSignal,
  dongTgplSignal,
  dongYTiepNhanSignal,
  duyetKetQuaTgplSignal,
  duyetPhanCongTgplSignal,
  duyetThamDinhTgplSignal,
  getHoSoDataQuery,
  hoSoWorkflow,
  nhapKetQuaTgplSignal,
  nhapKqThamDinhTgplChatLuongSignal,
  nhapKqThamDinhTgplThoiGianSignal,
  nhapKqThamMuuSignal,
  nopLuuTruTgplSignal,
  phanCongThamDinhTgplSignal,
  tinhToanBuocTienVaLui,
  yeuCauCbBoSungSignal,
  yeuCauDuyetPhanCongTgplSignal,
  yeuCauThamMuuSignal,
} from '@ac/bpmn/workflows'
import { Any, BadRequestException, Inject, Injectable, LimitDepthLevel2, REQUEST, Scope } from '@ac/common'
import {
  CaseAdvancedRepository,
  DatabaseService,
  EnumCaseAdvancedStatus,
  WorkflowHistory,
  WorkflowHistoryRepository,
  WorkflowHistoryStatus,
  WorkflowRepository,
} from '@ac/models'
import { isProd } from '@ac/utils'
import { Client } from '@temporalio/client'
import { writeFileSync } from 'fs'
import { Transactional } from 'typeorm-transactional'

import {
  BatDauXuLyDto,
  CbNopBoSungDto,
  DongTgplDto,
  DongYTiepNhanDto,
  DuyetKetQuaTgplDto,
  DuyetPhanCongTgplDto,
  DuyetThamDinhTgplDto,
  NhapKetQuaTgplDto,
  NhapKqThamDinhTgplDto,
  NhapKqThamMuuDto,
  NopLuuTruTgplDto,
  PhanCongThamDinhTgplDto,
  YeuCauCbBoSungDto,
  YeuCauDuyetPhanCongTgplDto,
  YeuCauThamMuuDto,
} from './dto'

@Injectable({ scope: Scope.REQUEST })
export class WorkflowService {
  constructor(
    @Inject('WORKFLOW_CLIENT') private readonly client: Client,
    private db: DatabaseService,
    @Inject(REQUEST) private readonly request: AcRequest,
    private readonly caseAdvancedRepository: CaseAdvancedRepository,
    private readonly workflowRepository: WorkflowRepository,
    private readonly workflowHistoryRepository: WorkflowHistoryRepository
  ) {}

  @Transactional()
  async batDauXuLy(caseAdvanceId: string, startWorkflowDto: BatDauXuLyDto) {
    console.log('✅[Service]  Bắt đầu xử lý workflow:', { caseAdvanceId })
    const user = this.request.metadata.getUser()
    const { caseAdvanced, process } = await this._checkProcessAndCaseAdvance(caseAdvanceId)

    if (caseAdvanced.status !== EnumCaseAdvancedStatus.CHO_TIEP_NHAN) {
      throw new BadRequestException('Lỗi, vụ việc đã được bắt đầu xử lý')
    }

    if (caseAdvanced.currentWorkflowId) {
      throw new BadRequestException('Lỗi, vụ việc đã được bắt đầu xử lý luồng')
    }

    const workflow = await this.workflowRepository.create({
      data: JSON.stringify(startWorkflowDto),
      processId: process.id,
      caseAdvancedId: caseAdvanced.id,
      orgUnitId: caseAdvanced.orgUnitId,
      startedById: user.getId(),
      processData: process.jsonData,
    })

    const quyTrinh = process.jsonData as unknown as QuyTrinhThucHien
    quyTrinh.id = process.id

    // Tự động tính toán bước tiến và lùi lại cho tất cả bước
    tinhToanBuocTienVaLui(quyTrinh)

    if (!isProd()) {
      writeFileSync('process-test.json', JSON.stringify(quyTrinh, null, 2))
    }

    const initialData: HoSoData = {
      id: caseAdvanced.id,
      citizenId: caseAdvanced.rpCardNumber ?? 'NA',
      buocHienTai: quyTrinh.buocDau || quyTrinh.cacBuoc[0].id,
      files: [],
      history: [{ timestamp: new Date(), message: 'Chuyển xử lý' }],
      quyTrinhId: process.id,
      quyTrinh,
    }

    const handle = await this.client.workflow.start(hoSoWorkflow, {
      taskQueue: VU_VIEC_TASK_QUEUE,
      workflowId: workflow.id,
      args: [initialData],
    })

    console.log('✅[Service]  Tạo workflow mới:', { currentWorkflowId: workflow.id })
    await this.caseAdvancedRepository.update(caseAdvanced.id, {
      currentWorkflowId: workflow.id,
      status: EnumCaseAdvancedStatus.DANG_XU_LY,
    })

    // Lazy Creation: Chỉ tạo history cho bước đầu tiên
    const buocDau = quyTrinh.cacBuoc[0]

    const workflowHistory = await this.createHistoryForStep({
      stepId: buocDau.id,
      workflow,
      process,
      caseAdvanced,
      actions: buocDau.hanhDongChoPhep,
      currentHistoryId: undefined, // Bước đầu không có parent
      selectedAction: undefined, // Bước đầu không có action từ bước trước
    })

    console.log('✅[Service]  Tạo history cho bước đầu tiên:', workflowHistory?.id)

    await this.workflowRepository.updateHistories(workflow.id, { temporalId: handle.workflowId })

    await this.workflowRepository.findOne({ id: workflow.id })

    // if (isProd() || AC_ENVS.DB_TYPE === 'oracle') {
    //   return {
    //     success: true,
    //   }
    // }

    return {
      // updatedWorkflow: updatedWorkflow, // as LimitDepthLevel1<Workflow>,
      workflowHistory: workflowHistory, // as LimitDepthLevel1<WorkflowHistory>,
    }
  }

  async dongYTiepNhan({
    caseAdvanceId,
    dongYTiepNhanDto,
    historyId,
  }: {
    caseAdvanceId: string
    dongYTiepNhanDto: DongYTiepNhanDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.DONG_Y_TIEP_NHAN,
    })

    // Gửi signal đến temporal workflow
    const handle = this.client.workflow.getHandle(history.workflowId)
    await handle.signal(dongYTiepNhanSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: dongYTiepNhanDto.content,
    })

    // Workflow sẽ tự sync database qua activity, không cần handleStepAction ở đây
    await this._delay()

    return {
      caseAdvanced,
      process,
      currentHistory: history,
      nextHistory: null, // Sẽ được update từ activity
    }
  }

  async _delay(ms: number = 500) {
    console.log(`🔄 Chờ WorkflowService ${ms}ms...`)
    return new Promise(resolve => setTimeout(resolve, ms))
    // return new Promise(resolve => setTimeout(resolve, 0))
  }

  async nhanTask({ caseAdvanceId, historyId }: { caseAdvanceId: string; historyId: string }) {
    const userId = this.request.metadata.getUser().getId()
    const { history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
    })

    if (history.assignedToId && history.assignedToId !== userId) {
      throw new BadRequestException(`Lỗi, việc đã được phân công cho ${history.assignedTo?.name}`)
    }

    if (history.assignedToId && history.assignedToId === userId) {
      return { history }
    }

    history.assignedToId = userId
    await this.workflowHistoryRepository.update(history.id, { assignedToId: userId, assignedAt: new Date() })
    await this.workflowRepository.updateHistories(history.workflowId)

    return { history }
  }

  async _checkProcessAndCaseAdvance(caseAdvanceId: string) {
    const caseAdvanced = await this._findCaseAdvance(caseAdvanceId)
    if (!caseAdvanced) {
      throw new BadRequestException('Lỗi, không thể tiếp nhận vụ việc do vụ việc đang trong quá trình xử lý.')
    }

    const process = await this._findProcess()
    if (!process) {
      throw new BadRequestException('Lỗi, không tìm thấy quy trinh mặc định (Đang hoạt động và Mặc định)')
    }

    return { caseAdvanced, process }
  }

  async _checkXuLy({
    caseAdvanceId,
    historyId,
    action,
  }: {
    caseAdvanceId: string
    action?: IHanhDongHoSo
    historyId?: string
  }) {
    const userId = this.request.metadata.getUser().getId()
    const { caseAdvanced, process } = await this._checkProcessAndCaseAdvance(caseAdvanceId)

    const history = await this.workflowHistoryRepository.findOne(
      {
        id: historyId,
        processId: process.id,
        caseAdvancedId: caseAdvanced.id,
      },
      {
        assignedTo: true,
      }
    )

    if (!history) {
      throw new BadRequestException('Lỗi, không tìm thấy lịch sử hồ sơ')
    }

    if (history.assignedToId && history.assignedToId !== userId) {
      throw new BadRequestException(`Lỗi 1, việc đã được phân công cho ${history.assignedTo?.name}`)
    }

    if (action && !history.actions.includes(action)) {
      throw new BadRequestException(
        `Lỗi, hành động với lịch sử hồ sơ không phù hợp, hành động cho phép là ${JSON.stringify({ historyId: history.id, actions: history.actions })}, hành động hiện tại: ${action}`
      )
    }

    // TODO: Check phân quyền

    return { caseAdvanced, process, history }
  }

  // _isNeedAssignTo(hanhDong: string) {
  //   return ![
  //     RHanhDongHoSo.DONG_Y_TIEP_NHAN,
  //     // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  //   ].includes(hanhDong as Any)
  // }

  async _findCaseAdvance(caseAdvanceId: string) {
    const item = await this.caseAdvancedRepository.findById(caseAdvanceId)

    if (!item) {
      throw new BadRequestException('Không tìm thấy vụ việc bạn đang thao tác')
    }

    // if (item.status !== EnumCaseAdvancedStatus.DANG_XU_LY) {
    //   throw new BadRequestException('Không thể tiếp nhận vụ việc do vụ việc đang trong quá trình xử lý.')
    // }

    return item
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async _findProcess(processId?: string) {
    const item = await this.db.process.findOne({ where: { isDefault: 1 } })

    if (!item) {
      throw new BadRequestException('Lỗi, không tìm thấy quy trinh mặc định (Đang hoạt động và Mặc định)')
    }

    return item
  }

  /**
   * Tạo WorkflowHistory cho một bước (Lazy Creation)
   */
  async createHistoryForStep({
    stepId,
    workflow,
    process,
    caseAdvanced,
    actions,
    currentHistoryId,
    selectedAction,
    fixedForm,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    assignedToIds = [],
  }: {
    stepId: string
    workflow: Any
    process: Any
    caseAdvanced: Any
    actions: string[]
    fixedForm?: string
    currentHistoryId?: string
    selectedAction?: string
    assignedToIds?: string[]
  }) {
    if (stepId === 'ket_qua_tgpl') {
      return null
    }
    // Tìm bước trong quy trình
    const quyTrinh = process.jsonData as unknown as QuyTrinhThucHien
    const buoc = quyTrinh.cacBuoc.find(b => b.id === stepId)

    if (!buoc) {
      throw new BadRequestException(`Không tìm thấy bước ${stepId} trong quy trình`)
    }

    // Tạo WorkflowHistory với lazy creation approach
    const workflowHistory = await this.workflowHistoryRepository.create({
      stepId: buoc.id,
      stepName: buoc.ten,
      actions, // Actions có thể thực hiện (Oracle friendly)
      selectedAction,
      parentHistoryId: currentHistoryId,
      processId: process.id,
      workflowId: workflow.id,
      caseAdvancedId: caseAdvanced.id,
      orgUnitId: caseAdvanced.orgUnitId,
      fixedForm,
      // TODO: Fix lại logic assign cho user
      // assignedToId: assignedToIds.length > 0 ? assignedToIds[0] : undefined, // Assign cho user đầu tiên nếu có
      data: {} as Any,
    })

    return await this.workflowHistoryRepository.findOne({ id: workflowHistory.id })
  }

  /**
   * Xử lý action với sync temporal state và kiểm tra parallel flows
   */
  async handleStepAction({
    currentHistoryId,
    selectedAction,
    content,
  }: {
    currentHistoryId: string
    selectedAction: string
    content?: object
  }) {
    return { currentHistory: null, nextHistory: null }
    const currentHistory = await this.workflowHistoryRepository.findOne(
      { id: currentHistoryId },
      { workflow: true, process: true, caseAdvanced: true }
    )

    if (!currentHistory) {
      throw new BadRequestException('Không tìm thấy lịch sử workflow')
    }

    // Get handle để query temporal state SAU khi signal đã được xử lý
    const handle = this.client.workflow.getHandle(currentHistory.workflowId)

    // Query temporal state hiện tại (signal đã được gửi trước đó)
    const temporalState = await handle.query(getHoSoDataQuery)
    console.log('🔄 Temporal state after signal:', {
      temporalCurrentStep: temporalState.buocHienTai,
      dbCurrentStep: currentHistory.stepId,
      hasMovedToNextStep: temporalState.buocHienTai !== currentHistory.stepId,
    })

    // Update current history với action đã chọn
    await this.workflowHistoryRepository.update(currentHistory.id, {
      selectedAction,
      data: { ...currentHistory.data, content, actionPerformedAt: new Date() },
    })

    // Nếu temporal đã chuyển bước, complete current history và tạo next history
    if (temporalState.buocHienTai !== currentHistory.stepId) {
      console.log('✅[Service]  Temporal đã chuyển bước - completing current history')

      // Complete bước hiện tại
      await this.workflowHistoryRepository.update(currentHistory.id, {
        status: WorkflowHistoryStatus.COMPLETED,
        data: { ...currentHistory.data, content, completedAt: new Date() },
      })

      // Sync và tạo history cho bước mới
      // const syncedHistory = await this.syncAndCreateHistoryForCurrentStep(
      //   currentHistory.workflowId,
      //   temporalState,
      //   currentHistory.id,
      //   selectedAction
      // )

      // if (syncedHistory) {
      //   return { currentHistory, nextHistory: syncedHistory }
      // }
    } else {
      console.log('⏸️ Temporal chưa chuyển bước - đang chờ parallel actions khác hoàn thành')
      // Không complete history, chỉ update với action đã thực hiện
    }

    return { currentHistory, nextHistory: null }
  }

  /**
   * Sync temporal state và tạo history cho bước hiện tại nếu chưa có
   */
  private async syncAndCreateHistoryForCurrentStep(
    workflowId: string,
    temporalState: Any,
    currentHistoryId: string,
    selectedAction: string,
    // isStepBack chỉ để debug
    isStepBack: boolean = false,
    fixedForm?: string
  ) {
    try {
      if (isStepBack) {
        console.log('↩️[Service]  [Debug] Back step detected while syncing history creation')
      }
      // Tìm workflow trong database
      const workflow = await this.workflowRepository.findOne({ id: workflowId })
      if (!workflow) {
        console.log('❌Service]  Không tìm thấy workflow trong database')
        return null
      }

      // Kiểm tra xem có history cho bước hiện tại chưa
      const existingHistory = await this.workflowHistoryRepository.findOne({
        workflowId: workflow.id,
        stepId: temporalState.buocHienTai,
        status: WorkflowHistoryStatus.PENDING,
      })

      if (existingHistory) {
        console.log('✅[Service]  History đã tồn tại cho bước hiện tại:', {
          stepId: temporalState.buocHienTai,
          historyId: existingHistory.id,
        })
        return existingHistory
      }

      // Tạo history cho bước hiện tại
      const quyTrinh = temporalState.quyTrinh as QuyTrinhThucHien
      const currentStep = quyTrinh.cacBuoc.find(b => b.id === temporalState.buocHienTai)

      if (!currentStep) {
        console.log('❌Service]  Không tìm thấy bước hiện tại trong quy trình:', temporalState.buocHienTai)
        return null
      }

      const process = await this.db.process.findOne({ where: { id: quyTrinh.id } })
      const caseAdvanced = await this.db.caseAdvanced.findOne({ where: { id: temporalState.id } })

      if (!process || !caseAdvanced) {
        console.log('❌Service]  Không tìm thấy process hoặc caseAdvanced')
        return null
      }

      // Tìm connection info để get assignedToIds
      const currentHistory = await this.workflowHistoryRepository.findOne(
        { id: currentHistoryId },
        { parentHistory: true, process: true }
      )

      let assignedToIds: string[] = []
      if (currentHistory) {
        const parentQuyTrinh = currentHistory.process!.jsonData as Any
        const parentStep = parentQuyTrinh.cacBuoc.find((b: Any) => b.id === currentHistory.stepId)
        const ketNoi = parentStep?.ketNoiDen?.find((k: Any) => k.hanhDong === selectedAction)
        assignedToIds = ketNoi?.canBoIds || []
      }

      // *** KIỂM TRA PARALLEL PROCESS ***
      if (currentStep.isParallelProcess) {
        console.log('🔥[Service]  Parallel process detected - tạo multiple histories cho:', currentStep.hanhDongChoPhep)

        // Tạo một history riêng cho mỗi action trong parallel process
        const parallelHistories = []
        console.log(currentStep.hanhDongTienLen, 'currentStep.hanhDongTienLen')
        for (const action of currentStep.hanhDongTienLen || []) {
          const parallelHistory = await this.createHistoryForStep({
            stepId: currentStep.id,
            workflow,
            process,
            caseAdvanced,
            actions: [action], // Mỗi history chỉ có 1 action riêng
            currentHistoryId,
            selectedAction,
            assignedToIds: [], // Không assign, để user tự nhận task
          })
          parallelHistories.push(parallelHistory)
          console.log(`   ✅ Tạo parallel history cho action ${action}:`, parallelHistory?.id)
        }

        // Trả về history đầu tiên (để compatibility)
        return parallelHistories[0]
      } else {
        console.log('[Service] tạo không kết nối đơn', isStepBack, typeof isStepBack)

        let actions = []
        let parentHistoryId = null
        if (isStepBack) {
          if (selectedAction === RHanhDongHoSo.CB_NOP_BO_SUNG) {
            actions = currentHistory?.parentHistory?.actions || []
            parentHistoryId = currentHistory?.parentHistory?.parentHistoryId
          } else {
            actions = [RHanhDongHoSo.CB_NOP_BO_SUNG, RHanhDongHoSo.TU_CHOI]
          }
        } else {
          actions = currentStep.hanhDongChoPhep
        }

        const baseData = {
          stepId: currentStep.id,
          actions,
          currentHistoryId,
          parentHistoryId,
          selectedAction: undefined,
          fixedForm: fixedForm,
          assignedToIds,
        }

        const createData = {
          workflow,
          process,
          caseAdvanced,
          ...baseData,
        }
        // const headers = this.request.headers as Any
        // console.log('✅[Service]  Tạo history mới cho bước hiện tại:', headers['x-request-id'])
        const newHistory = await this.createHistoryForStep(createData)

        return newHistory
      }
    } catch (error) {
      console.log('❌Service] [Service]  Lỗi sync và tạo history:', error)
      return null
    }
  }

  /**
   * Truy vấn công việc theo action/role (Oracle friendly với LIKE)
   */
  async queryWorksByAction(actions: string[], userId?: string): Promise<object[]> {
    if (!actions.length) {
      return []
    }

    const queryBuilder = this.db.workflowHistory
      .createQueryBuilder('wh')
      .leftJoinAndSelect('wh.caseAdvanced', 'ca')
      .leftJoinAndSelect('wh.assignedTo', 'user')
      .leftJoinAndSelect('wh.workflow', 'w')
      .where('wh.status = :status', { status: WorkflowHistoryStatus.PENDING })

    // Oracle-friendly LIKE query cho actions - tìm bất kì action nào trong mảng
    const conditions = actions.map((_, index) => `wh.actions LIKE :actionPattern${index}`).join(' OR ')
    const params: Record<string, string> = {}

    actions.forEach((action, index) => {
      params[`actionPattern${index}`] = `%${action}%`
    })

    queryBuilder.andWhere(`(${conditions})`, params)

    // Nếu có userId, chỉ lấy công việc được assign hoặc chưa assign
    if (userId) {
      queryBuilder.andWhere('(wh.assignedToId IS NULL OR wh.assignedToId = :userId)', { userId })
    }

    const works = await queryBuilder.orderBy('wh.createdAt', 'DESC').getMany()

    return works
  }

  /**
   * Xử lý hành động chung cho bước workflow với temporal signal
   */
  async xuLyHanhDong({
    caseAdvanceId,
    historyId,
    selectedAction,
    content,
    fixedForm,
  }: {
    caseAdvanceId: string
    historyId: string
    selectedAction: string
    content?: string
    fixedForm?: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: selectedAction as IHanhDongHoSo,
    })

    // Kiểm tra action có hợp lệ không
    if (!history.actions?.includes(selectedAction)) {
      throw new BadRequestException(
        `Hành động '${selectedAction}' không được phép cho bước này. Các hành động hợp lệ: ${history?.actions?.join(', ')}`
      )
    }

    // Gửi signal tương ứng đến temporal workflow
    await this.sendSignalForAction(history, selectedAction, content, fixedForm)

    // Workflow sẽ tự sync database qua activity, không cần handleStepAction ở đây

    return {
      caseAdvanced,
      process,
      currentHistory: history,
      nextHistory: null, // Sẽ được update từ activity
    }
  }

  /**
   * Gửi signal tương ứng với action đến temporal workflow
   */
  private async sendSignalForAction(
    history: LimitDepthLevel2<WorkflowHistory>,
    selectedAction: string,
    content?: string,
    fixedForm?: string
  ) {
    const handle = this.client.workflow.getHandle(history.workflowId)
    const actor = {
      id: history.assignedToId!,
      name: history.assignedTo?.name || 'N/A',
    }

    const signalMap = {
      [RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG]: yeuCauCbBoSungSignal,
      [RHanhDongHoSo.CB_NOP_BO_SUNG]: cbNopBoSungSignal,
      [RHanhDongHoSo.DONG_Y_TIEP_NHAN]: dongYTiepNhanSignal,
      [RHanhDongHoSo.YEU_CAU_DUYET_PHAN_CONG_TGPL]: yeuCauDuyetPhanCongTgplSignal,
      [RHanhDongHoSo.YEU_CAU_THAM_MUU]: yeuCauThamMuuSignal,
      [RHanhDongHoSo.NHAP_KQ_THAM_MUU]: nhapKqThamMuuSignal,
      [RHanhDongHoSo.DUYET_PHAN_CONG_TGPL]: duyetPhanCongTgplSignal,
      [RHanhDongHoSo.NHAP_KET_QUA_TGPL]: nhapKetQuaTgplSignal,
      [RHanhDongHoSo.DUYET_KET_QUA_TGPL]: duyetKetQuaTgplSignal,
      [RHanhDongHoSo.NOP_LUU_TRU_TGPL]: nopLuuTruTgplSignal,
      [RHanhDongHoSo.PHAN_CONG_THAM_DINH_TGPL]: phanCongThamDinhTgplSignal,
      [RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN]: nhapKqThamDinhTgplThoiGianSignal,
      [RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG]: nhapKqThamDinhTgplChatLuongSignal,
      [RHanhDongHoSo.DUYET_THAM_DINH_TGPL]: duyetThamDinhTgplSignal,
      [RHanhDongHoSo.DONG_TGPL]: dongTgplSignal,
    } as const

    const signal = signalMap[selectedAction as keyof typeof signalMap]
    if (!signal) {
      throw new BadRequestException(`Không tìm thấy signal cho action: ${selectedAction}`)
    }

    const signalPayload: Record<string, unknown> = { actor, content }

    // Xử lý payload đặc biệt cho một số actions
    if (selectedAction === RHanhDongHoSo.CB_NOP_BO_SUNG) {
      signalPayload.newFiles = [] // TODO: Xử lý file upload
    } else if (selectedAction === RHanhDongHoSo.NHAP_KQ_THAM_MUU) {
      signalPayload.assignedTo = { id: 'TODO', name: 'TODO' } // TODO: Get from context
    } else if (
      selectedAction === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN ||
      selectedAction === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG
    ) {
      signalPayload.newFiles = [] // TODO: Xử lý file upload
    } else if (selectedAction === RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG) {
      signalPayload.buocHienTai = history.stepId
      signalPayload.fixedForm = fixedForm
    }

    signalPayload.historyId = history.id

    await handle.signal(signal, signalPayload as Any)
    console.log(`📡 Signal sent: ${selectedAction}`)
  }

  /**
   * Lấy danh sách công việc của user hiện tại
   */
  async getMyWorks(): Promise<object[]> {
    const userId = this.request.metadata.getUser().getId()

    const works = await this.db.workflowHistory
      .createQueryBuilder('wh')
      .leftJoin('wh.caseAdvanced', 'ca')
      .leftJoin('wh.workflow', 'w')
      .leftJoin('wh.process', 'p')
      .where('wh.status = :status', { status: WorkflowHistoryStatus.PENDING })
      .andWhere('(wh.assignedToId IS NULL OR wh.assignedToId = :userId)', { userId })
      .orderBy('wh.createdAt', 'DESC')
      .getMany()

    return works.map((work: WorkflowHistory) => ({
      ...work,
    }))
  }

  /**
   * Thống kê số lượng công việc của tôi theo từng hành động
   */
  async getMyWorksStatsByAction() {
    const userId = this.request.metadata.getUser().getId()

    const works = await this.db.workflowHistory
      .createQueryBuilder('wh')
      .where('wh.status = :status', { status: WorkflowHistoryStatus.PENDING })
      .andWhere('(wh.assignedToId IS NULL OR wh.assignedToId = :userId)', { userId })
      .getMany()

    // Khởi tạo object thống kê với tất cả actions = 0
    const stats: { [key in IHanhDongHoSo]: number } = {} as { [key in IHanhDongHoSo]: number }
    Object.keys(RHanhDongHoSo).forEach(action => {
      stats[action as IHanhDongHoSo] = 0
    })

    // Đếm số lượng theo từng action
    works.forEach(work => {
      if (work.actions && Array.isArray(work.actions)) {
        work.actions.forEach(action => {
          if (action in RHanhDongHoSo) {
            stats[action as IHanhDongHoSo]++
          }
        })
      }
    })

    return stats
  }

  /**
   * Lấy danh sách công việc theo action/role
   */
  async getWorksByAction(action: string) {
    const actions = action.split(',')
    const works = await this.queryWorksByAction(actions)

    return works.map((work: WorkflowHistory) => ({
      ...work,
    }))
  }

  /**
   * Lấy lịch sử workflow của một case
   */
  async getWorkflowHistory(caseAdvanceId: string) {
    const histories = await this.db.workflowHistory
      .createQueryBuilder('wh')
      .leftJoinAndSelect('wh.assignedTo', 'user')
      .leftJoinAndSelect('wh.parentHistory', 'parent')
      .where('wh.caseAdvancedId = :caseAdvanceId', { caseAdvanceId })
      .orderBy('wh.createdAt', 'ASC')
      .getMany()

    return histories.map((h: WorkflowHistory) => ({
      ...h,
      availableActions: h.status === WorkflowHistoryStatus.PENDING ? h.actions : [],
    }))
  }

  // ===== WORKFLOW ACTION METHODS =====

  async yeuCauCbBoSung({
    caseAdvanceId,
    yeuCauCbBoSungDto,
    historyId,
  }: {
    caseAdvanceId: string
    yeuCauCbBoSungDto: YeuCauCbBoSungDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(yeuCauCbBoSungSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: yeuCauCbBoSungDto.content,
      buocHienTai: history.stepId,
      fixedForm: yeuCauCbBoSungDto.fixedForm,
    })

    // Sử dụng Lazy Creation để xử lý bước tiếp theo
    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.YEU_CAU_CB_NOP_BO_SUNG,
      content: yeuCauCbBoSungDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async cbNopBoSung({
    caseAdvanceId,
    cbNopBoSungDto,
    historyId,
  }: {
    caseAdvanceId: string
    cbNopBoSungDto: CbNopBoSungDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.CB_NOP_BO_SUNG,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(cbNopBoSungSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      newFiles: [], // TODO: Xử lý file upload
      content: cbNopBoSungDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.CB_NOP_BO_SUNG,
      content: cbNopBoSungDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async yeuCauDuyetPhanCongTgpl({
    caseAdvanceId,
    yeuCauDuyetPhanCongTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    yeuCauDuyetPhanCongTgplDto: YeuCauDuyetPhanCongTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.YEU_CAU_DUYET_PHAN_CONG_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(yeuCauDuyetPhanCongTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: yeuCauDuyetPhanCongTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.YEU_CAU_DUYET_PHAN_CONG_TGPL,
      content: yeuCauDuyetPhanCongTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async yeuCauThamMuu({
    caseAdvanceId,
    yeuCauThamMuuDto,
    historyId,
  }: {
    caseAdvanceId: string
    yeuCauThamMuuDto: YeuCauThamMuuDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.YEU_CAU_THAM_MUU,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(yeuCauThamMuuSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: yeuCauThamMuuDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.YEU_CAU_THAM_MUU,
      content: yeuCauThamMuuDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async nhapKqThamMuu({
    caseAdvanceId,
    nhapKqThamMuuDto,
    historyId,
  }: {
    caseAdvanceId: string
    nhapKqThamMuuDto: NhapKqThamMuuDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.NHAP_KQ_THAM_MUU,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(nhapKqThamMuuSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      assignedTo: {
        id: 'TODO',
        name: 'TODO', // TODO: Get from DTO or context
      },
      content: nhapKqThamMuuDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.NHAP_KQ_THAM_MUU,
      content: nhapKqThamMuuDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async duyetPhanCongTgpl({
    caseAdvanceId,
    duyetPhanCongTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    duyetPhanCongTgplDto: DuyetPhanCongTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.DUYET_PHAN_CONG_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(duyetPhanCongTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: duyetPhanCongTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.DUYET_PHAN_CONG_TGPL,
      content: duyetPhanCongTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async nhapKetQuaTgpl({
    caseAdvanceId,
    nhapKetQuaTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    nhapKetQuaTgplDto: NhapKetQuaTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.NHAP_KET_QUA_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(nhapKetQuaTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: nhapKetQuaTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.NHAP_KET_QUA_TGPL,
      content: nhapKetQuaTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async duyetKetQuaTgpl({
    caseAdvanceId,
    duyetKetQuaTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    duyetKetQuaTgplDto: DuyetKetQuaTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.DUYET_KET_QUA_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(duyetKetQuaTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: duyetKetQuaTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.DUYET_KET_QUA_TGPL,
      content: duyetKetQuaTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async nopLuuTruTgpl({
    caseAdvanceId,
    nopLuuTruTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    nopLuuTruTgplDto: NopLuuTruTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.NOP_LUU_TRU_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(nopLuuTruTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: nopLuuTruTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.NOP_LUU_TRU_TGPL,
      content: nopLuuTruTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async phanCongThamDinhTgpl({
    caseAdvanceId,
    phanCongThamDinhTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    phanCongThamDinhTgplDto: PhanCongThamDinhTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.PHAN_CONG_THAM_DINH_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(phanCongThamDinhTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: phanCongThamDinhTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.PHAN_CONG_THAM_DINH_TGPL,
      content: phanCongThamDinhTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async nhapKqThamDinhTgplThoiGian({
    caseAdvanceId,
    nhapKqThamDinhTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    nhapKqThamDinhTgplDto: NhapKqThamDinhTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(nhapKqThamDinhTgplThoiGianSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: nhapKqThamDinhTgplDto.content,
      newFiles: [], // TODO: Xử lý file upload
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_THOI_GIAN,
      content: nhapKqThamDinhTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async nhapKqThamDinhTgplChatLuong({
    caseAdvanceId,
    nhapKqThamDinhTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    nhapKqThamDinhTgplDto: NhapKqThamDinhTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(nhapKqThamDinhTgplChatLuongSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: nhapKqThamDinhTgplDto.content,
      newFiles: [], // TODO: Xử lý file upload
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG,
      content: nhapKqThamDinhTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async duyetThamDinhTgpl({
    caseAdvanceId,
    duyetThamDinhTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    duyetThamDinhTgplDto: DuyetThamDinhTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.DUYET_THAM_DINH_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(duyetThamDinhTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: duyetThamDinhTgplDto.content,
    })

    const { currentHistory, nextHistory } = await this.handleStepAction({
      currentHistoryId: history.id,
      selectedAction: RHanhDongHoSo.DUYET_THAM_DINH_TGPL,
      content: duyetThamDinhTgplDto.content,
    })

    return {
      caseAdvanced,
      process,
      currentHistory,
      nextHistory,
    }
  }

  async dongTgpl({
    caseAdvanceId,
    dongTgplDto,
    historyId,
  }: {
    caseAdvanceId: string
    dongTgplDto: DongTgplDto
    historyId: string
  }) {
    const { caseAdvanced, process, history } = await this._checkXuLy({
      caseAdvanceId,
      historyId,
      action: RHanhDongHoSo.DONG_TGPL,
    })

    const handle = this.client.workflow.getHandle(history.workflowId)

    await handle.signal(dongTgplSignal, {
      historyId,
      actor: {
        id: history.assignedToId!,
        name: history.assignedTo?.name || 'N/A',
      },
      content: dongTgplDto.content,
    })

    // Không gọi handleStepAction ở đây nữa, vì workflow sẽ tự sync qua activity

    return {
      caseAdvanced,
      process,
      currentHistory: history,
      nextHistory: null, // Sẽ được update từ activity
    }
  }

  /**
   * Sync database state từ Temporal workflow (được gọi từ activity)
   */
  @Transactional()
  async syncDatabaseFromTemporal({
    workflowId,
    currentStepId,
    currentHistoryId,
    selectedAction,
    isStepBack,
    content,
    fixedForm,
  }: {
    workflowId: string
    currentStepId: string
    currentHistoryId: string
    selectedAction: string
    isStepBack: boolean
    content: Any
    fixedForm?: string
  }) {
    const headers = this.request.headers as Any
    try {
      console.log(
        `🔄 [Service] Syncing request ${headers['x-request-id']} database for workflow ${workflowId}, step ${currentStepId}, ${JSON.stringify(content)}`
      )
      console.log(`🔄 [Debug] currentHistoryId: '${currentHistoryId}', selectedAction: ${selectedAction}`)

      // Lấy temporal state hiện tại
      const handle = this.client.workflow.getHandle(workflowId)
      const temporalState = await handle.query(getHoSoDataQuery)

      console.log(
        '🔄 Temporal state queried:',
        {
          temporalCurrentStep: temporalState.buocHienTai,
          requestedStepId: currentStepId,
          hasMovedToNextStep: temporalState.buocHienTai !== currentStepId,
        },
        {
          workflowId,
          currentStepId,
          currentHistoryId,
          selectedAction,
          isStepBack,
        }
      )

      // if (selectedAction === RHanhDongHoSo.NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG) {
      //   console.log('🔍 [DEBUG] selectedAction is NHAP_KQ_THAM_DINH_TGPL_CHAT_LUONG', currentStepId, temporalState)
      // }

      // Tìm current history từ database nếu currentHistoryId empty
      let actualParentHistoryId = currentHistoryId
      if (!currentHistoryId || currentHistoryId === '') {
        console.log('🔍 [Debug] currentHistoryId empty, tìm từ database...')

        const currentHistory = await this.db.workflowHistory
          .createQueryBuilder('wh')
          .where('wh.workflowId = :workflowId', { workflowId })
          .andWhere('wh.stepId = :stepId', { stepId: currentStepId })
          .andWhere('wh.status = :status', { status: WorkflowHistoryStatus.PENDING })
          .orderBy('wh.createdAt', 'DESC')
          .getOne()

        if (currentHistory) {
          actualParentHistoryId = currentHistory.id
          console.log(`🔍 [Debug] Found current history: ${actualParentHistoryId}`)
        } else {
          console.log('❌Service]  [Debug] Không tìm thấy current history')
        }
      }

      // Nếu temporal đã chuyển bước HOẶC là back step, sync database

      console.log(`🔄 [Debug] Completing history: ${actualParentHistoryId} - selectedAction: ${selectedAction}`)

      const workflowHistory = await this.workflowHistoryRepository.findById(actualParentHistoryId)

      if (workflowHistory.status === WorkflowHistoryStatus.COMPLETED) {
        console.log('⏸️ [Debug] History đã được complete')
        return {
          moved: false,
          currentStepId: currentStepId,
          historyId: actualParentHistoryId,
        }
      }

      if (selectedAction === RHanhDongHoSo.DONG_TGPL) {
        await this.caseAdvancedRepository.update(workflowHistory.caseAdvancedId, {
          status: EnumCaseAdvancedStatus.HOAN_THANH,
        })
      }

      // Complete current history và tạo next history
      if (actualParentHistoryId) {
        const completedAt = new Date()
        const updateResult = await this.workflowHistoryRepository.update(actualParentHistoryId, {
          status: WorkflowHistoryStatus.COMPLETED,
          selectedAction,
          completedAt,
          data: { completedAt, syncedFromTemporal: true, status: WorkflowHistoryStatus.COMPLETED } as Any,
          content,
        })
        await this.workflowRepository.updateHistories(workflowId)
        console.log(`✅ [Debug] History updated, affected: ${updateResult.affected} rows with content`, content)
      } else {
        console.log('⚠️ [Debug] Không có currentHistoryId để complete')
      }

      if (temporalState.buocHienTai !== currentStepId || isStepBack) {
        console.log('✅[Service]  Temporal đã chuyển bước - syncing database')
        // Sync và tạo history cho bước mới
        const syncedHistory = await this.syncAndCreateHistoryForCurrentStep(
          workflowId,
          temporalState,
          actualParentHistoryId,
          selectedAction,
          isStepBack,
          fixedForm
        )
        await this.workflowRepository.updateHistories(workflowId)

        console.log(
          `✅ [Debug] Tạo history ${isStepBack ? 'quay lại' : 'mới'}: ${syncedHistory?.id} cho step: ${temporalState.buocHienTai}`
        )

        return {
          moved: true,
          newStepId: temporalState.buocHienTai,
          historyId: syncedHistory?.id,
        }
      } else {
        console.log('⏸️ Temporal chưa chuyển bước - chỉ update action')
        console.log(`🔄 [Debug] Updating action cho history: ${actualParentHistoryId}`)

        // Chỉ update selected action, không complete
        if (actualParentHistoryId) {
          const updateResult = await this.workflowHistoryRepository.update(actualParentHistoryId, {
            selectedAction,
            data: { actionPerformedAt: new Date(), syncedFromTemporal: true } as Any,
            content,
          })
          await this.workflowRepository.updateHistories(workflowId)
          console.log(`✅ [Debug] Action updated, affected: ${updateResult.affected} rows`)
        }

        return {
          moved: false,
          currentStepId: currentStepId,
          historyId: actualParentHistoryId,
        }
      }
    } catch (error) {
      console.log('❌Service] [Service]  [Service] Database sync failed:', error)
      throw error
    }
  }
}
