---
description: Use this rule when asked to do crud nextjs
globs: 
alwaysApply: false
---

# Hướng dẫn tạo màn hình danh sách cho Next.js

**Document** • Version 1.0

----------

## 📁 1. <PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> thư mục

```txt
app/(admin)/[feature-name]/
├── page.tsx                    # Entry point với PermissionWrapper
├── [feature-name].page.tsx     # Component chính chứa logic
├── components/                 # Các component con (nếu cần)
└── [feature-name]-form.tsx    # Form tạo/sửa (nếu cần)
```

----------

## 📄 2. File Entry Point (page.tsx)

typescript

```typescript
'use client'

import { PermissionWrapper } from '@/components/permission-wrapper'
import { PERMISSIONS } from '@workspace/ui/constants/base'
import { YourFeaturePage } from './your-feature.page'

export default function Page() {
  return (
    <PermissionWrapper permissions={[PERMISSIONS.YOUR_READ_PERMISSION]}>
      <YourFeaturePage />
    </PermissionWrapper>
  )
}
```

----------

## 🎯 3. Component chính ([feature-name].page.tsx)

### 3.1. Import cần thiết

typescript

```typescript
'use client'

// Render components
import { RenderColumn } from '@/components/render-column'
import { RenderDateTime } from '@/components/render-datetime'
import { RenderInteractiveLink } from '@/components/render-interactive-link'
import { RenderStatus } from '@/components/render-status'
import { RenderText } from '@/components/render-text'
import { RenderAutoText } from '@/components/render-auto-text'
import { RenderCopyText } from '@/components/render-copy-text'

// Types & Constants
import { MODEL_STATUS, ModelStatus } from '@/constants'
import { YourDataType } from '@/lib/types'

// Store & State
import { useGlobalStore } from '@/stores/global.store'
import { useUserStore } from '@workspace/ui/stores/user.store'

// Table related
import { ColumnDef } from '@tanstack/react-table'
import { generateQueryParams, useEnhancedTable } from '@workspace/ui/hooks/use-table'
import { DataTable, selectColumn, TABLE_ALIGN, TABLE_SIZE } from '@workspace/ui/mi/table'

// UI Components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@workspace/ui/components/alert-dialog'
import { Badge } from '@workspace/ui/components/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog'
import { toast, toastError } from '@workspace/ui/components/toast'

// Layout & Buttons
import { AddButton } from '@workspace/ui/mi/add-button'
import { AdminPageContent, AdminPageLayout } from '@workspace/ui/mi/admin-page-layout'
import { DeleteButtonIcon } from '@workspace/ui/mi/delete-button-icon'
import { EditButtonIcon } from '@workspace/ui/mi/edit-button-icon'
import { ExportButton } from '@workspace/ui/mi/export-button'

// Icons
import { Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react'

// Hooks & Utilities
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
```

----------

### 3.2. State Management

typescript

```typescript
export function YourFeaturePage() {
  const t = useTranslations()
  const router = useRouter()
  const queryClient = useQueryClient()
  const { hasPermissions } = useUserStore()
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  
  // Selected item
  const [selectedItem, setSelectedItem] = useState<YourDataType | null>(null)
  
  // Export state
  const [isExporting, setIsExporting] = useState(false)
  
  // Load global data if needed
  useEffect(() => {
    // Load categories, organizations, etc.
  }, [])
}
```

----------

### 3.3. Định nghĩa Columns

#### Column cơ bản với RenderColumn

typescript

```typescript
const columns: ColumnDef<YourDataType>[] = [
  // Checkbox column
  selectColumn(t),
  
  // STT column
  {
    id: 'stt',
    header: 'STT',
    cell: ({ row }) => (
      <div className="text-center text-sm text-gray-600">
        {row.index + 1}
      </div>
    ),
    size: 50,
  },
  
  // Main content column with multiple info
  {
    accessorKey: 'name',
    meta: {
      title: 'Tên',
      filter: {
        type: 'text',
        placeholder: 'Tìm kiếm tên...',
        debounceMs: 300,
      },
    },
    cell: ({ row }) => (
      <RenderColumn>
        <RenderInteractiveLink
          onClick={() => router.push(`/your-feature/${row.original.id}`)}
        >
          {row.original.name}
        </RenderInteractiveLink>
        <RenderCopyText text={row.original.code} prefix="Mã" />
        <RenderText text={row.original.orgUnit?.name} prefix="Đơn vị" />
        <RenderAutoText 
          text={row.original.description} 
          textBehavior="wrap" 
          maxLines={2} 
          prefix="Mô tả" 
        />
      </RenderColumn>
    ),
  },
]
```

#### Column với Badge và Status hoặc dạng Enum trong API

Nếu có trạng thái dạng number (1, 0, -1, -99) theo API thì dùng component RenderStatus, nếu không thì viết ra 1 `Render[TableName]Status` ghi vào file này component trong thư mục `apps/web/components`, tất cả các Enum đều được định nghĩa trong `packages/data-types/src`

typescript

```typescript
{
  accessorKey: 'status',
  size: TABLE_SIZE.STATUS,
  meta: {
    title: 'Trạng thái',
    filter: {
      type: 'select',
      multiple: true,
      dataType: 'number',
      placeholder: 'Chọn trạng thái',
      options: MODEL_STATUS.map(status => ({
        label: t(status.label),
        value: status.value.toString(),
        icon: status.icon,
      })),
    },
  },
  cell: ({ row }) => (
    <RenderStatus status={row.original.status as ModelStatus} />
  ),
}

```

#### Column với Combobox Filter (API)

typescript

```typescript
{
  accessorKey: 'orgUnitId',
  enableHiding: true,
  meta: {
    showToggle: false,
    title: 'Đơn vị',
    filter: {
      type: 'combobox',
      position: 'advanced',
      placeholder: 'Chọn đơn vị',
      apiRequest: async (keyword?: string) => {
        const response = await fetch(`/api/organizations?keyword=${keyword}&pageSize=50`)
        const data = await response.json()
        
        return data.items?.map((item: any) => ({
          label: item.name || 'Không có tên',
          value: item.id || item._id,
        })).filter((item: any) => item.value && item.label) || []
      },
    },
  },
}
```

#### Column với Date Range Filter

typescript

```typescript
{
  accessorKey: 'createdAt',
  size: TABLE_SIZE.DATETIME,
  meta: {
    title: 'Ngày tạo',
    filter: {
      type: 'date-range',
      position: 'advanced',
    },
  },
  cell: ({ row }) => {
    const showTime = table.options.meta?.uiState?.showTime ?? false
    return <RenderDateTime datetime={row.getValue('createdAt')} showTime={showTime} />
  },
}
```

#### Actions Column với Dropdown Menu

typescript

```typescript
{
  id: 'actions',
  size: TABLE_SIZE.ACTIONS,
  meta: {
    title: 'Thao tác',
  },
  cell: ({ row }) => {
    const item = row.original
    
    return (
      <div className={TABLE_ALIGN.ACTIONS}>
        <EditButtonIcon
          permission={PERMISSIONS.YOUR_UPDATE_PERMISSION}
          onClick={() => {
            setSelectedItem(item)
            setIsEditDialogOpen(true)
          }}
        />
        <DeleteButtonIcon
          permission={PERMISSIONS.YOUR_DELETE_PERMISSION}
          onClick={() => {
            setSelectedItem(item)
            setIsDeleteDialogOpen(true)
          }}
        />
      </div>
    )
  },
}
```

----------

### 3.4. Table Configuration

typescript

```typescript
const { table } = useEnhancedTable<YourDataType>({
  columns,
  pageName: 'your-feature',
  keyObject: {},
  queryFn: async state => {
    // Build filter params
    const params = generateQueryParams(state, {
      baseParams: {},
      columns,
    })
    
    // Fetch data
    const res = await fetch(`/api/your-endpoint?${params}`, {
      cache: 'no-store',
    })
    
    if (!res.ok) {
      toastError(res, 'Không thể tải dữ liệu')
    }
    
    const data = await res.json()
    
    return {
      items: data.items || [],
      totalPages: data.pagination?.totalPages || 0,
      totalItems: data.pagination?.total || 0,
    }
  },
  initialState: {
    columnVisibility: {
      // Hide columns by default
      orgUnitId: false,
      updatedAt: false,
    },
    sorting: [{ id: 'createdAt', desc: true }],
    uiState: {
      showTime: false,
      showAdvancedFilter: false,
    },
  },
  enabled: true,
  queryKey: ['your-feature'],
})
```

----------

### 3.5. CRUD Handlers

#### Delete Handler

typescript

```typescript
const handleDelete = async () => {
  if (!selectedItem) {
    toast.error('Không tìm thấy mục cần xóa')
    return
  }
  
  try {
    const response = await fetch(`/api/your-endpoint/${selectedItem.id}`, {
      method: 'DELETE',
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Xóa thất bại')
    }
    
    toast.success(`Đã xóa "${selectedItem.name}" thành công`)
    table.options.meta?.reload()
    setIsDeleteDialogOpen(false)
  } catch (error) {
    console.error('Delete error:', error)
    toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
  }
}
```

#### Export Excel Handler

typescript

```typescript
import { useExportExcel } from '@workspace/ui/hooks/use-export-excel'

const { exportToExcel } = useExportExcel()

const handleExportExcel = async () => {
  try {
    setIsExporting(true)
    
    // Fetch all data
    const response = await fetch('/api/your-endpoint/all?orderBy=createdAt&orderDir=DESC')
    const data = await response.json()
    
    // Transform data for Excel
    const excelData = data.items.map((item: YourDataType, index: number) => ({
      'STT': index + 1,
      'Mã': item.code || '',
      'Tên': item.name || '',
      'Đơn vị': item.orgUnit?.name || '',
      'Trạng thái': item.status === 1 ? 'Hoạt động' : 'Không hoạt động',
      'Ngày tạo': item.createdAt ? new Date(item.createdAt).toLocaleDateString('vi-VN') : '',
    }))
    
    exportToExcel(excelData, {
      fileName: `danh-sach-${new Date().toISOString().split('T')[0]}`,
      sheetName: 'Danh sách',
    })
    
    toast.success('Xuất Excel thành công!')
  } catch (error) {
    console.error('Export error:', error)
    toast.error('Có lỗi xảy ra khi xuất Excel')
  } finally {
    setIsExporting(false)
  }
}
```

----------

### 3.6. JSX Structure

typescript

```typescript
return (
  <>
    <AdminPageLayout
      breadcrumb={[
        {
          label: 'Danh sách',
          href: '/your-feature',
        },
      ]}
    >
      <AdminPageContent
        title="Tiêu đề trang"
        subtitle="Mô tả ngắn về trang"
        icon={<YourIcon className="h-5 w-5" />}
        helpOptions={{
          content: {
            content: 'Nội dung hướng dẫn sử dụng trang này',
          },
          video: {
            url: 'https://www.youtube.com/embed/VIDEO_ID',
          },
        }}
        actions={[
          <ExportButton
            key="export"
            variant="outline"
            isExporting={isExporting}
            onClick={handleExportExcel}
            disabled={isExporting}
          />,
          <AddButton
            key="create"
            permission={PERMISSIONS.YOUR_CREATE_PERMISSION}
            tooltip="Tạo mới"
            onClick={() => setIsCreateDialogOpen(true)}
          />,
        ]}
      >
        <DataTable
          table={table}
          searchOptions={{
            helpContent: 'Tìm kiếm theo tên, mã, email...',
          }}
        />
      </AdminPageContent>
    </AdminPageLayout>

    {/* Create Dialog */}
    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Tạo mới</DialogTitle>
        </DialogHeader>
        <YourForm
          onSuccess={() => {
            setIsCreateDialogOpen(false)
            table.options.meta?.reload()
          }}
          onCancel={() => setIsCreateDialogOpen(false)}
        />
      </DialogContent>
    </Dialog>

    {/* Edit Dialog */}
    <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa</DialogTitle>
        </DialogHeader>
        <YourForm
          initialData={selectedItem}
          onSuccess={() => {
            setIsEditDialogOpen(false)
            table.options.meta?.reload()
          }}
          onCancel={() => setIsEditDialogOpen(false)}
        />
      </DialogContent>
    </Dialog>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa "{selectedItem?.name || ''}"? 
            Hành động này không thể hoàn tác.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Hủy</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-destructive hover:bg-destructive/90 text-white"
          >
            Xóa
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </>
)
```

----------

## 🔧 4. Filter Types Chi Tiết

### 4.1. Text Filter

typescript

```typescript
filter: {
  type: 'text',
  placeholder: 'Tìm kiếm...',
  debounceMs: 300,
  position: 'advanced', // optional: 'top' | 'advanced'
  label: 'Nhãn filter', // optional
}
```

### 4.2. Select Filter

typescript

```typescript
filter: {
  type: 'select',
  multiple: true, // cho phép chọn nhiều
  dataType: 'number', // 'string' | 'number'
  placeholder: 'Chọn...',
  options: [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
  ],
}
```

### 4.3. Combobox Filter (Static)

typescript

```typescript
filter: {
  type: 'combobox',
  multiple: true,
  placeholder: 'Chọn...',
  options: [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
  ],
}
```

### 4.4. Combobox Filter (Dynamic API)

typescript

```typescript
filter: {
  type: 'combobox',
  multiple: true,
  placeholder: 'Chọn đơn vị',
  apiRequest: async (keyword?: string) => {
    const response = await categoryApi.getCategories({
      keyword,
      filter: { type: 'ORGANIZATION' },
      pageSize: 50,
    })
    
    const mappedData = response.items?.map((item: any) => {
      const value = item.id || item._id || item.categoryId || item.code
      return {
        label: item.name || 'Không có tên',
        value: value,
      }
    }).filter((item: any) => item.value && item.label) || []
    
    return mappedData
  },
}
```

### 4.5. Date Range Filter

typescript

```typescript
filter: {
  type: 'date-range',
  position: 'advanced',
}
```

----------

## 📊 5. Render Components

### RenderColumn

Hiển thị nhiều thông tin theo chiều dọc

typescript

```typescript
<RenderColumn>
  <RenderInteractiveLink onClick={() => {}}>Tiêu đề</RenderInteractiveLink>
  <RenderCopyText text="ABC123" prefix="Mã" />
  <RenderText text="Thông tin" prefix="Loại" />
</RenderColumn>
```

### RenderInteractiveLink

Link có thể click với permission

typescript

```typescript
<RenderInteractiveLink
  permission={PERMISSIONS.YOUR_UPDATE_PERMISSION}
  onClick={() => router.push('/detail')}
>
  Tên hiển thị
</RenderInteractiveLink>
```

### RenderText

Hiển thị text với prefix

typescript

```typescript
<RenderText text="Nội dung" prefix="Nhãn" />
```

### RenderCopyText

Hiển thị text có thể copy

typescript

```typescript
<RenderCopyText text="ABC123" prefix="Mã" />
```

### RenderAutoText

Tự động wrap hoặc truncate text

typescript

```typescript
<RenderAutoText 
  text={longText} 
  textBehavior="wrap" // 'wrap' | 'truncate'
  maxLines={2} 
  prefix="Mô tả"
  defaultValue="Chưa cập nhật"
/>
```

### RenderDateTime

Hiển thị ngày giờ

typescript

```typescript
<RenderDateTime 
  datetime={item.createdAt} 
  showTime={true}
  prefix="Ngày tạo" 
/>
```

### RenderStatus

Hiển thị trạng thái với badge màu

typescript

```typescript
<RenderStatus status={item.status as ModelStatus} />
```

### RenderUser

Hiển thị thông tin user

typescript

```typescript
<RenderUser user={item.createdBy} />
```

----------

## ✅ 6. Best Practices

### 6.1. Code Organization

- ✅ Luôn sử dụng `'use client'` directive ở đầu file
- ✅ Tách imports thành các nhóm rõ ràng
- ✅ Đặt tên biến, hàm có ý nghĩa
- ✅ Sử dụng TypeScript types đầy đủ

### 6.2. Permission & Security

- ✅ Wrap page với `PermissionWrapper`
- ✅ Check permission ở buttons và actions
- ✅ Validate dữ liệu trước khi gửi API

### 6.3. User Experience

- ✅ Hiển thị loading state khi fetch data
- ✅ Show toast notification cho mọi action
- ✅ Confirm trước khi xóa
- ✅ Reload table sau CRUD operations

### 6.4. Performance

- ✅ Sử dụng `debounceMs` cho text filter
- ✅ Cache API responses khi có thể
- ✅ Lazy load components nếu cần
- ✅ Ẩn các column không cần thiết mặc định

### 6.5. Table Configuration

- ✅ Sử dụng `TABLE_SIZE` constants cho column width
- ✅ Sử dụng `TABLE_ALIGN` cho alignment
- ✅ Set `initialState` hợp lý
- ✅ Enable/disable columns với `columnVisibility`

----------

## ❌ 7. Common Pitfalls (Lỗi thường gặp)

### ❌ Tránh

1. Quên thêm `'use client'` directive
2. Không xử lý lỗi API với try-catch
3. Không reload table sau CRUD operations
4. Hard-code permissions thay vì dùng constants
5. Không có loading states
6. Fetch toàn bộ data mỗi lần render
7. Không validate input trước khi submit
8. Quên xử lý edge cases (null, undefined)
9. Không kiểm tra permissions ở UI
10. Copy-paste code không hiểu rõ logic

### ✅ Nên

1. Sử dụng type-safe với TypeScript
2. Tách logic phức tạp ra custom hooks
3. Sử dụng constants cho permissions
4. Implement proper error boundaries
5. Add loading indicators cho async operations
6. Validate dữ liệu ở cả client và server
7. Handle edge cases gracefully
8. Test thoroughly trước khi deploy
9. Document code khi cần thiết
10. Follow team coding conventions

----------

## 🎨 8. Advanced Patterns

### 8.1. Custom Cell Renderer với Dropdown Menu

typescript

```typescript
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu'

{
  id: 'actions',
  cell: ({ row }) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleView(row.original)}>
          <Eye className="h-4 w-4" />
          Xem chi tiết
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleEdit(row.original)}>
          <Edit className="h-4 w-4" />
          Chỉnh sửa
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleDelete(row.original)}
          className="text-red-600"
        >
          <Trash2 className="h-4 w-4" />
          Xóa
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  ),
}
```

### 8.2. Row Selection với Bulk Actions

typescript

```typescript
// State
const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())

// Helper functions
const toggleRowSelection = (id: string) => {
  const newSelected = new Set(selectedRows)
  if (newSelected.has(id)) {
    newSelected.delete(id)
  } else {
    newSelected.add(id)
  }
  setSelectedRows(newSelected)
}

const toggleAllRows = (items: YourDataType[]) => {
  if (selectedRows.size === items.length) {
    setSelectedRows(new Set())
  } else {
    setSelectedRows(new Set(items.map(item => item.id)))
  }
}

// Checkbox column
{
  id: 'select',
  header: ({ table }) => (
    <Checkbox
      checked={selectedRows.size === table.getRowModel().rows.length}
      onCheckedChange={() => toggleAllRows(table.getRowModel().rows.map(row => row.original))}
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={selectedRows.has(row.original.id)}
      onCheckedChange={() => toggleRowSelection(row.original.id)}
    />
  ),
}

// Bulk actions
{selectedRows.size > 0 && (
  <div className="flex gap-2">
    <Button onClick={handleBulkDelete}>
      Xóa {selectedRows.size} mục
    </Button>
    <Button onClick={handleBulkExport}>
      Xuất {selectedRows.size} mục
    </Button>
  </div>
)}
```

### 8.3. Complex Filter với nhiều điều kiện

typescript

```typescript
queryFn: async state => {
  // Build filter object từ state.filter
  const filterObj: Record<string, any> = {}
  
  state.filter.forEach(filter => {
    if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
      // Map field names to API parameter names
      let apiFieldName = filter.id
      
      if (filter.id === 'legalField') {
        apiFieldName = 'legalFieldId'
      }
      
      // Handle different value types
      if (typeof filter.value === 'string' || typeof filter.value === 'number') {
        filterObj[apiFieldName] = filter.value
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        const firstValue = filter.value[0]
        if (typeof firstValue === 'object' && 'value' in firstValue) {
          filterObj[apiFieldName] = firstValue.value
        }
      } else if (typeof filter.value === 'object' && 'from' in filter.value) {
        // Date range
        const dateRange = filter.value as { from?: Date; to?: Date }
        if (dateRange.from) {
          filterObj[`${apiFieldName}From`] = dateRange.from.toLocaleDateString('en-CA')
        }
        if (dateRange.to) {
          filterObj[`${apiFieldName}To`] = dateRange.to.toLocaleDateString('en-CA')
        }
      }
    }
  })
  
  const params = generateQueryParams(state, {
    baseParams: { filter: filterObj },
    columns,
  })
  
  const res = await fetch(`/api/your-endpoint?${params}`)
  return res.json()
}
```

----------

## 📚 9. API Response Format

### Expected Response Structure

typescript

```typescript
interface ApiResponse<T> {
  items: T[]
  pagination: {
    total: number
    totalPages: number
    page: number
    pageSize: number
  }
  message?: string
  success?: boolean
  error?: string
}

// queryFn should return:
return {
  items: data.items || [],
  totalPages: data.pagination?.totalPages || 0,
  totalItems: data.pagination?.total || 0,
}
```

----------

## 🧪 10. Testing Checklist

### UI Testing

- Trang hiển thị đúng layout
- Columns hiển thị đầy đủ
- Filters hoạt động đúng
- Sorting hoạt động đúng
- Pagination hoạt động đúng
- Search hoạt động đúng

### Functionality Testing

- Create: Form validation, API call, toast, reload
- Update: Load data, validation, API call, toast, reload
- Delete: Confirmation, API call, toast, reload
- Export: Fetch all data, transform, download file

### Permission Testing

- Page access với đúng permission
- Buttons ẩn/hiện theo permission
- Actions ẩn/hiện theo permission

### Error Handling

- API error được handle và hiển thị
- Network error được handle
- Invalid data được validate
- Empty states được hiển thị

----------

## 📖 11. Quick Reference

### Table Size Constants

typescript

```typescript
TABLE_SIZE.ACTIONS = 100
TABLE_SIZE.STATUS = 150
TABLE_SIZE.DATETIME = 180
```

### Table Align Constants

typescript

```typescript
TABLE_ALIGN.ACTIONS = 'flex items-center justify-center gap-1'
TABLE_ALIGN.CENTER = 'text-center'
TABLE_ALIGN.RIGHT = 'text-right'
```

### Model Status Values

typescript

```typescript
MODEL_STATUS = [
  { label: 'status.active', value: 1, icon: CheckCircle },
  { label: 'status.inactive', value: 0, icon: XCircle },
]
```

----------

## 🔗 12. Related Resources

- [TanStack Table Documentation](https://tanstack.com/table/v8)
- [Next.js Documentation](https://nextjs.org/docs)
- [Shadcn UI Components](https://ui.shadcn.com/)
- [React Hook Form](https://react-hook-form.com/)

----------

**Version History:**

- v1.0 (02/10/2025) - Initial documentation
